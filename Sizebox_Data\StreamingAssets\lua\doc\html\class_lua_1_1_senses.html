<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.Senses Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_senses.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="class_lua_1_1_senses-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.Senses Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Control the senses of a entity such as the vision.  
 <a href="class_lua_1_1_senses.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a57bce39cc60df494445e5869f4dec72b"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_senses.html#a57bce39cc60df494445e5869f4dec72b">CanSee</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> target)</td></tr>
<tr class="memdesc:a57bce39cc60df494445e5869f4dec72b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if the entity can see their target.  <a href="class_lua_1_1_senses.html#a57bce39cc60df494445e5869f4dec72b">More...</a><br /></td></tr>
<tr class="separator:a57bce39cc60df494445e5869f4dec72b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a91a2c0ed86752640a17f70d112459239"><td class="memItemLeft" align="right" valign="top">List&lt; <a class="el" href="class_lua_1_1_entity.html">Entity</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_senses.html#a91a2c0ed86752640a17f70d112459239">GetVisibleEntities</a> (float distance)</td></tr>
<tr class="memdesc:a91a2c0ed86752640a17f70d112459239"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the list of all visible entities. You have to choose a max distance relative to the agent, this is to reduce the number of entities to perform the visibiliy tests.  <a href="class_lua_1_1_senses.html#a91a2c0ed86752640a17f70d112459239">More...</a><br /></td></tr>
<tr class="separator:a91a2c0ed86752640a17f70d112459239"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abc4b35f7f0c31f0ab2628631f4df3f97"><td class="memItemLeft" align="right" valign="top">List&lt; <a class="el" href="class_lua_1_1_entity.html">Entity</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_senses.html#abc4b35f7f0c31f0ab2628631f4df3f97">GetEntitiesInRadius</a> (float distance)</td></tr>
<tr class="memdesc:abc4b35f7f0c31f0ab2628631f4df3f97"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the list of all entities within a distance relative to the agent.  <a href="class_lua_1_1_senses.html#abc4b35f7f0c31f0ab2628631f4df3f97">More...</a><br /></td></tr>
<tr class="separator:abc4b35f7f0c31f0ab2628631f4df3f97"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a50d6bb5aa8736f7e7e801601b62ecd95"><td class="memItemLeft" align="right" valign="top">List&lt; <a class="el" href="class_lua_1_1_entity.html">Entity</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_senses.html#a50d6bb5aa8736f7e7e801601b62ecd95">GetVisibleMicros</a> (float distance)</td></tr>
<tr class="memdesc:a50d6bb5aa8736f7e7e801601b62ecd95"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the list of all visible micros. You have to choose a max distance relative to the agent, this is to reduce the number of entities to perform the visibiliy tests.  <a href="class_lua_1_1_senses.html#a50d6bb5aa8736f7e7e801601b62ecd95">More...</a><br /></td></tr>
<tr class="separator:a50d6bb5aa8736f7e7e801601b62ecd95"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2ce5c645f14597c8682bb8bf127ea7b1"><td class="memItemLeft" align="right" valign="top">List&lt; <a class="el" href="class_lua_1_1_entity.html">Entity</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_senses.html#a2ce5c645f14597c8682bb8bf127ea7b1">GetMicrosInRadius</a> (float radius)</td></tr>
<tr class="memdesc:a2ce5c645f14597c8682bb8bf127ea7b1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns all the micros in the agent radius (relative to their size).  <a href="class_lua_1_1_senses.html#a2ce5c645f14597c8682bb8bf127ea7b1">More...</a><br /></td></tr>
<tr class="separator:a2ce5c645f14597c8682bb8bf127ea7b1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3ef9b23d5423c511f21c10f6284073f6"><td class="memItemLeft" align="right" valign="top">List&lt; <a class="el" href="class_lua_1_1_entity.html">Entity</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_senses.html#a3ef9b23d5423c511f21c10f6284073f6">GetGiantessesInRadius</a> (float radius)</td></tr>
<tr class="memdesc:a3ef9b23d5423c511f21c10f6284073f6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns all the giantesses in the agent radius.  <a href="class_lua_1_1_senses.html#a3ef9b23d5423c511f21c10f6284073f6">More...</a><br /></td></tr>
<tr class="separator:a3ef9b23d5423c511f21c10f6284073f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="properties" name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:aed3385cd359ba4734ac8c114b524a1b2"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_senses.html#aed3385cd359ba4734ac8c114b524a1b2">baseVisibilityDistance</a><code> [get, set]</code></td></tr>
<tr class="memdesc:aed3385cd359ba4734ac8c114b524a1b2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Changes the base visibility distance. This is multiplied to the target scale. If the entity is 0.05 compared to the agent, and the baseVisibility distance is 100, then the target will be visible at most at 5 meters (in the agent scale).  <a href="class_lua_1_1_senses.html#aed3385cd359ba4734ac8c114b524a1b2">More...</a><br /></td></tr>
<tr class="separator:aed3385cd359ba4734ac8c114b524a1b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac4a708caaa7a7381870d4f21d619c2e9"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_senses.html#ac4a708caaa7a7381870d4f21d619c2e9">fieldOfView</a><code> [get, set]</code></td></tr>
<tr class="memdesc:ac4a708caaa7a7381870d4f21d619c2e9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modify the field of view of the agent. The value can range from 0 to 360.  <a href="class_lua_1_1_senses.html#ac4a708caaa7a7381870d4f21d619c2e9">More...</a><br /></td></tr>
<tr class="separator:ac4a708caaa7a7381870d4f21d619c2e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >Control the senses of a entity such as the vision. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a57bce39cc60df494445e5869f4dec72b" name="a57bce39cc60df494445e5869f4dec72b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a57bce39cc60df494445e5869f4dec72b">&#9670;&nbsp;</a></span>CanSee()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.Senses.CanSee </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td>
          <td class="paramname"><em>target</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns true if the entity can see their target. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">target</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="abc4b35f7f0c31f0ab2628631f4df3f97" name="abc4b35f7f0c31f0ab2628631f4df3f97"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abc4b35f7f0c31f0ab2628631f4df3f97">&#9670;&nbsp;</a></span>GetEntitiesInRadius()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">List&lt; <a class="el" href="class_lua_1_1_entity.html">Entity</a> &gt; Lua.Senses.GetEntitiesInRadius </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>distance</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the list of all entities within a distance relative to the agent. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">distance</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a3ef9b23d5423c511f21c10f6284073f6" name="a3ef9b23d5423c511f21c10f6284073f6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3ef9b23d5423c511f21c10f6284073f6">&#9670;&nbsp;</a></span>GetGiantessesInRadius()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">List&lt; <a class="el" href="class_lua_1_1_entity.html">Entity</a> &gt; Lua.Senses.GetGiantessesInRadius </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>radius</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns all the giantesses in the agent radius. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">radius</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a2ce5c645f14597c8682bb8bf127ea7b1" name="a2ce5c645f14597c8682bb8bf127ea7b1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2ce5c645f14597c8682bb8bf127ea7b1">&#9670;&nbsp;</a></span>GetMicrosInRadius()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">List&lt; <a class="el" href="class_lua_1_1_entity.html">Entity</a> &gt; Lua.Senses.GetMicrosInRadius </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>radius</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns all the micros in the agent radius (relative to their size). </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">radius</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a91a2c0ed86752640a17f70d112459239" name="a91a2c0ed86752640a17f70d112459239"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a91a2c0ed86752640a17f70d112459239">&#9670;&nbsp;</a></span>GetVisibleEntities()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">List&lt; <a class="el" href="class_lua_1_1_entity.html">Entity</a> &gt; Lua.Senses.GetVisibleEntities </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>distance</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the list of all visible entities. You have to choose a max distance relative to the agent, this is to reduce the number of entities to perform the visibiliy tests. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">distance</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a50d6bb5aa8736f7e7e801601b62ecd95" name="a50d6bb5aa8736f7e7e801601b62ecd95"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a50d6bb5aa8736f7e7e801601b62ecd95">&#9670;&nbsp;</a></span>GetVisibleMicros()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">List&lt; <a class="el" href="class_lua_1_1_entity.html">Entity</a> &gt; Lua.Senses.GetVisibleMicros </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>distance</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the list of all visible micros. You have to choose a max distance relative to the agent, this is to reduce the number of entities to perform the visibiliy tests. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">distance</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="aed3385cd359ba4734ac8c114b524a1b2" name="aed3385cd359ba4734ac8c114b524a1b2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aed3385cd359ba4734ac8c114b524a1b2">&#9670;&nbsp;</a></span>baseVisibilityDistance</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Senses.baseVisibilityDistance</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Changes the base visibility distance. This is multiplied to the target scale. If the entity is 0.05 compared to the agent, and the baseVisibility distance is 100, then the target will be visible at most at 5 meters (in the agent scale). </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ac4a708caaa7a7381870d4f21d619c2e9" name="ac4a708caaa7a7381870d4f21d619c2e9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac4a708caaa7a7381870d4f21d619c2e9">&#9670;&nbsp;</a></span>fieldOfView</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Senses.fieldOfView</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Modify the field of view of the agent. The value can range from 0 to 360. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaSenses.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_senses.html">Senses</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

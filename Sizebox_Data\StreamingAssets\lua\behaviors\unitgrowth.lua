Behavior = RegisterBehavior("unitGrowth")

Behavior.data = { 

    agent = {
        type = { "giantess" }
    },
    target = {
        type = { "oneself" }
    },

    menuEntry = "Size/Unit Growth Control",
    ai = true,
    secondary = false,
}

function Behavior:Start()
	self.growthamount = 0
    self.runninggrowth = 0
    self.percentage = 0
    self.decimal = 0
    self.growthSpeed = 3.0
    self.unit = {"m", "km", "cm", "mm", "um", "mi", "ft", "in", "th"}
    self.u = 1
    self.controlmode = 0
    Log("Unit Growth Control script started")
    Log("Type inputs numerically while holding u (grow), i (shrink), = (set), or - (set duration)")
    Log("Press y to change size and l to exit, press [ or ] to toggle through units")
end

function Behavior:Update()
	if Input.GetKey("u") or Input.GetKey("i") or Input.GetKey("=") or Input.GetKey("-") then
		if Input.GetKeyDown("0") or Input.GetKeyDown("[0]") then
			self:TypeHeight(0)
		elseif Input.GetKeyDown("1") or Input.GetKeyDown("[1]") then
			self:TypeHeight(1)
		elseif Input.GetKeyDown("2") or Input.GetKeyDown("[2]") then
			self:TypeHeight(2)
		elseif Input.GetKeyDown("3") or Input.GetKeyDown("[3]") then
			self:TypeHeight(3)
		elseif Input.GetKeyDown("4") or Input.GetKeyDown("[4]") then
			self:TypeHeight(4)
		elseif Input.GetKeyDown("5") or Input.GetKeyDown("[5]") then
			self:TypeHeight(5)
		elseif Input.GetKeyDown("6") or Input.GetKeyDown("[6]") then
			self:TypeHeight(6)
		elseif Input.GetKeyDown("7") or Input.GetKeyDown("[7]") then
			self:TypeHeight(7)
		elseif Input.GetKeyDown("8") or Input.GetKeyDown("[8]") then
			self:TypeHeight(8)
		elseif Input.GetKeyDown("9") or Input.GetKeyDown("[9]") then
			self:TypeHeight(9)
        elseif Input.GetKeyDown(".") or Input.GetKeyDown("[.]") and self.decimal == 0 then
			self.decimal = 1
		end
	end

	if Input.GetKeyUp("u") or Input.GetKeyUp("i") or Input.GetKeyUp("=") then
		growth = Mathf.ConvertToMeter(self.runninggrowth, self.unit[self.u])
		-- growthamount will always be in meters, so we display runninggrowth
		if Input.GetKeyUp("i") then
			self.growthamount = 0.0 - growth
			Log("Shrink amount set to " .. self.runninggrowth .. self.unit[self.u])
			self.controlmode = 0
		elseif Input.GetKeyUp("u") then
			self.growthamount = growth
			Log("Growth amount set to " .. self.runninggrowth .. self.unit[self.u])
			self.controlmode = 0
		elseif Input.GetKeyUp("=") then
			self.growthamount = growth
			Log("Height will be set to " .. self.runninggrowth .. self.unit[self.u])
			self.controlmode = 1
		end
		self.decimal = 0
		self.runninggrowth = 0
	end

	if Input.GetKeyUp("-") then
		self.growthSpeed = self.runninggrowth
		Log("Size change over " .. self.runninggrowth .. "s")
		self.decimal = 0
		self.runninggrowth = 0
	end

	if Input.GetKeyDown("l") then
		self.agent.ai.StopBehavior()
	end

	if Input.GetKeyDown("]") then
		self.u = self.u + 1
		if self.u > 9 then
			self.u = 1
		end
		Log("Set Unit to " .. self.unit[self.u])
	end

	if Input.GetKeyDown("[") then
		self.u = self.u - 1
		if self.u < 1 then
			self.u = 9
		end
		Log("Set Unit to " .. self.unit[self.u])
	end

    if Input.GetKeyDown("y") then -- set new size
    	if self.controlmode == 0 then
	        height = self.agent.metricHeight + self.growthamount
	    else
	    	height = self.growthamount
	    end
	    self.percentage = (height / self.agent.metricHeight) - 1.0
	    self.agent.Grow(self.percentage, self.growthSpeed)
        Log("Resulting Size: " .. height .. " meters")
    end
end

function Behavior:TypeHeight(int)
	if self.decimal > 0 then
		self.runninggrowth = self.runninggrowth + (int / (10.0 ^ self.decimal))
		self.decimal = self.decimal + 1
	else
		self.runninggrowth = (self.runninggrowth * 10.0) + int
	end
end

function Behavior:Exit() 
    Log("Exiting unit growth.")
end
sitanimation = RegisterBehavior("sitanimation")
sitanimation.data = {
    menuEntry = "GTS Animation Keybound",
    agent = {
        type = { "giantess", "player"}
    },
    target = {
        type = { "none" }
    }
}



function sitanimation:Start()
	log("Thanks to <PERSON><PERSON><PERSON> for basically writing this script cuz i failed so bad")
	log("Animations - Sit: Key - h")
	log("Animations - Crouch: Key - j")
	log("Animations - Lie Down: Key - k")
	log("Animations - Idle (Stand up): Key - l")
end

function sitanimation:Update()
	if Input.GetKeyDown("h") then
		self.agent.ai.StopAction()
		self.agent.animation.Set("Sit 4")
	elseif Input.GetKeyDown("j") then
		self.agent.ai.StopAction()
		self.agent.animation.Set("Crouch Idle")
	elseif Input.GetKeyDown("k") then
		self.agent.animation.SetAndWait("Sit 4")
		self.agent.animation.Set("Lie Down")
	elseif Input.GetKeyDown("l") then
		self.agent.ai.StopAction()
		self.agent.animation.Set("Idle 4")
	end
end
	
function sitanimation:Exit()
    log("Ended")
end
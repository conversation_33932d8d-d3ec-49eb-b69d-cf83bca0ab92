-- Room Spawner Script for Sizebox
-- Spawns scalable rooms that adjust to macro size and position both characters safely

local RoomSpawner = {}
RoomSpawner.__index = RoomSpawner

function RoomSpawner:new()
    local obj = {}
    setmetatable(obj, RoomSpawner)
    
    -- Room configuration
    obj.roomHeight = 200  -- Y offset above ground
    obj.currentRoom = nil
    obj.roomScale = 1.0
    obj.isRoomActive = false
    
    -- Room keywords for chat detection
    obj.roomKeywords = {
        "take me to room",
        "spawn room",
        "create room",
        "room spawn",
        "make room",
        "build room",
        "room time",
        "go to room"
    }
    
    obj.removeKeywords = {
        "remove room",
        "delete room",
        "destroy room",
        "clear room",
        "no room"
    }
    
    print("Room Spawner initialized")
    return obj
end

function RoomSpawner:GetMacroCharacter()
    -- Find the macro character (giantess)
    local characters = EntityManager.GetCharacters()
    for i = 0, characters.Count - 1 do
        local character = characters[i]
        if character and character.transform.localScale.x > 5 then  -- Assume macro if scale > 5
            return character
        end
    end
    return nil
end

function RoomSpawner:GetMicroCharacter()
    -- Find the micro character (player)
    local characters = EntityManager.GetCharacters()
    for i = 0, characters.Count - 1 do
        local character = characters[i]
        if character and character.transform.localScale.x <= 5 then  -- Assume micro if scale <= 5
            return character
        end
    end
    return nil
end

function RoomSpawner:GetMacroScale()
    local macro = self:GetMacroCharacter()
    if macro then
        return macro.transform.localScale.x
    end
    return 1.0
end

function RoomSpawner:SpawnRoom()
    local macro = self:GetMacroCharacter()
    local micro = self:GetMicroCharacter()
    
    if not macro then
        print("ERROR: No macro character found for room spawning")
        return false
    end
    
    -- Remove existing room if any
    if self.currentRoom then
        self:RemoveRoom()
    end
    
    -- Get macro position and scale
    local macroPos = macro.transform.position
    local macroScale = self:GetMacroScale()
    
    print(f"Spawning room at macro position: {macroPos.x}, {macroPos.y}, {macroPos.z}")
    print(f"Macro scale: {macroScale}")
    
    -- Calculate room position (200 units above macro)
    local roomPosition = Vector3(
        macroPos.x,
        macroPos.y + self.roomHeight,
        macroPos.z
    )
    
    -- Calculate room scale based on macro size
    self.roomScale = macroScale * 0.5  -- Room is half the macro's scale
    
    -- Try to spawn ROOM1 object
    local success = false
    
    -- Method 1: Try using existing object spawning system
    if EntityManager and EntityManager.SpawnObject then
        try {
            self.currentRoom = EntityManager.SpawnObject("ROOM1", roomPosition)
            if self.currentRoom then
                success = true
                print("Room spawned using EntityManager.SpawnObject")
            end
        } catch {
            print("EntityManager.SpawnObject failed, trying alternative method")
        }
    end
    
    -- Method 2: Try using GameObject instantiation
    if not success and GameObject then
        try {
            -- Look for ROOM1 prefab or existing object
            local roomPrefab = GameObject.Find("ROOM1")
            if roomPrefab then
                self.currentRoom = GameObject.Instantiate(roomPrefab)
                success = true
                print("Room spawned using GameObject.Instantiate")
            else
                print("ROOM1 prefab not found")
            end
        } catch {
            print("GameObject.Instantiate failed")
        }
    end
    
    -- Method 3: Try using Resources.Load
    if not success and Resources then
        try {
            local roomPrefab = Resources.Load("ROOM1")
            if roomPrefab then
                self.currentRoom = GameObject.Instantiate(roomPrefab)
                success = true
                print("Room spawned using Resources.Load")
            end
        } catch {
            print("Resources.Load failed")
        }
    end
    
    if success and self.currentRoom then
        -- Set room position
        self.currentRoom.transform.position = roomPosition
        
        -- Set room scale
        local roomScaleVector = Vector3(self.roomScale, self.roomScale, self.roomScale)
        self.currentRoom.transform.localScale = roomScaleVector
        
        print(f"Room positioned at: {roomPosition.x}, {roomPosition.y}, {roomPosition.z}")
        print(f"Room scaled to: {self.roomScale}")
        
        -- Move macro up by roomHeight
        local newMacroPos = Vector3(
            macroPos.x,
            macroPos.y + self.roomHeight,
            macroPos.z
        )
        macro.transform.position = newMacroPos
        print(f"Macro moved to: {newMacroPos.x}, {newMacroPos.y}, {newMacroPos.z}")
        
        -- Move micro near macro if micro exists
        if micro then
            local newMicroPos = Vector3(
                macroPos.x + (macroScale * 0.1),  -- Slightly offset from macro
                macroPos.y + self.roomHeight + (macroScale * 0.05),  -- Slightly above room floor
                macroPos.z
            )
            micro.transform.position = newMicroPos
            print(f"Micro moved to: {newMicroPos.x}, {newMicroPos.y}, {newMicroPos.z}")
        end
        
        self.isRoomActive = true
        print("✓ Room spawned successfully!")
        return true
    else
        print("✗ Failed to spawn room - ROOM1 object not found")
        return false
    end
end

function RoomSpawner:RemoveRoom()
    if self.currentRoom then
        try {
            if GameObject and GameObject.Destroy then
                GameObject.Destroy(self.currentRoom)
            elseif self.currentRoom.Destroy then
                self.currentRoom:Destroy()
            end
            print("✓ Room removed")
        } catch {
            print("Error removing room")
        }
        
        self.currentRoom = nil
        self.isRoomActive = false
    end
end

function RoomSpawner:UpdateRoomScale()
    if not self.isRoomActive or not self.currentRoom then
        return
    end
    
    -- Update room scale based on current macro size
    local newMacroScale = self:GetMacroScale()
    local newRoomScale = newMacroScale * 0.5
    
    if math.abs(newRoomScale - self.roomScale) > 0.1 then  -- Only update if significant change
        self.roomScale = newRoomScale
        local roomScaleVector = Vector3(self.roomScale, self.roomScale, self.roomScale)
        self.currentRoom.transform.localScale = roomScaleVector
        print(f"Room scale updated to: {self.roomScale}")
    end
end

function RoomSpawner:ProcessChatMessage(message)
    if not message then return end
    
    local lowerMessage = string.lower(message)
    
    -- Check for room spawn keywords
    for _, keyword in ipairs(self.roomKeywords) do
        if string.find(lowerMessage, keyword, 1, true) then
            print(f"Room spawn triggered by: '{keyword}'")
            self:SpawnRoom()
            return true
        end
    end
    
    -- Check for room removal keywords
    for _, keyword in ipairs(self.removeKeywords) do
        if string.find(lowerMessage, keyword, 1, true) then
            print(f"Room removal triggered by: '{keyword}'")
            self:RemoveRoom()
            return true
        end
    end
    
    return false
end

function RoomSpawner:Update()
    -- Update room scale if room is active
    if self.isRoomActive then
        self:UpdateRoomScale()
    end
end

-- Global instance
_G.roomSpawner = RoomSpawner:new()

-- Hook into chat system if available
if _G.aiCompanion and _G.aiCompanion.ProcessChatMessage then
    local originalProcessChat = _G.aiCompanion.ProcessChatMessage
    _G.aiCompanion.ProcessChatMessage = function(self, message)
        -- Try room spawner first
        if _G.roomSpawner:ProcessChatMessage(message) then
            return  -- Room command processed, don't pass to AI
        end
        -- Pass to original AI companion
        return originalProcessChat(self, message)
    end
    print("Room spawner hooked into AI companion chat system")
end

print("Room Spawner script loaded successfully!")
print("Commands:")
print("  'take me to room' - Spawn room")
print("  'remove room' - Remove room")

return RoomSpawner

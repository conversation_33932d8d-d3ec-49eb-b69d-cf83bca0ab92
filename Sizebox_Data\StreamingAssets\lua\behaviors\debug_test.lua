-- Simple debug test script to verify all fixes work
-- Tests: surprise system, head tracking, chest bounce

local DebugTest = RegisterBehavior("Debug Test")
DebugTest.data = {
    menuEntry = "Test/Debug Test",
    agent = { type = { "giantess" } }
}

function DebugTest:Start()
    print("DEBUG TEST: Script started!")
    Game.Toast.New().Print("DEBUG TEST ACTIVE!")
    
    -- Initialize test system
    self.testSystem = {
        active = true,
        testCount = 0,
        maxTests = 5
    }
    
    -- Start testing after 2 seconds
    self.testSystem.timer = 2.0
end

function DebugTest:Update()
    if not self.testSystem.active then return end
    
    -- Count down timer
    self.testSystem.timer = self.testSystem.timer - Time.deltaTime
    
    if self.testSystem.timer <= 0 and self.testSystem.testCount < self.testSystem.maxTests then
        self:RunTest()
        self.testSystem.testCount = self.testSystem.testCount + 1
        self.testSystem.timer = 3.0  -- Next test in 3 seconds
        
        if self.testSystem.testCount >= self.testSystem.maxTests then
            self.testSystem.active = false
            Game.Toast.New().Print("🎉 ALL TESTS COMPLETE! 🎉")
        end
    end
end

function DebugTest:RunTest()
    local testNumber = self.testSystem.testCount + 1
    print("DEBUG TEST: Running test " .. testNumber)
    
    if testNumber == 1 then
        -- Test 1: Basic growth
        print("DEBUG TEST 1: Testing basic growth")
        Game.Toast.New().Print("TEST 1: Basic Growth")
        self.agent.scale = self.agent.scale * 1.5
        
    elseif testNumber == 2 then
        -- Test 2: Basic shrink
        print("DEBUG TEST 2: Testing basic shrink")
        Game.Toast.New().Print("TEST 2: Basic Shrink")
        self.agent.scale = self.agent.scale * 0.7
        
    elseif testNumber == 3 then
        -- Test 3: Animation
        print("DEBUG TEST 3: Testing animation")
        Game.Toast.New().Print("TEST 3: Animation")
        if self.agent.animation then
            self.agent.animation.Set("Victory")
        end
        
    elseif testNumber == 4 then
        -- Test 4: Size check
        print("DEBUG TEST 4: Testing size check")
        Game.Toast.New().Print("TEST 4: Size Check - Current: " .. string.format("%.1f", self.agent.scale))
        
    elseif testNumber == 5 then
        -- Test 5: Reset size
        print("DEBUG TEST 5: Testing size reset")
        Game.Toast.New().Print("TEST 5: Size Reset")
        self.agent.scale = 328.0  -- Reset to original
    end
end

function DebugTest:Destroy()
    print("DEBUG TEST: Script destroyed")
    Game.Toast.New().Print("Debug test ended")
end

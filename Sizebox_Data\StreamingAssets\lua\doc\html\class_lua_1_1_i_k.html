<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.IK Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_i_k.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#properties">Properties</a> &#124;
<a href="class_lua_1_1_i_k-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.IK Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Inverse Kinematics lets you animate individual bones to create procedural animations.  
 <a href="class_lua_1_1_i_k.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="properties" name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a9cbf5715c753f97860bf9d268e76abcc"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_i_k.html#a9cbf5715c753f97860bf9d268e76abcc">enabled</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a9cbf5715c753f97860bf9d268e76abcc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Enable / Disable the <a class="el" href="class_lua_1_1_i_k.html" title="Inverse Kinematics lets you animate individual bones to create procedural animations.">IK</a> to be used in scripts.  <a href="class_lua_1_1_i_k.html#a9cbf5715c753f97860bf9d268e76abcc">More...</a><br /></td></tr>
<tr class="separator:a9cbf5715c753f97860bf9d268e76abcc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8cea34c6258871a550fc9d56f8facea1"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_i_k_effector.html">IKEffector</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_i_k.html#a8cea34c6258871a550fc9d56f8facea1">leftFoot</a><code> [get]</code></td></tr>
<tr class="memdesc:a8cea34c6258871a550fc9d56f8facea1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Left Foot Effector  <a href="class_lua_1_1_i_k.html#a8cea34c6258871a550fc9d56f8facea1">More...</a><br /></td></tr>
<tr class="separator:a8cea34c6258871a550fc9d56f8facea1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0868dad53aa0ea22b3554edc766ee8bc"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_i_k_effector.html">IKEffector</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_i_k.html#a0868dad53aa0ea22b3554edc766ee8bc">rightFoot</a><code> [get]</code></td></tr>
<tr class="memdesc:a0868dad53aa0ea22b3554edc766ee8bc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Right Foot Effector  <a href="class_lua_1_1_i_k.html#a0868dad53aa0ea22b3554edc766ee8bc">More...</a><br /></td></tr>
<tr class="separator:a0868dad53aa0ea22b3554edc766ee8bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a88ba05bd1557a61e1f69e21fd172641c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_i_k_effector.html">IKEffector</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_i_k.html#a88ba05bd1557a61e1f69e21fd172641c">leftHand</a><code> [get]</code></td></tr>
<tr class="memdesc:a88ba05bd1557a61e1f69e21fd172641c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Left Hand Effector  <a href="class_lua_1_1_i_k.html#a88ba05bd1557a61e1f69e21fd172641c">More...</a><br /></td></tr>
<tr class="separator:a88ba05bd1557a61e1f69e21fd172641c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1321851e538f60beb7fa5d655e18ab75"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_i_k_effector.html">IKEffector</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_i_k.html#a1321851e538f60beb7fa5d655e18ab75">rightHand</a><code> [get]</code></td></tr>
<tr class="memdesc:a1321851e538f60beb7fa5d655e18ab75"><td class="mdescLeft">&#160;</td><td class="mdescRight">Right Hand Effector  <a href="class_lua_1_1_i_k.html#a1321851e538f60beb7fa5d655e18ab75">More...</a><br /></td></tr>
<tr class="separator:a1321851e538f60beb7fa5d655e18ab75"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aca9dea4db5e6c66d5d1e904266c626ef"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_i_k_effector.html">IKEffector</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_i_k.html#aca9dea4db5e6c66d5d1e904266c626ef">body</a><code> [get]</code></td></tr>
<tr class="memdesc:aca9dea4db5e6c66d5d1e904266c626ef"><td class="mdescLeft">&#160;</td><td class="mdescRight">Body Effector (hips)  <a href="class_lua_1_1_i_k.html#aca9dea4db5e6c66d5d1e904266c626ef">More...</a><br /></td></tr>
<tr class="separator:aca9dea4db5e6c66d5d1e904266c626ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >Inverse Kinematics lets you animate individual bones to create procedural animations. </p>
</div><h2 class="groupheader">Property Documentation</h2>
<a id="aca9dea4db5e6c66d5d1e904266c626ef" name="aca9dea4db5e6c66d5d1e904266c626ef"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aca9dea4db5e6c66d5d1e904266c626ef">&#9670;&nbsp;</a></span>body</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_i_k_effector.html">IKEffector</a> Lua.IK.body</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Body Effector (hips) </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a9cbf5715c753f97860bf9d268e76abcc" name="a9cbf5715c753f97860bf9d268e76abcc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9cbf5715c753f97860bf9d268e76abcc">&#9670;&nbsp;</a></span>enabled</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.IK.enabled</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Enable / Disable the <a class="el" href="class_lua_1_1_i_k.html" title="Inverse Kinematics lets you animate individual bones to create procedural animations.">IK</a> to be used in scripts. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a8cea34c6258871a550fc9d56f8facea1" name="a8cea34c6258871a550fc9d56f8facea1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8cea34c6258871a550fc9d56f8facea1">&#9670;&nbsp;</a></span>leftFoot</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_i_k_effector.html">IKEffector</a> Lua.IK.leftFoot</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Left Foot Effector </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a88ba05bd1557a61e1f69e21fd172641c" name="a88ba05bd1557a61e1f69e21fd172641c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a88ba05bd1557a61e1f69e21fd172641c">&#9670;&nbsp;</a></span>leftHand</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_i_k_effector.html">IKEffector</a> Lua.IK.leftHand</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Left Hand Effector </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a0868dad53aa0ea22b3554edc766ee8bc" name="a0868dad53aa0ea22b3554edc766ee8bc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0868dad53aa0ea22b3554edc766ee8bc">&#9670;&nbsp;</a></span>rightFoot</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_i_k_effector.html">IKEffector</a> Lua.IK.rightFoot</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Right Foot Effector </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a1321851e538f60beb7fa5d655e18ab75" name="a1321851e538f60beb7fa5d655e18ab75"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1321851e538f60beb7fa5d655e18ab75">&#9670;&nbsp;</a></span>rightHand</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_i_k_effector.html">IKEffector</a> Lua.IK.rightHand</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Right Hand Effector </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaIK.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_i_k.html">IK</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

Gizmo = RegisterBehavior("footprint_on_step")
Gizmo.data =  {
    menuEntry = "Spawn/Footprint Every Step",
    secondary = true,
    flags = {"footprint_on_step"},
    agent = {
        type = { "giantess" }
    },
    target = {
        type = { "oneself" }
    },
    settings = {
        {"LeftFoot", "LeftFootObject", "string", "footprint.object\\LeftC"},
        {"RightFoot", "LeftFootObject", "string", "footprint.object\\RightC"},
        {"Size", "Inital FootPrintSize", "string", "0.01"},
        {"Mirror", "Mirror FootPrint Placement", "bool", true},
        {"Offset", "foot Offset", "string", "0.01"},
        {"stop_key", "Stop key", "keybind", "k" }
    },
}


-- this function is not called unless you hook it to a event
-- OnStep event will pass folowing data to this function:
-- gts - giantess entity
-- position - position of the step epicenter (vector)
-- magnitude - force of the step (float)
-- foot - foot numer (0 - left, 1 - right)
function Gizmo:Listener(data)
    --Log(self.position)
    local size = tonumber(self.Size) * self.agent.scale
	--Log("---")
    --Log("Position: " .. data.position)
	--Log("---")
    --Log(data.gts)
    --we need to caculate an offset from the onStep Event, as the postion that gives us is center of the base of the model - but that's global so we can't just subtract from one axis for this...
    --could either offset or grab an IK? IK doesn't seem to be exposed to lua right now...
    self.RFootPos = data.position + self.OffsetR
    self.LFootPos = data.position + self.OffsetL
    --we somehow need to specify the rotation on the foot...
    --grabbing the agent's rotation is actually basically perfect, we just need to rotate it 180 degrees
    --Log(self.agent.bones.leftfoot.nane)
    --above doesn't give use anything but a no access error - maybe not implemented? doco says it returns nothing so...
    if self.Mirror then
        self.rotationL = Quaternion.Euler(0, 180, 0) 
        self.rotationL = self.rotationL * self.agent.rigidbody.rotation
        self.rotationR = Quaternion.Euler(0, 180, 0)
        self.rotationR = self.rotationR * self.agent.rigidbody.rotation
        --Log("Left Rotation: " .. self.rotationL)
        --Log("Right Rotation: " .. self.rotationR)
    elseif not self.Mirror then
        self.rotationL = self.agent.rigidbody.rotation
        self.rotationR = self.agent.rigidbody.rotation
    end
    --can't find something that doesn't flip the object 180 degress on one axis :(
    --self.rotation = Quaternion.LerpUnclamped(self.agent.rigidbody.rotation, self.rotation, 0.8)
    if data.foot == 0 and data.gts.isGiantess() then
        --Log("Offset: " .. self.LFootPos)
        --Entity.SpawnObject (self.LeftFoot, self.LFootPos, self.rotationL, size)
		upVector = Vector3.New(0, 0.5, 0)
		Entity.SpawnObject (self.LeftFoot, self.LFootPos - upVector * data.position.y, self.rotationL, size)
    elseif data.foot == 1 and data.gts.isGiantess() then
        --Log("Offset: " .. self.RFootPos)
		--Entity.SpawnObject (self.RightFoot, self.RFootPos, self.rotationR, size)
		upVector = Vector3.New(0, 0.5, 0)
		Entity.SpawnObject (self.RightFoot, self.RFootPos - upVector * data.position.y, self.rotationR, size)
    end
end

function Gizmo:Start()
    self.agent.dict.OnStep = Event.Register(self, EventCode.OnStep, self.Listener)
    self.offset_x = tonumber(self.Offset)
    self.OffsetL = Vector3.New(-self.offset_x, 0, 0)
    self.OffsetR = Vector3.New(-self.offset_x, 0, 0)
end

function Gizmo:Update()
    if Input.GetKeyDown(self.stop_key) then
        Event.Unregister(self.agent.dict.OnStep)
        self.agent.ai.StopSecondaryBehavior("footprint_on_step")
        return
	end
end

function Gizmo:Exit()
    Log("No Longer leaving FootPrints!")
end
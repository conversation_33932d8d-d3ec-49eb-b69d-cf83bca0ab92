Shrivels = <PERSON><PERSON><PERSON><PERSON><PERSON>("ShrivelSmooth")
Shrivels.agentType = "humanoid"
Shrivels.targetType = "oneself"

function Shrivels:Update()
    if not self.agent.ai.IsActionActive() then
       	self.agent.Grow(-0.01,0.1)
	self.agent.Grow(-0.03,0.1)
	self.agent.Grow(-0.05,0.13)
	self.agent.Grow(-0.07,0.13)
	self.agent.Grow(-0.10,1)
	self.agent.Grow(-0.06,0.20)
	self.agent.Grow(-0.03,0.20)
	self.agent.Grow(-0.01,0.20)
	self.agent.Grow(-0,4)
     end
end

function Shrivels:End()
    self.agent.Grow(0)
end
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_quaternion.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle"><div class="title">Lua.Quaternion Member List</div></div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#a4094309a8c66c50017bd92ffdfeb3bfd">Angle</a>(Quaternion a, Quaternion b)</td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#a25f65fcc019124366264558209108498">AngleAxis</a>(float angle, Vector3 axis)</td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#ac70013482fc53c72c664561d67d5d677">Concat</a>(Quaternion o, string s)</td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#a9eab1ab6fc89906ecc0b8159c1429a04">Concat</a>(string s, Quaternion o)</td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#aef26c0eb5e338baa7dc1a87530b360d1">Concat</a>(Quaternion o1, Quaternion o2)</td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#a8479fc724c544d8784afeae5778e6a27">Dot</a>(Quaternion a, Quaternion b)</td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#a9ecf171f4a2a8c0ad3ced7564ada2c6d">Eq</a>(Quaternion o1, Quaternion o2)</td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#ac7134c2bdc28902fc519d42b7b803d9f">Euler</a>(float x, float y, float z)</td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#a275393364a6f1475566b7bfe49dad51e">Euler</a>(Vector3 euler)</td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#ac50cf6b67c4cb0363b834b7054cdd5fa">eulerAngles</a></td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#a78b8152d55e05c4b35a74ed09eae9d41">FromToRotation</a>(Vector3 fromDirection, Vector3 toDirection)</td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#ac7d56f4c2496af59e66550e35bff614c">identity</a></td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#a324d82496815f927ebcaa21032843276">Inverse</a>(Quaternion rotation)</td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#a451a68530c7d148d83024edf4bb79e26">Lerp</a>(Quaternion a, Quaternion b, float t)</td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#ad43d2f3aa2d460ed567351f97aba6bfe">LerpUnclamped</a>(Quaternion a, Quaternion b, float t)</td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#a7a17f92fd9d83a8b472db78d5cb74642">LookRotation</a>(Vector3 forward)</td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#afa69beaa5748b6c941b9a3296d6c0638">LookRotation</a>(Vector3 forward, Vector3 upwards)</td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#a39cfba79d11687138569de7edd9e2727">New</a>(float x, float y, float z, float w)</td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#ac0e2a30dacbe7805913969ca02cc1e6b">operator*</a>(Quaternion lhs, Quaternion rhs)</td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#a2e8753f1c8b29cbfb49f4aef2df009c8">operator*</a>(Quaternion rotation, Vector3 vector)</td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#abfa084dd85c43e01da75d9bcf3305871">RotateTowards</a>(Quaternion from, Quaternion to, float maxDegreesDelta)</td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#a0cb037e26892fa50a048fc751f7f017a">Set</a>(float x, float y, float z, float w)</td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#aa7991983472a41d5d7c1b7cb61eb5580">SetFromToRotation</a>(Vector3 fromDirection, Vector3 toDirection)</td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#aab942db162b73214d4b563bd6a49bad4">SetLookRotation</a>(Vector3 view)</td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#a13062d4cdfe3635c74f2e57c893b6e2f">SetLookRotation</a>(Vector3 view, Vector3 up)</td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#a8848906e3924791706bc0bc853a4572b">Slerp</a>(Quaternion a, Quaternion b, float t)</td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#a5056ad858477a4b81765167a068d5379">SlerpUnclamped</a>(Quaternion a, Quaternion b, float t)</td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#a72a4b219c3442e088c7ee963feb1b372">ToString</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#a4a66f5c598907b4d906b0c5dd0e28526">w</a></td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#aefb405b7fafa79708a6d8120781debce">x</a></td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#ab7eb002a81cfc537a9c3afc8965ef2ec">y</a></td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html#ac26c0a2710dd86783dee62c8645ee55c">z</a></td><td class="entry"><a class="el" href="class_lua_1_1_quaternion.html">Lua.Quaternion</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

-- Behavior: Beta5 ShapeChange <PERSON>row (Linear, Burst, Oscillate Modes)

local G_AGENT = { type = { "giantess" } }
local G_TARGET = { type = { "oneself" } }
local G_SETTINGS = {
    {"boneName", "Bone/Object Name", "string", "Nex Gen.Object\\Beta5_ShapeChange"},
    {"limit", "Limit Size", "float", "50", {"1", "100"}},
    {"speed", "Growth Speed", "float", "0.03", {"0.01", "0.2"}},
    {"pulseCount", "Pulse Count", "float", "2", {"1", "5"}},
    {"pulseSpeed", "Pulse Speed", "float", "0.5", {"0.2", "2.0"}},
    {"burstMultiplier", "Burst Multiplier", "float", "0.2", {"0.05", "0.5"}}
}

local function FindBone(entity, boneName)
    if entity.bones and entity.bones.GetBonesByName then
        local bones = entity.bones.GetBonesByName(boneName, true)
        if bones and bones[1] then
            return bones[1]
        end
    end
    return nil
end

local ShapeChangeGrow = RegisterBehavior("Beta5 ShapeChange Grow")
ShapeChangeGrow.data = {
    menuEntry = "Body/Beta5 ShapeChange Grow",
    agent = { type = { "giantess" } },
    target = { type = { "oneself" } },
    settings = {
        {"boneName", "Bone/Object Name", "string", "Nex Gen.Object\\Beta5_ShapeChange"},
        {"limit", "Limit Size", "float", "50", {"1", "100"}},
        {"speed", "Growth Speed", "float", "0.03", {"0.01", "0.2"}},
        {"pulseCount", "Pulse Count", "float", "2", {"1", "5"}},
        {"pulseSpeed", "Pulse Speed", "float", "0.5", {"0.2", "2.0"}},
        {"burstMultiplier", "Burst Multiplier", "float", "0.2", {"0.05", "0.5"}},
        -- New offset settings:
        {"offsetBoneName", "Offset Bone Name", "string", "185.!JOINT_SHITA?_2_0"},
        {"offsetX", "Offset X", "float", "0"},
        {"offsetY", "Offset Y", "float", "0"},
        {"offsetZ", "Offset Z", "float", "-200"}
    },
    secondary = true
}

function ShapeChangeGrow:Start()
    self.initialized = false
    self.active = true -- Growth is ON by default
    self.mode = "Linear"
    self.modes = {"Linear", "Burst", "Oscillate"}
    self:ResetPhases()
    -- Initialize hug-related variables
    self.hugActive = false
    self.hugNoddingTimer = 0
    self._manuallyDisabled = false  -- Track if user manually disabled hug
    self._hugExtraRotation = false  -- Track if extra rotation should be applied
    self._hugRotationTransition = false  -- Track if rotation is transitioning
    self._hugRotationTimer = 0  -- Timer for rotation transition
    self._hugExtraRotation2 = false  -- Track if second extra rotation should be applied (size 4.5+)
    self._hugRotationTransition2 = false  -- Track if second rotation is transitioning
    self._hugRotationTimer2 = 0  -- Timer for second rotation transition
    -- Shoulder movement variables
    self._shoulderMovementActive = false  -- Track if shoulder movement should be active
    self._shoulderMovementEnabled = true  -- Toggle for shoulder movement (K key)
    self._shoulderTimer = 0  -- Timer for shoulder movement
    self._shoulderCycle = 0  -- Current shoulder movement cycle
    self._shoulderMaxCycles = 4  -- Number of up/down cycles
    self._shoulderState = "wait"  -- wait, up, toDefault1, down, toDefault2
    self._shoulderTransitionActive = false  -- Track if shoulder is transitioning
    self._shoulderTransitionTimer = 0  -- Timer for shoulder transition
    self._shoulderTransitionDuration = 0.3  -- Duration for each transition
    self._shoulderWaitTimer = 0  -- Timer for waiting between cycles
    self._shoulderWaitDuration = 0  -- Random wait duration
    self._shoulderLargeDefaultActive = false  -- Track if large size shoulder default is active
    self._shoulderWideDefaultActive = false  -- Track if wide shoulder default is active (size 8.5+)
    -- Character rotation variables
    self._characterRotationSpeed = 30  -- Degrees per second
    self._isRotatingLeft = false
    self._isRotatingRight = false
    self._currentRotationY = 0  -- Track cumulative Y rotation
    -- New mode variables
    self._specialModeActive = false  -- Track if special mode is active
    self._specialModeInitialized = false  -- Track if special mode has been set up
    self._objectInitialPosition = nil  -- Store initial object position
    self._targetBoneInitialPosition = nil  -- Store initial target bone position
    self._targetBoneInitialRotation = nil  -- Store initial target bone rotation
    self._lipsInitialPosition = nil  -- Store initial lips position
    self._lipsInitialRotation = nil  -- Store initial lips rotation
    -- Smooth animation variables
    self._smoothAnimationActive = false  -- Track if smooth animation is active
    self._animationTimer = 0  -- Timer for animation
    self._animationDirection = 1  -- 1 for up, -1 for down
    self._animationSpeed = 2  -- Speed of animation
    self._animationRange = 100  -- Range of up/down movement (doubled)
    -- Size adjustment variables
    self._sizeAdjustmentStep = 0.1  -- How much to grow/shrink per key press
    self._currentSizeScale = 1.0  -- Current size multiplier
    -- Animation variables (replacing new arm movement)
    self._animationCooldown = 0  -- Cooldown timer for animations
    self._massageAnimationActive = false  -- Track if massage animation is playing
    self._savedBoneScale = nil  -- Store bone scale before animation
    self._forceScaleFrames = 0  -- Force scale for a few frames after animation starts
    Game.Toast.New().Print("Press Y to auto-offset bone for Lips! Press L for Hug! Press K for Shoulder Movement! Press Q/E to rotate! Press 1 for Special Mode! Press N to toggle Massage Breasts 5! Auto-hug at size 2.5-3, shoulder movement at size 3!")
end

function ShapeChangeGrow:ResetPhases()
    self.phase = "idle"
    self.growTimer = 0
    self.gsCycle = 0
    self.gsMaxCycles = math.random(4, 5)
    self.gsDirection = 1
    self.gsPhaseTimer = 0
    self.burstPauseTimer = 0
    self.oscTimer = 0
    self.burstRepeatDelay = math.random(2, 6) -- random delay between bursts
    self.burstGrowthTimer = 0
    self.burstGrowthDuration = 2  -- Reduced from 5 to 2 seconds
    self.burstGrowthStartScale = nil
    self.burstGrowthTargetScale = nil
    self.rotationTimer = 0
    self.rotatingOut = true
    self.rotationLerp = 0
    self.originalRotation = nil
    self.targetRotation = nil
    -- Reset GS transition variables
    self.gsTransitionActive = false
    self.gsTransitionTimer = 0
    self.gsCurrentScale = nil
    self.gsTargetScale = nil
end

function ShapeChangeGrow:ResetToDefault()
    -- Reset all growth phases
    self:ResetPhases()
    
    -- Reset shoulder movement states
    self._shoulderMovementActive = false
    self._shoulderCycle = 0
    self._shoulderTransitionActive = false
    self._shoulderState = "wait"
    self._shoulderLargeDefaultActive = false
    self._shoulderWideDefaultActive = false
    self._shoulderMovementEnabled = true
    
    -- Reset character rotation
    self._currentRotationY = 0
    
    -- Reset hug states
    self.hugActive = false
    self._hugExtraRotation = false
    self._hugRotationTransition = false
    self._hugExtraRotation2 = false
    self._hugRotationTransition2 = false
    self._autoHugTriggered = false
    self._manuallyDisabled = false
    
    -- Reset special mode flags
    self._specialModeInitialized = false
    self._smoothAnimationActive = false  -- Stop smooth animation
    self._currentSizeScale = 1.0  -- Reset size scale
    
    -- Reset object position and size to default
    if self.agent then
        -- Store initial object position if not already stored
        if not self._objectInitialPosition then
            self._objectInitialPosition = self.agent.transform.position
        end
        
        -- Reset object position to initial position (don't reset rotation to avoid glitch)
        if self.agent.transform then
            self.agent.transform.position = self._objectInitialPosition
            -- Don't reset rotation to prevent glitch
        end
        
        -- Reset object size
        local bone = FindBone(self.agent, self.boneName)
        if bone then
            bone.localScale = Vector3.new(1, 1, 1)  -- Reset to default size
        end
        
        -- Reset shoulder bones
        local leftShoulder = FindBone(self.agent, "87.JOINT_LEFTARM")
        local rightShoulder = FindBone(self.agent, "51.JOINT_RIGHTARM")
        if leftShoulder and self._leftShoulderInitialRotation then
            leftShoulder.localRotation = self._leftShoulderInitialRotation
        end
        if rightShoulder and self._rightShoulderInitialRotation then
            rightShoulder.localRotation = self._rightShoulderInitialRotation
        end
        
        -- Reset head bones
        local headBone = FindBone(self.agent, "16.JOINT_HEAD")
        if headBone and self._headInitialRotation then
            headBone.localRotation = self._headInitialRotation
        end
        
        -- Reset target bone
        local targetBone = FindBone(self.agent, "185.!JOINT_SHITA?_2_0")
        if targetBone then
            if self._targetBoneInitialPosition then
                targetBone.localPosition = self._targetBoneInitialPosition
            end
            if self._targetBoneInitialRotation then
                targetBone.localRotation = self._targetBoneInitialRotation
            end
        end
        
        -- Reset lips bones
        local lipsBone = FindBone(self.agent, "18.JOINT_LIPS")
        if lipsBone then
            if self._lipsInitialPosition then
                lipsBone.localPosition = self._lipsInitialPosition
            end
            if self._lipsInitialRotation then
                lipsBone.localRotation = self._lipsInitialRotation
            end
        end
    end
    
    Game.Toast.New().Print("All settings reset to default!")
end

function ShapeChangeGrow:SetupSpecialMode()
    if not self.agent then return end
    
    -- Find the target bone and store initial values
    local targetBone = FindBone(self.agent, "185.!JOINT_SHITA?_2_0")
    if targetBone then
        -- Store initial target bone position and rotation if not already stored
        if not self._targetBoneInitialPosition then
            self._targetBoneInitialPosition = targetBone.localPosition
        end
        if not self._targetBoneInitialRotation then
            self._targetBoneInitialRotation = targetBone.localRotation
        end
    end
    
    -- Find the lips bone and store initial values
    local lipsBone = FindBone(self.agent, "18.JOINT_LIPS")
    if lipsBone then
        -- Store initial lips position and rotation if not already stored
        if not self._lipsInitialPosition then
            self._lipsInitialPosition = lipsBone.localPosition
        end
        if not self._lipsInitialRotation then
            self._lipsInitialRotation = lipsBone.localRotation
        end
    end
    
    self._specialModeInitialized = true
    Game.Toast.New().Print("Special Mode: ON - Press Y to position bone!")
end

function ShapeChangeGrow:RepositionObject()
    if not self.agent then return end
    
    -- Find the specific bone
    local targetBone = FindBone(self.agent, "185.!JOINT_SHITA?_2_0")
    if not targetBone then
        Game.Toast.New().Print("Target bone 185.!JOINT_SHITA?_2_0 not found!")
        return
    end
    
    -- Store initial bone position if not already stored
    if not self._targetBoneInitialPosition then
        self._targetBoneInitialPosition = targetBone.localPosition
    end
    if not self._targetBoneInitialRotation then
        self._targetBoneInitialRotation = targetBone.localRotation
    end
    
    -- Move bone to initial position (Z axis forward +10, Y axis down -100)
    local newPosition = targetBone.localPosition
    newPosition.z = self._targetBoneInitialPosition.z - -10
    newPosition.y = self._targetBoneInitialPosition.y + -100
    targetBone.localPosition = newPosition
    
    -- Rotate bone downward by -90 degrees
    local newRotation = self._targetBoneInitialRotation * Quaternion.Euler(-90, 0, 0)
    targetBone.localRotation = newRotation
    
    -- Start smooth animation
    self._smoothAnimationActive = true
    self._animationTimer = 0
    self._animationDirection = 1
    
    Game.Toast.New().Print("Bone positioned! (Forward: 10, Down: 100, Rotated: -90°) - Animation started (2x height)!")
end

function ShapeChangeGrow:UpdateSmoothAnimation()
    if not self._smoothAnimationActive or not self._specialModeActive then return end
    
    local targetBone = FindBone(self.agent, "185.!JOINT_SHITA?_2_0")
    if not targetBone then return end
    
    -- Update animation timer
    self._animationTimer = self._animationTimer + Time.deltaTime * self._animationSpeed
    
    -- Calculate smooth up/down movement using sine wave
    local animationOffset = math.sin(self._animationTimer) * self._animationRange
    
    -- Apply the animation offset to the Y position
    local newPosition = targetBone.localPosition
    newPosition.y = (self._targetBoneInitialPosition.y + -100) + animationOffset
    targetBone.localPosition = newPosition
end

function ShapeChangeGrow:AdjustSize(grow)
    if not self.agent then return end

    -- Don't allow manual size adjustment during massage animation
    if self._massageAnimationActive then
        Game.Toast.New().Print("Cannot adjust size while massage animation is active!")
        return
    end

    local bone = FindBone(self.agent, self.boneName)
    if not bone then
        Game.Toast.New().Print("Bone not found: " .. self.boneName)
        return
    end
    
    -- Adjust size scale
    if grow then
        self._currentSizeScale = self._currentSizeScale + self._sizeAdjustmentStep
    else
        self._currentSizeScale = self._currentSizeScale - self._sizeAdjustmentStep
        -- Prevent negative scaling
        if self._currentSizeScale < 0.1 then
            self._currentSizeScale = 0.1
        end
    end
    
    -- Apply new scale
    bone.localScale = Vector3.new(self._currentSizeScale, self._currentSizeScale, self._currentSizeScale)
    
    -- Show message
    local action = grow and "Grown" or "Shrunk"
    Game.Toast.New().Print(action .. " to scale: " .. string.format("%.1f", self._currentSizeScale))
end

function ShapeChangeGrow:ToggleMassageBreastsAnimation()
    if not self.agent then return end

    -- Check cooldown to prevent spam
    if self._animationCooldown > 0 then
        Game.Toast.New().Print("Animation on cooldown! Wait " .. string.format("%.1f", self._animationCooldown) .. " seconds.")
        return
    end

    -- Toggle animation state
    self._massageAnimationActive = not self._massageAnimationActive

    if self._massageAnimationActive then
        -- Save current bone scale before starting animation
        local bone = FindBone(self.agent, self.boneName)
        if bone then
            self._savedBoneScale = Vector3.new(bone.localScale.x, bone.localScale.y, bone.localScale.z)
            Game.Toast.New().Print("Saved bone scale: " .. string.format("%.2f", bone.localScale.x))

            -- Stop any current action and play the Massage Breasts 5 animation
            self.agent.ai.StopAction()
            self.agent.animation.Set("Massage Breasts 5")

            -- Force scale restoration for the next 30 frames (about 1 second)
            self._forceScaleFrames = 30

            Game.Toast.New().Print("Massage Breasts 5: ON - Scale locked at: " .. string.format("%.2f", self._savedBoneScale.x))
        end
    else
        -- Stop animation and return to idle
        self.agent.ai.StopAction()
        self.agent.animation.Set("Idle")

        -- Force restore bone scale if it was saved
        if self._savedBoneScale then
            local bone = FindBone(self.agent, self.boneName)
            if bone then
                bone.localScale = self._savedBoneScale
                Game.Toast.New().Print("Restored bone scale: " .. string.format("%.2f", self._savedBoneScale.x))
            end
        end
        Game.Toast.New().Print("Massage Breasts 5: OFF")
    end

    -- Set cooldown to prevent spam (1 second for toggles)
    self._animationCooldown = 1.0
end







function ShapeChangeGrow:UpdateShoulderMovement()
    if not self.agent then return end
    
    -- Find shoulder bones
    local leftShoulder = FindBone(self.agent, "87.JOINT_LEFTARM")
    local rightShoulder = FindBone(self.agent, "51.JOINT_RIGHTARM")
    
    if not leftShoulder or not rightShoulder then return end
    
    -- Store initial rotations if not already stored
    if not self._leftShoulderInitialRotation then
        self._leftShoulderInitialRotation = leftShoulder.localRotation
    end
    if not self._rightShoulderInitialRotation then
        self._rightShoulderInitialRotation = rightShoulder.localRotation
    end
    
    -- State machine: wait → up → toDefault1 → down → toDefault2 → (repeat or end)
    if self._shoulderState == "wait" then
        self._shoulderWaitTimer = self._shoulderWaitTimer + Time.deltaTime
        if self._shoulderWaitTimer >= self._shoulderWaitDuration then
            self._shoulderState = "up"
            self._shoulderTransitionTimer = 0
            self._shoulderTransitionActive = true
        end
        
    elseif self._shoulderState == "up" then
        self._shoulderTransitionTimer = self._shoulderTransitionTimer + Time.deltaTime
        local t = math.min(self._shoulderTransitionTimer / self._shoulderTransitionDuration, 1)
        
        -- Interpolate to 10 degrees up
        local upRotation = Quaternion.Euler(10, 0, 0) * self._leftShoulderInitialRotation
        local upRotationRight = Quaternion.Euler(10, 0, 0) * self._rightShoulderInitialRotation
        
        leftShoulder.localRotation = Quaternion.Slerp(self._leftShoulderInitialRotation, upRotation, t)
        rightShoulder.localRotation = Quaternion.Slerp(self._rightShoulderInitialRotation, upRotationRight, t)
        
        if t >= 1 then
            self._shoulderState = "toDefault1"
            self._shoulderTransitionTimer = 0
        end
        
    elseif self._shoulderState == "toDefault1" then
        self._shoulderTransitionTimer = self._shoulderTransitionTimer + Time.deltaTime
        local t = math.min(self._shoulderTransitionTimer / self._shoulderTransitionDuration, 1)
        
        -- Interpolate from up back to default
        local upRotation = Quaternion.Euler(10, 0, 0) * self._leftShoulderInitialRotation
        local upRotationRight = Quaternion.Euler(10, 0, 0) * self._rightShoulderInitialRotation
        
        leftShoulder.localRotation = Quaternion.Slerp(upRotation, self._leftShoulderInitialRotation, t)
        rightShoulder.localRotation = Quaternion.Slerp(upRotationRight, self._rightShoulderInitialRotation, t)
        
        if t >= 1 then
            self._shoulderState = "down"
            self._shoulderTransitionTimer = 0
        end
        
    elseif self._shoulderState == "down" then
        self._shoulderTransitionTimer = self._shoulderTransitionTimer + Time.deltaTime
        local t = math.min(self._shoulderTransitionTimer / self._shoulderTransitionDuration, 1)
        
        -- Interpolate to 20 degrees down
        local downRotation = Quaternion.Euler(-20, 0, 0) * self._leftShoulderInitialRotation
        local downRotationRight = Quaternion.Euler(-20, 0, 0) * self._rightShoulderInitialRotation
        
        leftShoulder.localRotation = Quaternion.Slerp(self._leftShoulderInitialRotation, downRotation, t)
        rightShoulder.localRotation = Quaternion.Slerp(self._rightShoulderInitialRotation, downRotationRight, t)
        
        if t >= 1 then
            self._shoulderState = "toDefault2"
            self._shoulderTransitionTimer = 0
        end
        
    elseif self._shoulderState == "toDefault2" then
        self._shoulderTransitionTimer = self._shoulderTransitionTimer + Time.deltaTime
        local t = math.min(self._shoulderTransitionTimer / self._shoulderTransitionDuration, 1)
        
        -- Interpolate from down back to default
        local downRotation = Quaternion.Euler(-20, 0, 0) * self._leftShoulderInitialRotation
        local downRotationRight = Quaternion.Euler(-20, 0, 0) * self._rightShoulderInitialRotation
        
        leftShoulder.localRotation = Quaternion.Slerp(downRotation, self._leftShoulderInitialRotation, t)
        rightShoulder.localRotation = Quaternion.Slerp(downRotationRight, self._rightShoulderInitialRotation, t)
        
        if t >= 1 then
            -- Completed one full cycle (up → default → down → default)
            self._shoulderCycle = self._shoulderCycle + 1
            
            -- Check if we've completed all cycles
            if self._shoulderCycle >= self._shoulderMaxCycles then
                -- End shoulder movement
                self._shoulderMovementActive = false
                self._shoulderCycle = 0
                self._shoulderState = "wait"
            else
                -- Wait before next cycle
                self._shoulderState = "wait"
                self._shoulderWaitTimer = 0
                self._shoulderWaitDuration = math.random(0.5, 1.5)
            end
        end
    end
end



function ShapeChangeGrow:Update()
    -- Update animation cooldown
    if self._animationCooldown > 0 then
        self._animationCooldown = self._animationCooldown - Time.deltaTime
        if self._animationCooldown < 0 then
            self._animationCooldown = 0
        end
    end

    -- Force bone scale during massage animation to prevent size changes
    if self._massageAnimationActive and self._savedBoneScale and self.agent then
        local bone = FindBone(self.agent, self.boneName)
        if bone and bone.localScale then
            bone.localScale = self._savedBoneScale

            -- Extra aggressive forcing for the first few frames after animation starts
            if self._forceScaleFrames > 0 then
                self._forceScaleFrames = self._forceScaleFrames - 1
                -- Force it multiple times per frame for the first few frames
                bone.localScale = self._savedBoneScale
            end
        end
    end

    -- Update smooth animation if active
    self:UpdateSmoothAnimation()
    
    -- Check for auto-hug activation based on object size (disabled in special mode)
    local autoHugTriggered = false
    if self.agent and not self._specialModeActive then
        local bone = FindBone(self.agent, self.boneName)
        if bone then
            local currentSize = math.max(bone.localScale.x, bone.localScale.y, bone.localScale.z)
            -- Debug: Show size every few seconds
            self._sizeDebugTimer = (self._sizeDebugTimer or 0) + Time.deltaTime
            if self._sizeDebugTimer >= 2.0 then
                Game.Toast.New().Print("Current Size: " .. string.format("%.1f", currentSize))
                self._sizeDebugTimer = 0
            end
            -- Trigger auto-hug ONCE in the correct range (adjusting for bone scale vs visual size)
            if currentSize >= 2.5 and currentSize <= 3.0 and not self.hugActive and not self._autoHugTriggered and not self._manuallyDisabled then
                self.hugActive = true
                self._autoHugTriggered = true
                autoHugTriggered = true
                Game.Toast.New().Print("Auto-Hug: ON (Size: " .. string.format("%.1f", currentSize) .. ")")
            end
            
            -- Reset auto-hug flag when size goes below range
            if currentSize < 2.5 then
                self._autoHugTriggered = false
                self._manuallyDisabled = false  -- Reset manual disable flag
            end
        else
            -- Debug: bone not found
            if not self._boneDebugShown then
                Game.Toast.New().Print("DEBUG: Bone not found: " .. tostring(self.boneName))
                self._boneDebugShown = true
            end
        end
    end

    -- Check for WASD keys to disable hug pose (with cooldown to prevent spam)
    if self.hugActive and Input and Input.GetKey then
        if Input.GetKey("w") or Input.GetKey("a") or Input.GetKey("s") or Input.GetKey("d") then
            self._movementDisableTimer = (self._movementDisableTimer or 0) + Time.deltaTime
            if self._movementDisableTimer >= 0.5 then  -- 0.5 second delay before disabling
                self.hugActive = false
                self._autoHugTriggered = false  -- Allow auto-hug to trigger again
                self._manuallyDisabled = true  -- Mark as manually disabled
                -- Reset extra rotation flags
                self._hugExtraRotation = false
                self._hugRotationTransition = false
                self._hugExtraRotation2 = false
                self._hugRotationTransition2 = false
                -- Reset shoulder movement
                self._shoulderMovementActive = false
                self._shoulderCycle = 0
                self._shoulderTransitionActive = false
                self._shoulderState = "wait"
                self._shoulderLargeDefaultActive = false
                Game.Toast.New().Print("Hug: OFF (Movement detected)")
                self._movementDisableTimer = 0
            end
        else
            self._movementDisableTimer = 0  -- Reset timer if not moving
        end
    end

    -- Hug pose toggle (works independently without main object, disabled in special mode)
    if Input and Input.GetKeyDown and Input.GetKeyDown("l") and not self._specialModeActive then
        -- TEMPORARY: Allow running without an agent
        if not self.agent then
            self.agent = Player
        end
        
        self.hugActive = not self.hugActive
        if self.hugActive then
            self._manuallyDisabled = false  -- Clear manual disable flag when enabling
            Game.Toast.New().Print("Hug: ON (Manual)")
        else
            -- Reset auto-hug flag when manually disabling
            self._autoHugTriggered = false
            self._manuallyDisabled = true  -- Mark as manually disabled
            -- Reset extra rotation flags
            self._hugExtraRotation = false
            self._hugRotationTransition = false
            self._hugExtraRotation2 = false
            self._hugRotationTransition2 = false
            -- Reset shoulder movement
            self._shoulderMovementActive = false
            self._shoulderCycle = 0
            self._shoulderTransitionActive = false
            self._shoulderState = "wait"
            self._shoulderLargeDefaultActive = false
            self._shoulderWideDefaultActive = false
            Game.Toast.New().Print("Hug: OFF (Manual) - Auto-hug disabled until size changes")
        end
    end
    
    -- Shoulder movement toggle
    if Input and Input.GetKeyDown and Input.GetKeyDown("k") then
        self._shoulderMovementEnabled = not self._shoulderMovementEnabled
        if self._shoulderMovementEnabled then
            Game.Toast.New().Print("Shoulder Movement: ON")
            -- Force start shoulder movement if hug is active, regardless of size
            if self.hugActive and not self._shoulderMovementActive then
                self._shoulderMovementActive = true
                self._shoulderCycle = 0
                self._shoulderState = "wait"
                self._shoulderTransitionActive = false
                self._shoulderTransitionTimer = 0
                self._shoulderWaitTimer = 0
                self._shoulderWaitDuration = math.random(0.5, 1.5)
                self._shoulderLargeDefaultActive = false
            end
        else
            Game.Toast.New().Print("Shoulder Movement: OFF")
            -- Reset shoulder movement if it was active
            if self._shoulderMovementActive then
                self._shoulderMovementActive = false
                self._shoulderCycle = 0
                self._shoulderTransitionActive = false
                self._shoulderState = "wait"
                self._shoulderLargeDefaultActive = false
                self._shoulderWideDefaultActive = false
                -- Reset shoulders to default position
                if self.agent then
                    local leftShoulder = FindBone(self.agent, "87.JOINT_LEFTARM")
                    local rightShoulder = FindBone(self.agent, "51.JOINT_RIGHTARM")
                    if leftShoulder and self._leftShoulderInitialRotation then
                        leftShoulder.localRotation = self._leftShoulderInitialRotation
                    end
                    if rightShoulder and self._rightShoulderInitialRotation then
                        rightShoulder.localRotation = self._rightShoulderInitialRotation
                    end
                end
            end
        end
    end
    
    -- Special mode toggle (Button 1)
    if Input and Input.GetKeyDown and Input.GetKeyDown("1") then
        self._specialModeActive = not self._specialModeActive
        if self._specialModeActive then
            -- Reset everything to default first
            self:ResetToDefault()
            -- Then setup special mode (just store initial values)
            self:SetupSpecialMode()
        else
            -- Reset to default when turning off
            self:ResetToDefault()
            Game.Toast.New().Print("Special Mode: OFF (All functions restored)")
        end
    end
    
    -- Character rotation controls (Q/E keys) - only during hug
    if Input and Input.GetKey and self.agent and self.agent.transform and self.hugActive then
        -- Update rotation when keys are pressed
        if Input.GetKey("q") then
            local rotationAmount = -self._characterRotationSpeed * Time.deltaTime
            self._currentRotationY = self._currentRotationY + rotationAmount
        elseif Input.GetKey("e") then
            local rotationAmount = self._characterRotationSpeed * Time.deltaTime
            self._currentRotationY = self._currentRotationY + rotationAmount
        end
        
        -- Apply rotation every frame to prevent it from being reset
        self.agent.transform.rotation = Quaternion.Euler(0, self._currentRotationY, 0)
    end

    -- Apply hug pose every frame (works with both manual and auto-hug)
    if self.agent then
        -- Bones for both arms
        local bones = {
            right = {
                shoulder = FindBone(self.agent, "51.JOINT_RIGHTARM"),
                elbow    = FindBone(self.agent, "56.JOINT_RIGHTELBOW"),
                wrist    = FindBone(self.agent, "61.JOINT_RIGHTWRIST"),
            },
            left = {
                shoulder = FindBone(self.agent, "87.JOINT_LEFTARM"),
                elbow    = FindBone(self.agent, "92.JOINT_LEFTELBOW"),
                wrist    = FindBone(self.agent, "97.JOINT_LEFTWRIST"),
            }
        }

        -- Store original rotations and positions if not already
        self._hugOrig = self._hugOrig or {}
        for side, joints in pairs(bones) do
            self._hugOrig[side] = self._hugOrig[side] or {}
            for name, bone in pairs(joints) do
                if bone and not self._hugOrig[side][name] then
                    self._hugOrig[side][name] = {
                        rotation = bone.localRotation,
                        position = bone.localPosition
                    }
                end
            end
        end

        -- Set pose
        if self.hugActive then
            -- Debug: confirm hug is executing
            if not self._hugExecuteShown then
                Game.Toast.New().Print("DEBUG: Executing hug pose!")
                self._hugExecuteShown = true
            end
            

            -- Shoulders: rotate forward (X axis)
            if bones.right.shoulder then
                bones.right.shoulder.localRotation = Quaternion.Euler(-60, 0, 0) * self._hugOrig.right.shoulder.rotation
            end
            if bones.left.shoulder then
                bones.left.shoulder.localRotation = Quaternion.Euler(-60, 0, 0) * self._hugOrig.left.shoulder.rotation
            end
            -- Elbows: keep straight (X axis), swing inward (Y axis), move outward (position)
            if bones.right.elbow then
                bones.right.elbow.localRotation = Quaternion.Euler(10, -30, -30) * self._hugOrig.right.elbow.rotation
                -- Move right elbow to the right
                bones.right.elbow.localPosition = self._hugOrig.right.elbow.position + Vector3.new(100, 0, 0)
            end
            if bones.left.elbow then
                bones.left.elbow.localRotation = Quaternion.Euler(10, 30, 30) * self._hugOrig.left.elbow.rotation
                -- Move left elbow to the left
                bones.left.elbow.localPosition = self._hugOrig.left.elbow.position + Vector3.new(-100, 0, 0)
            end
            -- Wrists: rotate in (Z axis, smaller angle), move inward (position)
            if bones.right.wrist then
                bones.right.wrist.localRotation = Quaternion.Euler(0, 0, -40) * self._hugOrig.right.wrist.rotation
                -- Move right wrist to the left
                bones.right.wrist.localPosition = self._hugOrig.right.wrist.position + Vector3.new(110, 90, -10)
            end
            if bones.left.wrist then
                bones.left.wrist.localRotation = Quaternion.Euler(0, 0, 40) * self._hugOrig.left.wrist.rotation
                -- Move left wrist to the right
                bones.left.wrist.localPosition = self._hugOrig.left.wrist.position + Vector3.new(-110, 90, -10)
            end
        else
            -- Debug: confirm hug is off
            if self._hugExecuteShown then
                Game.Toast.New().Print("DEBUG: Hug pose OFF, restoring positions!")
                self._hugExecuteShown = false
            end
            

            
            -- Restore original rotations and positions
            for side, joints in pairs(bones) do
                for name, bone in pairs(joints) do
                    if bone and self._hugOrig[side][name] then
                        bone.localRotation = self._hugOrig[side][name].rotation
                        bone.localPosition = self._hugOrig[side][name].position
                    end
                end
            end
        end
    end

    -- Key handling at the very top
    if not Input then
        Game.Toast.New().Print("Input is nil!")
    else
        if Input.GetKeyDown and Input.GetKeyDown("g") then
            self.active = not self.active
            Game.Toast.New().Print(self.active and "Growth Started" or "Growth Paused")
        end
        if Input.GetKeyDown and Input.GetKeyDown("m") then
            local idx = 1
            for i, v in ipairs(self.modes) do
                if v == self.mode then idx = i end
            end
            idx = idx + 1
            if idx > #self.modes then idx = 1 end
            self.mode = self.modes[idx]
            self:ResetPhases()
            Game.Toast.New().Print("Mode: " .. self.mode)
        end
        if Input.GetKeyDown and Input.GetKeyDown("y") then
            if self._specialModeActive then
                -- In special mode, Y repositions object
                self:RepositionObject()
            else
                -- Normal mode behavior
                local offsetBone = FindBone(self.agent, self.offsetBoneName)
                if offsetBone then
                    local offset = Vector3.new(tonumber(self.offsetX), tonumber(self.offsetY), tonumber(self.offsetZ))
                    offsetBone.localPosition = offsetBone.localPosition + offset
                    Game.Toast.New().Print("Lips: Offset bone moved by " .. tostring(offset))
                else
                    Game.Toast.New().Print("Lips: Offset bone not found!")
                end
            end
        end
        
        -- Size adjustment keys (= to grow, - to shrink)
        if Input.GetKeyDown and Input.GetKeyDown("=") then
            self:AdjustSize(true)  -- Grow
        end
        if Input.GetKeyDown and Input.GetKeyDown("-") then
            self:AdjustSize(false)  -- Shrink
        end
        
        -- Massage Breasts 5 animation key (N to toggle)
        if Input.GetKeyDown and Input.GetKeyDown("n") then
            self:ToggleMassageBreastsAnimation()
        end

    end

    local agent = self.agent
    if not agent then return end
    local bone = FindBone(agent, self.boneName)
    if not bone then return end
    local parentBone = bone.parent
    if not parentBone then return end

    -- Always re-calculate these every frame
    local baseScale = bone.localScale
    local initialLocalOffset = bone.position - parentBone.position

    -- Only set these ONCE, or if the bone changes (e.g., after moving/respawn)
    if not self._lastBone or self._lastBone ~= bone then
        self._lastBone = bone
        self._initialRotation = bone.localRotation
        self.rotationState = "wait"
        self.rotationTimer = 0
        self.waitTime = math.random(2, 3)
    end

    -- Check if we need to apply extra rotation and shoulder movement based on size (disabled in special mode)
    if self.hugActive and self.agent and not self._specialModeActive then
        local bone = FindBone(self.agent, self.boneName)
        if bone then
            local currentSize = math.max(bone.localScale.x, bone.localScale.y, bone.localScale.z)
            -- Set "sticky" extra rotation flag when size >= 3.5
            if currentSize >= 3.5 and not self._hugExtraRotation then
                self._hugExtraRotation = true
                self._hugRotationTransition = true
                self._hugRotationTimer = 0
            end
            -- Set second extra rotation flag when size >= 4.5
            if currentSize >= 4.5 and not self._hugExtraRotation2 then
                self._hugExtraRotation2 = true
                self._hugRotationTransition2 = true
                self._hugRotationTimer2 = 0
            end
            -- Activate shoulder movement when size >= 3.0 (only if enabled and size < 6)
            if currentSize >= 3.0 and currentSize < 6 and not self._shoulderMovementActive and self._shoulderMovementEnabled then
                self._shoulderMovementActive = true
                self._shoulderCycle = 0
                self._shoulderState = "wait"  -- Start in wait state
                self._shoulderTransitionActive = false
                self._shoulderTransitionTimer = 0
                self._shoulderWaitTimer = 0
                self._shoulderWaitDuration = math.random(0.5, 1.5)  -- Random wait between 0.5-1.5 seconds
            end
            
            -- Stop shoulder movement when size >= 6 and apply new default
            if currentSize >= 6 and currentSize < 8.5 then
                if self._shoulderMovementActive then
                    self._shoulderMovementActive = false
                    self._shoulderCycle = 0
                    self._shoulderTransitionActive = false
                    self._shoulderState = "wait"
                end
                
                -- Apply new default shoulder position (down 10 degrees)
                if not self._shoulderLargeDefaultActive then
                    self._shoulderLargeDefaultActive = true
                    local leftShoulder = FindBone(self.agent, "87.JOINT_LEFTARM")
                    local rightShoulder = FindBone(self.agent, "51.JOINT_RIGHTARM")
                    if leftShoulder and rightShoulder then
                        -- Store initial rotations if not already stored
                        if not self._leftShoulderInitialRotation then
                            self._leftShoulderInitialRotation = leftShoulder.localRotation
                        end
                        if not self._rightShoulderInitialRotation then
                            self._rightShoulderInitialRotation = rightShoulder.localRotation
                        end
                        -- Apply new default (down 10 degrees)
                        local newDefaultLeft = Quaternion.Euler(-10, 0, 0) * self._leftShoulderInitialRotation
                        local newDefaultRight = Quaternion.Euler(-10, 0, 0) * self._rightShoulderInitialRotation
                        leftShoulder.localRotation = newDefaultLeft
                        rightShoulder.localRotation = newDefaultRight
                    end
                end
            end
            
            -- Apply wide shoulder default at size 8.5
            if currentSize >= 8.5 and not self._shoulderWideDefaultActive then
                self._shoulderWideDefaultActive = true
                self._shoulderLargeDefaultActive = false  -- Turn off the 6.0+ default
                
                local leftShoulder = FindBone(self.agent, "87.JOINT_LEFTARM")
                local rightShoulder = FindBone(self.agent, "51.JOINT_RIGHTARM")
                if leftShoulder and rightShoulder then
                    -- Store initial rotations if not already stored
                    if not self._leftShoulderInitialRotation then
                        self._leftShoulderInitialRotation = leftShoulder.localRotation
                    end
                    if not self._rightShoulderInitialRotation then
                        self._rightShoulderInitialRotation = rightShoulder.localRotation
                    end
                    -- Apply wide shoulder position (spread out - Z axis rotation)
                    local wideLeftRotation = Quaternion.Euler(0, 0, -10) * self._leftShoulderInitialRotation  -- Left shoulder out
                    local wideRightRotation = Quaternion.Euler(0, 0, 10) * self._rightShoulderInitialRotation  -- Right shoulder out
                    leftShoulder.localRotation = wideLeftRotation
                    rightShoulder.localRotation = wideRightRotation
                end
            end
        end
    end
    
    -- Handle gradual rotation transitions
    local extraRotationAmount = 0
    
    -- First extra rotation (size 3.5+)
    if self._hugExtraRotation then
        if self._hugRotationTransition then
            self._hugRotationTimer = self._hugRotationTimer + Time.deltaTime
            local t = math.min(self._hugRotationTimer / 1.0, 1)  -- 1 second transition
            extraRotationAmount = extraRotationAmount + (-10 * t)  -- Gradually apply -10 degrees
            if t >= 1 then
                self._hugRotationTransition = false
            end
        else
            extraRotationAmount = extraRotationAmount - 10  -- Full extra rotation
        end
    end
    
    -- Second extra rotation (size 4.5+)
    if self._hugExtraRotation2 then
        if self._hugRotationTransition2 then
            self._hugRotationTimer2 = self._hugRotationTimer2 + Time.deltaTime
            local t = math.min(self._hugRotationTimer2 / 1.0, 1)  -- 1 second transition
            extraRotationAmount = extraRotationAmount + (-5 * t)  -- Gradually apply -5 degrees
            if t >= 1 then
                self._hugRotationTransition2 = false
            end
        else
            extraRotationAmount = extraRotationAmount - 5  -- Full second extra rotation
        end
    end
    
    -- Update rotations based on current hug state (disabled in special mode)
    local hugBaseOffset = 0
    if self.hugActive and not self._specialModeActive then
        hugBaseOffset = -20 + extraRotationAmount  -- Base hug rotation + extra rotation
    end
    local originalRotation = Quaternion.Euler(hugBaseOffset, 0, 0) * self._initialRotation  -- Base position
    local targetRotation = Quaternion.Euler(-30 + hugBaseOffset, 0, 0) * self._initialRotation  -- Nod position

    -- Head nodding animation (disabled in special mode)
    if not self._specialModeActive then
        -- Rotation state machine (store only the state, not the rotation itself)
        self.rotationState = self.rotationState or "wait"
        self.rotationTimer = self.rotationTimer or 0
        self.waitTime = self.waitTime or math.random(2, 3)

        -- Rotation timing parameters
        local rotateOutTime = 0.2   -- fast out
        local holdTime = 0.2        -- hold at max
        local rotateBackTime = 0.8  -- slow back

        -- Rotation state machine (same as before, just using new targetRotation)
        if self.rotationState == "wait" then
            self.rotationTimer = self.rotationTimer + Time.deltaTime
            bone.localRotation = originalRotation
            if self.rotationTimer >= self.waitTime then
                self.rotationState = "out"
                self.rotationTimer = 0
            end
        elseif self.rotationState == "out" then
            local t = math.min(self.rotationTimer / rotateOutTime, 1)
            bone.localRotation = Quaternion.Slerp(originalRotation, targetRotation, t)
            self.rotationTimer = self.rotationTimer + Time.deltaTime
            if t >= 1 then
                self.rotationState = "hold"
                self.rotationTimer = 0
            end
        elseif self.rotationState == "hold" then
            bone.localRotation = targetRotation
            self.rotationTimer = self.rotationTimer + Time.deltaTime
            if self.rotationTimer >= holdTime then
                self.rotationState = "back"
                self.rotationTimer = 0
            end
        elseif self.rotationState == "back" then
            local t = math.min(self.rotationTimer / rotateBackTime, 1)
            bone.localRotation = Quaternion.Slerp(targetRotation, originalRotation, t)
            self.rotationTimer = self.rotationTimer + Time.deltaTime
            if t >= 1 then
                self.rotationState = "wait"
                self.rotationTimer = 0
                self.waitTime = math.random(2, 3)
            end
        end
    else
        -- In special mode, keep head at original position
        bone.localRotation = self._initialRotation
    end

    -- Handle shoulder movement when active and enabled (not when large default is active)
    if self._shoulderMovementActive and self.hugActive and self._shoulderMovementEnabled and not self._shoulderLargeDefaultActive then
        self:UpdateShoulderMovement()
    end
    


    -- Disable all growth functionality when special mode is active
    if self._specialModeActive then
        return
    end

    -- Disable growth when massage animation is active to prevent scaling conflicts
    if self._massageAnimationActive then
        return
    end

    -- Now check for growth activation
    if not self.active then return end

    -- Clamp scale to limit (set to huge for unlimited)
    local function clampScale(scale, limit)
        local max = math.max(scale.x, scale.y, scale.z)
        if max > 1e6 then
            local factor = 1e6 / max
            return Vector3.new(scale.x * factor, scale.y * factor, scale.z * factor)
        end
        return scale
    end

    -- LINEAR MODE
    if self.mode == "Linear" then
        self.phase = "grow"
        if bone.localScale.y < self.limit then
            local growAmount = self.speed * 2.5 * Time.deltaTime -- Faster growth
            -- Slow down growth when in hug pose
            if self.hugActive then
                growAmount = growAmount * 0.3  -- 30% of normal growth speed
            end
            local targetScale = bone.localScale + Vector3.new(growAmount, growAmount, growAmount)
            bone.localScale = clampScale(targetScale, self.limit)
        end
        bone.position = parentBone.position + (parentBone.rotation * initialLocalOffset)
        return
    end

    -- OSCILLATE MODE
    if self.mode == "Oscillate" then
        self.oscTimer = self.oscTimer + Time.deltaTime
        local osc = math.sin(self.oscTimer * math.pi) * 0.15
        local targetScale = baseScale * (1 + osc)
        bone.localScale = clampScale(targetScale, self.limit)
        bone.position = parentBone.position + (parentBone.rotation * initialLocalOffset)
        return
    end

    -- BURST MODE
    if self.mode == "Burst" then
        self.baseScale = self.baseScale or bone.localScale
        self.nextPulseInterval = self.nextPulseInterval or math.random(15, 40) / 100

        if self.phase == "gs" then
            -- Initialize GS transition if needed
            if not self.gsTransitionActive then
                self.gsTransitionActive = true
                self.gsTransitionTimer = 0
                self.gsTransitionDuration = 0.3  -- Slightly longer for smoother feel
                self.gsCurrentScale = bone.localScale
                -- Always start by growing (positive direction)
                self.gsDirection = 1
                -- Make sure we grow from current size, not base size
                local currentSize = math.max(bone.localScale.x, bone.localScale.y, bone.localScale.z)
                local scaleAmount = 0.03  -- Use same conservative scaling (3%)
                local growScale = bone.localScale * (1 + scaleAmount)
                self.gsTargetScale = clampScale(growScale, self.limit)
            end
            
            -- Handle continuous gradual transition
            if self.gsTransitionActive then
                self.gsTransitionTimer = self.gsTransitionTimer + Time.deltaTime
                local t = math.min(self.gsTransitionTimer / self.gsTransitionDuration, 1)
                
                -- Smooth interpolation between current and target scale
                bone.localScale = Vector3.Lerp(self.gsCurrentScale, self.gsTargetScale, t)
                
                -- When transition completes, immediately start next one
                if t >= 1 then
                    self.gsCycle = self.gsCycle + 1
                    bone.localScale = self.gsTargetScale
                    
                    -- Check if we should continue cycling or go to burst
                    if self.gsCycle >= self.gsMaxCycles * 2 then
                        -- Trigger burst growth
                        self.phase = "burstGrow"
                        self.burstGrowthTimer = 0
                        self.burstGrowthStartScale = bone.localScale
                        -- Reduced burst multiplier for smaller final burst
                        local burstMult = 0.08 + (self.gsMaxCycles - 2) * 0.05  -- Reduced from 0.15 + 0.1
                        local burstTarget = self.burstGrowthStartScale * (1 + burstMult)
                        self.burstGrowthTargetScale = clampScale(burstTarget, self.limit)
                    else
                        -- Continue cycling - flip direction and start next transition
                        self.gsDirection = self.gsDirection * -1
                        -- Use smaller, more conservative scaling for smoother cycles
                        local scaleAmount = 0.03  -- Reduced from 0.05 to 0.03 (3% instead of 5%)
                        local nextScale = bone.localScale * (1 + self.gsDirection * scaleAmount)
                        self.gsTargetScale = clampScale(nextScale, self.limit)
                        self.gsCurrentScale = bone.localScale
                        self.gsTransitionTimer = 0
                        -- gsTransitionActive stays true for continuous cycling
                    end
                end
            end
            
            bone.localScale = clampScale(bone.localScale, self.limit)
            bone.position = parentBone.position + (parentBone.rotation * initialLocalOffset)
            return
        end

        -- When starting a new GS phase, randomize gsMaxCycles:
        if self.phase == "idle" then
            self.phase = "gs"
            self.gsCycle = 0
            self.gsMaxCycles = math.random(2, 6)
            self.gsDirection = 1  -- Start with positive direction (grow first)
            self.gsPhaseTimer = 0
            self.nextPulseInterval = math.random(15, 40) / 100
        end

        if self.phase == "burstGrow" then
            local deltaTime = Time.deltaTime
            -- Slow down burst growth when in hug pose
            if self.hugActive then
                deltaTime = deltaTime * 0.3  -- 30% of normal growth speed
            end
            self.burstGrowthTimer = self.burstGrowthTimer + deltaTime
            local t = math.min(self.burstGrowthTimer / self.burstGrowthDuration, 1)
            local easeT = 1 - (1 - t) * (1 - t)
            local start = self.burstGrowthStartScale
            local target = self.burstGrowthTargetScale
            local newScale = Vector3.new(
                start.x + (target.x - start.x) * easeT,
                start.y + (target.y - start.y) * easeT,
                start.z + (target.z - start.z) * easeT
            )
            bone.localScale = clampScale(newScale, self.limit)
            bone.position = parentBone.position + (parentBone.rotation * initialLocalOffset)
            if t >= 1 then
                self.baseScale = bone.localScale
                self.phase = "burstPause"
                self.burstPauseTimer = 0
            end
            return
        end

        if self.phase == "burstPause" then
            self.burstPauseTimer = self.burstPauseTimer + Time.deltaTime
            if self.burstPauseTimer >= self.burstRepeatDelay then
                self.phase = "gs"
                self.gsCycle = 0
                self.gsMaxCycles = math.random(2, 6)
                self.gsDirection = 1
                self.gsPhaseTimer = 0
                self.nextPulseInterval = math.random(15, 40) / 100
                -- IMPORTANT: Update baseScale to current size after burst
                self.baseScale = bone.localScale
                -- Reset GS transition flags for clean start
                self.gsTransitionActive = false
                self.gsTransitionTimer = 0
            end
            bone.localScale = clampScale(bone.localScale, self.limit)
            bone.position = parentBone.position + (parentBone.rotation * initialLocalOffset)
            return
        end

        bone.localScale = clampScale(bone.localScale, self.limit)
        bone.position = parentBone.position + (parentBone.rotation * initialLocalOffset)
    end
end

return ShapeChangeGrow
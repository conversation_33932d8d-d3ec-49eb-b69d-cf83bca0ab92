<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.Game.Toast Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_game_1_1_toast.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="class_lua_1_1_game_1_1_toast-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.Game.Toast Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Toasts notifications act similar to a volume control interface on a modern television or operating systems where you can update the information inside the existing notification while it's still being displayed. However unlike those interfaces, multiple toast notifications can be displayed and updated at the same time. This can be useful when you want to debug a script or notify a user about something without interrupting game play. A <a class="el" href="class_lua_1_1_game_1_1_toast.html" title="Toasts notifications act similar to a volume control interface on a modern television or operating sy...">Toast</a> notification will stay on the screen for 5 seconds after its last message.  
 <a href="class_lua_1_1_game_1_1_toast.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a5ebc4bf8c4b6a0a14947ac2ff5dfd25c"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_game_1_1_toast.html#a5ebc4bf8c4b6a0a14947ac2ff5dfd25c">Print</a> (string message=null)</td></tr>
<tr class="memdesc:a5ebc4bf8c4b6a0a14947ac2ff5dfd25c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Update the toast notification with a new message. If the toast notification is not currently visible it will reappear.  <a href="class_lua_1_1_game_1_1_toast.html#a5ebc4bf8c4b6a0a14947ac2ff5dfd25c">More...</a><br /></td></tr>
<tr class="separator:a5ebc4bf8c4b6a0a14947ac2ff5dfd25c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-static-methods" name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:a8a0015fd4ae0538932b1855f5977c353"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_game_1_1_toast.html">Toast</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_game_1_1_toast.html#a8a0015fd4ae0538932b1855f5977c353">New</a> ()</td></tr>
<tr class="memdesc:a8a0015fd4ae0538932b1855f5977c353"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a new instance of a toast notification  <a href="class_lua_1_1_game_1_1_toast.html#a8a0015fd4ae0538932b1855f5977c353">More...</a><br /></td></tr>
<tr class="separator:a8a0015fd4ae0538932b1855f5977c353"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad315dc396b4011873c10f48db1685805"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_game_1_1_toast.html">Toast</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_game_1_1_toast.html#ad315dc396b4011873c10f48db1685805">New</a> (string identifier)</td></tr>
<tr class="memdesc:ad315dc396b4011873c10f48db1685805"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a new instance of a toast notification with a specific identifier. This allows several scripts to update the same toast.  <a href="class_lua_1_1_game_1_1_toast.html#ad315dc396b4011873c10f48db1685805">More...</a><br /></td></tr>
<tr class="separator:ad315dc396b4011873c10f48db1685805"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >Toasts notifications act similar to a volume control interface on a modern television or operating systems where you can update the information inside the existing notification while it's still being displayed. However unlike those interfaces, multiple toast notifications can be displayed and updated at the same time. This can be useful when you want to debug a script or notify a user about something without interrupting game play. A <a class="el" href="class_lua_1_1_game_1_1_toast.html" title="Toasts notifications act similar to a volume control interface on a modern television or operating sy...">Toast</a> notification will stay on the screen for 5 seconds after its last message. </p>
<p ><a class="el" href="class_lua_1_1_game_1_1_toast.html" title="Toasts notifications act similar to a volume control interface on a modern television or operating sy...">Toast</a> notification are very efficient and should be used when you expect to write a message frequently.</p>
<p >This snippet of code counts and displays how many frames have been generated since it started. Take note how <a class="el" href="class_lua_1_1_game_1_1_toast.html#a8a0015fd4ae0538932b1855f5977c353" title="Creates a new instance of a toast notification">Game.Toast.New()</a> has been kept out of the Update() loop while <a class="el" href="class_lua_1_1_game_1_1_toast.html#a5ebc4bf8c4b6a0a14947ac2ff5dfd25c" title="Update the toast notification with a new message. If the toast notification is not currently visible ...">Print()</a> is called within it. </p><div class="fragment"><div class="line"><span class="keyword">function</span> Start()</div>
<div class="line">    myToast = <a class="code hl_class" href="class_lua_1_1_game.html">Game</a>.<a class="code hl_class" href="class_lua_1_1_game_1_1_toast.html">Toast</a>.<a class="code hl_function" href="class_lua_1_1_game_1_1_toast.html#a8a0015fd4ae0538932b1855f5977c353">New</a>()</div>
<div class="line">    myFrame = 0</div>
<div class="line">end</div>
<div class="line"> </div>
<div class="line">function Update()</div>
<div class="line">    myFrame = myFrame + 1</div>
<div class="line">    myToast.<a class="code hl_function" href="class_lua_1_1_game_1_1_toast.html#a5ebc4bf8c4b6a0a14947ac2ff5dfd25c">Print</a>(&quot;Frame: &quot; .. myFrame)</div>
<div class="line">end</div>
<div class="ttc" id="aclass_lua_1_1_game_1_1_toast_html"><div class="ttname"><a href="class_lua_1_1_game_1_1_toast.html">Lua.Game.Toast</a></div><div class="ttdoc">Toasts notifications act similar to a volume control interface on a modern television or operating sy...</div><div class="ttdef"><b>Definition:</b> LuaGame.cs:124</div></div>
<div class="ttc" id="aclass_lua_1_1_game_1_1_toast_html_a5ebc4bf8c4b6a0a14947ac2ff5dfd25c"><div class="ttname"><a href="class_lua_1_1_game_1_1_toast.html#a5ebc4bf8c4b6a0a14947ac2ff5dfd25c">Lua.Game.Toast.Print</a></div><div class="ttdeci">void Print(string message=null)</div><div class="ttdoc">Update the toast notification with a new message. If the toast notification is not currently visible ...</div><div class="ttdef"><b>Definition:</b> LuaGame.cs:156</div></div>
<div class="ttc" id="aclass_lua_1_1_game_1_1_toast_html_a8a0015fd4ae0538932b1855f5977c353"><div class="ttname"><a href="class_lua_1_1_game_1_1_toast.html#a8a0015fd4ae0538932b1855f5977c353">Lua.Game.Toast.New</a></div><div class="ttdeci">static Toast New()</div><div class="ttdoc">Creates a new instance of a toast notification</div><div class="ttdef"><b>Definition:</b> LuaGame.cs:133</div></div>
<div class="ttc" id="aclass_lua_1_1_game_html"><div class="ttname"><a href="class_lua_1_1_game.html">Lua.Game</a></div><div class="ttdoc">A collection of Sizebox specific functions that don't belong to any object.</div><div class="ttdef"><b>Definition:</b> LuaGame.cs:13</div></div>
</div><!-- fragment --> </div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a8a0015fd4ae0538932b1855f5977c353" name="a8a0015fd4ae0538932b1855f5977c353"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8a0015fd4ae0538932b1855f5977c353">&#9670;&nbsp;</a></span>New() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="class_lua_1_1_game_1_1_toast.html">Toast</a> Lua.Game.Toast.New </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Creates a new instance of a toast notification </p>
<dl class="section return"><dt>Returns</dt><dd>A instance of <a class="el" href="class_lua_1_1_game_1_1_toast.html" title="Toasts notifications act similar to a volume control interface on a modern television or operating sy...">Toast</a> that you can call <a class="el" href="class_lua_1_1_game_1_1_toast.html#a5ebc4bf8c4b6a0a14947ac2ff5dfd25c" title="Update the toast notification with a new message. If the toast notification is not currently visible ...">Print()</a> on to update the message</dd></dl>
<p >For your own sanity. You should never make a new toast notification inside a loop or frequently called function like Update().</p>

</div>
</div>
<a id="ad315dc396b4011873c10f48db1685805" name="ad315dc396b4011873c10f48db1685805"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad315dc396b4011873c10f48db1685805">&#9670;&nbsp;</a></span>New() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="class_lua_1_1_game_1_1_toast.html">Toast</a> Lua.Game.Toast.New </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>identifier</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Creates a new instance of a toast notification with a specific identifier. This allows several scripts to update the same toast. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">identifier</td><td>A string that represents a unique identifier that this toast notification should have. Any and all toasts with the same identifier will control the same toast that is displayed to the user. A identifier must not be nil or start with an underscore (_); if it does, then the identifier is ignored and the returned toast is made as if it came from <a class="el" href="class_lua_1_1_game_1_1_toast.html#a8a0015fd4ae0538932b1855f5977c353" title="Creates a new instance of a toast notification">New()</a></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>A instance of <a class="el" href="class_lua_1_1_game_1_1_toast.html" title="Toasts notifications act similar to a volume control interface on a modern television or operating sy...">Toast</a> that you can call <a class="el" href="class_lua_1_1_game_1_1_toast.html#a5ebc4bf8c4b6a0a14947ac2ff5dfd25c" title="Update the toast notification with a new message. If the toast notification is not currently visible ...">Print()</a> on to update the message</dd></dl>
<p ><a class="el" href="class_lua_1_1_game_1_1_toast.html#a8a0015fd4ae0538932b1855f5977c353" title="Creates a new instance of a toast notification">New()</a> should be adequate for most tasks. Unless you know what you're doing and have a very good reason to assign a custom identifier to your toast instance then you shouldn't.</p>

</div>
</div>
<a id="a5ebc4bf8c4b6a0a14947ac2ff5dfd25c" name="a5ebc4bf8c4b6a0a14947ac2ff5dfd25c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5ebc4bf8c4b6a0a14947ac2ff5dfd25c">&#9670;&nbsp;</a></span>Print()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Game.Toast.Print </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>message</em> = <code>null</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Update the toast notification with a new message. If the toast notification is not currently visible it will reappear. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">message</td><td>The message to display in the toast notification. If nil is passed, an existing toast notification will start fading out immediately.</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaGame.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_game.html">Game</a></li><li class="navelem"><a class="el" href="class_lua_1_1_game_1_1_toast.html">Toast</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

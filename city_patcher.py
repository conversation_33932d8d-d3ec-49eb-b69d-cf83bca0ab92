#!/usr/bin/env python3
"""
Sizebox City Auto-Generator Patcher
Modifies Assembly-CSharp.dll to automatically generate cities without UI interaction
"""

import os
import shutil
import hashlib

def backup_file(source, backup_dir):
    """Create a backup of the original file"""
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    
    backup_path = os.path.join(backup_dir, os.path.basename(source) + '.backup')
    shutil.copy2(source, backup_path)
    print(f"Backup created: {backup_path}")
    return backup_path

def get_file_hash(filepath):
    """Get MD5 hash of file for verification"""
    with open(filepath, 'rb') as f:
        return hashlib.md5(f.read()).hexdigest()

def find_pattern_in_file(filepath, pattern):
    """Find byte pattern in file and return offset"""
    with open(filepath, 'rb') as f:
        data = f.read()
    
    if isinstance(pattern, str):
        pattern = pattern.encode('utf-8')
    
    offset = data.find(pattern)
    return offset if offset != -1 else None

def patch_dll():
    """Main patching function"""
    dll_path = r"Sizebox_Data\Managed\Assembly-CSharp.dll"
    backup_dir = r"..\Sizebox_Backup_2025-07-21_23-43-08"
    
    print("Sizebox City Auto-Generator Patcher")
    print("===================================")
    
    # Verify file exists
    if not os.path.exists(dll_path):
        print(f"ERROR: {dll_path} not found!")
        return False
    
    # Get original hash
    original_hash = get_file_hash(dll_path)
    print(f"Original file hash: {original_hash}")
    
    # Create backup
    backup_path = backup_file(dll_path, backup_dir)
    
    # Load the DLL
    with open(dll_path, 'rb') as f:
        dll_data = bytearray(f.read())
    
    print(f"Loaded DLL: {len(dll_data)} bytes")
    
    # Search for key patterns
    patterns_to_find = [
        b"CityBuilder",
        b"ExecuteCityBuilding", 
        b"SpawnObject",
        b"CITY"
    ]
    
    pattern_offsets = {}
    for pattern in patterns_to_find:
        offset = dll_data.find(pattern)
        if offset != -1:
            pattern_offsets[pattern.decode('utf-8')] = offset
            print(f"Found '{pattern.decode('utf-8')}' at offset: 0x{offset:08X}")
        else:
            print(f"Pattern '{pattern.decode('utf-8')}' not found")
    
    # Strategy: Look for patterns that suggest UI interaction and bypass them
    # We'll look for conditional checks that require UI confirmation

    if 'CityBuilder' in pattern_offsets and 'ExecuteCityBuilding' in pattern_offsets:
        print("\nFound target methods! Implementing patch...")

        # Look for common UI-related patterns that might block auto-execution
        ui_patterns = [
            b"Dialog",
            b"Button",
            b"UI",
            b"Input",
            b"Click",
            b"Accept",
            b"Confirm"
        ]

        ui_locations = {}
        for pattern in ui_patterns:
            offset = dll_data.find(pattern)
            if offset != -1:
                ui_locations[pattern.decode('utf-8')] = offset
                print(f"Found UI pattern '{pattern.decode('utf-8')}' at: 0x{offset:08X}")

        # Look for conditional jumps or checks near the ExecuteCityBuilding method
        execute_offset = pattern_offsets['ExecuteCityBuilding']

        # Search around the ExecuteCityBuilding method for conditional logic
        search_start = max(0, execute_offset - 1000)
        search_end = min(len(dll_data), execute_offset + 1000)
        search_area = dll_data[search_start:search_end]

        # Look for common IL opcodes that might represent conditional checks
        # These are common .NET IL opcodes for conditional branching
        conditional_opcodes = [
            b'\x2D',  # brtrue
            b'\x2E',  # brfalse
            b'\x38',  # br
            b'\x39',  # brfalse.s
            b'\x3A',  # brtrue.s
        ]

        print(f"\nSearching for conditional logic near ExecuteCityBuilding...")
        conditionals_found = []
        for opcode in conditional_opcodes:
            pos = 0
            while True:
                pos = search_area.find(opcode, pos)
                if pos == -1:
                    break
                actual_offset = search_start + pos
                conditionals_found.append((opcode.hex(), actual_offset))
                pos += 1

        if conditionals_found:
            print(f"Found {len(conditionals_found)} conditional branches near ExecuteCityBuilding")

            # Try a simple approach: look for a pattern that might be checking for UI confirmation
            # and modify it to always proceed

            # This is a risky but targeted approach
            print("\nAttempting to patch conditional logic...")

            # Look for the most likely candidate - a brfalse (branch if false) instruction
            # that might be checking if the UI dialog was confirmed
            brfalse_candidates = [c for c in conditionals_found if c[0] in ['2e', '39']]

            if brfalse_candidates:
                # Take the first brfalse near ExecuteCityBuilding
                target_opcode, target_offset = brfalse_candidates[0]
                print(f"Targeting conditional at offset: 0x{target_offset:08X}")

                # Create the patch: change brfalse to br (unconditional branch)
                # This would make the code always proceed as if the condition was true
                if target_opcode == '2e':  # brfalse
                    dll_data[target_offset] = 0x38  # Change to br (unconditional)
                    print("Patched brfalse -> br (unconditional branch)")
                elif target_opcode == '39':  # brfalse.s
                    dll_data[target_offset] = 0x2B  # Change to br.s (short unconditional)
                    print("Patched brfalse.s -> br.s (short unconditional branch)")

                # Write the patched DLL
                patched_path = dll_path + ".patched"
                with open(patched_path, 'wb') as f:
                    f.write(dll_data)

                print(f"Patched DLL saved as: {patched_path}")

                # Verify the patch
                patched_hash = get_file_hash(patched_path)
                print(f"Patched file hash: {patched_hash}")

                if patched_hash != original_hash:
                    print("✓ File successfully modified!")

                    # Ask user if they want to apply the patch
                    print("\nPatch ready! This will:")
                    print("1. Make CITY objects auto-generate without UI confirmation")
                    print("2. Bypass the configuration dialog")
                    print("3. Allow script-based city spawning to work fully")

                    apply = input("\nApply patch to game? (y/N): ").lower().strip()
                    if apply == 'y':
                        # Backup original and apply patch
                        shutil.copy2(dll_path, dll_path + ".original")
                        shutil.copy2(patched_path, dll_path)
                        print("✓ Patch applied! Original backed up as .original")
                        print("✓ Try spawning a city now - it should auto-generate!")
                        return True
                    else:
                        print("Patch not applied. Files remain unchanged.")
                        return True
                else:
                    print("✗ No changes made to file")
                    return False
            else:
                print("No suitable conditional branches found for patching")
                return False
        else:
            print("No conditional logic found near ExecuteCityBuilding")
            return False
    else:
        print("ERROR: Could not find required methods for patching")
        return False

if __name__ == "__main__":
    try:
        success = patch_dll()
        if success:
            print("\nAnalysis successful! Ready for actual patching.")
        else:
            print("\nAnalysis failed!")
    except Exception as e:
        print(f"Error: {e}")
    
    input("Press Enter to exit...")

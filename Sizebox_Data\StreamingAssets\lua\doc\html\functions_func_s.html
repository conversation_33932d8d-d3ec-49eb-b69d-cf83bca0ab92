<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Class Members - Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('functions_func_s.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a id="index_s" name="index_s"></a>- s -</h3><ul>
<li>Scale()&#160;:&#160;<a class="el" href="class_lua_1_1_vector3.html#a1b7601ca79dfd9b6f3ab3636508c197a">Lua.Vector3</a></li>
<li>Seek()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a32545d25ff6935a006bdb6b5ad45ad9a">Lua.Entity</a></li>
<li>Send()&#160;:&#160;<a class="el" href="class_lua_1_1_event.html#a4f4b7c37fb65d1ee54fd1acbf0c9a62a">Lua.Event</a></li>
<li>Set()&#160;:&#160;<a class="el" href="class_lua_1_1_animation.html#a27ca2f8a6b74867c4727315bbce3878f">Lua.Animation</a>, <a class="el" href="class_lua_1_1_quaternion.html#a0cb037e26892fa50a048fc751f7f017a">Lua.Quaternion</a>, <a class="el" href="class_lua_1_1_vector3.html#a5f40e6344654b7590958df867f1d5b03">Lua.Vector3</a></li>
<li>SetAndWait()&#160;:&#160;<a class="el" href="class_lua_1_1_animation.html#afbab459005c0736f8b52459185cf1637">Lua.Animation</a></li>
<li>SetBehavior()&#160;:&#160;<a class="el" href="class_lua_1_1_a_i.html#a2ab87c868530bf4f91fb44374cd58337">Lua.AI</a></li>
<li>SetBurstFire()&#160;:&#160;<a class="el" href="class_lua_1_1_shooting.html#af0e7769dd39d32787d3197ebee1a3247">Lua.Shooting</a></li>
<li>SetFiringSFX()&#160;:&#160;<a class="el" href="class_lua_1_1_shooting.html#af28162d50b374775ee7618d1cc6c2063">Lua.Shooting</a></li>
<li>SetFromToRotation()&#160;:&#160;<a class="el" href="class_lua_1_1_quaternion.html#aa7991983472a41d5d7c1b7cb61eb5580">Lua.Quaternion</a></li>
<li>SetGlobalSpeed()&#160;:&#160;<a class="el" href="class_lua_1_1_animation.html#af1f4b9f2d2a3e595a08a7f5e5d095580">Lua.Animation</a></li>
<li>SetGrowEnergyColor()&#160;:&#160;<a class="el" href="class_lua_1_1_lua_player_raygun.html#a6787608c1207618d069fba8aee7c8fff">Lua.LuaPlayerRaygun</a></li>
<li>SetLookRotation()&#160;:&#160;<a class="el" href="class_lua_1_1_quaternion.html#aab942db162b73214d4b563bd6a49bad4">Lua.Quaternion</a></li>
<li>SetMorphValue()&#160;:&#160;<a class="el" href="class_lua_1_1_morphs.html#aefc93668891557c3c980857b3ee6aa0b">Lua.Morphs</a></li>
<li>SetNpcRaygunProjectileFireSFX()&#160;:&#160;<a class="el" href="class_lua_1_1_custom_sound_manager.html#a920b942d5d7751c3ff7676a48189c8ca">Lua.CustomSoundManager</a></li>
<li>SetNpcRaygunProjectileImpactSFX()&#160;:&#160;<a class="el" href="class_lua_1_1_custom_sound_manager.html#a5cf94ae4cc82a6ae9137b953c2b37b0f">Lua.CustomSoundManager</a></li>
<li>SetNpcSmgProjectileFireSFX()&#160;:&#160;<a class="el" href="class_lua_1_1_custom_sound_manager.html#a98258d37f9bfcd6d0ca5a53974629ab7">Lua.CustomSoundManager</a></li>
<li>SetNpcSmgProjectileImpactSFX()&#160;:&#160;<a class="el" href="class_lua_1_1_custom_sound_manager.html#ac2be140403d813db6f58549e8e1ec024">Lua.CustomSoundManager</a></li>
<li>SetParent()&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#a44299747664c0a77b5f03f69f032a86f">Lua.Transform</a></li>
<li>SetPlayerRaygunArmingSFX()&#160;:&#160;<a class="el" href="class_lua_1_1_custom_sound_manager.html#a77518b2fe52893bc0d3b7473d5ac33d8">Lua.CustomSoundManager</a></li>
<li>SetPlayerRaygunDisarmingSFX()&#160;:&#160;<a class="el" href="class_lua_1_1_custom_sound_manager.html#aedbcce119473f77ce87a9742c2a3bd2d">Lua.CustomSoundManager</a></li>
<li>SetPlayerRaygunLaserSFX()&#160;:&#160;<a class="el" href="class_lua_1_1_custom_sound_manager.html#a3d7a983d62142d44f24d0e3dea537a65">Lua.CustomSoundManager</a></li>
<li>SetPlayerRaygunModeSwitchSFX()&#160;:&#160;<a class="el" href="class_lua_1_1_custom_sound_manager.html#a6ad73ad3423d997434d87a1829f9d910">Lua.CustomSoundManager</a></li>
<li>SetPlayerRaygunPolaritySFX()&#160;:&#160;<a class="el" href="class_lua_1_1_custom_sound_manager.html#aeb74ff0f630bbb90bada33635f382d66">Lua.CustomSoundManager</a></li>
<li>SetPlayerRaygunProjectileFireSFX()&#160;:&#160;<a class="el" href="class_lua_1_1_custom_sound_manager.html#acb7307ffeed459bb90bf63f20f5fa3b9">Lua.CustomSoundManager</a></li>
<li>SetPlayerRaygunProjectileImpactSFX()&#160;:&#160;<a class="el" href="class_lua_1_1_custom_sound_manager.html#a5de014d5743332790a05044b5f918ee6">Lua.CustomSoundManager</a></li>
<li>SetPlayerRaygunSonicFireSFX()&#160;:&#160;<a class="el" href="class_lua_1_1_custom_sound_manager.html#a923ad0297c9ec8e83ec48880b29a7670">Lua.CustomSoundManager</a></li>
<li>SetPlayerRaygunSonicSustainSFX()&#160;:&#160;<a class="el" href="class_lua_1_1_custom_sound_manager.html#a09ff65bfa0a9c0144771c73edcc3643a">Lua.CustomSoundManager</a></li>
<li>SetPlayerRaygunUtilitySFX()&#160;:&#160;<a class="el" href="class_lua_1_1_custom_sound_manager.html#aa79c6dbaef8130988fcdebb2437cb36e">Lua.CustomSoundManager</a></li>
<li>SetPose()&#160;:&#160;<a class="el" href="class_lua_1_1_animation.html#ad3fe96a39d87c9f3b1121fbbd05f61ea">Lua.Animation</a></li>
<li>SetProjectileColor()&#160;:&#160;<a class="el" href="class_lua_1_1_shooting.html#adbbd67fdfd65fd5d754e66c44907c974">Lua.Shooting</a></li>
<li>SetProjectileImpactSFX()&#160;:&#160;<a class="el" href="class_lua_1_1_shooting.html#aa89af0a3474a9f2ac09e3305986df10d">Lua.Shooting</a></li>
<li>SetProjectileScale()&#160;:&#160;<a class="el" href="class_lua_1_1_shooting.html#a376edd82b6c42daab355831d5ecbc340">Lua.Shooting</a></li>
<li>SetProjectileSpeed()&#160;:&#160;<a class="el" href="class_lua_1_1_shooting.html#a916d6868d5ab051898ea2f7d676587aa">Lua.Shooting</a></li>
<li>SetShrinkEnergyColor()&#160;:&#160;<a class="el" href="class_lua_1_1_lua_player_raygun.html#aad74166092a52e766a0296da4fabb98b">Lua.LuaPlayerRaygun</a></li>
<li>SetSpeed()&#160;:&#160;<a class="el" href="class_lua_1_1_animation.html#aefdc9f4c78bada7dacfdb39d07c4b576">Lua.Animation</a></li>
<li>ShowBreastPhysicsOptions()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#ac2bbfd97ccf17d7cd96fb1cf3b3c51e4">Lua.Entity</a></li>
<li>Sign()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#ae5d3654f321079c30baaf42bdf226d89">Lua.Mathf</a></li>
<li>Sin()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a23fb3f1fdbc09b29120c653bb184fb96">Lua.Mathf</a></li>
<li>Slerp()&#160;:&#160;<a class="el" href="class_lua_1_1_quaternion.html#a8848906e3924791706bc0bc853a4572b">Lua.Quaternion</a>, <a class="el" href="class_lua_1_1_vector3.html#a952dff0d8cc76a32589c2020e957adbf">Lua.Vector3</a></li>
<li>SlerpUnclamped()&#160;:&#160;<a class="el" href="class_lua_1_1_quaternion.html#a5056ad858477a4b81765167a068d5379">Lua.Quaternion</a>, <a class="el" href="class_lua_1_1_vector3.html#a951d3fea17d487e6c7a27d842fcaf3f7">Lua.Vector3</a></li>
<li>SmoothStep()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#afee5fd526c03b9d4f8f5e6fc978e04b8">Lua.Mathf</a></li>
<li>SpawnFemaleMicro()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a347a27b3fea83b83461600b3b80ce5d8">Lua.Entity</a></li>
<li>SpawnGiantess()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a204097539b6932308832916892ba77e5">Lua.Entity</a></li>
<li>SpawnMaleMicro()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#ad8f4dd9eec83d4df5d28efa120210ef6">Lua.Entity</a></li>
<li>SpawnObject()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a5bb33d1afcfac1b1452d362cb5d8bb94">Lua.Entity</a></li>
<li>Sqrt()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#aa20bfd858dc30b96f3fb7a5033c4b6bf">Lua.Mathf</a></li>
<li>StandUp()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#af93c20b6cca387d60a5368c079ae65ef">Lua.Entity</a></li>
<li>StartFiring()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a3a5c197d31f8834f8ed67f71c6157719">Lua.Entity</a></li>
<li>Stomp()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a0a8058c8b504215e492471b4e7d557d4">Lua.Entity</a></li>
<li>Stop()&#160;:&#160;<a class="el" href="class_lua_1_1_audio_source.html#a3a622d080321f25beda52619d417dbce">Lua.AudioSource</a></li>
<li>StopAction()&#160;:&#160;<a class="el" href="class_lua_1_1_a_i.html#ab29a427f16c210c0839771b2552b21ce">Lua.AI</a></li>
<li>StopAiming()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#aadc969ba1387cf03d80b8432705f0750">Lua.Entity</a></li>
<li>StopBehavior()&#160;:&#160;<a class="el" href="class_lua_1_1_a_i.html#aa30ed6fc0195cd828d3f5971b80ae053">Lua.AI</a></li>
<li>StopEngaging()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a9ead5c7d5e821fa285ab065b9cc3185f">Lua.Entity</a></li>
<li>StopFiring()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a4d7809fc03b618624a6d7640674fe646">Lua.Entity</a></li>
<li>StopSecondaryBehavior()&#160;:&#160;<a class="el" href="class_lua_1_1_a_i.html#a8c9a883b10e07fe120ff68d75391fe2b">Lua.AI</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

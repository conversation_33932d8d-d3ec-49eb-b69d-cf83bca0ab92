\hypertarget{class_lua_1_1_lua_player_raygun}{}\section{Lua.\+Lua\+Player\+Raygun Class Reference}
\label{class_lua_1_1_lua_player_raygun}\index{Lua.LuaPlayerRaygun@{Lua.LuaPlayerRaygun}}


Use this component to control some elements of the raygun of the player  


\subsection*{Public Member Functions}
\begin{DoxyCompactItemize}
\item 
void \mbox{\hyperlink{class_lua_1_1_lua_player_raygun_a6787608c1207618d069fba8aee7c8fff}{Set\+Grow\+Energy\+Color}} (int r, int g, int b)
\begin{DoxyCompactList}\small\item\em Set the energy color for growing (positive polarity). \end{DoxyCompactList}\item 
void \mbox{\hyperlink{class_lua_1_1_lua_player_raygun_aa7b98cc0da08ae879220e9729a47659e}{Reset\+Grow\+Energy\+Color}} ()
\begin{DoxyCompactList}\small\item\em Reset the energy color for growing (positive polarity) back to the original. \end{DoxyCompactList}\item 
void \mbox{\hyperlink{class_lua_1_1_lua_player_raygun_aad74166092a52e766a0296da4fabb98b}{Set\+Shrink\+Energy\+Color}} (int r, int g, int b)
\begin{DoxyCompactList}\small\item\em Set the energy color for shrinking (negative polarity). \end{DoxyCompactList}\item 
void \mbox{\hyperlink{class_lua_1_1_lua_player_raygun_a80852badcc3f3b0c14e73196b5299c37}{Reset\+Shrink\+Energy\+Color}} ()
\begin{DoxyCompactList}\small\item\em Reset the energy color for shrinking (negative polarity) back to the original. \end{DoxyCompactList}\end{DoxyCompactItemize}
\subsection*{Properties}
\begin{DoxyCompactItemize}
\item 
bool \mbox{\hyperlink{class_lua_1_1_lua_player_raygun_a002f731fc9b1c217a12b03045e87a0cd}{firing\+Enabled}}\hspace{0.3cm}{\ttfamily  \mbox{[}get, set\mbox{]}}
\begin{DoxyCompactList}\small\item\em Whether the player can fire their raygun in Script Mode \end{DoxyCompactList}\end{DoxyCompactItemize}


\subsection{Detailed Description}
Use this component to control some elements of the raygun of the player 



\subsection{Member Function Documentation}
\mbox{\Hypertarget{class_lua_1_1_lua_player_raygun_aa7b98cc0da08ae879220e9729a47659e}\label{class_lua_1_1_lua_player_raygun_aa7b98cc0da08ae879220e9729a47659e}} 
\index{Lua.LuaPlayerRaygun@{Lua.LuaPlayerRaygun}!ResetGrowEnergyColor@{ResetGrowEnergyColor}}
\index{ResetGrowEnergyColor@{ResetGrowEnergyColor}!Lua.LuaPlayerRaygun@{Lua.LuaPlayerRaygun}}
\subsubsection{\texorpdfstring{ResetGrowEnergyColor()}{ResetGrowEnergyColor()}}
{\footnotesize\ttfamily void Lua.\+Lua\+Player\+Raygun.\+Reset\+Grow\+Energy\+Color (\begin{DoxyParamCaption}{ }\end{DoxyParamCaption})}



Reset the energy color for growing (positive polarity) back to the original. 

\mbox{\Hypertarget{class_lua_1_1_lua_player_raygun_a80852badcc3f3b0c14e73196b5299c37}\label{class_lua_1_1_lua_player_raygun_a80852badcc3f3b0c14e73196b5299c37}} 
\index{Lua.LuaPlayerRaygun@{Lua.LuaPlayerRaygun}!ResetShrinkEnergyColor@{ResetShrinkEnergyColor}}
\index{ResetShrinkEnergyColor@{ResetShrinkEnergyColor}!Lua.LuaPlayerRaygun@{Lua.LuaPlayerRaygun}}
\subsubsection{\texorpdfstring{ResetShrinkEnergyColor()}{ResetShrinkEnergyColor()}}
{\footnotesize\ttfamily void Lua.\+Lua\+Player\+Raygun.\+Reset\+Shrink\+Energy\+Color (\begin{DoxyParamCaption}{ }\end{DoxyParamCaption})}



Reset the energy color for shrinking (negative polarity) back to the original. 

\mbox{\Hypertarget{class_lua_1_1_lua_player_raygun_a6787608c1207618d069fba8aee7c8fff}\label{class_lua_1_1_lua_player_raygun_a6787608c1207618d069fba8aee7c8fff}} 
\index{Lua.LuaPlayerRaygun@{Lua.LuaPlayerRaygun}!SetGrowEnergyColor@{SetGrowEnergyColor}}
\index{SetGrowEnergyColor@{SetGrowEnergyColor}!Lua.LuaPlayerRaygun@{Lua.LuaPlayerRaygun}}
\subsubsection{\texorpdfstring{SetGrowEnergyColor()}{SetGrowEnergyColor()}}
{\footnotesize\ttfamily void Lua.\+Lua\+Player\+Raygun.\+Set\+Grow\+Energy\+Color (\begin{DoxyParamCaption}\item[{int}]{r,  }\item[{int}]{g,  }\item[{int}]{b }\end{DoxyParamCaption})}



Set the energy color for growing (positive polarity). 


\begin{DoxyParams}{Parameters}
{\em r} & red color value (0-\/255)\\
\hline
{\em g} & green color value (0-\/255)\\
\hline
{\em b} & blue color value (0-\/255)\\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{class_lua_1_1_lua_player_raygun_aad74166092a52e766a0296da4fabb98b}\label{class_lua_1_1_lua_player_raygun_aad74166092a52e766a0296da4fabb98b}} 
\index{Lua.LuaPlayerRaygun@{Lua.LuaPlayerRaygun}!SetShrinkEnergyColor@{SetShrinkEnergyColor}}
\index{SetShrinkEnergyColor@{SetShrinkEnergyColor}!Lua.LuaPlayerRaygun@{Lua.LuaPlayerRaygun}}
\subsubsection{\texorpdfstring{SetShrinkEnergyColor()}{SetShrinkEnergyColor()}}
{\footnotesize\ttfamily void Lua.\+Lua\+Player\+Raygun.\+Set\+Shrink\+Energy\+Color (\begin{DoxyParamCaption}\item[{int}]{r,  }\item[{int}]{g,  }\item[{int}]{b }\end{DoxyParamCaption})}



Set the energy color for shrinking (negative polarity). 


\begin{DoxyParams}{Parameters}
{\em r} & red color value (0-\/255)\\
\hline
{\em g} & green color value (0-\/255)\\
\hline
{\em b} & blue color value (0-\/255)\\
\hline
\end{DoxyParams}


\subsection{Property Documentation}
\mbox{\Hypertarget{class_lua_1_1_lua_player_raygun_a002f731fc9b1c217a12b03045e87a0cd}\label{class_lua_1_1_lua_player_raygun_a002f731fc9b1c217a12b03045e87a0cd}} 
\index{Lua.LuaPlayerRaygun@{Lua.LuaPlayerRaygun}!firingEnabled@{firingEnabled}}
\index{firingEnabled@{firingEnabled}!Lua.LuaPlayerRaygun@{Lua.LuaPlayerRaygun}}
\subsubsection{\texorpdfstring{firingEnabled}{firingEnabled}}
{\footnotesize\ttfamily bool Lua.\+Lua\+Player\+Raygun.\+firing\+Enabled\hspace{0.3cm}{\ttfamily [get]}, {\ttfamily [set]}}



Whether the player can fire their raygun in Script Mode 



The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
Lua\+Player\+Raygun.\+cs\end{DoxyCompactItemize}

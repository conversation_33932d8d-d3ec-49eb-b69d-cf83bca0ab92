<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Class Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('functions_f.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_f" name="index_f"></a>- f -</h3><ul>
<li>Face()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a442bfc9dcbb33b4fa3922a59357a8723">Lua.Entity</a></li>
<li>fieldOfView&#160;:&#160;<a class="el" href="class_lua_1_1_senses.html#ac4a708caaa7a7381870d4f21d619c2e9">Lua.Senses</a></li>
<li>Find()&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#a79476caab32d323b7fee1955fda8d808">Lua.Transform</a></li>
<li>FindClosestGiantess()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#ac7eac4ebcb1fe784ab5c06eed7885cf7">Lua.Entity</a></li>
<li>FindClosestMicro()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a2e06bf904c49f705ac361e1538365e2e">Lua.Entity</a></li>
<li>FindMorphIndex()&#160;:&#160;<a class="el" href="class_lua_1_1_morphs.html#aef69d4abbbf5f61ff1a2d4fe50737b4b">Lua.Morphs</a></li>
<li>FindRandomBuilding()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a72e0a626a062ba116cf62cfeb77f87f8">Lua.Entity</a></li>
<li>FireOnce()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a17d117771166d30924da118c1fed9968">Lua.Entity</a></li>
<li>firingEnabled&#160;:&#160;<a class="el" href="class_lua_1_1_lua_player_raygun.html#a002f731fc9b1c217a12b03045e87a0cd">Lua.LuaPlayerRaygun</a></li>
<li>firingInterval&#160;:&#160;<a class="el" href="class_lua_1_1_shooting.html#aece1867c834c0c4fc31af53c55c8040e">Lua.Shooting</a></li>
<li>fixedDeltaTime&#160;:&#160;<a class="el" href="class_lua_1_1_time.html#a8b9eb6a7ddf143242c72e6e9378604c6">Lua.Time</a></li>
<li>FixGunAimingOrientation()&#160;:&#160;<a class="el" href="class_lua_1_1_shooting.html#a8bd9055060bba675e803483c5a1f71d2">Lua.Shooting</a></li>
<li>Flee()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#af1290dfaf8e3da8c2adb7279359bf036">Lua.Entity</a></li>
<li>Floor()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a72fc411403ab2b7e87ffd6e3989bc9e4">Lua.Mathf</a></li>
<li>flySpeed&#160;:&#160;<a class="el" href="class_lua_1_1_player.html#aba74b8604fe18ff58fbe7cab856c759b">Lua.Player</a></li>
<li>forward&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#ad07cf6c2802bfbab50272030379f1826">Lua.Transform</a>, <a class="el" href="class_lua_1_1_vector3.html#ad8be15240d9bfa336d926ab023f11ad4">Lua.Vector3</a></li>
<li>frameCount&#160;:&#160;<a class="el" href="class_lua_1_1_time.html#ae821e279218bd2e418f6bafc2e66cc4f">Lua.Time</a></li>
<li>freezeRotation&#160;:&#160;<a class="el" href="class_lua_1_1_rigidbody.html#ad3f2380221d01eedada3df4d282a37d4">Lua.Rigidbody</a></li>
<li>FromToRotation()&#160;:&#160;<a class="el" href="class_lua_1_1_quaternion.html#a78b8152d55e05c4b35a74ed09eae9d41">Lua.Quaternion</a></li>
<li>fullScreen&#160;:&#160;<a class="el" href="class_lua_1_1_screen.html#a7b08588981d36493e586b0d59a1f1b7a">Lua.Screen</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

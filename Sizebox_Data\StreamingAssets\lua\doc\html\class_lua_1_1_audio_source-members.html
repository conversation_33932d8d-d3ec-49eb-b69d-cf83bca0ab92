<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_audio_source.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle"><div class="title">Lua.AudioSource Member List</div></div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="class_lua_1_1_audio_source.html">Lua.AudioSource</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html#a4913b6f1fa8dfe5eb528cd7e40f91684">clip</a></td><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html">Lua.AudioSource</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html#a7d14ab87ad493f85fa9ee1b747bf6df0">GetClipLength</a>(string clip)</td><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html">Lua.AudioSource</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html#aaef47740090cbe41ff5b4f4b40aad7b7">isPlaying</a></td><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html">Lua.AudioSource</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html#a4ffd0dfe8f989efe964e368cc2a5995c">loop</a></td><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html">Lua.AudioSource</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html#a55ad4d09380c973f2001b2164aba4771">maxDistance</a></td><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html">Lua.AudioSource</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html#a66155ad664c665455476ea70f4779669">minDistance</a></td><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html">Lua.AudioSource</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html#ad82dcfe66567f1dac8a15edc327c89ff">mute</a></td><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html">Lua.AudioSource</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html#a9960981498216301c82794fa6a14d7ac">New</a>(Entity entity)</td><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html">Lua.AudioSource</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html#a6b5a139f410004dd985f118c90dd5179">New</a>(Transform transform)</td><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html">Lua.AudioSource</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html#aad8aea6c6f265dfe440a6a8620416bf4">Pause</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html">Lua.AudioSource</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html#a49d0053bb3cfa0b42c70f3b678e0d78f">pitch</a></td><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html">Lua.AudioSource</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html#aef1a519a4611e2aa72570d113d92c904">Play</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html">Lua.AudioSource</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html#a132381c13acd88e5ef3e53e0e8c1ad66">PlayClipAtPoint</a>(string clip, Vector3 position, float volume)</td><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html">Lua.AudioSource</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html#a10ba6ce0794c8050458466082302bc09">PlayDelayed</a>(float delay)</td><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html">Lua.AudioSource</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html#ac8a51a95f2285660337ddf45fd10252e">PlayOneShot</a>(string clip, float volumeScale)</td><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html">Lua.AudioSource</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html#a404e19781f62fa9186ecdcf535e7d4ae">PlayOneShot</a>(string clip)</td><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html">Lua.AudioSource</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html#a88e74fc4c2c4cf17747d6bbdad516a8f">spatialBlend</a></td><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html">Lua.AudioSource</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html#a3a622d080321f25beda52619d417dbce">Stop</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html">Lua.AudioSource</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html#a3bbbde9384f38a43f97e48cd162b0dac">UnPause</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html">Lua.AudioSource</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html#a6e631df6c296491e5a29c8025878ddb4">volume</a></td><td class="entry"><a class="el" href="class_lua_1_1_audio_source.html">Lua.AudioSource</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

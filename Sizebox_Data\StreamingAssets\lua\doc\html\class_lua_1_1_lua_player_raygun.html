<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.LuaPlayerRaygun Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_lua_player_raygun.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="class_lua_1_1_lua_player_raygun-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.LuaPlayerRaygun Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Use this component to control some elements of the raygun of the player  
 <a href="class_lua_1_1_lua_player_raygun.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a6787608c1207618d069fba8aee7c8fff"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_lua_player_raygun.html#a6787608c1207618d069fba8aee7c8fff">SetGrowEnergyColor</a> (int r, int g, int b)</td></tr>
<tr class="memdesc:a6787608c1207618d069fba8aee7c8fff"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the energy color for growing (positive polarity).  <a href="class_lua_1_1_lua_player_raygun.html#a6787608c1207618d069fba8aee7c8fff">More...</a><br /></td></tr>
<tr class="separator:a6787608c1207618d069fba8aee7c8fff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa7b98cc0da08ae879220e9729a47659e"><td class="memItemLeft" align="right" valign="top"><a id="aa7b98cc0da08ae879220e9729a47659e" name="aa7b98cc0da08ae879220e9729a47659e"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>ResetGrowEnergyColor</b> ()</td></tr>
<tr class="memdesc:aa7b98cc0da08ae879220e9729a47659e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reset the energy color for growing (positive polarity) back to the original. <br /></td></tr>
<tr class="separator:aa7b98cc0da08ae879220e9729a47659e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aad74166092a52e766a0296da4fabb98b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_lua_player_raygun.html#aad74166092a52e766a0296da4fabb98b">SetShrinkEnergyColor</a> (int r, int g, int b)</td></tr>
<tr class="memdesc:aad74166092a52e766a0296da4fabb98b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the energy color for shrinking (negative polarity).  <a href="class_lua_1_1_lua_player_raygun.html#aad74166092a52e766a0296da4fabb98b">More...</a><br /></td></tr>
<tr class="separator:aad74166092a52e766a0296da4fabb98b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a80852badcc3f3b0c14e73196b5299c37"><td class="memItemLeft" align="right" valign="top"><a id="a80852badcc3f3b0c14e73196b5299c37" name="a80852badcc3f3b0c14e73196b5299c37"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>ResetShrinkEnergyColor</b> ()</td></tr>
<tr class="memdesc:a80852badcc3f3b0c14e73196b5299c37"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reset the energy color for shrinking (negative polarity) back to the original. <br /></td></tr>
<tr class="separator:a80852badcc3f3b0c14e73196b5299c37"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="properties" name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a002f731fc9b1c217a12b03045e87a0cd"><td class="memItemLeft" align="right" valign="top"><a id="a002f731fc9b1c217a12b03045e87a0cd" name="a002f731fc9b1c217a12b03045e87a0cd"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>firingEnabled</b><code> [get, set]</code></td></tr>
<tr class="memdesc:a002f731fc9b1c217a12b03045e87a0cd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Whether the player can fire their raygun in Script Mode <br /></td></tr>
<tr class="separator:a002f731fc9b1c217a12b03045e87a0cd"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >Use this component to control some elements of the raygun of the player </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a6787608c1207618d069fba8aee7c8fff" name="a6787608c1207618d069fba8aee7c8fff"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6787608c1207618d069fba8aee7c8fff">&#9670;&nbsp;</a></span>SetGrowEnergyColor()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.LuaPlayerRaygun.SetGrowEnergyColor </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>r</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>g</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>b</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set the energy color for growing (positive polarity). </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">r</td><td>red color value (0-255)</td></tr>
    <tr><td class="paramname">g</td><td>green color value (0-255)</td></tr>
    <tr><td class="paramname">b</td><td>blue color value (0-255)</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="aad74166092a52e766a0296da4fabb98b" name="aad74166092a52e766a0296da4fabb98b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aad74166092a52e766a0296da4fabb98b">&#9670;&nbsp;</a></span>SetShrinkEnergyColor()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.LuaPlayerRaygun.SetShrinkEnergyColor </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>r</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>g</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>b</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set the energy color for shrinking (negative polarity). </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">r</td><td>red color value (0-255)</td></tr>
    <tr><td class="paramname">g</td><td>green color value (0-255)</td></tr>
    <tr><td class="paramname">b</td><td>blue color value (0-255)</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaPlayerRaygun.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_lua_player_raygun.html">LuaPlayerRaygun</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

-- Simple test script for burst growth and swap sizes
-- Based on spurts.lua for proper burst mechanics

BurstAndSwapTest = RegisterBehavior("Burst & Swap Test")
BurstAndSwapTest.data = {
    menuEntry = "Test/Burst & Swap Test",
    agent = {
        type = { "giantess" }
    },
    target = {
        type = { "oneself" }
    }
}

function BurstAndSwapTest:Start()
    print("DEBUG: Burst & Swap Test started")

    -- Initialize burst system exactly like spurts
    self.burstSystem = {
        active = false,
        growing = false,
        waiting = false,
        timer = 0,
        baseSpeed = 0.13, -- Like spurts
        dynamicSpeed = 0,
        spurtDuration = 1.3, -- Like spurts
        spurtRespite = 6.0, -- Longer pause between pulses
        smoothifier = 1.0, -- Like spurts
        decelQuicken = 5.0, -- Like spurts
        direction = 1 -- 1 for grow, -1 for shrink
    }

    print("DEBUG: Burst system initialized - active: " .. tostring(self.burstSystem.active))

    -- Size tracking for swap
    self.sizeTracking = {
        giantessSize = self.agent.scale,
        microSize = 1.0
    }

    -- Initialize toasts properly
    self.toast = Game.Toast.New()
    self.testStarted = false
    self.swapTested = false
    self.stopTested = false

    -- Initialize chat tracking
    self.lastProcessedMessage = nil

    -- Show instructions
    self.toast.Print("Burst & Swap Test loaded! Press 1=Burst Grow, 2=Burst Shrink, 3=Swap Sizes, 0=Stop")
    print("DEBUG: Script started - use keyboard shortcuts or chat commands")
    print("DEBUG: Keyboard shortcuts: 1=Burst Grow, 2=Burst Shrink, 3=Swap Sizes, 0=Stop")
end

function BurstAndSwapTest:Update()
    -- Handle burst growth exactly like spurts script
    self:UpdateBurstSystem()

    -- Update size tracking
    self:UpdateSizeTracking()

    -- Handle chat input
    self:HandleChatInput()

    -- Simple test: try swap after 10 seconds if not done yet
    if not self.swapTested and self.agent and self.agent.scale and self.agent.scale > 10 then
        self.swapTested = true
        print("DEBUG: Character grew large enough, testing swap...")
        self:ProcessCommand("swap sizes")
    end
end

function BurstAndSwapTest:UpdateBurstSystem()
    if not self.burstSystem.active then return end
    
    if self.burstSystem.growing then
        -- ACCELERATION PHASE (exactly like spurts ScaleAccelUpdate)
        if self.burstSystem.dynamicSpeed < self.burstSystem.baseSpeed then
            self.burstSystem.dynamicSpeed = self.burstSystem.dynamicSpeed + 
                self.burstSystem.baseSpeed * Time.deltaTime * self.burstSystem.smoothifier
        end
        if self.burstSystem.dynamicSpeed > self.burstSystem.baseSpeed then
            self.burstSystem.dynamicSpeed = self.burstSystem.baseSpeed
        end
        
        -- SPURT TIMER (exactly like spurts SpurtTimer)
        if Time.time >= self.burstSystem.timer + self.burstSystem.spurtDuration then
            self.burstSystem.growing = false
            self.burstSystem.timer = Time.time
            self.burstSystem.waiting = true
            print("DEBUG: Spurt ended, starting respite")
        end
        
    elseif self.burstSystem.waiting then
        -- RESPITE PHASE (exactly like spurts WaitTimer)
        if Time.time >= self.burstSystem.timer + self.burstSystem.spurtRespite then
            self.burstSystem.waiting = false
            self.burstSystem.growing = true
            self.burstSystem.timer = Time.time
            self.burstSystem.dynamicSpeed = 0
            print("DEBUG: Starting next spurt")
        end
        
    else
        -- DECELERATION PHASE (exactly like spurts ScaleDecelUpdate)
        if self.burstSystem.dynamicSpeed > 0 then
            self.burstSystem.dynamicSpeed = self.burstSystem.dynamicSpeed - 
                self.burstSystem.baseSpeed * Time.deltaTime * self.burstSystem.smoothifier * self.burstSystem.decelQuicken
        end
        if self.burstSystem.dynamicSpeed <= 0 then
            self.burstSystem.dynamicSpeed = 0
            self.burstSystem.timer = Time.time
            self.burstSystem.waiting = true
        end
    end
    
    -- SCALE UPDATE (exactly like spurts ScaleUpdate)
    if self.burstSystem.dynamicSpeed > 0 then
        if self.burstSystem.direction > 0 then
            -- Growing
            self.agent.scale = self.agent.scale * (1 + self.burstSystem.dynamicSpeed * Time.deltaTime)
        else
            -- Shrinking
            self.agent.scale = self.agent.scale * (1 - self.burstSystem.dynamicSpeed * Time.deltaTime)
        end
        
        if math.random() < 0.02 then -- 2% chance for debug
            print(string.format("DEBUG: Spurt active - dynamicSpeed: %.4f, size: %.2f", 
                  self.burstSystem.dynamicSpeed, self.agent.scale))
        end
    end
end

function BurstAndSwapTest:UpdateSizeTracking()
    -- Update size tracking
    self.sizeTracking.giantessSize = self.agent.scale
    
    -- Try to find micro character
    local microCharacter = self:GetMicroCharacter()
    if microCharacter then
        self.sizeTracking.microSize = microCharacter.scale
    end
end

function BurstAndSwapTest:GetMicroCharacter()
    -- Try to find micro character using Sizebox API
    local player = Game.GetLocalPlayer()
    if player and player ~= self.agent and player.scale then
        print("DEBUG: Found micro via Game.GetLocalPlayer, scale: " .. player.scale)
        return player
    end

    -- Alternative: try to find any character that isn't this giantess
    -- This is a fallback method
    print("DEBUG: No micro character found")
    return nil
end

function BurstAndSwapTest:HandleChatInput()
    -- Handle keyboard shortcuts for testing
    if Input.GetKeyDown("1") then
        print("DEBUG: Key '1' pressed - starting burst growth")
        self:ProcessCommand("grow bursts")
    elseif Input.GetKeyDown("2") then
        print("DEBUG: Key '2' pressed - starting burst shrink")
        self:ProcessCommand("shrink bursts")
    elseif Input.GetKeyDown("3") then
        print("DEBUG: Key '3' pressed - swapping sizes")
        self:ProcessCommand("swap sizes")
    elseif Input.GetKeyDown("0") then
        print("DEBUG: Key '0' pressed - stopping")
        self:ProcessCommand("stop")
    end

    -- Also try to handle chat input if available
    if Game.Chat and Game.Chat.GetLastMessage then
        local lastMessage = Game.Chat.GetLastMessage()
        if lastMessage and lastMessage ~= self.lastProcessedMessage then
            self.lastProcessedMessage = lastMessage
            print("DEBUG: Chat message received: " .. lastMessage)

            -- Process the command
            local response = self:ProcessCommand(lastMessage)
            if response then
                Game.Toast.New().Print(response)
            end
        end
    end
end

function BurstAndSwapTest:ProcessCommand(input)
    local lowerInput = string.lower(input)
    print("DEBUG: Processing command: '" .. input .. "' (lowercase: '" .. lowerInput .. "')")

    -- Check for burst commands FIRST
    local hasBurst = string.find(lowerInput, "burst")
    local hasGrow = string.find(lowerInput, "grow")
    local hasShrink = string.find(lowerInput, "shrink")

    print("DEBUG: Command analysis - hasBurst: " .. tostring(hasBurst) .. ", hasGrow: " .. tostring(hasGrow) .. ", hasShrink: " .. tostring(hasShrink))
    
    if hasBurst and hasGrow then
        print("DEBUG: *** BURST GROWTH DETECTED ***")
        
        -- Start burst growth
        self.burstSystem.active = true
        self.burstSystem.growing = true
        self.burstSystem.waiting = false
        self.burstSystem.timer = Time.time
        self.burstSystem.dynamicSpeed = 0
        self.burstSystem.direction = 1
        
        self.toast.Print("🔥 BURST GROWTH ENABLED! 🔥")
        return
    end
    
    if hasBurst and hasShrink then
        print("DEBUG: *** BURST SHRINK DETECTED ***")
        
        -- Start burst shrink
        self.burstSystem.active = true
        self.burstSystem.growing = true
        self.burstSystem.waiting = false
        self.burstSystem.timer = Time.time
        self.burstSystem.dynamicSpeed = 0
        self.burstSystem.direction = -1
        
        self.toast.Print("💥 BURST SHRINK ENABLED! 💥")
        return
    end
    
    -- Check for swap command
    local hasSwap = string.find(lowerInput, "swap")
    local hasSize = string.find(lowerInput, "size")
    
    if hasSwap and hasSize then
        print("DEBUG: *** SIZE SWAP DETECTED ***")
        
        local microCharacter = self:GetMicroCharacter()
        if not microCharacter then
            self.toast.Print("❌ No micro character found!")
            return
        end
        
        local giantessSize = self.agent.scale
        local microSize = microCharacter.scale
        
        print(string.format("DEBUG: Swapping - Giantess: %.2f → %.2f, Micro: %.2f → %.2f", 
              giantessSize, microSize, microSize, giantessSize))
        
        -- Instant swap
        self.agent.scale = microSize
        microCharacter.scale = giantessSize
        
        self.toast.Print("✨ SIZES SWAPPED! ✨")
        return
    end
    
    -- Check for stop command
    if string.find(lowerInput, "stop") then
        print("DEBUG: *** STOP DETECTED *** - burstSystem.active: " .. tostring(self.burstSystem.active))

        if self.burstSystem.active then
            self.burstSystem.active = false
            self.burstSystem.growing = false
            self.burstSystem.waiting = false

            self.toast.Print("⏹️ BURST MODE STOPPED")
            print("DEBUG: Burst mode stopped")
        else
            print("DEBUG: Stop command received but burst was not active")
        end
        return
    end

    print("DEBUG: No matching command found for: " .. input)
end

function BurstAndSwapTest:Exit()
    print("DEBUG: Burst & Swap Test ended")
end

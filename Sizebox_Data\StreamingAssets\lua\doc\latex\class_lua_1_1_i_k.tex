\hypertarget{class_lua_1_1_i_k}{}\section{Lua.\+IK Class Reference}
\label{class_lua_1_1_i_k}\index{Lua.IK@{Lua.IK}}


Inverse Kinematics lets you animate individual bones to create procedural animations.  


\subsection*{Properties}
\begin{DoxyCompactItemize}
\item 
bool \mbox{\hyperlink{class_lua_1_1_i_k_a9cbf5715c753f97860bf9d268e76abcc}{enabled}}\hspace{0.3cm}{\ttfamily  \mbox{[}get, set\mbox{]}}
\begin{DoxyCompactList}\small\item\em Enable / Disable the \mbox{\hyperlink{class_lua_1_1_i_k}{IK}} to be used in scripts. \end{DoxyCompactList}\item 
\mbox{\hyperlink{class_lua_1_1_i_k_effector}{I\+K\+Effector}} \mbox{\hyperlink{class_lua_1_1_i_k_a8cea34c6258871a550fc9d56f8facea1}{left\+Foot}}\hspace{0.3cm}{\ttfamily  \mbox{[}get\mbox{]}}
\begin{DoxyCompactList}\small\item\em Left Foot Effector \end{DoxyCompactList}\item 
\mbox{\hyperlink{class_lua_1_1_i_k_effector}{I\+K\+Effector}} \mbox{\hyperlink{class_lua_1_1_i_k_a0868dad53aa0ea22b3554edc766ee8bc}{right\+Foot}}\hspace{0.3cm}{\ttfamily  \mbox{[}get\mbox{]}}
\begin{DoxyCompactList}\small\item\em Right Foot Effector \end{DoxyCompactList}\item 
\mbox{\hyperlink{class_lua_1_1_i_k_effector}{I\+K\+Effector}} \mbox{\hyperlink{class_lua_1_1_i_k_a88ba05bd1557a61e1f69e21fd172641c}{left\+Hand}}\hspace{0.3cm}{\ttfamily  \mbox{[}get\mbox{]}}
\begin{DoxyCompactList}\small\item\em Left Hand Effector \end{DoxyCompactList}\item 
\mbox{\hyperlink{class_lua_1_1_i_k_effector}{I\+K\+Effector}} \mbox{\hyperlink{class_lua_1_1_i_k_a1321851e538f60beb7fa5d655e18ab75}{right\+Hand}}\hspace{0.3cm}{\ttfamily  \mbox{[}get\mbox{]}}
\begin{DoxyCompactList}\small\item\em Right Hand Effector \end{DoxyCompactList}\item 
\mbox{\hyperlink{class_lua_1_1_i_k_effector}{I\+K\+Effector}} \mbox{\hyperlink{class_lua_1_1_i_k_aca9dea4db5e6c66d5d1e904266c626ef}{body}}\hspace{0.3cm}{\ttfamily  \mbox{[}get\mbox{]}}
\begin{DoxyCompactList}\small\item\em Body Effector (hips) \end{DoxyCompactList}\end{DoxyCompactItemize}


\subsection{Detailed Description}
Inverse Kinematics lets you animate individual bones to create procedural animations. 



\subsection{Property Documentation}
\mbox{\Hypertarget{class_lua_1_1_i_k_aca9dea4db5e6c66d5d1e904266c626ef}\label{class_lua_1_1_i_k_aca9dea4db5e6c66d5d1e904266c626ef}} 
\index{Lua.IK@{Lua.IK}!body@{body}}
\index{body@{body}!Lua.IK@{Lua.IK}}
\subsubsection{\texorpdfstring{body}{body}}
{\footnotesize\ttfamily \mbox{\hyperlink{class_lua_1_1_i_k_effector}{I\+K\+Effector}} Lua.\+I\+K.\+body\hspace{0.3cm}{\ttfamily [get]}}



Body Effector (hips) 

\begin{DoxyReturn}{Returns}

\end{DoxyReturn}
\mbox{\Hypertarget{class_lua_1_1_i_k_a9cbf5715c753f97860bf9d268e76abcc}\label{class_lua_1_1_i_k_a9cbf5715c753f97860bf9d268e76abcc}} 
\index{Lua.IK@{Lua.IK}!enabled@{enabled}}
\index{enabled@{enabled}!Lua.IK@{Lua.IK}}
\subsubsection{\texorpdfstring{enabled}{enabled}}
{\footnotesize\ttfamily bool Lua.\+I\+K.\+enabled\hspace{0.3cm}{\ttfamily [get]}, {\ttfamily [set]}}



Enable / Disable the \mbox{\hyperlink{class_lua_1_1_i_k}{IK}} to be used in scripts. 

\begin{DoxyReturn}{Returns}

\end{DoxyReturn}
\mbox{\Hypertarget{class_lua_1_1_i_k_a8cea34c6258871a550fc9d56f8facea1}\label{class_lua_1_1_i_k_a8cea34c6258871a550fc9d56f8facea1}} 
\index{Lua.IK@{Lua.IK}!leftFoot@{leftFoot}}
\index{leftFoot@{leftFoot}!Lua.IK@{Lua.IK}}
\subsubsection{\texorpdfstring{leftFoot}{leftFoot}}
{\footnotesize\ttfamily \mbox{\hyperlink{class_lua_1_1_i_k_effector}{I\+K\+Effector}} Lua.\+I\+K.\+left\+Foot\hspace{0.3cm}{\ttfamily [get]}}



Left Foot Effector 

\begin{DoxyReturn}{Returns}

\end{DoxyReturn}
\mbox{\Hypertarget{class_lua_1_1_i_k_a88ba05bd1557a61e1f69e21fd172641c}\label{class_lua_1_1_i_k_a88ba05bd1557a61e1f69e21fd172641c}} 
\index{Lua.IK@{Lua.IK}!leftHand@{leftHand}}
\index{leftHand@{leftHand}!Lua.IK@{Lua.IK}}
\subsubsection{\texorpdfstring{leftHand}{leftHand}}
{\footnotesize\ttfamily \mbox{\hyperlink{class_lua_1_1_i_k_effector}{I\+K\+Effector}} Lua.\+I\+K.\+left\+Hand\hspace{0.3cm}{\ttfamily [get]}}



Left Hand Effector 

\begin{DoxyReturn}{Returns}

\end{DoxyReturn}
\mbox{\Hypertarget{class_lua_1_1_i_k_a0868dad53aa0ea22b3554edc766ee8bc}\label{class_lua_1_1_i_k_a0868dad53aa0ea22b3554edc766ee8bc}} 
\index{Lua.IK@{Lua.IK}!rightFoot@{rightFoot}}
\index{rightFoot@{rightFoot}!Lua.IK@{Lua.IK}}
\subsubsection{\texorpdfstring{rightFoot}{rightFoot}}
{\footnotesize\ttfamily \mbox{\hyperlink{class_lua_1_1_i_k_effector}{I\+K\+Effector}} Lua.\+I\+K.\+right\+Foot\hspace{0.3cm}{\ttfamily [get]}}



Right Foot Effector 

\begin{DoxyReturn}{Returns}

\end{DoxyReturn}
\mbox{\Hypertarget{class_lua_1_1_i_k_a1321851e538f60beb7fa5d655e18ab75}\label{class_lua_1_1_i_k_a1321851e538f60beb7fa5d655e18ab75}} 
\index{Lua.IK@{Lua.IK}!rightHand@{rightHand}}
\index{rightHand@{rightHand}!Lua.IK@{Lua.IK}}
\subsubsection{\texorpdfstring{rightHand}{rightHand}}
{\footnotesize\ttfamily \mbox{\hyperlink{class_lua_1_1_i_k_effector}{I\+K\+Effector}} Lua.\+I\+K.\+right\+Hand\hspace{0.3cm}{\ttfamily [get]}}



Right Hand Effector 

\begin{DoxyReturn}{Returns}

\end{DoxyReturn}


The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
Lua\+I\+K.\+cs\end{DoxyCompactItemize}

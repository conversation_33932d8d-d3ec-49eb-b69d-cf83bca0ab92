<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.Time Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_time.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#properties">Properties</a> &#124;
<a href="class_lua_1_1_time-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.Time Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>The interface to get time information from Unity.  
 <a href="class_lua_1_1_time.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="properties" name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a0c34615f0ecde357e396cab65ecd4428"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_time.html#a0c34615f0ecde357e396cab65ecd4428">deltaTime</a><code> [get]</code></td></tr>
<tr class="memdesc:a0c34615f0ecde357e396cab65ecd4428"><td class="mdescLeft">&#160;</td><td class="mdescRight">The time in seconds it took to complete the last frame (Read Only).  <a href="class_lua_1_1_time.html#a0c34615f0ecde357e396cab65ecd4428">More...</a><br /></td></tr>
<tr class="separator:a0c34615f0ecde357e396cab65ecd4428"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b9eb6a7ddf143242c72e6e9378604c6"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_time.html#a8b9eb6a7ddf143242c72e6e9378604c6">fixedDeltaTime</a><code> [get]</code></td></tr>
<tr class="memdesc:a8b9eb6a7ddf143242c72e6e9378604c6"><td class="mdescLeft">&#160;</td><td class="mdescRight">The time in seconds it took to complete the last fixed frame (Read Only).  <a href="class_lua_1_1_time.html#a8b9eb6a7ddf143242c72e6e9378604c6">More...</a><br /></td></tr>
<tr class="separator:a8b9eb6a7ddf143242c72e6e9378604c6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae821e279218bd2e418f6bafc2e66cc4f"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_time.html#ae821e279218bd2e418f6bafc2e66cc4f">frameCount</a><code> [get]</code></td></tr>
<tr class="memdesc:ae821e279218bd2e418f6bafc2e66cc4f"><td class="mdescLeft">&#160;</td><td class="mdescRight">The total number of frames that have passed (Read Only).  <a href="class_lua_1_1_time.html#ae821e279218bd2e418f6bafc2e66cc4f">More...</a><br /></td></tr>
<tr class="separator:ae821e279218bd2e418f6bafc2e66cc4f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6a7753473015073c35d5ae5bc4edfdf3"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_time.html#a6a7753473015073c35d5ae5bc4edfdf3">time</a><code> [get]</code></td></tr>
<tr class="memdesc:a6a7753473015073c35d5ae5bc4edfdf3"><td class="mdescLeft">&#160;</td><td class="mdescRight">The time at the beginning of this frame (Read Only). This is the time in seconds since the start of the game.  <a href="class_lua_1_1_time.html#a6a7753473015073c35d5ae5bc4edfdf3">More...</a><br /></td></tr>
<tr class="separator:a6a7753473015073c35d5ae5bc4edfdf3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af1a087ea59af5ee339aa26ae49c13370"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_time.html#af1a087ea59af5ee339aa26ae49c13370">timeSinceLevelLoad</a><code> [get]</code></td></tr>
<tr class="memdesc:af1a087ea59af5ee339aa26ae49c13370"><td class="mdescLeft">&#160;</td><td class="mdescRight">The time this frame has started (Read Only). This is the time in seconds since the last level has been loaded.  <a href="class_lua_1_1_time.html#af1a087ea59af5ee339aa26ae49c13370">More...</a><br /></td></tr>
<tr class="separator:af1a087ea59af5ee339aa26ae49c13370"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >The interface to get time information from Unity. </p>
</div><h2 class="groupheader">Property Documentation</h2>
<a id="a0c34615f0ecde357e396cab65ecd4428" name="a0c34615f0ecde357e396cab65ecd4428"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0c34615f0ecde357e396cab65ecd4428">&#9670;&nbsp;</a></span>deltaTime</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Time.deltaTime</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The time in seconds it took to complete the last frame (Read Only). </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>
<p>Use this function to make your game frame rate independent. If you add or subtract to a value every frame chances are you should multiply with <a class="el" href="class_lua_1_1_time.html#a0c34615f0ecde357e396cab65ecd4428" title="The time in seconds it took to complete the last frame (Read Only).">Time.deltaTime</a>. When you multiply with <a class="el" href="class_lua_1_1_time.html#a0c34615f0ecde357e396cab65ecd4428" title="The time in seconds it took to complete the last frame (Read Only).">Time.deltaTime</a> you essentially express: I want to move this object 10 meters per second instead of 10 meters per frame. </p>

</div>
</div>
<a id="a8b9eb6a7ddf143242c72e6e9378604c6" name="a8b9eb6a7ddf143242c72e6e9378604c6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8b9eb6a7ddf143242c72e6e9378604c6">&#9670;&nbsp;</a></span>fixedDeltaTime</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Time.fixedDeltaTime</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The time in seconds it took to complete the last fixed frame (Read Only). </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ae821e279218bd2e418f6bafc2e66cc4f" name="ae821e279218bd2e418f6bafc2e66cc4f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae821e279218bd2e418f6bafc2e66cc4f">&#9670;&nbsp;</a></span>frameCount</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Time.frameCount</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The total number of frames that have passed (Read Only). </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a6a7753473015073c35d5ae5bc4edfdf3" name="a6a7753473015073c35d5ae5bc4edfdf3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6a7753473015073c35d5ae5bc4edfdf3">&#9670;&nbsp;</a></span>time</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Time.time</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The time at the beginning of this frame (Read Only). This is the time in seconds since the start of the game. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>
<p>Returns the same value if called multiple times in a single frame. When called from inside MonoBehaviour's FixedUpdate, returns fixedTime property. </p>

</div>
</div>
<a id="af1a087ea59af5ee339aa26ae49c13370" name="af1a087ea59af5ee339aa26ae49c13370"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af1a087ea59af5ee339aa26ae49c13370">&#9670;&nbsp;</a></span>timeSinceLevelLoad</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Time.timeSinceLevelLoad</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The time this frame has started (Read Only). This is the time in seconds since the last level has been loaded. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaTime.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_time.html">Time</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

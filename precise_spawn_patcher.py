#!/usr/bin/env python3
"""
Precise Sizebox Spawn Key Patcher
Uses string analysis to find exact spawn key bindings
"""

import os
import shutil
import hashlib
import re

def backup_file(source, backup_dir):
    """Create a backup of the original file"""
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    
    backup_path = os.path.join(backup_dir, os.path.basename(source) + '.precise_backup')
    shutil.copy2(source, backup_path)
    print(f"Backup created: {backup_path}")
    return backup_path

def find_exact_spawn_keys(dll_data):
    """Find exact spawn key patterns using string analysis"""
    
    print("Analyzing DLL for exact spawn key patterns...")
    
    # Convert to string for pattern matching (ignore encoding errors)
    try:
        dll_text = dll_data.decode('utf-8', errors='ignore')
    except:
        dll_text = dll_data.decode('latin-1', errors='ignore')
    
    # Look for specific spawn-related patterns
    spawn_patterns = [
        # Look for Input.GetKeyDown patterns
        r'Input\.GetKeyDown\s*\(\s*KeyCode\.([PO])\s*\)',
        r'GetKeyDown\s*\(\s*KeyCode\.([PO])\s*\)',
        r'KeyCode\.([PO])\s*\)\s*.*spawn',
        r'spawn.*KeyCode\.([PO])',
        # Look for direct key references
        r'case\s+KeyCode\.([PO])\s*:.*spawn',
        r'KeyCode\.([PO]).*Instantiate',
        r'KeyCode\.([PO]).*CreateCharacter',
        r'KeyCode\.([PO]).*SpawnMicro',
    ]
    
    found_patterns = []
    
    for pattern in spawn_patterns:
        matches = re.finditer(pattern, dll_text, re.IGNORECASE)
        for match in matches:
            key = match.group(1)
            start_pos = match.start()
            end_pos = match.end()
            context = dll_text[max(0, start_pos-100):min(len(dll_text), end_pos+100)]
            
            found_patterns.append({
                'key': key,
                'pattern': pattern,
                'position': start_pos,
                'context': context,
                'match_text': match.group(0)
            })
            
            print(f"Found spawn {key} key pattern: {match.group(0)}")
            print(f"  Position: {start_pos}")
            print(f"  Context: ...{context[:50]}...")
            print()
    
    # Now find the actual byte positions of these keys
    keycodes = {
        'P': b'\x50',  # KeyCode.P = 80 (0x50)
        'O': b'\x4F',  # KeyCode.O = 79 (0x4F)
        'F9': b'\x78',  # KeyCode.F9 = 120 (0x78)
        'F10': b'\x79', # KeyCode.F10 = 121 (0x79)
    }
    
    # For each pattern found, look for nearby keycode bytes
    precise_targets = []
    
    for pattern_info in found_patterns:
        key = pattern_info['key']
        text_pos = pattern_info['position']
        
        # Convert text position to approximate byte position
        # This is rough but should get us in the right area
        byte_pos_approx = text_pos
        
        # Search for the keycode byte in a range around this position
        search_start = max(0, byte_pos_approx - 500)
        search_end = min(len(dll_data), byte_pos_approx + 500)
        
        keycode_byte = keycodes[key]
        
        # Find all occurrences of this keycode in the search range
        pos = search_start
        while pos < search_end:
            found_pos = dll_data.find(keycode_byte, pos, search_end)
            if found_pos == -1:
                break
            
            # Check if this looks like a real keycode reference
            # (not just random data that happens to be 0x50 or 0x4F)
            context_bytes = dll_data[max(0, found_pos-20):found_pos+20]
            
            # Look for patterns that suggest this is a keycode
            if (b'\x00' in context_bytes or  # Null bytes suggest structured data
                len(set(context_bytes)) > 10):  # Varied bytes suggest code, not random data
                
                precise_targets.append({
                    'key': key,
                    'byte_position': found_pos,
                    'pattern_match': pattern_info['match_text'],
                    'confidence': 'HIGH'
                })
                
                print(f"HIGH CONFIDENCE: {key} key at byte position 0x{found_pos:08X}")
                print(f"  Related to pattern: {pattern_info['match_text']}")
                print(f"  Context bytes: {context_bytes.hex()}")
                print()
            
            pos = found_pos + 1
    
    return precise_targets, keycodes

def patch_precise_spawn_keys():
    """Main precise patching function"""
    dll_path = r"Sizebox_Data\Managed\Assembly-CSharp.dll"
    backup_dir = r"Sizebox_Backup_Precise"
    
    print("Precise Sizebox Spawn Key Patcher")
    print("=================================")
    print("This will find and patch ONLY the exact spawn key bindings")
    print()
    
    # Verify file exists
    if not os.path.exists(dll_path):
        print(f"ERROR: {dll_path} not found!")
        return False
    
    # Create backup
    backup_path = backup_file(dll_path, backup_dir)
    
    # Load the DLL
    with open(dll_path, 'rb') as f:
        dll_data = bytearray(f.read())
    
    print(f"Loaded DLL: {len(dll_data)} bytes")
    
    # Find precise spawn patterns
    precise_targets, keycodes = find_exact_spawn_keys(dll_data)
    
    if not precise_targets:
        print("No precise spawn key bindings found!")
        print("The spawn keys might be implemented differently.")
        return False
    
    print(f"\nFound {len(precise_targets)} precise spawn key targets")
    
    # Show what we found
    p_targets = [t for t in precise_targets if t['key'] == 'P']
    o_targets = [t for t in precise_targets if t['key'] == 'O']
    
    print(f"• {len(p_targets)} precise P key spawn bindings -> F9")
    print(f"• {len(o_targets)} precise O key spawn bindings -> F10")
    print(f"• Total: {len(precise_targets)} precise patches")
    
    if len(precise_targets) > 20:
        print("WARNING: Still found many references. Might not be precise enough.")
        proceed = input("Continue anyway? (y/N): ").lower().strip()
        if proceed != 'y':
            print("Aborted for safety.")
            return False
    
    # Apply precise patches
    patches_applied = 0
    
    for target in precise_targets:
        pos = target['byte_position']
        key = target['key']
        
        if key == 'P':
            dll_data[pos] = keycodes['F9'][0]  # P -> F9
            patches_applied += 1
            print(f"Patched precise P -> F9 at offset: 0x{pos:08X}")
            print(f"  Pattern: {target['pattern_match']}")
        elif key == 'O':
            dll_data[pos] = keycodes['F10'][0]  # O -> F10
            patches_applied += 1
            print(f"Patched precise O -> F10 at offset: 0x{pos:08X}")
            print(f"  Pattern: {target['pattern_match']}")
    
    if patches_applied == 0:
        print("No patches applied!")
        return False
    
    # Write the patched DLL
    patched_path = dll_path + ".precise_patched"
    with open(patched_path, 'wb') as f:
        f.write(dll_data)
    
    print(f"\nPrecise patch ready!")
    print(f"Applied {patches_applied} precise patches")
    
    apply = input("\nApply precise patch to game? (y/N): ").lower().strip()
    if apply == 'y':
        # Backup original and apply patch
        if not os.path.exists(dll_path + ".original"):
            shutil.copy2(dll_path, dll_path + ".original")
        shutil.copy2(patched_path, dll_path)
        print("✓ Precise patch applied!")
        print("✓ Spawn keys changed: P -> F9, O -> F10 (precise only)")
        print("✓ Restart Sizebox to test!")
        return True
    else:
        print("Patch not applied.")
        return True

def restore_original():
    """Restore the original DLL"""
    dll_path = r"Sizebox_Data\Managed\Assembly-CSharp.dll"
    original_path = dll_path + ".original"
    
    if os.path.exists(original_path):
        shutil.copy2(original_path, dll_path)
        print("✓ Original DLL restored")
        return True
    else:
        print("✗ Original backup not found")
        return False

if __name__ == "__main__":
    try:
        print("Precise Sizebox Spawn Key Patcher")
        print("=================================")
        print("1. Apply precise spawn key patch (P->F9, O->F10)")
        print("2. Restore original keys")
        print()
        
        choice = input("Enter choice (1 or 2): ").strip()
        
        if choice == "1":
            success = patch_precise_spawn_keys()
        elif choice == "2":
            success = restore_original()
        else:
            print("Invalid choice!")
            success = False
            
        if success:
            print("\nOperation completed!")
        else:
            print("\nOperation failed!")
            
    except Exception as e:
        print(f"Error: {e}")
    
    input("Press Enter to exit...")

-- Simple Grab & Release Behavior for Sizebox
-- Press H to grab closest micro, K to release

SimpleGrabRelease = RegisterBehavior("SimpleGrabRelease")

SimpleGrabRelease.data = {
    menuEntry = "Simple Grab & Release",
    agent = {
        type = { "giantess" }
    },
    target = {
        type = { "micro" }
    },
    secondary = true,
    flags = { "grab" }
}

local pickupAnimations = { "Crouch Idle", "Dig and Plant Seeds", "Lifting" }
local pickupGrabMoments = {
    ["Crouch Idle"] = 1.0,
    ["Dig and Plant Seeds"] = 1.7,
    ["Lifting"] = 1.0
}
local pickupAnimDurations = {
    ["Crouch Idle"] = 1.5,
    ["Dig and Plant Seeds"] = 3.5,
    ["Lifting"] = 2.7
}
local pickupAnimIndex = 1

function SimpleGrabRelease:Start()
    -- Initialize all variables
    self.micro = nil
    self.isGrabbed = false
    self.debugMode = true
    self.isGrabbing = false
    self.grabTimer = 0
    self.grabPhase = "none"
    self.animationTimer = 0
    self.forceIdleTimer = 0
    self.hasGrabbed = false  -- New flag from official script logic
    
    -- Try to find closest micro
    self.micro = self:FindClosestMicro()
    
    -- Startup message
    if Log then
        Log("=== Simple Grab & Release Started ===")
        Log("Controls: H=Grab with crouch animation, K=Release, G=Debug")
    end
end

-- Safe property access function
function SimpleGrabRelease:SafeGetProperty(obj, property)
    if not obj then
        return nil
    end
    
    local success, result = pcall(function()
        return obj[property]
    end)
    
    return success and result or nil
end

-- Find closest micro function
function SimpleGrabRelease:FindClosestMicro()
    if not self.agent then
        return nil
    end

    local micro = nil

    -- Method 1: Use agent's built-in finder (like official script)
    pcall(function()
        if self.agent.findClosestMicro then
            local found = self.agent.findClosestMicro()
            -- Only use if not parented
            if found and found.transform and not found.transform.parent then
                micro = found
            end
        end
    end)

    -- Method 2: Use senses if available
    if not micro then
        pcall(function()
            if self.agent.senses and self.agent.senses.GetMicrosInRadius then
                local micros = self.agent.senses.GetMicrosInRadius(15)
                if micros and #micros > 0 then
                    for _, m in ipairs(micros) do
                        if m and m.transform and not m.transform.parent then
                            micro = m
                            break
                        end
                    end
                end
            end
        end)
    end

    -- Method 3: Search all entities
    if not micro then
        pcall(function()
            local allEntities = Game.GetAllEntities()
            if allEntities then
                for _, entity in ipairs(allEntities) do
                    if entity and entity.type == "micro" and entity.transform and not entity.transform.parent then
                        micro = entity
                        break
                    end
                end
            end
        end)
    end

    return micro
end

-- Start grab sequence with crouch idle animation
function SimpleGrabRelease:StartGrabSequence()
    if not self.micro or self.isGrabbed or self.isGrabbing then
        if Log then Log("Cannot start grab") end
        return false
    end

    -- Use forced animation if set, otherwise random
    local anim = self.forcedPickupAnim or pickupAnimations[math.random(#pickupAnimations)]
    self.forcedPickupAnim = nil -- Reset after use

    self.currentPickupAnim = anim
    self.currentPickupAnimDuration = pickupAnimDurations[anim] or 1.5
    self.currentPickupGrabMoment = pickupGrabMoments[anim] or 1.0
    self.hasGrabbedDuringAnim = false

    if Log then Log("Starting pickup animation: " .. tostring(anim)) end

    -- STOP AI to prevent animation override
    if self.agent.ai then
        self.agent.ai.StopAction()
        if self.agent.ai.StopBehavior then
            self.agent.ai.StopBehavior()
        end
    end

    -- Play the selected animation
    if self.agent.animation and self.agent.animation.Set then
        self.agent.animation.Set(anim, true)
    end

    -- Set grab state
    self.isGrabbing = true
    self.grabPhase = "crouching"
    self.grabTimer = 0
    self.animationTimer = 0
    self.forceIdleTimer = 0
    self.hasGrabbed = false

    return true
end

-- Actually grab the micro using official method but with better control
function SimpleGrabRelease:GrabMicro()
    if not self.micro or self.hasGrabbed then
        if Log then
            Log("No micro to grab or already grabbed!")
        end
        return false
    end
    
    if Log then
        Log("Grabbing micro using official method")
    end
    
    local success = false
    pcall(function()
        if not self.agent.ai.IsActionActive() then
            self.agent.lookAt(self.micro)
            if self.agent.animation and self.agent.animation.Set then
                self.agent.animation.Set("Lifting", true)
            end
            if self.agent.grab and self.micro then
                self.agent.grab(self.micro)
                if Log then
                    Log("Used official Sizebox grab function")
                end
            else
                -- Fallback to manual method
                if Log then
                    Log("Fallback to manual grab method")
                end
                
                local transform = self:SafeGetProperty(self.micro, "transform")
                if not transform then
                    return false
                end
                
                -- Find grab location
                local grabParent = nil
                local grabOffset = Vector3.new(0, 0, 0)
                
                if self.agent.bones and self.agent.bones.rightHand then
                    grabParent = self.agent.bones.rightHand
                    grabOffset = Vector3.new(0, 0, 0)
                elseif self.agent.bones and self.agent.bones.chest then
                    grabParent = self.agent.bones.chest
                    grabOffset = Vector3.new(0, 0.5, 0.3)
                elseif self.agent.transform then
                    grabParent = self.agent.transform
                    grabOffset = Vector3.new(0, 1.5, 0)
                end
                
                if grabParent then
                    transform:SetParent(grabParent)
                    transform.localPosition = grabOffset
                    transform.localRotation = Quaternion.identity
                    
                    -- Disable physics
                    local rigidbody = self:SafeGetProperty(self.micro, "rigidbody")
                    if rigidbody then
                        rigidbody.isKinematic = true
                        rigidbody.useGravity = false
                        rigidbody.detectCollisions = false
                    end
                    
                    -- Disable collider
                    local collider = self:SafeGetProperty(self.micro, "collider")
                    if collider then
                        collider.enabled = false
                    end
                    
                    -- Disable movement
                    local movement = self:SafeGetProperty(self.micro, "movement")
                    if movement then
                        movement.enabled = false
                    end
                end
            end
            
            -- Mark as grabbed
            self.isGrabbed = true
            self.isGrabbing = false
            self.hasGrabbed = true
            self.grabPhase = "complete"
            self.justGrabbed = false -- Indicate that we just grabbed an object
            
            if Log then
                Log("GRABBED: Using official method - should go to idle naturally")
            end
            success = true
        else
            if Log then
                Log("AI action still active, waiting...")
            end
            success = false
        end
    end)
    return success
end

-- Emergency release function (the working one)
function SimpleGrabRelease:EmergencyRelease()
    if not self.micro then
        if Log then Log("No micro to release") end
        return false
    end

    pcall(function()
        local transform = self:SafeGetProperty(self.micro, "transform")
        if transform then
            -- Detach from parent
            transform:SetParent(nil)
            if transform.parent then
                transform.parent = nil
            end

            -- Restore position above ground
            local pos = self:SafeGetProperty(transform, "position")
            if pos then
                local safeY = math.max(pos.y, 0.2)
                transform.position = Vector3.new(pos.x, safeY, pos.z)
            end

            -- Restore rigidbody
            local rigidbody = self:SafeGetProperty(self.micro, "rigidbody")
            if rigidbody then
                rigidbody.isKinematic = false
                rigidbody.useGravity = true
                rigidbody.detectCollisions = true
                rigidbody.constraints = RigidbodyConstraints.None
                rigidbody.velocity = Vector3.zero
            end

            -- Restore movement
            local movement = self:SafeGetProperty(self.micro, "movement")
            if movement then
                movement.enabled = true
            end

            -- Restore collider
            local collider = self:SafeGetProperty(self.micro, "collider")
            if collider then
                collider.enabled = true
                collider.isTrigger = false
            end
        end
    end)

    -- Reset all states
    self.isGrabbed = false
    self.isGrabbing = false
    self.hasGrabbed = false
    self.grabPhase = "none"
    self.forceIdleTimer = 0

    -- Return to idle
    if self.agent.animation and self.agent.animation.Set then
        self.agent.animation.Set("Idle", true)
    end

    if Log then Log("Reset complete - ready for new grab") end

    -- Clear micro reference so FindClosestMicro works again
    if self.micro then
        local t = self:SafeGetProperty(self.micro, "transform")
        if t and Log then Log("After release, micro parent is: " .. tostring(t.parent)) end
    end
    self.micro = nil

    return true
end

-- Main update loop - Hybrid approach
function SimpleGrabRelease:Update()
    if not self.agent or not Input then
        return
    end

    -- Find micro if needed
    if not self.isGrabbed and not self.micro and not self.isGrabbing then
        self.micro = self:FindClosestMicro()
        if Log then Log("FindClosestMicro result: " .. tostring(self.micro)) end
    end

    -- Animation/Grab logic for random animation
    if self.isGrabbing and self.grabPhase == "crouching" and self.currentPickupAnim then
        self.animationTimer = self.animationTimer + Time.deltaTime
        self.grabTimer = self.grabTimer + Time.deltaTime

        -- Keep animation active
        if self.animationTimer >= 0.2 then
            self.animationTimer = 0
            if self.agent.animation and self.agent.animation.Set then
                self.agent.animation.Set(self.currentPickupAnim, true)
            end
        end

        local grabMoment = pickupGrabMoments[self.currentPickupAnim] or 1.0
        local animEnd = pickupAnimDurations[self.currentPickupAnim] or 1.5

        -- Grab at the right moment
        if not self.hasGrabbedDuringAnim and self.grabTimer >= grabMoment then
            if self.agent.grab and self.micro then
                self.agent.grab(self.micro)
                self.isGrabbed = true
                self.hasGrabbedDuringAnim = true
                if Log then Log("Grabbed micro during animation!") end
            end
        end

        -- End animation and return to idle
        if self.grabTimer >= animEnd then
            self.isGrabbing = false
            self.grabPhase = "none"
            self.hasGrabbed = true
            if self.agent.animation and self.agent.animation.Set then
                self.agent.animation.Set("Idle", true)
            end
            self.animationTimer = 0
            self.grabTimer = 0
            self.hasGrabbedDuringAnim = false
            self.currentPickupAnim = nil
        end
    end

    -- Input handling
    if Input.GetKeyDown("h") then
        if not self.isGrabbed and not self.isGrabbing then
            if not self.micro then
                self.micro = self:FindClosestMicro()
                if Log then Log("Manual FindClosestMicro on H: " .. tostring(self.micro)) end
            end
            if self.micro then
                self:StartGrabSequence()
            else
                if Log then Log("No micro found to grab!") end
            end
        else
            if Log then
                if self.isGrabbed then
                    Log("Already holding a micro!")
                elseif self.isGrabbing then
                    Log("Already in grab sequence! Phase: " .. self.grabPhase)
                end
            end
        end
    end
    
    -- K key - Emergency release (the working one)
    if Input.GetKeyDown("k") then
        if Log then
            Log("K pressed - releasing")
        end
        self:EmergencyRelease()
    end
    
    -- G key - Debug info
    if Input.GetKeyDown("g") then
        if Log then
            Log("=== DEBUG INFO ===")
            Log("isGrabbed: " .. tostring(self.isGrabbed))
            Log("isGrabbing: " .. tostring(self.isGrabbing))
            Log("hasGrabbed: " .. tostring(self.hasGrabbed))
            Log("grabPhase: " .. tostring(self.grabPhase))
            Log("grabTimer: " .. tostring(self.grabTimer))
            Log("micro exists: " .. tostring(self.micro ~= nil))
            
            -- Check AI state
            if self.agent.ai then
                Log("AI action active: " .. tostring(self.agent.ai.IsActionActive()))
            end
            
            Log("=== END DEBUG ===")
        end
    end
    
    -- R key - Reset everything (emergency)
    if Input.GetKeyDown("r") then
        if Log then
            Log("R pressed - FULL RESET")
        end
        
        self.isGrabbed = false
        self.isGrabbing = false
        self.hasGrabbed = false
        self.grabPhase = "none"
        self.grabTimer = 0
        self.micro = nil
        
        if self.agent.animation and self.agent.animation.Set then
            self.agent.animation.Set("Idle", true)
        end
        
        if Log then
            Log("Full reset complete")
        end
    end

    -- Y key: Force next grab to use "Crouch Idle"
    if Input.GetKeyDown("y") then
        self.forcedPickupAnim = "Crouch Idle"
        if Log then Log("Next grab will use: Crouch Idle") end
    end

    -- U key: Force next grab to use "Dig and Plant Seeds"
    if Input.GetKeyDown("u") then
        self.forcedPickupAnim = "Dig and Plant Seeds"
        if Log then Log("Next grab will use: Dig and Plant Seeds") end
    end

    -- I key: Force next grab to use "Lifting"
    if Input.GetKeyDown("i") then
        self.forcedPickupAnim = "Lifting"
        if Log then Log("Next grab will use: Lifting") end
    end

    -- 1 key: Play "Texting" animation while holding a micro
    if Input.GetKeyDown("1") then
        if self.isGrabbed and self.agent.animation and self.agent.animation.Set then
            self.agent.animation.Set("Texting", true)
            if Log then Log("Playing 'Texting' animation while holding micro") end
        end
    end

    -- 2 key: Play "Thinking 2" animation while holding a micro
    if Input.GetKeyDown("2") then
        if self.isGrabbed and self.agent.animation and self.agent.animation.Set then
            self.agent.animation.Set("Thinking 2", true)
            if Log then Log("Playing 'Thinking 2' animation while holding micro") end
        end
    end

    -- Instantly return to idle after a successful grab, just like grab.lua
    if self.isGrabbed and self.micro and self.agent.animation and self.agent.animation.Get then
        local currentAnim = self.agent.animation.Get()
        -- Check if we're still crouching and actually holding the micro
        local transform = self:SafeGetProperty(self.micro, "transform")
        if (currentAnim == "Crouch Idle" or currentAnim == "Crouch") and transform and transform.IsChildOf and self.agent.transform and transform:IsChildOf(self.agent.transform) then
            self.agent.ai.StopAction() -- Stop any ongoing action to prevent animation override
            self.agent.animation.Set("Idle 4", true) -- or "Idle 4" if you want to match grab.lua
            if Log then Log("Switched to Idle after grab (auto-fix)") end
        end
    end

    if self.justGrabbed and self.isGrabbed and self.micro and self.agent.animation and self.agent.animation.Get then
        local currentAnim = self.agent.animation.Get()
        local transform = self:SafeGetProperty(self.micro, "transform")
        if (currentAnim == "Crouch Idle" or currentAnim == "Crouch") and transform and transform.IsChildOf and self.agent.transform and transform:IsChildOf(self.agent.transform) then
            self.agent.ai.StopAction()
            self.agent.animation.Set("Idle 4", true)
            if Log then Log("Switched to Idle after grab (auto-fix)") end
            self.justGrabbed = false -- Only do this once per grab
        end
    end
end

-- Clean exit function
function SimpleGrabRelease:Exit()
    if self.isGrabbed and self.micro then
        self:EmergencyRelease()
    elseif self.isGrabbing then
        self.isGrabbing = false
        self.grabPhase = "none"
        if self.agent.animation and self.agent.animation.Set then
            self.agent.animation.Set("Idle", true)
        end
    end
    
    -- Clean up all states
    self.micro = nil
    self.isGrabbed = false
    self.isGrabbing = false
    self.hasGrabbed = false
    self.grabPhase = "none"
    self.grabTimer = 0
    self.animationTimer = 0
    self.forceIdleTimer = 0
    
    if Log then
        Log("Simple Grab & Release behavior ended - all states cleared")
    end
end

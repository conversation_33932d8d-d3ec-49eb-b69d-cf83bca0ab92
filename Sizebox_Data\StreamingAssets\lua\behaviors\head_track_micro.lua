-- Head Track Micro - Makes 47.JOINT_HEAD look at micro
-- Simple head tracking behavior for giantess

local HeadTrackMicro = RegisterBehavior("Head Track Micro")
HeadTrackMicro.data = {
    menuEntry = "AI/Head Track Micro",
    agent = { type = { "giantess" } },
    target = { type = { "micro" } },
    settings = {
        {"trackingSpeed", "Tracking Speed", "float", 2.0, 0.1, 10.0},
        {"enabled", "Enable Tracking", "bool", true}
    },
    secondary = true
}

function HeadTrackMicro:Start()
    self.trackingSpeed = self.trackingSpeed or 2.0
    self.enabled = self.enabled or true
    self.headJointName = "47.JOINT_HEAD"
    
    print("Head Track Micro: Started tracking")
    Game.Toast.New().Print("Head tracking enabled")
end

function HeadTrackMicro:Update()
    if not self.enabled then return end
    
    local giantess = self.agent
    local micro = self.target
    
    if not giantess or not micro then return end
    
    -- Get head bone
    local headBone = giantess:GetBone(self.headJointName)
    if not headBone then return end
    
    -- Get positions
    local headPos = headBone.transform.position
    local microPos = micro.transform.position
    
    -- Calculate direction
    local direction = (microPos - headPos).normalized
    
    -- Create look rotation
    local lookRotation = Quaternion.LookRotation(direction)
    
    -- Apply smooth rotation
    headBone.transform.rotation = Quaternion.Slerp(
        headBone.transform.rotation,
        lookRotation,
        Time.deltaTime * self.trackingSpeed
    )
end

function HeadTrackMicro:Stop()
    print("Head Track Micro: Stopped")
    Game.Toast.New().Print("Head tracking disabled")
end

return HeadTrackMicro
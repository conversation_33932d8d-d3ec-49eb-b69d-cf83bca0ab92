--v1.4 28/08/21
Walk = RegisterBehavior("Walk Here 2.0")
Walk.data = {
	menuEntry = "Walk/Walk There (simple)",
	agent = {
		type = { "humanoid"}, 
		exclude = {"player"}
	},
	target = {
		type = {"none"}
	}
}

function Walk:Start()
	idles = {
	"Neutral Idle", "Neutral Idle", "Neutral Idle", "Neutral Idle", "Neutral Idle", "Idle", "Idle 4",
	"Breathing Idle", "Yawn", "Happy", "Wait Strech Arms", "Wait Torso Twist", "Bashful", "Bored"
	}
	self.idleAnimation = idles[math.random(#idles)]
	destination = self.cursorPoint
end

function Walk:Update()
	
	if self.agent.DistanceTo(destination) > self.agent.scale * 0.4 then --Sets small radius to ensure stopping at destination
		if not self.agent.ai.IsActionActive() then
			--self.agent.movement.speed = globals["walkspeed"]
			self.agent.animation.Set(globals["walk"])
			self.agent.MoveTo(destination) -- MoveTo uses the pathfinding system
		end
	else
		self.agent.ai.StopBehavior()
	end

	--WALK ANIM SWITCHER
	if Input.GetKeyDown("right alt") or Input.GetKeyDown("right ctrl") or Input.GetKeyDown("left") then
		self.agent.ai.StopAction()
	end
end

function Walk:Exit()
	self.agent.animation.Set(self.idleAnimation)
end

Walk2 = RegisterBehavior("Walk There")
Walk2.scores = {
	normal = 40
}
Walk2.data = {
	menuEntry = "Walk/Walk There",
	ai = true,
	agent = {
		type = { "humanoid" }
	},
	target = {
		type = { "none" }
	},
	settings = {
        --{"activateOnGTS", "Enable on GTS AI", "bool", true},
        --{"activateOnMicros", "Enable on Micros AI", "bool", true},
        {"walkAnimation", "Movement Animation", "string", "Walk 2"},
        {"idleAnimation", "Idle Animation", "string", "Idle 4"},
        {"wildRandomIdle", "Expressive Anims", "bool", false},
        {"randomIdleNoAI", "Random Idle No AI", "bool", false},
        {"lookDownAnim", "Look Down animation", "string", "random"},
        {"enableAnimMad", "Stomp after hostile anim", "bool", true},
		{"enableDialogue", "Show Toast Dialogue", "bool", true}
    }
}

--walkAnimation = "Walk"
--idleAnimation = "Idle"
AIAnimations = {
	"Idle 2", "Idle 5", "Jump", "Jump 3", "Jump 4", "Jump Low", "Look Down", "No", "Pick Up", "Plotting", "Pointing", "Wait Strech Arms",
	"Whatever Gesture", "Refuse", "Crazy Gesture", "Dismissing Gesture", "Dismissing Gesture 2", "Embar", "Embar 2", "Look Away Gesture",
	"Waving", "Waving 2", "Roar", "Pointing Forward", "Scratch Head", "Breathing Idle", "Greet", "Greet 2", "Greet 5", "Wait Torso Twist",
	"Surprised", "Taunt 3", "Thinking 2", "Victory", "Wait Gesture", "Happy", "Gathering Objects", "Bashful", "Angry"
}
AIAnimationsTame = {
	"Idle 2", "Look Down", "Embar", "Embar 2", "Scratch Head", "Scratch Head", "Scratch Head", "Breathing Idle", "Neutral Idle", "Idle 4",
	"Thinking 2", "Look Away Gesture", "Wait Strech Arms"
}
madAnim = {
	"Angry", "Jump", "Jump 3", "Jump 4", "Plotting", "Happy", "Surprised", "Roar", "Refuse", "Whatever Gesture", "Pointing Forward"
}

function isAnimMad(anim)
	for k,v in pairs(madAnim) do
		if v == anim then return true end
	end
end

function SetIdleAnim(ref, firstAnim)
	local idleAnim = ref.idleAnimation
	if ref.randomIdleNoAI then
		local idles = AIAnimationsTame
		if firstAnim and ref.wildRandomIdle then idles = AIAnimations end
		idleAnim = idles[math.random(#idles)]
	end
	ref.agent.animation.Set(idleAnim)
	--log(idleAnim)
end

function Walk2:Start()
	--[[if self.agent.ai.IsAIEnabled() and ((self.agent.isGiantess() and not self.activateOnGTS) or (self.agent.isMicro() and not self.activateOnMicros)) then --If not enabled on GTS or Micro and entity fits, skip behavior.
        self.agent.ai.StopBehavior()
        return
    end]]
    self.stop = false
    self.destination = self.cursorPoint
	
	if not self.agent.dict.wanderCounter then
		self.agent.dict.wanderCounter = 0
	end	

	--If not GTS with AI
	if not self.agent.ai.IsAIEnabled() or self.agent.isMicro() then --If used in AI, walk to random micro instead, and look down.
		if self.target and self.target.isHumanoid() and self.target.scale < self.agent.scale * 3 then
			self.agent.LookAt(self.target)
			self.destination = self.target
		end
		self.agent.animation.Set(self.walkAnimation)
		self.agent.MoveTo(self.destination)
		SetIdleAnim(self, true)
		if not self.agent.isMicro() then self.loopIdles = true else self.stop = true end
		--log("walked here, not giantess + ai")

	--If GTS with AI
	else
		--log("giantess ai walk here")
		self.arrived = false
		self.target = self.agent.findClosestMicro()
		local microList = self.agent.senses.GetMicrosInRadius(100)
		if self.lookDownAnim ~= "random" then
			local animList = self.agent.animation.GetAnimationList()
			for k,v in pairs(animList) do if v == self.lookDownAnim then animExists = true end end
			if not animExists then log("Walk Here: Chosen animation in behavior manager does not exist.") self.lookDownAnim = "random" end
		end
		if not self.target or not self.target.IsTargettable() then
			--log("giantess ai no target")
			self.stop = true
			return
		elseif self.agent.dict.wanderCounter <= 3 and self.target == self.agent.dict.previousMicro and #microList > 1 then
			local time = math.random(4, 8)
			self.agent.animation.Set(self.walkAnimation)
			self.agent.wander(time)
			self.agent.dict.wanderCounter = self.agent.dict.wanderCounter + 1
			self.stop = true
			--log("giantess ai same target (wandering)")
		else
			self.destination = self.target
			self.scaleRatio = self.agent.scale / self.target.scale --Prep for 'If size diff is too small'
			self.lastScale = self.agent.scale
			self.agent.LookAt(self.target)
			self.agent.animation.Set(self.walkAnimation)
			self.agent.MoveTo(self.target)
			self.agent.dict.wanderCounter = 0
			--log("giantess ai new target")
		end
	end

	--If AI, prep for stuck check
	if self.agent.ai.IsAIEnabled() then
		--if self.agent.isMicro() then self.destination = self.cursorPoint else self.destination =  self.target end
		self.lastPosition = self.agent.transform.position
		self.lastDistance = self.agent.DistanceTo(self.destination)
		self.timer = Time.time
		self.timerScaleAdd = Mathf.Clamp(4 + self.agent.scale / 20, 4, 10) --Used to adapt to GTS speed being slowed down by larger sizes. Defaults to "4", goes up to "10" as GTS rises in size (maxes out at "scale = 120")
	end

	--Only do randomseed once per scene
	if not globals["walkRand"] then math.randomseed(tonumber(os.time()) + self.agent.id) globals["walkRand"] = true end
end

function Walk2:Update()
	
	if self.loopIdles then
		if self.agent.animation.IsCompleted() and not self.agent.ai.IsActionActive() then
			SetIdleAnim(self)
		end
		return
	end

	if (self.stop and self.agent.isMicro() and not self.agent.ai.IsActionActive()) or (self.lastScale and self.target.IsDead()) then
		--log("stopping walk")
		self.agent.ai.StopBehavior()
		return
	end

	--Check if entity gets stuck in AI; if it gets stuck, stop behavior.
    if self.timer and not self.lookedDown and Time.time > self.timer + self.timerScaleAdd then --if self.timer = if AI
        if self.lastScale and (self.target.animation.Get() == "Run" or self.target.animation.Get() == "Running") then --If target fleeing, compare agent positions; better for moving targets, doesn't care about distance to target
            self.distanceCheck = self.agent.DistanceTo(self.lastPosition) < self.agent.scale * 0.5
        else
            self.distanceCheck = self.lastDistance - self.agent.DistanceTo(self.destination) < self.agent.scale * 0.5 --If target relatively still, compare distances to target; better for avoiding getting stuck but moving sideways
        end

        if self.distanceCheck then
            --log("stuck")
            if not self.stop then self.agent.dict.wanderCounter = 3 end --If not stuck while wandering /// If stuck while regular Walk Here
            self.agent.ai.StopBehavior()
            return
        else
            --log("not stuck!")
            if self.lastScale and self.agent.scale ~= self.lastScale then
                self.scaleRatio = self.agent.scale / self.target.scale
                self.lastScale = self.agent.scale
            end
            self.lastPosition = self.agent.transform.position
            self.lastDistance = self.agent.DistanceTo(self.destination)
            self.timer = Time.time
        end
    end

	--self.timer = if AI /// self.lastScale = if GTS
	if self.target and self.timer and self.lastScale then
		if not self.arrived and not self.stop and self.scaleRatio < 5
		and self.agent.DistanceTo(self.target) < self.agent.scale * 1 then --If size diff is too small, to avoid constantly pushing micro, use larger arrival radius
			self.agent.ai.StopAction() --Stop MoveTo action
			self.arrived = true --Avoid looping this block
			--("arrived earlier cause too small")
		end
		if not self.agent.ai.IsActionActive() then
			if self.stop then
				--log("stopping walk")
				self.agent.ai.StopBehavior()
				return
			end
			if not self.lookedDown then
				if not self.target or not self.target.IsTargettable() then --If stepped on target while arriving
					--log("target dead :O")
					self.agent.LookAt(nil)
					self.agent.animation.SetAndWait("Whatever Gesture")
					self.stop = true
					return
				end
				if self.lookDownAnim == "random" then
					self.lookDownAnim = AIAnimations[math.random(#AIAnimations)]
				end
				self.agent.animation.SetAndWait(self.lookDownAnim)
				self.lookedDown = true
				
			else
				--If animation is fairly aggressive, set next couple behaviors in queue as Stomp
				if self.enableAnimMad and isAnimMad(self.lookDownAnim) then
					if not self.stomped then
						--log("stomping anim")
						self.agent.animation.SetAndWait("Stomping")
						self.stomped = true
					else
						if not self.target or not self.target.IsTargettable() then
							self.target = self.agent.findClosestMicro()
						end
						--log("setting stomp behavior")
						self.agent.ai.SetBehavior("Stomp",self.target)
					 end
				else
					--log("not mad")
					self.stop = true
				end
			end
		end
	end
end

function Walk2:Exit()
	self.agent.LookAt(nil)
	if not self.agent.ai.IsAIEnabled() then
		self.agent.animation.Set("Idle 2")
	elseif self.agent.isGiantess() and self.target then
		self.agent.dict.previousMicro = self.target
	end
	--log("stopped walk")
end



Run = RegisterBehavior("Run Here")
Run.scores = {
	normal = 40
}
Run.data = {
	menuEntry = "Walk/Run There",
	ai = true,
	agent = {
		type = { "humanoid" }
	},
	target = {
		type = { "none" }
	},
	settings = {
        --{"activateOnGTS", "Enable on GTS AI", "bool", true},
        --{"activateOnMicros", "Enable on Micros AI", "bool", true},
        {"runAnimSetting", "Movement Animation", "array", 2, {"Goofy Running", "Slow Run", "Running", "Run", "Fast Run", "Random Normal", "Random All"}},
        {"idleAnimation", "Idle Animation", "string", "Idle 4"},
        {"wildRandomIdle", "Expressive Anims", "bool", false},
        {"randomIdleNoAI", "Random Idle No AI", "bool", false},
        {"lookDownAnim", "Look Down animation", "string", "random"},
        {"enableAnimMad", "Stomp after hostile anim", "bool", true}
    }
}

RunAnims = {
	"Goofy Running", "Slow Run", "Running", "Run", "Fast Run", "Slow Run", "Running", "Slow Run", "Running", "Run"
}

function Run:Start()
	--[[if self.agent.ai.IsAIEnabled() and ((self.agent.isGiantess() and not self.activateOnGTS) or (self.agent.isMicro() and not self.activateOnMicros)) then --If not enabled on GTS or Micro and entity fits, skip behavior.
        self.agent.ai.StopBehavior()
        return
    end]]

    self.stop = false
    self.destination = self.cursorPoint
    
    if self.runAnimSetting >= 5 then
    	if self.runAnimSetting == 5 then
    		self.runAnimation = RunAnims[math.random(#RunAnims - 1) + 1]
    	else
    		self.runAnimation = RunAnims[math.random(#RunAnims)]
    	end
    else
    	self.runAnimation = RunAnims[self.runAnimSetting + 1]
    end
	
	if not self.agent.dict.wanderCounter then
		self.agent.dict.wanderCounter = 0
	end	

	--If not GTS with AI
	if not self.agent.ai.IsAIEnabled() or self.agent.isMicro() then --If used in AI, walk to random micro instead, and look down.
		if self.target and self.target.isHumanoid() and self.target.scale < self.agent.scale * 3 then
			self.agent.LookAt(self.target)
			self.destination = self.target
		end
		self.agent.animation.Set(self.runAnimation)
		self.agent.MoveTo(self.destination)
		SetIdleAnim(self, true)
		if not self.agent.isMicro() then self.loopIdles = true else self.stop = true end
		--log("walked here, not giantess + ai")

	--If GTS with AI
	else
		--log("giantess ai walk here")
		self.arrived = false
		self.target = self.agent.findClosestMicro()
		local microList = self.agent.senses.GetMicrosInRadius(100)
		if self.lookDownAnim ~= "random" then
			local animList = self.agent.animation.GetAnimationList()
			for k,v in pairs(animList) do if v == self.lookDownAnim then animExists = true end end
			if not animExists then log("Run Here: Chosen animation in behavior manager does not exist.") self.lookDownAnim = "random" end
		end
		if not self.target or not self.target.IsTargettable() then
			--log("giantess ai no target")
			self.stop = true
			return
		elseif self.agent.dict.wanderCounter <= 3 and self.target == self.agent.dict.previousMicro and #microList > 1 then
			local time = math.random(4, 8)
			self.agent.animation.Set(self.runAnimation)
			self.agent.wander(time)
			self.agent.dict.wanderCounter = self.agent.dict.wanderCounter + 1
			self.stop = true
			--log("giantess ai same target (wandering)")
		else
			self.destination = self.target
			self.scaleRatio = self.agent.scale / self.target.scale --Prep for 'If size diff is too small'
			self.lastScale = self.agent.scale
			self.agent.LookAt(self.target)
			self.agent.animation.Set(self.runAnimation)
			self.agent.MoveTo(self.target)
			self.agent.dict.wanderCounter = 0
			--log("giantess ai new target")
		end
	end

	--If AI, prep for stuck check
	if self.agent.ai.IsAIEnabled() then
		--if self.agent.isMicro() then self.destination = self.cursorPoint else self.destination =  self.target end
		self.lastPosition = self.agent.transform.position
		self.lastDistance = self.agent.DistanceTo(self.destination)
		self.timer = Time.time
		self.timerScaleAdd = Mathf.Clamp(4 + self.agent.scale / 20, 4, 10) --Used to adapt to GTS speed being slowed down by larger sizes. Defaults to "4", goes up to "10" as GTS rises in size (maxes out at "scale = 120")
	end

	--Only do randomseed once per scene
	if not globals["runRand"] then math.randomseed(tonumber(os.time()) + self.agent.id) globals["runRand"] = true end
end

function Run:Update()
	
	if self.loopIdles then
		if self.agent.animation.IsCompleted() and not self.agent.ai.IsActionActive() then
			SetIdleAnim(self)
		end
		return
	end

	if (self.stop and self.agent.isMicro() and not self.agent.ai.IsActionActive()) or (self.lastScale and self.target.IsDead()) then
		--log("stopping walk")
		self.agent.ai.StopBehavior()
		return
	end

	--Check if entity gets stuck in AI; if it gets stuck, stop behavior.
    if self.timer and not self.lookedDown and Time.time > self.timer + self.timerScaleAdd then --if self.timer = if AI
        if self.lastScale and (self.target.animation.Get() == "Run" or self.target.animation.Get() == "Running") then --If target fleeing, compare agent positions; better for moving targets, doesn't care about distance to target
            self.distanceCheck = self.agent.DistanceTo(self.lastPosition) < self.agent.scale * 0.5
        else
            self.distanceCheck = self.lastDistance - self.agent.DistanceTo(self.destination) < self.agent.scale * 0.5 --If target relatively still, compare distances to target; better for avoiding getting stuck but moving sideways
        end

        if self.distanceCheck then
            --log("stuck")
            if not self.stop then self.agent.dict.wanderCounter = 3 end --If not stuck while wandering /// If stuck while regular Walk Here
            self.agent.ai.StopBehavior()
            return
        else
            --log("not stuck!")
            if self.lastScale and self.agent.scale ~= self.lastScale then
                self.scaleRatio = self.agent.scale / self.target.scale
                self.lastScale = self.agent.scale
            end
            self.lastPosition = self.agent.transform.position
            self.lastDistance = self.agent.DistanceTo(self.destination)
            self.timer = Time.time
        end
    end

	--self.timer = if AI /// self.lastScale = if GTS
	if self.target and self.timer and self.lastScale then
		if not self.arrived and not self.stop and self.scaleRatio < 5
		and self.agent.DistanceTo(self.target) < self.agent.scale * 1 * 1.25 then --If size diff is too small, to avoid constantly pushing micro, use larger arrival radius
			self.agent.ai.StopAction() --Stop MoveTo action
			self.arrived = true --Avoid looping this block
			--("arrived earlier cause too small")
		end
		if not self.agent.ai.IsActionActive() then
			if self.stop then
				--log("stopping walk")
				self.agent.ai.StopBehavior()
				return
			end
			if not self.lookedDown then
				if not self.target or not self.target.IsTargettable() then --If stepped on target while arriving
					--log("target dead :O")
					self.agent.LookAt(nil)
					self.agent.animation.SetAndWait("Whatever Gesture")
					self.stop = true
					return
				end
				if self.lookDownAnim == "random" then
					self.lookDownAnim = AIAnimations[math.random(#AIAnimations)]
				end
				self.agent.animation.SetAndWait(self.lookDownAnim)
				self.lookedDown = true
				
			else
				--If animation is fairly aggressive, set next couple behaviors in queue as Stomp
				if self.enableAnimMad and isAnimMad(self.lookDownAnim) then
					if not self.stomped then
						--log("stomping anim")
						self.agent.animation.SetAndWait("Stomping")
						self.stomped = true
					else
						if not self.target or not self.target.IsTargettable() then
							self.target = self.agent.findClosestMicro()
						end
						--log("setting stomp behavior")
						self.agent.ai.SetBehavior("Stomp",self.target)
					 end
				else
					--log("not mad")
					self.stop = true
				end
			end
		end
	end
end

function Run:Exit()
	self.agent.LookAt(nil)
	if not self.agent.ai.IsAIEnabled() then
		self.agent.animation.Set("Idle 2")
	elseif self.agent.isGiantess() and self.target then
		self.agent.dict.previousMicro = self.target
	end
	--log("stopped walk")
end
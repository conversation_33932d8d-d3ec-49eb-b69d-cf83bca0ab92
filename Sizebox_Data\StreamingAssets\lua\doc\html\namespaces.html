<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Package List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('namespaces.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle"><div class="title">Package List</div></div>
</div><!--header-->
<div class="contents">
<div class="textblock">Here are the packages with brief descriptions (if available):</div><div class="directory">
<div class="levels">[detail level <span onclick="javascript:toggleLevel(1);">1</span><span onclick="javascript:toggleLevel(2);">2</span><span onclick="javascript:toggleLevel(3);">3</span>]</div><table class="directory">
<tr id="row_0_" class="even"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_0_" class="arrow" onclick="toggleFolder('0_')">&#9660;</span><span class="icona"><span class="icon">N</span></span><a class="el" href="namespace_lua.html" target="_self">Lua</a></td><td class="desc"></td></tr>
<tr id="row_0_0_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_a_i.html" target="_self">AI</a></td><td class="desc">Controls the <a class="el" href="class_lua_1_1_a_i.html" title="Controls the AI of humanoid agent.">AI</a> of humanoid agent. </td></tr>
<tr id="row_0_1_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_all_giantess.html" target="_self">AllGiantess</a></td><td class="desc">A class containing settings affecting all giantesses. </td></tr>
<tr id="row_0_2_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_all_micros.html" target="_self">AllMicros</a></td><td class="desc">A class containing settings affecting all micros. </td></tr>
<tr id="row_0_3_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_animation.html" target="_self">Animation</a></td><td class="desc">Component to control the animation for humanoid entities. </td></tr>
<tr id="row_0_4_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_audio_source.html" target="_self">AudioSource</a></td><td class="desc">A representation of audio sources in 3D. </td></tr>
<tr id="row_0_5_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_bones.html" target="_self">Bones</a></td><td class="desc">Access bone transforms of humanoid characters. </td></tr>
<tr id="row_0_6_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_custom_sound_manager.html" target="_self">CustomSoundManager</a></td><td class="desc">An interface to change in-game sound effects with custom sounds in the Sounds folder </td></tr>
<tr id="row_0_7_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_entity.html" target="_self">Entity</a></td><td class="desc">A <a class="el" href="class_lua_1_1_entity.html" title="A Entity represents Characters and Objects">Entity</a> represents Characters and Objects </td></tr>
<tr id="row_0_8_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_entity_initialized_callback.html" target="_self">EntityInitializedCallback</a></td><td class="desc"></td></tr>
<tr id="row_0_9_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_event.html" target="_self">Event</a></td><td class="desc">Interface of the <a class="el" href="class_lua_1_1_event.html" title="Interface of the Event system.">Event</a> system. </td></tr>
<tr id="row_0_10_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_0_10_" class="arrow" onclick="toggleFolder('0_10_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_game.html" target="_self">Game</a></td><td class="desc">A collection of Sizebox specific functions that don't belong to any object. </td></tr>
<tr id="row_0_10_0_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_game_1_1_toast.html" target="_self">Toast</a></td><td class="desc">Toasts notifications act similar to a volume control interface on a modern television or operating systems where you can update the information inside the existing notification while it's still being displayed. However unlike those interfaces, multiple toast notifications can be displayed and updated at the same time. This can be useful when you want to debug a script or notify a user about something without interrupting game play. A <a class="el" href="class_lua_1_1_game_1_1_toast.html" title="Toasts notifications act similar to a volume control interface on a modern television or operating sy...">Toast</a> notification will stay on the screen for 5 seconds after its last message. </td></tr>
<tr id="row_0_10_1_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_game_1_1_version.html" target="_self">Version</a></td><td class="desc">The version of Sizebox the script is being run under. Your script can use the Major and Minor numbers to take different paths for different versions of Sizebox allowing custom scripts to be more portable </td></tr>
<tr id="row_0_11_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_globals.html" target="_self">Globals</a></td><td class="desc">Global dictionary. It can be used to store and exchange arbitrary data between scripts. </td></tr>
<tr id="row_0_12_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_i_k.html" target="_self">IK</a></td><td class="desc">Inverse Kinematics lets you animate individual bones to create procedural animations. </td></tr>
<tr id="row_0_13_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_i_k_effector.html" target="_self">IKEffector</a></td><td class="desc">Each effector lets you control one bone and animate the body. </td></tr>
<tr id="row_0_14_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_input.html" target="_self">Input</a></td><td class="desc">Interface into the <a class="el" href="class_lua_1_1_input.html" title="Interface into the Input system.">Input</a> system. </td></tr>
<tr id="row_0_15_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_lua_player_raygun.html" target="_self">LuaPlayerRaygun</a></td><td class="desc">Use this component to control some elements of the raygun of the player </td></tr>
<tr id="row_0_16_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_mathf.html" target="_self">Mathf</a></td><td class="desc">A collection of common Unity math functions. Largely overlaps with built-in <a class="el" href="namespace_lua.html">Lua</a> math library (<a href="https://www.lua.org/manual/5.3/manual.html#6.7">https://www.lua.org/manual/5.3/manual.html#6.7</a>) </td></tr>
<tr id="row_0_17_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_morphs.html" target="_self">Morphs</a></td><td class="desc">Component to control the morphs for giantess entities. </td></tr>
<tr id="row_0_18_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_movement.html" target="_self">Movement</a></td><td class="desc">Use this component to control the movement of agents. </td></tr>
<tr id="row_0_19_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_player.html" target="_self">Player</a></td><td class="desc">A <a class="el" href="class_lua_1_1_player.html" title="A Player represents settings only applicable for a player-controlled character.">Player</a> represents settings only applicable for a player-controlled character. </td></tr>
<tr id="row_0_20_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_quaternion.html" target="_self">Quaternion</a></td><td class="desc">Quaternions are used to represent rotations </td></tr>
<tr id="row_0_21_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_random.html" target="_self">Random</a></td><td class="desc">Class for generating random data. </td></tr>
<tr id="row_0_22_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_rigidbody.html" target="_self">Rigidbody</a></td><td class="desc">Control of an object's position through physics simulation. </td></tr>
<tr id="row_0_23_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_screen.html" target="_self">Screen</a></td><td class="desc">Access to display information. </td></tr>
<tr id="row_0_24_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_senses.html" target="_self">Senses</a></td><td class="desc">Control the senses of a entity such as the vision. </td></tr>
<tr id="row_0_25_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_shooting.html" target="_self">Shooting</a></td><td class="desc">Use this component to control the shooting-related and gun properties of agents. </td></tr>
<tr id="row_0_26_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_time.html" target="_self">Time</a></td><td class="desc">The interface to get time information from Unity. </td></tr>
<tr id="row_0_27_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_transform.html" target="_self">Transform</a></td><td class="desc">Position, rotation and scale of an object. </td></tr>
<tr id="row_0_28_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_vector3.html" target="_self">Vector3</a></td><td class="desc">Representation of 3D vectors and points. </td></tr>
<tr id="row_0_29_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_world.html" target="_self">World</a></td><td class="desc">A class containing settings affecting the world. </td></tr>
</table>
</div><!-- directory -->
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

local Roulette = RegisterBehavior("Random Size Roulette")
Roulette.data = {
    menuEntry = "Random Size Roulette",
    agent = { type = { "giantess" } },
    secondary = true
}

-- State
local rouletteActive = false
local timer = 0
local interval = 2.5
local toast
local soundList = {
    "GaspMoan001_Mmm.ogg",
    "GaspMoan002_Augh_MidLong.ogg",
    "GaspMoan003_Ahh_MidLong.ogg"
}
local failSound = "FailBuzzer.ogg"
local audioSource
local round = 0
local highScore = 0
local minSafeSize = 0.5
local invincibleRounds = 0
local doubleOrNothing = false
local beScale = 1.0
local beTargetScale = 1.0
local beStep = 0.15
local beSmoothSpeed = 5
local beMin = 0.5
local gradualScaling = false
local gradualTargetScale = 1.0
local gradualSpeed = 0.7
local growthMultiplier = 1.0
local shrinkMultiplier = 1.0
local multiplierActive = false
local multiplierRounds = 0
local miniGameActive = false
local miniGameKey = nil
local miniGameTimer = 0
local miniGameDuration = 2.0

-- Utility
local function flashScreen(color)
    Game.Toast.New().Print("<color="..color..">JACKPOT!</color>")
end

local function setBreastScale(agent, scale)
    local names = { "Breast", "Ichichi", "LeftBreast", "RightBreast", "leftbreast", "rightbreast", "breast left", "breast right", "hidarichichi", "migichichi", "lPectoral", "rPectoral" }
    for _, name in ipairs(names) do
        local bones = agent.bones.GetBonesByName(name, true)
        if bones then
            for _, bone in ipairs(bones) do
                bone.localScale = Vector3.New(scale, scale, scale)
            end
        end
    end
end

local function pickGradual()
    return math.random() < 0.5
end

local function getMiniGameKey()
    local badKeys = { r=true, x=true, z=true }
    local key
    repeat
        key = string.char(math.random(97, 122))
    until not badKeys[key]
    return key
end

-- Start
function Roulette:Start()
    if gts and gts.maxSize then gts.maxSize = 0 end
    if gts and gts.minSize then gts.minSize = 0.0000000001 end
    pcall(function() self.agent.sizeLocked = false end)

    toast = Game.Toast.New()
    audioSource = AudioSource:new(self.agent.bones.spine)
    audioSource.spatialBlend = 1
    audioSource.loop = false
    audioSource.volume = 1

    -- Reset all states
    round = 0
    highScore = 0
    invincibleRounds = 0
    doubleOrNothing = false
    beScale = 1.0
    beTargetScale = 1.0
    gradualScaling = false
    growthMultiplier = 1.0
    shrinkMultiplier = 1.0
    multiplierActive = false
    multiplierRounds = 0
    miniGameActive = false

    toast.Print("Press R to start/stop Size Roulette!\nUse [ and ] to change speed.")
end

-- Update
function Roulette:Update()
    -- Interval controls
    if Input.GetKeyDown("[") then
        interval = math.max(0.5, interval - 0.5)
        toast.Print("Roulette speed: " .. interval .. "s")
    elseif Input.GetKeyDown("]") then
        interval = interval + 0.5
        toast.Print("Roulette speed: " .. interval .. "s")
    end

    -- Start/Stop
    if Input.GetKeyDown("r") then
        rouletteActive = not rouletteActive
        toast.Print(rouletteActive and "Roulette started!" or "Roulette stopped!")
        if rouletteActive then round = 0 end
    end

    -- Gradual breast scale update
    beScale = beScale + (beTargetScale - beScale) * math.min(1, Time.deltaTime * beSmoothSpeed)
    setBreastScale(self.agent, beScale)

    -- Mini-game input handling
    if miniGameActive then
        miniGameTimer = miniGameTimer + Time.deltaTime
        if Input.GetKeyDown(miniGameKey:lower()) or Input.GetKeyDown(miniGameKey:upper()) then
            local scale = self.agent.scale or self.agent.localScale or 1
            local bonus = (scale * 0.5) * growthMultiplier
            gradualTargetScale = scale + bonus
            gradualScaling = true
            miniGameActive = false
            toast.Print("Mini-game success! Bonus growth!")
            audioSource.clip = soundList[math.random(1, #soundList)]
            audioSource:Play()
            return
        elseif miniGameTimer > miniGameDuration then
            miniGameActive = false
            if invincibleRounds > 0 then
                toast.Print("Mini-game failed! No shrink (invincible).")
            else
                local scale = self.agent.scale or self.agent.localScale or 1
                local penalty = math.max(0.1, scale * 0.25) * shrinkMultiplier
                gradualTargetScale = math.max(minSafeSize, scale - penalty)
                gradualScaling = true
                audioSource.clip = failSound
                audioSource:Play()
                toast.Print("Mini-game failed! Penalty shrink.")
            end
            return
        end
    end

    -- Gradual scale update
    if gradualScaling then
        local scale = self.agent.scale or self.agent.localScale or 1
        local diff = gradualTargetScale - scale
        local step = gradualSpeed * Time.deltaTime
        if math.abs(diff) <= 0.01 then
            gradualScaling = false
            if self.agent.scale then
                self.agent.scale = gradualTargetScale
            else
                self.agent.localScale = gradualTargetScale
            end
        else
            local newScale = scale + diff * step
            if self.agent.scale then
                self.agent.scale = newScale
            else
                self.agent.localScale = newScale
            end
        end
        return
    end

    if not rouletteActive then return end

    -- Roulette logic
    timer = timer + Time.deltaTime
    if timer >= interval then
        timer = 0
        round = round + 1
        if round > highScore then highScore = round end

        local scale = self.agent.scale or self.agent.localScale or 1
        local roll = math.random()
        local msg = ""

        -- Decrease invincibility
        if invincibleRounds > 0 then invincibleRounds = invincibleRounds - 1 end

        -- Multiplier mode logic
        if multiplierActive then
            multiplierRounds = multiplierRounds - 1
            if multiplierRounds <= 0 then
                multiplierActive = false
                growthMultiplier = 1
                shrinkMultiplier = 1
                toast.Print("Multiplier mode ended.")
            end
        end

        -- Mini-game roll (10% chance)
        if roll < 0.10 then
            miniGameActive = true
            miniGameKey = getMiniGameKey()
            miniGameTimer = 0
            toast.Print("Mini-game! Press '"..miniGameKey.."' in "..miniGameDuration.."s!")
            return
        end

        -- Multiplier trigger (7%)
        if not multiplierActive and roll >= 0.10 and roll < 0.17 then
            multiplierActive = true
            multiplierRounds = math.random(3, 6)
            growthMultiplier = math.random(15, 30) / 10
            shrinkMultiplier = math.random(15, 25) / 10
            toast.Print(string.format("Multiplier: x%.1f grow / x%.1f shrink for %d rounds!", growthMultiplier, shrinkMultiplier, multiplierRounds))
            return
        end

        -- Double or Nothing
        if doubleOrNothing then
            doubleOrNothing = false
            if math.random() < 0.5 then
                gradualTargetScale = scale + (scale * 2 * growthMultiplier)
                gradualScaling = true
                flashScreen("orange")
                audioSource.clip = soundList[math.random(1, #soundList)]
                audioSource:Play()
                msg = "DOUBLE OR NOTHING: Double Growth!"
            else
                gradualTargetScale = math.max(minSafeSize, scale - (scale * 2 * shrinkMultiplier))
                gradualScaling = true
                flashScreen("red")
                audioSource.clip = failSound
                audioSource:Play()
                msg = "DOUBLE OR NOTHING: Double Shrink!"
            end
        elseif roll < 0.18 then
            doubleOrNothing = true
            toast.Print("Double or Nothing next round!")
            return
        elseif roll < 0.20 then
            gradualTargetScale = scale + (scale * 2 + 10)
            gradualScaling = true
            msg = "SUPER JACKPOT! Colossal Growth!"
            flashScreen("yellow")
            audioSource.clip = soundList[math.random(1, #soundList)]
            audioSource:Play()
        elseif roll < 0.22 then
            gradualTargetScale = math.max(minSafeSize, scale - (scale * 2 + 10))
            gradualScaling = true
            msg = "SUPER JACKPOT! Tiny Shrink!"
            flashScreen("cyan")
        elseif roll < 0.26 then
            beTargetScale = beTargetScale + beStep
            msg = "BE Jackpot! Breast Expansion!"
            audioSource.clip = soundList[math.random(1, #soundList)]
            audioSource:Play()
            flashScreen("pink")
        elseif roll < 0.30 then
            beTargetScale = math.max(beMin, beTargetScale - beStep)
            msg = "BE Roulette! Breast Shrink!"
            flashScreen("aqua")
        elseif roll < 0.40 then
            msg = "Safe! No change this round."
        else
            local growthChance = 0.65
            if math.random() < growthChance then
                if pickGradual() then
                    gradualTargetScale = scale + math.max(0.1, scale * 0.10) * growthMultiplier
                    gradualScaling = true
                    msg = "Gradual Growth!"
                else
                    self.agent.scale = scale + math.max(0.1, scale * 0.10) * growthMultiplier
                    msg = "Instant Growth!"
                end
            else
                if invincibleRounds > 0 then
                    msg = "Invincible! No shrink."
                else
                    if pickGradual() then
                        gradualTargetScale = math.max(minSafeSize, scale - math.max(0.1, scale * 0.10) * shrinkMultiplier)
                        gradualScaling = true
                        msg = "Gradual Shrink!"
                    else
                        self.agent.scale = math.max(minSafeSize, scale - math.max(0.1, scale * 0.10) * shrinkMultiplier)
                        msg = "Instant Shrink!"
                    end
                end
            end
        end

        toast.Print(msg .. "\nRound: " .. round .. "\nHigh Score: " .. highScore .. "\nSize: " .. string.format("%.2f", self.agent.scale or self.agent.localScale or 1.0))
    end
end

return Roulette

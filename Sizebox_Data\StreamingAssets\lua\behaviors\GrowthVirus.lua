GrowthVirus = RegisterBehavior("GrowthVirus")
GrowthVirus.data =  {
    menuEntry = "Size/Growth Virus",
    description = "A Strange Growth Virus begins spreading throughout the population!",
    secondary = true,
	flags = {"GrowthVirus"},
    agent = {
        type = { "humanoid" }
    },
	settings = {
		{ "fatal", "Fatal?", "bool", true },
        { "duplicate", "Duplicate?", "bool", false },
        { "debug", "Debug Mode?", "bool", true },
        { "infinite", "Get Reinfected?", "bool", false },
		{ "min_action_steps", "Min Symptoms", "string","1"},
		{ "max_action_steps", "Max Symptoms","string","3"},
		{ "incubation_period", "Incubation Period in Seconds", "string","10"},
		{ "min_infection_period", "Minimum Infection Period in Seconds", "string","10"},
        { "max_infection_period", "Maximum Infection Period in Seconds", "string","60"},
        { "resistance", "Infection Chance", "float","0.9"}
	}
}
function GrowthVirus:Start()
    --init our values
    Debugging = self.debug
    Resist = self.resistance
    murder = self.fatal
    clone = self.duplicate
    body_dispose_time = 10 --time to wait before removing dead bodies
    updateTicks = 10 --time to update in seconds
    maxInfectionDistance = 50 --max units for infection target distance
    Min_action_steps = tonumber(self.min_action_steps)
    Max_action_steps = tonumber(self.max_action_steps)
    Incubation_period = tonumber(self.incubation_period)
    Min_infection_period = tonumber(self.min_infection_period)
    Max_infection_period = tonumber(self.max_infection_period)
    --init time for performance optimization
    GameTicks = Time.time
    --tracking array
    Virus = {}
    --fix dumb entries
    --people didn't put in numbers, quit
    if Min_action_steps == nil or Max_action_steps == nil or Incubation_period == nil or Min_infection_period == nil or Max_infection_period == nil then
        return
    end
    --people goofed the min/maxes, fix them
    if Min_action_steps > Max_action_steps then
        Min_action_steps = Max_action_steps
        Log("Min Action Steps was greater than Max Action Steps, making them the same.")
    end
    if Max_action_steps < Min_action_steps then
        Max_action_steps = Min_action_steps
        Log("Max Action Steps was less than Min Action Steps, making them the same.")
    end
    if Min_infection_period > Max_infection_period then
        Min_infection_period = Max_infection_period
        Log("Min infection Period was greater than Max infection Period, making them the same.")
    end
    if Max_infection_period < Min_infection_period then
        Max_infection_period = Min_infection_period
        Log("Max infection Period was less than Min infection Period, making them the same.")
    end

    --add the agent or target to the inital infecties array
    local tactor = self.agent
    local target = {}
    target[0] = tactor
    target[1] = Time.time --init infection time
    --time infectious
    target[2] = math.random(Min_infection_period,Max_infection_period)
    --calculate action steps now
    target[3] = math.random(Min_action_steps,Max_action_steps) --action steps
    target[4] = nil --remove body time
    Virus[0] = target
    
    if self.debug then
        Log("ActionSteps: " .. tostring(target[3]) .. "Infection Period:" .. tostring(target[2]) .. "Actor:" .. self.agent.name)
    end
end

function GrowthVirus:Update()
    --init
    local tempactor = nil
    local actor = nil
    local infect_time = nil
    local infect_from = nil
    local action_steps = nil
    --only update every updateTicks period
    if Time.time >= (GameTicks + updateTicks) then
        for i=0, #Virus, 1 do
            tempactor = Virus[i]
            if tempactor[0] == nil then
                --they literally just died. whelp. set actor to nil
                actor = nil
            else
                actor = tempactor[0]
            end
            infect_time = tempactor[1]
            infect_from = tempactor[2]
            action_steps = tempactor[3]
            cleanup_time = tempactor[4]
            if self.debug then
                if actor == nil then
                    
                else
                    Log("Processing " .. actor.name .. actor.position)
                end
                if infect_time == nil then
                    --this actor is dead
                    Log("cleanup_time " .. tostring(cleanup_time))
                else
                    Log("currenttime:" .. tostring(Time.time) .. "infected time:" .. tostring(infect_time) .. " ActionTime:" .. tostring(Time.time + infect_from) .. " action steps:" .. tostring(action_steps))
                end
            end
            --check if infection time is empty first
            if infect_time == nil then
                Log("dead person:" .. actor.name .. "deathtime: " .. tostring(cleanup_time))
                if Time.time >= (cleanup_time + body_dispose_time) then
                    --they dead, cleanup
                    if self.debug then
                        Log("Deleting" .. actor.name)
                    end
                    actor.Delete()
                    Virus[i] = nil
                end
            else
                if Time.time >= (infect_time + Incubation_period + infect_from) then
                    --we are no longer infectious
                    if action_steps == nil then
                        --idk how we got here this is just error catching
               
                    elseif action_steps <= 0 then
                        if infinite then
                            if self.debug then
                                Log("delete entry" .. actor.name)
                            end
                            Virus[i] = nil
                        end
                    else
                        --subtract one action step from count
                        tempactor[3] = (action_steps -1)
                        Virus[i] = tempactor
                        --do actions
                        if cleanup_time == nil then
                            doAction(actor, i)
                        end
                    end 
                elseif Time.time >= (infect_time + Incubation_period) then
                    --start infecting people
                    doInfection(actor)
                end
            end
        end
        --update GameTicks
        GameTicks = Time.time
    else
        --do nothing
    end

end

function doInfection(actor)
    --init var
    local caninfect = false
    local tempactor = nil
    local tactor = nil
    if Debugging then
        Log("Infectious:" .. actor.name)
    end
    
    --pick our victim
    local target = actor.FindClosestMicro()
    if target == nil then
        --try giantess
        target = actor.FindClosestGiantess()
    end
    --check if they have already been infected
    for i=0, #Virus, 1 do
        tempactor = Virus[i]
        if tempactor[0] == nil then
            --they literally just died. whelp. make our target ourselves so the next check ends in false.
            tactor = actor
        else
            tactor = tempactor[0]
        end
        if target == nil then
            --most likely trying to reinfect the player, do nothing
            caninfect = false
        else
            if not (tactor == target) then
                caninfect = true
            else
                caninfect = false
            end
        end
    end 
    if Debugging then
        if not target == nil then
            Log(target.name .. " ".. tostring(caninfect))
        else
          --  Log("No Target to infect")
        end
    end
    if caninfect then
        --caculate the distance between infected and victim to determine infection chance
        if (actor.Distance(target.position)) == nil then
            --no one was within 100m, sadge.
        elseif (actor.Distance(target.position)) >= maxInfectionDistance then
            --skip infection step, this is too far away
        else
            --distance given is in metres to target so
            if Resist == 1 then
                --WE GOT A SUPERSPREADER
                infect_chance = 100
            else
                infect_chance = (100 - (actor.Distance(target.position)) * Resist)
            end
            if Debugging then
                Log("Infect Chance:" ..tostring(infect_chance))
            end
            if (math.random(1,100) <= infect_chance) then
                --do infection
                if Debugging then
                    Log("Infected: " .. target.name)
                end
                local infectee = {}
                infectee[0] = target
                infectee[1] = Time.time --init infection time
                --time until infectious
                infectee[2] = math.random(Min_infection_period,Max_infection_period)
                --calculate action steps now
                infectee[3] = math.random(Min_action_steps,Max_action_steps) --action steps
                infectee[4] = nil --death time
                infectee[5] = false --done actions
                arraypos = (#Virus) +1
                Virus[arraypos] = infectee
            end
        end
    end
end

function doAction(actor, apos)
    if Debugging then
        Log("Actions:" .. actor.name .. " apos:" .. tostring(apos))
    end
    local option = nil
    local growthRate = math.random()
    option = math.random(1,4)    
--growth
    if option == 1 then
        if Debugging then
            Game.Toast.New().Print("Growing " .. actor.name)
        end
        actor.GrowAndWait(growthRate, 1)
    end
--clone
    if option == 2 then
        if clone then
            if Debugging then
                Game.Toast.New().Print("Cloning " .. actor.name)
            end
            pos = actor.position                                   -- our position
            angle = actor.transform.rotation.eulerAngles.y         -- our facing
            angle = angle + 180                                         -- turn the clone around
            scale = tonumber(actor.height)

            gtsAngle = angle                      -- angle for this giantess
            gtsRot = Quaternion.angleAxis(gtsAngle, Vector3.up)                 -- rotation quaternion
            gtsPos = pos - gtsRot * Vector3.forward * (scale*10)                    -- rotate a vector of radius length and add to player position
            gts = Entity.spawnGiantess(actor.modelName, scale)
            gts.ai.EnableAI()
            --infect them as well
            local infectee = {}
            infectee[0] = gts
            infectee[1] = Time.time --init infection time
            --time until infectious
            infectee[2] = math.random(Min_infection_period,Max_infection_period)
            --calculate action steps now
            infectee[3] = math.random(Min_action_steps,Max_action_steps) --action steps
            infectee[4] = nil --death time
            arraypos = (#Virus) +1
            Virus[arraypos] = infectee
        else
            --add another action step to reroll
            local infectee = {}
            local temp = Virus[apos]
            infectee[0] = actor
            infectee[1] = temp[1] --init infection time
            --time until infectious
            infectee[2] = temp[2]
            --calculate action steps now
            asteps = temp[3] + 1
            infectee[3] = asteps --action steps
            infectee[4] = nil --death time
            Virus[apos] = infectee --update entry
        end
    end

--die
    if option == 3 then
        if murder then
            if Debugging then
                Game.Toast.New().Print("Killing " .. actor.name)
            end
            actor.ai.StopBehavior()
            actor.ai.StopAction()
            actor.ai.DisableAI()
            actor.animation.set("Dying")
            
            if Game.GetLocalPlayer() == actor then
                --we killed the player character pogu
                Game.Toast.New().Print("The Virus claimed you as a victim...")
            end
            local infectee = {}
            infectee[0] = actor
            infectee[1] = nil --init infection time
            --time until infectious
            infectee[2] = nil
            --calculate action steps now
            infectee[3] = 0--action steps
            infectee[4] = Time.time
            Virus[apos] = infectee
            if Debugging then
                test = Virus[apos]
                name = test[0].name
                tsteps = test[3]
                Game.Toast.New().Print("removed" .. name .. " actionsteps " .. tostring(tsteps))
            end
        else
            --add another action step to reroll
            local infectee = {}
            local temp = Virus[apos]
            infectee[0] = actor
            infectee[1] = temp[1] --init infection time
            --time until infectious
            infectee[2] = temp[2]
            --calculate action steps now
            asteps = temp[3] + 1
            infectee[3] = asteps --action steps
            infectee[4] = nil --death time
            Virus[apos] = infectee --update entry
        end
    end
--nothing
    if option == 4 then
        if Debugging then
            Game.Toast.New().Print("Nothing " .. actor.name)
        end
    end

end
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Class Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('functions_g.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_g" name="index_g"></a>- g -</h3><ul>
<li>Get()&#160;:&#160;<a class="el" href="class_lua_1_1_animation.html#afa4d0d4dc374917da05e5c26e75c20b0">Lua.Animation</a></li>
<li>GetAnimationList()&#160;:&#160;<a class="el" href="class_lua_1_1_animation.html#a8a1938fee4f3fc1967112c49c26105b3">Lua.Animation</a></li>
<li>GetAxis()&#160;:&#160;<a class="el" href="class_lua_1_1_input.html#a616e22e4f3b9c973c9763c10dc495395">Lua.Input</a></li>
<li>GetAxisRaw()&#160;:&#160;<a class="el" href="class_lua_1_1_input.html#aa25e7d2e0c828c4661e8a77db269e5b3">Lua.Input</a></li>
<li>GetBoneByName()&#160;:&#160;<a class="el" href="class_lua_1_1_bones.html#a13cba4cdb3e2503912cc6eb9a9b9f187">Lua.Bones</a></li>
<li>GetBonesByName()&#160;:&#160;<a class="el" href="class_lua_1_1_bones.html#abc25d3c71e0a41ce1a65407f6ac77bca">Lua.Bones</a></li>
<li>GetButton()&#160;:&#160;<a class="el" href="class_lua_1_1_input.html#ac45bfbc1aaa71822f9dd32ee446e3e26">Lua.Input</a></li>
<li>GetButtonDown()&#160;:&#160;<a class="el" href="class_lua_1_1_input.html#a59b2338d29a39f0694aebef890598b7c">Lua.Input</a></li>
<li>GetButtonUp()&#160;:&#160;<a class="el" href="class_lua_1_1_input.html#a74cdd1903a2b531d575a20ea9cbb0ec0">Lua.Input</a></li>
<li>GetChild()&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#a2535f3bade200a7f2c2c59debaeed41a">Lua.Transform</a></li>
<li>GetClipLength()&#160;:&#160;<a class="el" href="class_lua_1_1_audio_source.html#a7d14ab87ad493f85fa9ee1b747bf6df0">Lua.AudioSource</a></li>
<li>GetEntitiesInRadius()&#160;:&#160;<a class="el" href="class_lua_1_1_senses.html#abc4b35f7f0c31f0ab2628631f4df3f97">Lua.Senses</a></li>
<li>GetFemaleMicroList()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a4ba19c862b99a6f66c65894ca07228b1">Lua.Entity</a></li>
<li>GetGiantessesInRadius()&#160;:&#160;<a class="el" href="class_lua_1_1_senses.html#a3ef9b23d5423c511f21c10f6284073f6">Lua.Senses</a></li>
<li>GetGlobalSpeed()&#160;:&#160;<a class="el" href="class_lua_1_1_animation.html#ad91c4ef8fcb303877802e1e38fd27b85">Lua.Animation</a></li>
<li>GetGtsModelList()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a3b1c9ef716345edfa2edb2608d8180cc">Lua.Entity</a></li>
<li>GetKey()&#160;:&#160;<a class="el" href="class_lua_1_1_input.html#abf392b3cf9d208f67a6ea02c0288206a">Lua.Input</a></li>
<li>GetKeyDown()&#160;:&#160;<a class="el" href="class_lua_1_1_input.html#a8701b16492ad5e1ec79c019d74f7b051">Lua.Input</a></li>
<li>GetKeyUp()&#160;:&#160;<a class="el" href="class_lua_1_1_input.html#a7c9f4df5dcb4bc4d194da55be31fb0ea">Lua.Input</a></li>
<li>GetLength()&#160;:&#160;<a class="el" href="class_lua_1_1_animation.html#a8da88aefb747e3128bcbf35be8451b21">Lua.Animation</a></li>
<li>GetLocalPlayer()&#160;:&#160;<a class="el" href="class_lua_1_1_game.html#a9b67e06c39b502f973d3eb4878591c5b">Lua.Game</a></li>
<li>GetLocalPlayerSettings()&#160;:&#160;<a class="el" href="class_lua_1_1_game.html#ad5f0d2e71e75f5ed3c9e77ef657deb8c">Lua.Game</a></li>
<li>GetLocalSelection()&#160;:&#160;<a class="el" href="class_lua_1_1_game.html#a105ff522a63e5c561359adb9a49c124f">Lua.Game</a></li>
<li>GetLocalSelections()&#160;:&#160;<a class="el" href="class_lua_1_1_game.html#a9223af639759f362907b7c5041123e58">Lua.Game</a></li>
<li>GetMaleMicroList()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a4140900b6cce1a0da2723df84fae3cbb">Lua.Entity</a></li>
<li>GetMicrosInRadius()&#160;:&#160;<a class="el" href="class_lua_1_1_senses.html#a2ce5c645f14597c8682bb8bf127ea7b1">Lua.Senses</a></li>
<li>GetMorphCount()&#160;:&#160;<a class="el" href="class_lua_1_1_morphs.html#ad8c500e4a1dafbc13c39b762b860d49b">Lua.Morphs</a></li>
<li>GetMorphList()&#160;:&#160;<a class="el" href="class_lua_1_1_morphs.html#ae7b53cf0daa20f7fa1baa338badcb633">Lua.Morphs</a></li>
<li>GetMorphName()&#160;:&#160;<a class="el" href="class_lua_1_1_morphs.html#ae9a3153146428adc11341bddf1363442">Lua.Morphs</a></li>
<li>GetMorphValue()&#160;:&#160;<a class="el" href="class_lua_1_1_morphs.html#aa2abe4bd241377c0589799e32bb6fe55">Lua.Morphs</a></li>
<li>GetMouseButton()&#160;:&#160;<a class="el" href="class_lua_1_1_input.html#a40addbec8d9b18f0dc0cb101edc38b8d">Lua.Input</a></li>
<li>GetMouseButtonDown()&#160;:&#160;<a class="el" href="class_lua_1_1_input.html#a1a6498f2fab91a72642ac064359edef7">Lua.Input</a></li>
<li>GetMouseButtonUp()&#160;:&#160;<a class="el" href="class_lua_1_1_input.html#ac9d47356c504c74bfcbee5fff1e9939b">Lua.Input</a></li>
<li>GetObjectList()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a3412e9016f582bd74344865dadc6a954">Lua.Entity</a></li>
<li>GetPoseList()&#160;:&#160;<a class="el" href="class_lua_1_1_animation.html#af7313d3ee3fd378b8136d8f72f1d90a1">Lua.Animation</a></li>
<li>GetProgress()&#160;:&#160;<a class="el" href="class_lua_1_1_animation.html#ae9196e188d96824f23cf65b1f835aa1a">Lua.Animation</a></li>
<li>GetRandomGiantess()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#aa9df9f39762cb24b89449e8b61aab43c">Lua.Entity</a></li>
<li>GetRandomMicro()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#aad0651348795eb39acee39055e0b7638">Lua.Entity</a></li>
<li>GetSelectedEntity()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#af3f7aae31ecb691380a6f18f053fb907">Lua.Entity</a></li>
<li>GetSiblingIndex()&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#a839d8eeda6ca8e0ea2a2e7b50643b0ca">Lua.Transform</a></li>
<li>GetSpeed()&#160;:&#160;<a class="el" href="class_lua_1_1_animation.html#ac615b08b06a84330cddb327e6d28f6c9">Lua.Animation</a></li>
<li>GetTime()&#160;:&#160;<a class="el" href="class_lua_1_1_animation.html#ad7c23e47dae3b3c354d317724e93e887">Lua.Animation</a></li>
<li>GetVisibleEntities()&#160;:&#160;<a class="el" href="class_lua_1_1_senses.html#a91a2c0ed86752640a17f70d112459239">Lua.Senses</a></li>
<li>GetVisibleMicros()&#160;:&#160;<a class="el" href="class_lua_1_1_senses.html#a50d6bb5aa8736f7e7e801601b62ecd95">Lua.Senses</a></li>
<li>globalSpeed&#160;:&#160;<a class="el" href="class_lua_1_1_all_giantess.html#a76e7e6cca768273ffd53a6b8a3127ffe">Lua.AllGiantess</a></li>
<li>Grab()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a6ddff7e7a95c85ba34bee6b8b5c4ee93">Lua.Entity</a></li>
<li>gravity&#160;:&#160;<a class="el" href="class_lua_1_1_world.html#a22ab3b01f22a00741c9b11a1f32ff7db">Lua.World</a></li>
<li>Grow()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a92feb21c4219c60a7e5935733302083f">Lua.Entity</a></li>
<li>GrowAndWait()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a0007133219ff5ec24e9eecf6a9d2dd50">Lua.Entity</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

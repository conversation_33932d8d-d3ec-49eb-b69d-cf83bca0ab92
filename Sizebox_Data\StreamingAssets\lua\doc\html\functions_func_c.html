<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Class Members - Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('functions_func_c.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a id="index_c" name="index_c"></a>- c -</h3><ul>
<li>CancelQueuedActions()&#160;:&#160;<a class="el" href="class_lua_1_1_a_i.html#acae3b8822867d658c5dc7c409af76e11">Lua.AI</a></li>
<li>CancelQueuedBehaviors()&#160;:&#160;<a class="el" href="class_lua_1_1_a_i.html#a1c8effd2d78be1f69eb84e3db9b02b0f">Lua.AI</a></li>
<li>CanSee()&#160;:&#160;<a class="el" href="class_lua_1_1_senses.html#a57bce39cc60df494445e5869f4dec72b">Lua.Senses</a></li>
<li>Ceil()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#aa17eeb105797860b39d5765c5f4a8929">Lua.Mathf</a></li>
<li>Chase()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#ab4f0e1c31b16110cdadb7479ba008423">Lua.Entity</a></li>
<li>Clamp()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#ad734e258b7adf07ed4a34557d80f0122">Lua.Mathf</a></li>
<li>Clamp01()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a8302fdbff60f945480e559d3f97474d5">Lua.Mathf</a></li>
<li>ClampMagnitude()&#160;:&#160;<a class="el" href="class_lua_1_1_vector3.html#ab4ac1349372b28a8e490a94e96d950aa">Lua.Vector3</a></li>
<li>ClosestPointOnBounds()&#160;:&#160;<a class="el" href="class_lua_1_1_rigidbody.html#a267bf6dacb4ef9d2aeb0c798d2460245">Lua.Rigidbody</a></li>
<li>ClosestPowerOfTwo()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#ae78395f9919d38bd29ae567f6f1aac3e">Lua.Mathf</a></li>
<li>Concat()&#160;:&#160;<a class="el" href="class_lua_1_1_quaternion.html#ac70013482fc53c72c664561d67d5d677">Lua.Quaternion</a>, <a class="el" href="class_lua_1_1_vector3.html#a7a6a85bde6d3a1072d52ebf902f291e0">Lua.Vector3</a></li>
<li>ConvertToMeter()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#ab274b83f44ab3e9457e67f949097bc27">Lua.Mathf</a></li>
<li>Cos()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a9bb7f6cc54371f64edaf224c4a5365b6">Lua.Mathf</a></li>
<li>Cross()&#160;:&#160;<a class="el" href="class_lua_1_1_vector3.html#aada99c9426a2253aed6e6a150efade76">Lua.Vector3</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

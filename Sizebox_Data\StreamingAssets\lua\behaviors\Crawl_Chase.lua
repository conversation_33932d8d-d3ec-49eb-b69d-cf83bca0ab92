Follow = RegisterB<PERSON><PERSON>or("Chase Follow")
Follow.data = {
    menuEntry = "Walk/Follow/Chase Follow",
    agent = {
        type = { "humanoid" }, 
        exclude = { "player" }
    },
    target = {
        type = { "humanoid" }
    }
}

walkAnimation = "Crawl"
idleAnimation = "Crouch Idle"

function Follow:Update()
    self.agent.LookAt(self.target)
    self.agent.animation.Set(walkAnimation)
    self.agent.chase(self.target)
end

function Follow:Exit()
    self.agent.animation.Set(idleAnimation)
end

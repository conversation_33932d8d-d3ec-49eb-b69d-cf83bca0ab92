-- POSITION LOCK BEHAVIOR
-- Locks micro position to specific locations on macro (between legs, etc.)
-- Inspired by playas.lua position locking mechanism

local PositionLock = {}
PositionLock.__index = PositionLock

-- BEHAVIOR REGISTRATION
function PositionLock.GetBehaviorName()
    return "Position Lock"
end

function PositionLock.GetBehaviorDescription()
    return "Lock micro position to macro body parts. Say 'put me between your legs' or 'unlock position'."
end

function PositionLock.new(agent)
    local self = setmetatable({}, PositionLock)
    
    -- Store the agent (macro character this behavior is attached to)
    self.agent = agent
    self.microCharacter = nil
    self.isLocked = false
    self.lockPosition = "none"
    self.lockBone = nil
    self.lockOffset = Vector3.new(0, 0, 0)
    
    -- Position presets (scaled relative to macro)
    self.positions = {
        ["between legs"] = {
            bone = "hips",
            offset = Vector3.new(0, -50, 0),  -- Will be scaled
            description = "between your legs"
        },
        ["on chest"] = {
            bone = "chest", 
            offset = Vector3.new(0, 20, 0),
            description = "on your chest"
        },
        ["in hand"] = {
            bone = "rightHand",
            offset = Vector3.new(0, 0, 0),
            description = "in your hand"
        },
        ["on shoulder"] = {
            bone = "rightShoulder",
            offset = Vector3.new(10, 0, 0),
            description = "on your shoulder"
        }
    }
    
    print("PositionLock: Behavior created for " .. (agent and agent.name or "unknown"))
    
    return self
end

function PositionLock:Update()
    -- Handle position locking
    if self.isLocked and self.microCharacter and self.lockBone then
        self:MaintainLock()
    end
end

function PositionLock:FindMicro()
    -- Try to find a micro character to lock
    local micro = nil
    
    -- Method 1: Try to find player character if it's a micro
    if Entity and Entity.GetPlayerCharacter then
        local player = Entity.GetPlayerCharacter()
        if player and player.scale and player.scale < self.agent.scale * 0.5 then
            micro = player
            print("PositionLock: Found player micro")
        end
    end
    
    -- Method 2: Find closest micro
    if not micro and self.agent.findClosestMicro then
        micro = self.agent.findClosestMicro()
        if micro then
            print("PositionLock: Found closest micro")
        end
    end
    
    -- Method 3: Search all characters for micros
    if not micro and Entity and Entity.GetCharacters then
        local characters = Entity.GetCharacters()
        if characters then
            for i = 1, #characters do
                local char = characters[i]
                if char and char.scale and char.scale < self.agent.scale * 0.5 then
                    micro = char
                    print("PositionLock: Found micro in character list")
                    break
                end
            end
        end
    end
    
    return micro
end

function PositionLock:FindBone(boneName)
    -- Try multiple ways to find the bone
    local bone = nil
    
    -- Try common bone name variations
    local boneVariations = {
        boneName,
        boneName:lower(),
        boneName:gsub("^%l", string.upper), -- Capitalize first letter
        "JOINT_" .. boneName:upper(),
        "37.JOINT_" .. boneName:upper(),
        "185.!JOINT_" .. boneName:upper() .. "_2_0"
    }
    
    for _, name in ipairs(boneVariations) do
        -- Method 1: Direct bone access
        if self.agent.bones and self.agent.bones[name] then
            bone = self.agent.bones[name]
            print("PositionLock: Found bone '" .. name .. "' via direct access")
            return bone
        end
        
        -- Method 2: FindBone function
        if self.agent.FindBone then
            bone = self.agent.FindBone(name)
            if bone then
                print("PositionLock: Found bone '" .. name .. "' via FindBone")
                return bone
            end
        end
        
        -- Method 3: bones.Find
        if self.agent.bones and self.agent.bones.Find then
            bone = self.agent.bones.Find(name)
            if bone then
                print("PositionLock: Found bone '" .. name .. "' via bones.Find")
                return bone
            end
        end
    end
    
    print("PositionLock: Could not find bone: " .. boneName)
    return nil
end

function PositionLock:LockToPosition(positionName)
    print("PositionLock: Attempting to lock to position: " .. positionName)
    
    -- Find the micro
    local micro = self:FindMicro()
    if not micro then
        print("PositionLock: No micro found to lock")
        Game.Toast.New().Print("❌ No micro found to lock")
        return false
    end
    
    -- Get position data
    local posData = self.positions[positionName]
    if not posData then
        print("PositionLock: Unknown position: " .. positionName)
        Game.Toast.New().Print("❌ Unknown position: " .. positionName)
        return false
    end
    
    -- Find the target bone
    local bone = self:FindBone(posData.bone)
    if not bone then
        print("PositionLock: Could not find bone: " .. posData.bone)
        Game.Toast.New().Print("❌ Could not find bone: " .. posData.bone)
        return false
    end
    
    -- Calculate scaled offset based on macro size
    local scale = self.agent.scale or 1.0
    local scaledOffset = Vector3.new(
        posData.offset.x * scale,
        posData.offset.y * scale, 
        posData.offset.z * scale
    )
    
    -- Lock the micro to the bone
    print("PositionLock: Locking micro to bone with offset: " .. tostring(scaledOffset))
    
    -- Disable micro physics
    if micro.rigidbody then
        micro.rigidbody.isKinematic = true
        micro.rigidbody.detectCollisions = false
    end
    
    -- Disable micro movement
    if micro.movement then
        micro.movement.enabled = false
    end
    
    -- Set parent to the bone (this is the key mechanism from playas.lua)
    micro.transform.SetParent(bone)
    
    -- Set local position with scaled offset
    micro.transform.localPosition = scaledOffset
    
    -- Keep micro's rotation relative to macro
    micro.transform.localRotation = Quaternion.identity
    
    -- Store lock state
    self.microCharacter = micro
    self.isLocked = true
    self.lockPosition = positionName
    self.lockBone = bone
    self.lockOffset = scaledOffset
    
    print("PositionLock: Successfully locked micro " .. posData.description)
    Game.Toast.New().Print("✅ Locked micro " .. posData.description)
    
    return true
end

function PositionLock:UnlockPosition()
    if not self.isLocked or not self.microCharacter then
        print("PositionLock: No micro currently locked")
        Game.Toast.New().Print("❌ No micro currently locked")
        return false
    end
    
    print("PositionLock: Unlocking micro position")
    
    -- Save current world position before detaching
    local currentPos = self.microCharacter.transform.position
    
    -- Detach from parent bone
    self.microCharacter.transform.SetParent(nil)
    
    -- Restore world position
    self.microCharacter.transform.position = currentPos
    
    -- Re-enable physics
    if self.microCharacter.rigidbody then
        self.microCharacter.rigidbody.isKinematic = false
        self.microCharacter.rigidbody.detectCollisions = true
    end
    
    -- Re-enable movement
    if self.microCharacter.movement then
        self.microCharacter.movement.enabled = true
    end
    
    -- Reset player state if it's the player
    if self.microCharacter.isPlayer and self.microCharacter.player then
        self.microCharacter.player.isGrabbed = false
    end
    
    -- Clear lock state
    self.microCharacter = nil
    self.isLocked = false
    self.lockPosition = "none"
    self.lockBone = nil
    
    print("PositionLock: Successfully unlocked micro")
    Game.Toast.New().Print("✅ Micro position unlocked")
    
    return true
end

function PositionLock:MaintainLock()
    -- Continuously maintain the lock position (like playas.lua does)
    if self.microCharacter and self.lockBone and self.lockOffset then
        -- Ensure micro stays parented to the bone
        if not self.microCharacter.transform.IsChildOf(self.lockBone) then
            self.microCharacter.transform.SetParent(self.lockBone)
        end
        
        -- Maintain the local position
        self.microCharacter.transform.localPosition = self.lockOffset
        
        -- Maintain rotation
        self.microCharacter.transform.localRotation = Quaternion.identity
    end
end

function PositionLock:ProcessChatInput(input)
    local lowerInput = input:lower()
    
    -- Check for position lock commands
    if string.find(lowerInput, "put me between") and string.find(lowerInput, "legs") then
        return self:LockToPosition("between legs")
    elseif string.find(lowerInput, "put me on") and string.find(lowerInput, "chest") then
        return self:LockToPosition("on chest")
    elseif string.find(lowerInput, "put me in") and string.find(lowerInput, "hand") then
        return self:LockToPosition("in hand")
    elseif string.find(lowerInput, "put me on") and string.find(lowerInput, "shoulder") then
        return self:LockToPosition("on shoulder")
    elseif string.find(lowerInput, "unlock") and string.find(lowerInput, "position") then
        return self:UnlockPosition()
    elseif string.find(lowerInput, "release me") or string.find(lowerInput, "let me go") then
        return self:UnlockPosition()
    end
    
    return false
end

-- REQUIRED: Return the behavior class for Sizebox registration
return PositionLock

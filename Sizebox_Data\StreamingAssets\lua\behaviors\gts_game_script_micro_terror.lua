-- Here i'm making a custom reaction to this behavior
-- the micro will be scared for at least 10 seconds
MicroTerror = RegisterBehavior("Terror")
MicroTerror.react = true -- i just mark it as react so is not interrupted by the micro running
MicroTerror.data = {
    hideMenu = true,
    agent = {
        type = { "micro"}, 
        exclude = {"player"}
    },
    target = {
        type = {"oneself"}
    }
}

fearAnim = "Nervously Look Around"

function MicroTerror:Start()
    self.agent.animation.Set(fearAnim)
    self.startTime = Time.time
end

function MicroTerror:Update()
    if Time.time - self.startTime > 10 then
        self.agent.ai.StopBehavior()
    end
end

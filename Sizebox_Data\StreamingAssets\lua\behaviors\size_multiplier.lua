    -- Size Multiplier Script
-- Instantly multiply your size by different factors
-- Press H for help with keybinds

Gizmo = RegisterBehavior("size_multiplier")
Gizmo.data = {
    menuEntry = "Size/Size Multiplier",
    flags = { "grow" },
    agent = {
        type = { "humanoid" }
    },
    target = {
        type = { "oneself" }
    }
}

-- Size settings
sizeHistory = {}         -- History of size changes
maxHistory = 10          -- Maximum number of history entries

function Gizmo:Start()
    self.startScale = self.agent.scale
    table.insert(sizeHistory, self.startScale)
    
    -- Log instructions
    Log("Size Multiplier script started!")
    Log("Press H for help with keybinds")
end

function Gizmo:Update()
    -- Check for key presses
    if Input.GetKeyDown("h") then
        self:ShowHelp()
    end
    
    -- Size multipliers
    if Input.GetKeyDown("1") then
        self:MultiplySize(2)
    elseif Input.GetKeyDown("2") then
        self:MultiplySize(5)
    elseif Input.GetKeyDown("3") then
        self:MultiplySize(10)
    elseif Input.GetKeyDown("4") then
        self:MultiplySize(50)
    elseif Input.GetKeyDown("5") then
        self:MultiplySize(100)
    end
    
    -- Size dividers
    if Input.GetKeyDown("q") then
        self:DivideSize(2)
    elseif Input.GetKeyDown("w") then
        self:DivideSize(5)
    elseif Input.GetKeyDown("e") then
        self:DivideSize(10)
    elseif Input.GetKeyDown("r") then
        self:DivideSize(50)
    elseif Input.GetKeyDown("t") then
        self:DivideSize(100)
    end
    
    -- Reset size
    if Input.GetKeyDown("0") then
        self:ResetSize()
    end
    
    -- Undo last size change
    if Input.GetKeyDown("z") then
        self:UndoSizeChange()
    end
end

function Gizmo:ShowHelp()
    Log("=== SIZE MULTIPLIER KEYBINDS ===")
    Log("H: Show this help")
    Log("1-5: Multiply size (1=2x, 2=5x, 3=10x, 4=50x, 5=100x)")
    Log("Q-T: Divide size (Q=2x, W=5x, E=10x, R=50x, T=100x)")
    Log("0: Reset to starting size")
    Log("Z: Undo last size change")
    Log("Current size: " .. string.format("%.2f", self.agent.scale) .. "x")
    Log("Starting size: " .. string.format("%.2f", self.startScale) .. "x")
    Log("============================")
end

function Gizmo:MultiplySize(factor)
    -- Calculate new size
    local newSize = self.agent.scale * factor
    
    -- Ensure we don't exceed max size
    if newSize > gts.maxSize then
        newSize = gts.maxSize
    end
    
    -- Apply size change
    self.agent.scale = newSize
    
    -- Add to history
    self:AddToHistory(newSize)
    
    -- Log change
    Log("Size multiplied by " .. factor .. "x")
    Log("New size: " .. string.format("%.2f", newSize) .. "x")
end

function Gizmo:DivideSize(factor)
    -- Calculate new size
    local newSize = self.agent.scale / factor
    
    -- Ensure we don't go below min size
    if newSize < gts.minSize then
        newSize = gts.minSize
    end
    
    -- Apply size change
    self.agent.scale = newSize
    
    -- Add to history
    self:AddToHistory(newSize)
    
    -- Log change
    Log("Size divided by " .. factor .. "x")
    Log("New size: " .. string.format("%.2f", newSize) .. "x")
end

function Gizmo:ResetSize()
    self.agent.scale = self.startScale
    
    -- Add to history
    self:AddToHistory(self.startScale)
    
    Log("Size reset to starting value: " .. string.format("%.2f", self.startScale) .. "x")
end

function Gizmo:UndoSizeChange()
    if #sizeHistory > 1 then
        -- Remove current size from history
        table.remove(sizeHistory)
        
        -- Get previous size
        local previousSize = sizeHistory[#sizeHistory]
        
        -- Apply previous size
        self.agent.scale = previousSize
        
        Log("Size change undone. Current size: " .. string.format("%.2f", previousSize) .. "x")
    else
        Log("No size changes to undo!")
    end
end

function Gizmo:AddToHistory(size)
    table.insert(sizeHistory, size)
    
    -- Trim history if it gets too long
    if #sizeHistory > maxHistory then
        table.remove(sizeHistory, 1)
    end
end
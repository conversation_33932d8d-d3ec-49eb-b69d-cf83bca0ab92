<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.Game Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_game.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="class_lua_1_1_game-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.Game Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>A collection of Sizebox specific functions that don't belong to any object.  
 <a href="class_lua_1_1_game.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="nested-classes" name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_game_1_1_toast.html">Toast</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Toasts notifications act similar to a volume control interface on a modern television or operating systems where you can update the information inside the existing notification while it's still being displayed. However unlike those interfaces, multiple toast notifications can be displayed and updated at the same time. This can be useful when you want to debug a script or notify a user about something without interrupting game play. A <a class="el" href="class_lua_1_1_game_1_1_toast.html" title="Toasts notifications act similar to a volume control interface on a modern television or operating sy...">Toast</a> notification will stay on the screen for 5 seconds after its last message.  <a href="class_lua_1_1_game_1_1_toast.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_game_1_1_version.html">Version</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">The version of Sizebox the script is being run under. Your script can use the Major and Minor numbers to take different paths for different versions of Sizebox allowing custom scripts to be more portable  <a href="class_lua_1_1_game_1_1_version.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-static-methods" name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:a105ff522a63e5c561359adb9a49c124f"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_game.html#a105ff522a63e5c561359adb9a49c124f">GetLocalSelection</a> ()</td></tr>
<tr class="memdesc:a105ff522a63e5c561359adb9a49c124f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get an entity the player currently has selected.  <a href="class_lua_1_1_game.html#a105ff522a63e5c561359adb9a49c124f">More...</a><br /></td></tr>
<tr class="separator:a105ff522a63e5c561359adb9a49c124f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9b67e06c39b502f973d3eb4878591c5b"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_game.html#a9b67e06c39b502f973d3eb4878591c5b">GetLocalPlayer</a> ()</td></tr>
<tr class="memdesc:a9b67e06c39b502f973d3eb4878591c5b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the entity the user of this machine is currently playing as  <a href="class_lua_1_1_game.html#a9b67e06c39b502f973d3eb4878591c5b">More...</a><br /></td></tr>
<tr class="separator:a9b67e06c39b502f973d3eb4878591c5b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9223af639759f362907b7c5041123e58"><td class="memItemLeft" align="right" valign="top">static IList&lt; <a class="el" href="class_lua_1_1_entity.html">Entity</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_game.html#a9223af639759f362907b7c5041123e58">GetLocalSelections</a> ()</td></tr>
<tr class="memdesc:a9223af639759f362907b7c5041123e58"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get all the entities the player currently has selected.  <a href="class_lua_1_1_game.html#a9223af639759f362907b7c5041123e58">More...</a><br /></td></tr>
<tr class="separator:a9223af639759f362907b7c5041123e58"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad5f0d2e71e75f5ed3c9e77ef657deb8c"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_player.html">Player</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_game.html#ad5f0d2e71e75f5ed3c9e77ef657deb8c">GetLocalPlayerSettings</a> ()</td></tr>
<tr class="memdesc:ad5f0d2e71e75f5ed3c9e77ef657deb8c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the player the user of this machine is currently playing as  <a href="class_lua_1_1_game.html#ad5f0d2e71e75f5ed3c9e77ef657deb8c">More...</a><br /></td></tr>
<tr class="separator:ad5f0d2e71e75f5ed3c9e77ef657deb8c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a02a20189930a05adae52e4243a3179db"><td class="memItemLeft" align="right" valign="top">static void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_game.html#a02a20189930a05adae52e4243a3179db">Message</a> (string message, string title, bool log=true)</td></tr>
<tr class="memdesc:a02a20189930a05adae52e4243a3179db"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a popup message box on the screen displaying a message as well as logging the message and a "OK" button. This is useful for informing the user about a error when starting your script but shouldn't be used as a general logger nor should it be frequently called.  <a href="class_lua_1_1_game.html#a02a20189930a05adae52e4243a3179db">More...</a><br /></td></tr>
<tr class="separator:a02a20189930a05adae52e4243a3179db"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >A collection of Sizebox specific functions that don't belong to any object. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a9b67e06c39b502f973d3eb4878591c5b" name="a9b67e06c39b502f973d3eb4878591c5b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9b67e06c39b502f973d3eb4878591c5b">&#9670;&nbsp;</a></span>GetLocalPlayer()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="class_lua_1_1_entity.html">Entity</a> Lua.Game.GetLocalPlayer </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the entity the user of this machine is currently playing as </p>
<dl class="section return"><dt>Returns</dt><dd>The entity of the player or nil if the user isn't possessing any entity right now.</dd></dl>
<p >It's best practise to combine this in a function called by the <a class="el" href="class_event_code.html#aa0bed09744a5043231ce30c55d7d70ac" title="User switched the entity they are playing as.">EventCode.OnLocalPlayerChanged</a> event as the players entity could change on any frame.</p>

</div>
</div>
<a id="ad5f0d2e71e75f5ed3c9e77ef657deb8c" name="ad5f0d2e71e75f5ed3c9e77ef657deb8c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad5f0d2e71e75f5ed3c9e77ef657deb8c">&#9670;&nbsp;</a></span>GetLocalPlayerSettings()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="class_lua_1_1_player.html">Player</a> Lua.Game.GetLocalPlayerSettings </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the player the user of this machine is currently playing as </p>
<dl class="section return"><dt>Returns</dt><dd>The setting of the player on this machine or nil if there's no player.</dd></dl>

</div>
</div>
<a id="a105ff522a63e5c561359adb9a49c124f" name="a105ff522a63e5c561359adb9a49c124f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a105ff522a63e5c561359adb9a49c124f">&#9670;&nbsp;</a></span>GetLocalSelection()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="class_lua_1_1_entity.html">Entity</a> Lua.Game.GetLocalSelection </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get an entity the player currently has selected. </p>
<dl class="section return"><dt>Returns</dt><dd>A selected entity or nil if there is no selection.</dd></dl>
<p >It's best practise to combine this with <a class="el" href="class_event_code.html#ae742599b4e26effaa66be61f267fa9d0" title="User switch the entity selection.">EventCode.OnLocalSelectionChanged</a> event as the select entity could change on any frame. The entity returned in a multi-select is undefined and could be effectively used as a means of randomization</p>

</div>
</div>
<a id="a9223af639759f362907b7c5041123e58" name="a9223af639759f362907b7c5041123e58"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9223af639759f362907b7c5041123e58">&#9670;&nbsp;</a></span>GetLocalSelections()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static IList&lt; <a class="el" href="class_lua_1_1_entity.html">Entity</a> &gt; Lua.Game.GetLocalSelections </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get all the entities the player currently has selected. </p>
<dl class="section return"><dt>Returns</dt><dd>A list of selected entities or nil if there is no selection.</dd></dl>

</div>
</div>
<a id="a02a20189930a05adae52e4243a3179db" name="a02a20189930a05adae52e4243a3179db"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a02a20189930a05adae52e4243a3179db">&#9670;&nbsp;</a></span>Message()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static void Lua.Game.Message </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>message</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>title</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>log</em> = <code>true</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Creates a popup message box on the screen displaying a message as well as logging the message and a "OK" button. This is useful for informing the user about a error when starting your script but shouldn't be used as a general logger nor should it be frequently called. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">message</td><td>The message to put into the main body</td></tr>
    <tr><td class="paramname">title</td><td>The title at the top of the box</td></tr>
    <tr><td class="paramname">log</td><td>Whether to log this message</td></tr>
  </table>
  </dd>
</dl>
<p >A message box should only be used when your script has run into an error and can't continue. To prevent spam, a message box will not appear if it is called too frequently or has the same message as the last. In this case the message is still visible in the log. If you would like to notify the user about something that isn't a script error then you should consider using <a class="el" href="class_lua_1_1_game_1_1_toast.html" title="Toasts notifications act similar to a volume control interface on a modern television or operating sy...">Game.Toast</a> instead.</p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaGame.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_game.html">Game</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

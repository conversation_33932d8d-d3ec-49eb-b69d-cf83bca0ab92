-- SFX Extender Behavior
-- Stretches and plays a sound effect by lowering pitch

SFXExtender = RegisterBehavior("SFX Extender")
SFXExtender.data = {
    menuEntry = "Audio/SFX Extender",
    agent = { type = { "humanoid" } },
    target = { type = { "oneself" } },
    settings = {
        { "sfxPitch", "Pitch (Stretch)", "float", 1.0 },
        { "sfxPath", "Sound File Name", "string", "grow.ogg" },
        { "pitchStep", "Pitch Change Step", "float", 0.1 },
        { "minPitch", "Minimum Pitch", "float", 0.1 },
        { "maxPitch", "Maximum Pitch", "float", 3.0 },
        { "playbackSpeed", "Playback Speed", "float", 1.0 },
        { "loopOverlap", "Loop Overlap (seconds)", "float", 0.2 }
    }
}

function SFXExtender:Start()
    -- Create AudioSource attached to the agent's spine bone
    self.audio_source = AudioSource:new(self.agent.bones.spine)
    
    -- Configure audio properties
    self.audio_source.spatialBlend = 1  -- 3D audio
    self.audio_source.loop = true       -- Loop for continuous stretching
    self.audio_source.volume = 1.0      -- Full volume
    self.audio_source.pitch = self.sfxPitch  -- Set pitch for stretching effect
    self.audio_source.clip = self.sfxPath    -- Set the sound file
    
    -- Store current values for real-time adjustment
    self.currentPitch = self.sfxPitch
    self.currentSpeed = self.playbackSpeed
    self.isExtended = false
    self.extensionTimer = 0
    self.originalClipLength = 1.0  -- Will be updated when clip loads
    
    -- Play the sound
    self.audio_source:Play()
    
    print("[SFX Extender] Playing sound:", self.sfxPath, "at pitch:", self.sfxPitch)
    print("[SFX Extender] Controls:")
    print("  E = Lower pitch (longer/deeper)")
    print("  Q = Higher pitch (shorter/higher)")
    print("  R = Toggle time extension (same pitch, longer duration)")
end

function SFXExtender:Update()
    -- Check for key presses to adjust pitch in real-time
    if Input.GetKeyDown("e") then  -- E key (longer/lower pitch)
        self.currentPitch = math.max(self.minPitch, self.currentPitch - self.pitchStep)
        self.audio_source.pitch = self.currentPitch
        print("[SFX Extender] Pitch decreased to:", string.format("%.2f", self.currentPitch), "(longer duration)")
    elseif Input.GetKeyDown("q") then  -- Q key (shorter/higher pitch)
        self.currentPitch = math.min(self.maxPitch, self.currentPitch + self.pitchStep)
        self.audio_source.pitch = self.currentPitch
        print("[SFX Extender] Pitch increased to:", string.format("%.2f", self.currentPitch), "(shorter duration)")
    elseif Input.GetKeyDown("r") then  -- R key (toggle time extension)
        self:ToggleTimeExtension()
    end
    
    -- Handle time extension logic
    if self.isExtended then
        self:UpdateTimeExtension()
    end
end

function SFXExtender:ToggleTimeExtension()
    self.isExtended = not self.isExtended
    
    if self.isExtended then
        -- Start time extension - keep same pitch but extend duration
        print("[SFX Extender] Time extension ON - same pitch, extended duration")
        self.audio_source.loop = true
        -- Reset pitch to original to maintain same sound
        self.audio_source.pitch = self.sfxPitch
        self.currentPitch = self.sfxPitch
    else
        -- Stop time extension
        print("[SFX Extender] Time extension OFF - normal playback")
        self.audio_source.loop = true  -- Keep looping for pitch adjustments
    end
end

function SFXExtender:UpdateTimeExtension()
    -- This creates a seamless extension by managing the loop timing
    -- The audio continues at the same pitch but loops to extend duration
    
    -- Optional: Add subtle volume modulation to make looping less noticeable
    local time = Time.time
    local volumeModulation = 0.95 + 0.05 * math.sin(time * 0.5)  -- Subtle volume variation
    self.audio_source.volume = volumeModulation
end

function SFXExtender:End()
    if self.audio_source then
        self.audio_source:Stop()
        self.audio_source = nil
    end
end

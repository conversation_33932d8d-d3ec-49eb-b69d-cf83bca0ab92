<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_mathf.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle"><div class="title">Lua.Mathf Member List</div></div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#a321380853cd01074ad91e673a7071c99">Abs</a>(float f)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#a33b558ad9dabaee4792399525a89e4ce">Acos</a>(float f)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#a2a53d9e05a8d05eaa05d1fced26906e6">Approximately</a>(float a, float b)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#a85731b3f55f246a46df2a2f40bc87ae7">Asin</a>(float f)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#a03740fc6f71760901fbe5f8a6295edeb">Atan</a>(float f)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#ab7f3034dcb7244d1cfb06ae17f014278">Atan2</a>(float y, float x)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#aa17eeb105797860b39d5765c5f4a8929">Ceil</a>(float f)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#ad734e258b7adf07ed4a34557d80f0122">Clamp</a>(float value, float min, float max)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#a8302fdbff60f945480e559d3f97474d5">Clamp01</a>(float value)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#ae78395f9919d38bd29ae567f6f1aac3e">ClosestPowerOfTwo</a>(int value)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#ab274b83f44ab3e9457e67f949097bc27">ConvertToMeter</a>(string measurement)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#a45b9fc3bad2de4240be1352cc4fa7bca">ConvertToMeter</a>(float measurement, string unit)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#a9bb7f6cc54371f64edaf224c4a5365b6">Cos</a>(float f)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#a5f003a3aab6299095b301066d0af6eab">Deg2Rad</a></td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#aad5844ff29ce4955f86e72b701cf871b">DeltaAngle</a>(float current, float target)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#adf551e91a5f4ff23ff24de2b64cdad15">DistanceToString</a>(float distance)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#a121e67b35c4d96893e79a5be089ebc8a">Epsilon</a></td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#a2bde76bb17351b51bc55e2d65f8e9263">Exp</a>(float power)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#a72fc411403ab2b7e87ffd6e3989bc9e4">Floor</a>(float f)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#ac3686fdccff0df0a6c797af8ba4722b1">Infinity</a></td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#a4344694ab95eb4dc13046b0798a88ff3">InverseLerp</a>(float a, float b, float value)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#a44aca0d32ffbf1de0925f05b65d94032">IsPowerOfTwo</a>(int value)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#a659f0bf0690e5056165eb8bd958d6751">Lerp</a>(float a, float b, float t)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#a2363a79cc48061f10c4e7e1b47df2538">LerpAngle</a>(float a, float b, float t)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#a2707664a0c93b38cece4445ee6750709">LerpUnclamped</a>(float a, float b, float t)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#a9d1e276c7cfc8fe9f902ebda005e04e1">Log</a>(float f, float p)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#afcdb61fd1acfbe37c5e7a675421f3dc9">Log10</a>(float f)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#ae0c3619a26a60fdc6442011911e0e412">Max</a>(float a, float b)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#ab683c60c60e63553655e8727a55f3e61">Max</a>(params float[] values)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#acc164cac8453f2551303265e346f4969">Min</a>(float a, float b)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#a1d580ef078dacb928fb6df039df2281b">Min</a>(params float[] values)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#a0b9df3fa414f0b12c2fcfd1eee83570c">MoveTowards</a>(float current, float target, float maxDelta)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#a0f4a13d448b3d1a96f3aaff159d6636b">NegativeInfinity</a></td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#abc78356d294242d7d4f10dc5e3e81a81">NextPowerOfTwo</a>(int value)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#a3044ff5b1dd835169520fd054c713d63">PerlinNoise</a>(float x, float y)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#a408b4fa7c06dd48e2aa0d6fcde7adedc">PI</a></td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#a8eed89df943f9dc0df1398e541e023ad">PingPong</a>(float t, float length)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#ace6b91fa037354fa541a5de450ba6e23">Pow</a>(float f, float p)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#aed19ee907a834cbea518af347c4f39d7">Rad2Deg</a></td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#abf1f882dcf08b3749a27a28f6f7f3630">Repeat</a>(float t, float length)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#a9f6511ccc1da8fd5228959f67b995d91">Round</a>(float f)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#ae5d3654f321079c30baaf42bdf226d89">Sign</a>(float f)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#a23fb3f1fdbc09b29120c653bb184fb96">Sin</a>(float f)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#afee5fd526c03b9d4f8f5e6fc978e04b8">SmoothStep</a>(float from, float to, float t)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#aa20bfd858dc30b96f3fb7a5033c4b6bf">Sqrt</a>(float f)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_mathf.html#aefc20a4773c6e7ce2f493974dbdf27c1">Tan</a>(float f)</td><td class="entry"><a class="el" href="class_lua_1_1_mathf.html">Lua.Mathf</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
</table></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

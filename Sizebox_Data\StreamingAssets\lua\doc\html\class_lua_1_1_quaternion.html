<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.Quaternion Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_quaternion.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="class_lua_1_1_quaternion-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.Quaternion Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Quaternions are used to represent rotations.  
 <a href="class_lua_1_1_quaternion.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a72a4b219c3442e088c7ee963feb1b372"><td class="memItemLeft" align="right" valign="top"><a id="a72a4b219c3442e088c7ee963feb1b372" name="a72a4b219c3442e088c7ee963feb1b372"></a>
override string&#160;</td><td class="memItemRight" valign="bottom"><b>ToString</b> ()</td></tr>
<tr class="memdesc:a72a4b219c3442e088c7ee963feb1b372"><td class="mdescLeft">&#160;</td><td class="mdescRight">String representation of this quaternion. <br /></td></tr>
<tr class="separator:a72a4b219c3442e088c7ee963feb1b372"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0cb037e26892fa50a048fc751f7f017a"><td class="memItemLeft" align="right" valign="top"><a id="a0cb037e26892fa50a048fc751f7f017a" name="a0cb037e26892fa50a048fc751f7f017a"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>Set</b> (float <a class="el" href="class_lua_1_1_quaternion.html#aefb405b7fafa79708a6d8120781debce">x</a>, float <a class="el" href="class_lua_1_1_quaternion.html#ab7eb002a81cfc537a9c3afc8965ef2ec">y</a>, float <a class="el" href="class_lua_1_1_quaternion.html#ac26c0a2710dd86783dee62c8645ee55c">z</a>, float <a class="el" href="class_lua_1_1_quaternion.html#a4a66f5c598907b4d906b0c5dd0e28526">w</a>)</td></tr>
<tr class="memdesc:a0cb037e26892fa50a048fc751f7f017a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set x, y, z and w components of an existing <a class="el" href="class_lua_1_1_quaternion.html" title="Quaternions are used to represent rotations.">Quaternion</a>. <br /></td></tr>
<tr class="separator:a0cb037e26892fa50a048fc751f7f017a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa7991983472a41d5d7c1b7cb61eb5580"><td class="memItemLeft" align="right" valign="top"><a id="aa7991983472a41d5d7c1b7cb61eb5580" name="aa7991983472a41d5d7c1b7cb61eb5580"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>SetFromToRotation</b> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> fromDirection, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> toDirection)</td></tr>
<tr class="memdesc:aa7991983472a41d5d7c1b7cb61eb5580"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a rotation which rotates from fromDirection to toDirection. <br /></td></tr>
<tr class="separator:aa7991983472a41d5d7c1b7cb61eb5580"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aab942db162b73214d4b563bd6a49bad4"><td class="memItemLeft" align="right" valign="top"><a id="aab942db162b73214d4b563bd6a49bad4" name="aab942db162b73214d4b563bd6a49bad4"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>SetLookRotation</b> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> view)</td></tr>
<tr class="memdesc:aab942db162b73214d4b563bd6a49bad4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a rotation with the specified forward and upwards directions. <br /></td></tr>
<tr class="separator:aab942db162b73214d4b563bd6a49bad4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a13062d4cdfe3635c74f2e57c893b6e2f"><td class="memItemLeft" align="right" valign="top"><a id="a13062d4cdfe3635c74f2e57c893b6e2f" name="a13062d4cdfe3635c74f2e57c893b6e2f"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>SetLookRotation</b> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> view, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> up)</td></tr>
<tr class="memdesc:a13062d4cdfe3635c74f2e57c893b6e2f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a rotation with the specified forward and upwards directions. <br /></td></tr>
<tr class="separator:a13062d4cdfe3635c74f2e57c893b6e2f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-static-methods" name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:a39cfba79d11687138569de7edd9e2727"><td class="memItemLeft" align="right" valign="top"><a id="a39cfba79d11687138569de7edd9e2727" name="a39cfba79d11687138569de7edd9e2727"></a>
static <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td><td class="memItemRight" valign="bottom"><b>New</b> (float <a class="el" href="class_lua_1_1_quaternion.html#aefb405b7fafa79708a6d8120781debce">x</a>, float <a class="el" href="class_lua_1_1_quaternion.html#ab7eb002a81cfc537a9c3afc8965ef2ec">y</a>, float <a class="el" href="class_lua_1_1_quaternion.html#ac26c0a2710dd86783dee62c8645ee55c">z</a>, float <a class="el" href="class_lua_1_1_quaternion.html#a4a66f5c598907b4d906b0c5dd0e28526">w</a>)</td></tr>
<tr class="memdesc:a39cfba79d11687138569de7edd9e2727"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructs new <a class="el" href="class_lua_1_1_quaternion.html" title="Quaternions are used to represent rotations.">Quaternion</a> with given x,y,z,w components. <br /></td></tr>
<tr class="separator:a39cfba79d11687138569de7edd9e2727"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac70013482fc53c72c664561d67d5d677"><td class="memItemLeft" align="right" valign="top">static string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_quaternion.html#ac70013482fc53c72c664561d67d5d677">Concat</a> (<a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> o, string s)</td></tr>
<tr class="memdesc:ac70013482fc53c72c664561d67d5d677"><td class="mdescLeft">&#160;</td><td class="mdescRight">Concatenates a quaternion and a string.  <a href="class_lua_1_1_quaternion.html#ac70013482fc53c72c664561d67d5d677">More...</a><br /></td></tr>
<tr class="separator:ac70013482fc53c72c664561d67d5d677"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9eab1ab6fc89906ecc0b8159c1429a04"><td class="memItemLeft" align="right" valign="top">static string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_quaternion.html#a9eab1ab6fc89906ecc0b8159c1429a04">Concat</a> (string s, <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> o)</td></tr>
<tr class="memdesc:a9eab1ab6fc89906ecc0b8159c1429a04"><td class="mdescLeft">&#160;</td><td class="mdescRight">Concatenates a string and a quaternion.  <a href="class_lua_1_1_quaternion.html#a9eab1ab6fc89906ecc0b8159c1429a04">More...</a><br /></td></tr>
<tr class="separator:a9eab1ab6fc89906ecc0b8159c1429a04"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aef26c0eb5e338baa7dc1a87530b360d1"><td class="memItemLeft" align="right" valign="top">static string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_quaternion.html#aef26c0eb5e338baa7dc1a87530b360d1">Concat</a> (<a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> o1, <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> o2)</td></tr>
<tr class="memdesc:aef26c0eb5e338baa7dc1a87530b360d1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Concatenates 2 quaternions.  <a href="class_lua_1_1_quaternion.html#aef26c0eb5e338baa7dc1a87530b360d1">More...</a><br /></td></tr>
<tr class="separator:aef26c0eb5e338baa7dc1a87530b360d1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9ecf171f4a2a8c0ad3ced7564ada2c6d"><td class="memItemLeft" align="right" valign="top">static bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_quaternion.html#a9ecf171f4a2a8c0ad3ced7564ada2c6d">Eq</a> (<a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> o1, <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> o2)</td></tr>
<tr class="memdesc:a9ecf171f4a2a8c0ad3ced7564ada2c6d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tests 2 quaternions for equality.  <a href="class_lua_1_1_quaternion.html#a9ecf171f4a2a8c0ad3ced7564ada2c6d">More...</a><br /></td></tr>
<tr class="separator:a9ecf171f4a2a8c0ad3ced7564ada2c6d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac0e2a30dacbe7805913969ca02cc1e6b"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_quaternion.html#ac0e2a30dacbe7805913969ca02cc1e6b">operator*</a> (<a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> lhs, <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> rhs)</td></tr>
<tr class="memdesc:ac0e2a30dacbe7805913969ca02cc1e6b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Combines rotations lhs and rhs.  <a href="class_lua_1_1_quaternion.html#ac0e2a30dacbe7805913969ca02cc1e6b">More...</a><br /></td></tr>
<tr class="separator:ac0e2a30dacbe7805913969ca02cc1e6b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2e8753f1c8b29cbfb49f4aef2df009c8"><td class="memItemLeft" align="right" valign="top"><a id="a2e8753f1c8b29cbfb49f4aef2df009c8" name="a2e8753f1c8b29cbfb49f4aef2df009c8"></a>
static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><b>operator*</b> (<a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> rotation, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> vector)</td></tr>
<tr class="memdesc:a2e8753f1c8b29cbfb49f4aef2df009c8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Rotates a vector with rotation. <br /></td></tr>
<tr class="separator:a2e8753f1c8b29cbfb49f4aef2df009c8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4094309a8c66c50017bd92ffdfeb3bfd"><td class="memItemLeft" align="right" valign="top"><a id="a4094309a8c66c50017bd92ffdfeb3bfd" name="a4094309a8c66c50017bd92ffdfeb3bfd"></a>
static float&#160;</td><td class="memItemRight" valign="bottom"><b>Angle</b> (<a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> a, <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> b)</td></tr>
<tr class="memdesc:a4094309a8c66c50017bd92ffdfeb3bfd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the angle in degrees between two rotations a and b. <br /></td></tr>
<tr class="separator:a4094309a8c66c50017bd92ffdfeb3bfd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a25f65fcc019124366264558209108498"><td class="memItemLeft" align="right" valign="top"><a id="a25f65fcc019124366264558209108498" name="a25f65fcc019124366264558209108498"></a>
static <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td><td class="memItemRight" valign="bottom"><b>AngleAxis</b> (float angle, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> axis)</td></tr>
<tr class="memdesc:a25f65fcc019124366264558209108498"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a rotation which rotates angle degrees around axis. <br /></td></tr>
<tr class="separator:a25f65fcc019124366264558209108498"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8479fc724c544d8784afeae5778e6a27"><td class="memItemLeft" align="right" valign="top"><a id="a8479fc724c544d8784afeae5778e6a27" name="a8479fc724c544d8784afeae5778e6a27"></a>
static float&#160;</td><td class="memItemRight" valign="bottom"><b>Dot</b> (<a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> a, <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> b)</td></tr>
<tr class="memdesc:a8479fc724c544d8784afeae5778e6a27"><td class="mdescLeft">&#160;</td><td class="mdescRight">The dot product between two rotations. <br /></td></tr>
<tr class="separator:a8479fc724c544d8784afeae5778e6a27"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7134c2bdc28902fc519d42b7b803d9f"><td class="memItemLeft" align="right" valign="top"><a id="ac7134c2bdc28902fc519d42b7b803d9f" name="ac7134c2bdc28902fc519d42b7b803d9f"></a>
static <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td><td class="memItemRight" valign="bottom"><b>Euler</b> (float <a class="el" href="class_lua_1_1_quaternion.html#aefb405b7fafa79708a6d8120781debce">x</a>, float <a class="el" href="class_lua_1_1_quaternion.html#ab7eb002a81cfc537a9c3afc8965ef2ec">y</a>, float <a class="el" href="class_lua_1_1_quaternion.html#ac26c0a2710dd86783dee62c8645ee55c">z</a>)</td></tr>
<tr class="memdesc:ac7134c2bdc28902fc519d42b7b803d9f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a rotation that rotates z degrees around the z axis, x degrees around the x axis, and y degrees around the y axis (in that order). <br /></td></tr>
<tr class="separator:ac7134c2bdc28902fc519d42b7b803d9f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a275393364a6f1475566b7bfe49dad51e"><td class="memItemLeft" align="right" valign="top"><a id="a275393364a6f1475566b7bfe49dad51e" name="a275393364a6f1475566b7bfe49dad51e"></a>
static <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td><td class="memItemRight" valign="bottom"><b>Euler</b> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> euler)</td></tr>
<tr class="memdesc:a275393364a6f1475566b7bfe49dad51e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a rotation that rotates z degrees around the z axis, x degrees around the x axis, and y degrees around the y axis (in that order). <br /></td></tr>
<tr class="separator:a275393364a6f1475566b7bfe49dad51e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a78b8152d55e05c4b35a74ed09eae9d41"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_quaternion.html#a78b8152d55e05c4b35a74ed09eae9d41">FromToRotation</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> fromDirection, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> toDirection)</td></tr>
<tr class="memdesc:a78b8152d55e05c4b35a74ed09eae9d41"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a rotation which rotates from fromDirection to toDirection.  <a href="class_lua_1_1_quaternion.html#a78b8152d55e05c4b35a74ed09eae9d41">More...</a><br /></td></tr>
<tr class="separator:a78b8152d55e05c4b35a74ed09eae9d41"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a324d82496815f927ebcaa21032843276"><td class="memItemLeft" align="right" valign="top"><a id="a324d82496815f927ebcaa21032843276" name="a324d82496815f927ebcaa21032843276"></a>
static <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td><td class="memItemRight" valign="bottom"><b>Inverse</b> (<a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> rotation)</td></tr>
<tr class="memdesc:a324d82496815f927ebcaa21032843276"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the Inverse of rotation. <br /></td></tr>
<tr class="separator:a324d82496815f927ebcaa21032843276"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a451a68530c7d148d83024edf4bb79e26"><td class="memItemLeft" align="right" valign="top"><a id="a451a68530c7d148d83024edf4bb79e26" name="a451a68530c7d148d83024edf4bb79e26"></a>
static <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td><td class="memItemRight" valign="bottom"><b>Lerp</b> (<a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> a, <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> b, float t)</td></tr>
<tr class="memdesc:a451a68530c7d148d83024edf4bb79e26"><td class="mdescLeft">&#160;</td><td class="mdescRight">Interpolates between a and b by t and normalizes the result afterwards. The parameter t is clamped to the range [0, 1]. <br /></td></tr>
<tr class="separator:a451a68530c7d148d83024edf4bb79e26"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad43d2f3aa2d460ed567351f97aba6bfe"><td class="memItemLeft" align="right" valign="top"><a id="ad43d2f3aa2d460ed567351f97aba6bfe" name="ad43d2f3aa2d460ed567351f97aba6bfe"></a>
static <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td><td class="memItemRight" valign="bottom"><b>LerpUnclamped</b> (<a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> a, <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> b, float t)</td></tr>
<tr class="memdesc:ad43d2f3aa2d460ed567351f97aba6bfe"><td class="mdescLeft">&#160;</td><td class="mdescRight">Interpolates between a and b by t and normalizes the result afterwards. The parameter t is not clamped. <br /></td></tr>
<tr class="separator:ad43d2f3aa2d460ed567351f97aba6bfe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7a17f92fd9d83a8b472db78d5cb74642"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_quaternion.html#a7a17f92fd9d83a8b472db78d5cb74642">LookRotation</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> forward)</td></tr>
<tr class="memdesc:a7a17f92fd9d83a8b472db78d5cb74642"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a rotation with the specified forward and upwards directions.  <a href="class_lua_1_1_quaternion.html#a7a17f92fd9d83a8b472db78d5cb74642">More...</a><br /></td></tr>
<tr class="separator:a7a17f92fd9d83a8b472db78d5cb74642"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afa69beaa5748b6c941b9a3296d6c0638"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_quaternion.html#afa69beaa5748b6c941b9a3296d6c0638">LookRotation</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> forward, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> upwards)</td></tr>
<tr class="memdesc:afa69beaa5748b6c941b9a3296d6c0638"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a rotation with the specified forward and upwards directions.  <a href="class_lua_1_1_quaternion.html#afa69beaa5748b6c941b9a3296d6c0638">More...</a><br /></td></tr>
<tr class="separator:afa69beaa5748b6c941b9a3296d6c0638"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abfa084dd85c43e01da75d9bcf3305871"><td class="memItemLeft" align="right" valign="top"><a id="abfa084dd85c43e01da75d9bcf3305871" name="abfa084dd85c43e01da75d9bcf3305871"></a>
static <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td><td class="memItemRight" valign="bottom"><b>RotateTowards</b> (<a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> from, <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> to, float maxDegreesDelta)</td></tr>
<tr class="memdesc:abfa084dd85c43e01da75d9bcf3305871"><td class="mdescLeft">&#160;</td><td class="mdescRight">Rotates a rotation from towards to. <br /></td></tr>
<tr class="separator:abfa084dd85c43e01da75d9bcf3305871"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8848906e3924791706bc0bc853a4572b"><td class="memItemLeft" align="right" valign="top"><a id="a8848906e3924791706bc0bc853a4572b" name="a8848906e3924791706bc0bc853a4572b"></a>
static <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td><td class="memItemRight" valign="bottom"><b>Slerp</b> (<a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> a, <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> b, float t)</td></tr>
<tr class="memdesc:a8848906e3924791706bc0bc853a4572b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Spherically interpolates between a and b by t. The parameter t is clamped to the range [0, 1]. <br /></td></tr>
<tr class="separator:a8848906e3924791706bc0bc853a4572b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5056ad858477a4b81765167a068d5379"><td class="memItemLeft" align="right" valign="top"><a id="a5056ad858477a4b81765167a068d5379" name="a5056ad858477a4b81765167a068d5379"></a>
static <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td><td class="memItemRight" valign="bottom"><b>SlerpUnclamped</b> (<a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> a, <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> b, float t)</td></tr>
<tr class="memdesc:a5056ad858477a4b81765167a068d5379"><td class="mdescLeft">&#160;</td><td class="mdescRight">Spherically interpolates between a and b by t. The parameter t is not clamped. <br /></td></tr>
<tr class="separator:a5056ad858477a4b81765167a068d5379"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="properties" name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a4a66f5c598907b4d906b0c5dd0e28526"><td class="memItemLeft" align="right" valign="top"><a id="a4a66f5c598907b4d906b0c5dd0e28526" name="a4a66f5c598907b4d906b0c5dd0e28526"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><b>w</b><code> [get, set]</code></td></tr>
<tr class="memdesc:a4a66f5c598907b4d906b0c5dd0e28526"><td class="mdescLeft">&#160;</td><td class="mdescRight">W component of the <a class="el" href="class_lua_1_1_quaternion.html" title="Quaternions are used to represent rotations.">Quaternion</a>. Don't modify this directly unless you know quaternions inside out. <br /></td></tr>
<tr class="separator:a4a66f5c598907b4d906b0c5dd0e28526"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aefb405b7fafa79708a6d8120781debce"><td class="memItemLeft" align="right" valign="top"><a id="aefb405b7fafa79708a6d8120781debce" name="aefb405b7fafa79708a6d8120781debce"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><b>x</b><code> [get, set]</code></td></tr>
<tr class="memdesc:aefb405b7fafa79708a6d8120781debce"><td class="mdescLeft">&#160;</td><td class="mdescRight">X component of the <a class="el" href="class_lua_1_1_quaternion.html" title="Quaternions are used to represent rotations.">Quaternion</a>. Don't modify this directly unless you know quaternions inside out. <br /></td></tr>
<tr class="separator:aefb405b7fafa79708a6d8120781debce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab7eb002a81cfc537a9c3afc8965ef2ec"><td class="memItemLeft" align="right" valign="top"><a id="ab7eb002a81cfc537a9c3afc8965ef2ec" name="ab7eb002a81cfc537a9c3afc8965ef2ec"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><b>y</b><code> [get, set]</code></td></tr>
<tr class="memdesc:ab7eb002a81cfc537a9c3afc8965ef2ec"><td class="mdescLeft">&#160;</td><td class="mdescRight">Y component of the <a class="el" href="class_lua_1_1_quaternion.html" title="Quaternions are used to represent rotations.">Quaternion</a>. Don't modify this directly unless you know quaternions inside out. <br /></td></tr>
<tr class="separator:ab7eb002a81cfc537a9c3afc8965ef2ec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac26c0a2710dd86783dee62c8645ee55c"><td class="memItemLeft" align="right" valign="top"><a id="ac26c0a2710dd86783dee62c8645ee55c" name="ac26c0a2710dd86783dee62c8645ee55c"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><b>z</b><code> [get, set]</code></td></tr>
<tr class="memdesc:ac26c0a2710dd86783dee62c8645ee55c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Z component of the <a class="el" href="class_lua_1_1_quaternion.html" title="Quaternions are used to represent rotations.">Quaternion</a>. Don't modify this directly unless you know quaternions inside out. <br /></td></tr>
<tr class="separator:ac26c0a2710dd86783dee62c8645ee55c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac50cf6b67c4cb0363b834b7054cdd5fa"><td class="memItemLeft" align="right" valign="top"><a id="ac50cf6b67c4cb0363b834b7054cdd5fa" name="ac50cf6b67c4cb0363b834b7054cdd5fa"></a>
<a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><b>eulerAngles</b><code> [get]</code></td></tr>
<tr class="memdesc:ac50cf6b67c4cb0363b834b7054cdd5fa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the euler angle representation of the rotation. <br /></td></tr>
<tr class="separator:ac50cf6b67c4cb0363b834b7054cdd5fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7d56f4c2496af59e66550e35bff614c"><td class="memItemLeft" align="right" valign="top"><a id="ac7d56f4c2496af59e66550e35bff614c" name="ac7d56f4c2496af59e66550e35bff614c"></a>
static <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td><td class="memItemRight" valign="bottom"><b>identity</b><code> [get]</code></td></tr>
<tr class="memdesc:ac7d56f4c2496af59e66550e35bff614c"><td class="mdescLeft">&#160;</td><td class="mdescRight">The identity rotation (Read Only). <br /></td></tr>
<tr class="separator:ac7d56f4c2496af59e66550e35bff614c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >Quaternions are used to represent rotations. </p>
<p >They are compact, don't suffer from gimbal lock and can easily be interpolated. Unity internally uses Quaternions to represent all rotations.</p>
<p >They are based on complex numbers and are not easy to understand intuitively. You almost never access or modify individual <a class="el" href="class_lua_1_1_quaternion.html" title="Quaternions are used to represent rotations.">Quaternion</a> components (x,y,z,w); most often you would just take existing rotations (e.g. from the <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a>) and use them to construct new rotations (e.g. to smoothly interpolate between two rotations). The <a class="el" href="class_lua_1_1_quaternion.html" title="Quaternions are used to represent rotations.">Quaternion</a> functions that you use 99% of the time are: <a class="el" href="class_lua_1_1_quaternion.html#a7a17f92fd9d83a8b472db78d5cb74642" title="Creates a rotation with the specified forward and upwards directions.">Quaternion.LookRotation</a>, <a class="el" href="class_lua_1_1_quaternion.html#a4094309a8c66c50017bd92ffdfeb3bfd" title="Returns the angle in degrees between two rotations a and b.">Quaternion.Angle</a>, <a class="el" href="class_lua_1_1_quaternion.html#ac7134c2bdc28902fc519d42b7b803d9f" title="Returns a rotation that rotates z degrees around the z axis, x degrees around the x axis,...">Quaternion.Euler</a>, <a class="el" href="class_lua_1_1_quaternion.html#a8848906e3924791706bc0bc853a4572b" title="Spherically interpolates between a and b by t. The parameter t is clamped to the range [0,...">Quaternion.Slerp</a>, <a class="el" href="class_lua_1_1_quaternion.html#a78b8152d55e05c4b35a74ed09eae9d41" title="Creates a rotation which rotates from fromDirection to toDirection.">Quaternion.FromToRotation</a>, and <a class="el" href="class_lua_1_1_quaternion.html#ac7d56f4c2496af59e66550e35bff614c" title="The identity rotation (Read Only).">Quaternion.identity</a>. (The other functions are only for exotic uses.)</p>
<p >You can use the <a class="el" href="class_lua_1_1_quaternion.html#ac0e2a30dacbe7805913969ca02cc1e6b" title="Combines rotations lhs and rhs.">Quaternion.operator*</a> to rotate one rotation by another, or to rotate a vector by a rotation. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="ac70013482fc53c72c664561d67d5d677" name="ac70013482fc53c72c664561d67d5d677"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac70013482fc53c72c664561d67d5d677">&#9670;&nbsp;</a></span>Concat() <span class="overload">[1/3]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static string Lua.Quaternion.Concat </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td>
          <td class="paramname"><em>o</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>s</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Concatenates a quaternion and a string. </p>
<p >Syntax for this operation is <code>q .. s</code>. <a class="el" href="class_lua_1_1_quaternion.html#a72a4b219c3442e088c7ee963feb1b372" title="String representation of this quaternion.">Quaternion.ToString</a> is used to convert quaternion to string. </p>

</div>
</div>
<a id="aef26c0eb5e338baa7dc1a87530b360d1" name="aef26c0eb5e338baa7dc1a87530b360d1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aef26c0eb5e338baa7dc1a87530b360d1">&#9670;&nbsp;</a></span>Concat() <span class="overload">[2/3]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static string Lua.Quaternion.Concat </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td>
          <td class="paramname"><em>o1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td>
          <td class="paramname"><em>o2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Concatenates 2 quaternions. </p>
<p >Syntax for this operation is <code>q1 .. q2</code>. <a class="el" href="class_lua_1_1_quaternion.html#a72a4b219c3442e088c7ee963feb1b372" title="String representation of this quaternion.">Quaternion.ToString</a> is used to convert quaternions to strings. </p>

</div>
</div>
<a id="a9eab1ab6fc89906ecc0b8159c1429a04" name="a9eab1ab6fc89906ecc0b8159c1429a04"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9eab1ab6fc89906ecc0b8159c1429a04">&#9670;&nbsp;</a></span>Concat() <span class="overload">[3/3]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static string Lua.Quaternion.Concat </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>s</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td>
          <td class="paramname"><em>o</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Concatenates a string and a quaternion. </p>
<p >Syntax for this operation is <code>s .. q</code>. <a class="el" href="class_lua_1_1_quaternion.html#a72a4b219c3442e088c7ee963feb1b372" title="String representation of this quaternion.">Quaternion.ToString</a> is used to convert quaternion to string. </p>

</div>
</div>
<a id="a9ecf171f4a2a8c0ad3ced7564ada2c6d" name="a9ecf171f4a2a8c0ad3ced7564ada2c6d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9ecf171f4a2a8c0ad3ced7564ada2c6d">&#9670;&nbsp;</a></span>Eq()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static bool Lua.Quaternion.Eq </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td>
          <td class="paramname"><em>o1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td>
          <td class="paramname"><em>o2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Tests 2 quaternions for equality. </p>
<p >Syntax for this operation is <code>q1 == q2</code>. This function tests whether dot product of two quaternions is close to 1.0.</p>
<p >Note that because quaternions can represent rotations that are up to two full revolutions (720 degrees), this comparison can return false even if resulting rotations look the same. </p>

</div>
</div>
<a id="a78b8152d55e05c4b35a74ed09eae9d41" name="a78b8152d55e05c4b35a74ed09eae9d41"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a78b8152d55e05c4b35a74ed09eae9d41">&#9670;&nbsp;</a></span>FromToRotation()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> Lua.Quaternion.FromToRotation </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>fromDirection</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>toDirection</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Creates a rotation which rotates from fromDirection to toDirection. </p>
<p >Usually you use this to rotate a transform so that one of its axes eg. the y-axis - follows a target direction toDirection in world space. </p>

</div>
</div>
<a id="a7a17f92fd9d83a8b472db78d5cb74642" name="a7a17f92fd9d83a8b472db78d5cb74642"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7a17f92fd9d83a8b472db78d5cb74642">&#9670;&nbsp;</a></span>LookRotation() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> Lua.Quaternion.LookRotation </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>forward</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Creates a rotation with the specified forward and upwards directions. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">forward</td><td>The direction to look in.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Returns the computed quaternion. If used to orient a <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a>, the Z axis will be aligned with forward/ and the Y axis with upwards if these vectors are orthogonal. Logs an error if the forward direction is zero.</dd></dl>

</div>
</div>
<a id="afa69beaa5748b6c941b9a3296d6c0638" name="afa69beaa5748b6c941b9a3296d6c0638"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afa69beaa5748b6c941b9a3296d6c0638">&#9670;&nbsp;</a></span>LookRotation() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> Lua.Quaternion.LookRotation </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>forward</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>upwards</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Creates a rotation with the specified forward and upwards directions. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">forward</td><td>The direction to look in.</td></tr>
    <tr><td class="paramname">upwards</td><td>The vector that defines in which direction up is.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Returns the computed quaternion. If used to orient a <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a>, the Z axis will be aligned with forward/ and the Y axis with upwards if these vectors are orthogonal. Logs an error if the forward direction is zero.</dd></dl>

</div>
</div>
<a id="ac0e2a30dacbe7805913969ca02cc1e6b" name="ac0e2a30dacbe7805913969ca02cc1e6b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac0e2a30dacbe7805913969ca02cc1e6b">&#9670;&nbsp;</a></span>operator*()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> Lua.Quaternion.operator* </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td>
          <td class="paramname"><em>lhs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td>
          <td class="paramname"><em>rhs</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Combines rotations lhs and rhs. </p>
<p >Rotating by the product <code>lhs * rhs</code> is the same as applying the two rotations in sequence: <code>lhs</code> first and then <code>rhs</code>, relative to the reference frame resulting from <code>lhs</code> rotation. Note that this means rotations are not commutative, so <code>lhs * rhs</code> does not give the same rotation as <code>rhs * lhs</code>. </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaQuaternion.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Class Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('functions_m.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_m" name="index_m"></a>- m -</h3><ul>
<li>magnitude&#160;:&#160;<a class="el" href="class_lua_1_1_vector3.html#af7a889228914dff8226722de5c47a9b7">Lua.Vector3</a></li>
<li>Major&#160;:&#160;<a class="el" href="class_lua_1_1_game_1_1_version.html#a8c329c68bcf96acc94c59705eace0791">Lua.Game.Version</a></li>
<li>mass&#160;:&#160;<a class="el" href="class_lua_1_1_rigidbody.html#aeab0f1c55ada296d501909dd61533a35">Lua.Rigidbody</a></li>
<li>Max()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#ae0c3619a26a60fdc6442011911e0e412">Lua.Mathf</a>, <a class="el" href="class_lua_1_1_vector3.html#a3495c2bc3847d7ec201c1e4209359c25">Lua.Vector3</a></li>
<li>maxAngularVelocity&#160;:&#160;<a class="el" href="class_lua_1_1_rigidbody.html#a8171fc4d6eb8d7e448eeb45f9fbc05d8">Lua.Rigidbody</a></li>
<li>maxDistance&#160;:&#160;<a class="el" href="class_lua_1_1_audio_source.html#a55ad4d09380c973f2001b2164aba4771">Lua.AudioSource</a></li>
<li>maxSize&#160;:&#160;<a class="el" href="class_lua_1_1_all_giantess.html#af82af573b560f17c3b0cd575bd61ed00">Lua.AllGiantess</a>, <a class="el" href="class_lua_1_1_entity.html#a133133afe701b7ca4f0e2d6632beae33">Lua.Entity</a>, <a class="el" href="class_lua_1_1_player.html#aa940451f7c74d6b8bdf34c728b6a688d">Lua.Player</a></li>
<li>maxSpeed&#160;:&#160;<a class="el" href="class_lua_1_1_animation.html#a49aa08ef58b67f1af48526781176fce3">Lua.Animation</a></li>
<li>Message()&#160;:&#160;<a class="el" href="class_lua_1_1_game.html#a02a20189930a05adae52e4243a3179db">Lua.Game</a></li>
<li>metricHeight&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#ac821c347ba04965d00132f02e88adecd">Lua.Entity</a></li>
<li>Min()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#acc164cac8453f2551303265e346f4969">Lua.Mathf</a>, <a class="el" href="class_lua_1_1_vector3.html#a514e1f8b6c974e522e290ccf3f113a7e">Lua.Vector3</a></li>
<li>minDistance&#160;:&#160;<a class="el" href="class_lua_1_1_audio_source.html#a66155ad664c665455476ea70f4779669">Lua.AudioSource</a></li>
<li>Minor&#160;:&#160;<a class="el" href="class_lua_1_1_game_1_1_version.html#aec3cfaebb94290f3405dbeeaee9277d6">Lua.Game.Version</a></li>
<li>minSize&#160;:&#160;<a class="el" href="class_lua_1_1_all_giantess.html#a4852dd60230117796328807f41509dbf">Lua.AllGiantess</a>, <a class="el" href="class_lua_1_1_entity.html#a642603e2952e4fcea70979837049f813">Lua.Entity</a>, <a class="el" href="class_lua_1_1_player.html#a101475733e91a310fdd874be94d5464b">Lua.Player</a></li>
<li>minSpeed&#160;:&#160;<a class="el" href="class_lua_1_1_animation.html#a7dee7e3d2e6bdffed96f20822e4bb307">Lua.Animation</a></li>
<li>modelName&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#acd67b39a7c95e3cb87171073c2877de1">Lua.Entity</a></li>
<li>morphs&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#aa9723210eb2494461ff12c55b52e8844">Lua.Entity</a></li>
<li>MouseDown&#160;:&#160;<a class="el" href="class_event_code.html#a32fa7eb106bc429a8c854b81caa2d38c">EventCode</a></li>
<li>mousePosition&#160;:&#160;<a class="el" href="class_lua_1_1_input.html#ab5d4bcb7c637ec0760fc8ca8033ecec7">Lua.Input</a></li>
<li>mouseScrollDelta&#160;:&#160;<a class="el" href="class_lua_1_1_input.html#ac508b474e85e336be67f2ad7ef94f751">Lua.Input</a></li>
<li>MouseUp&#160;:&#160;<a class="el" href="class_event_code.html#ae6b50b9aff11a3a1a10e0e2836e159f8">EventCode</a></li>
<li>MoveDirection()&#160;:&#160;<a class="el" href="class_lua_1_1_movement.html#ab1a73835885dca908bb6e5d23509ce40">Lua.Movement</a></li>
<li>movement&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a2845d63d6164b33ee49f760211fa4116">Lua.Entity</a></li>
<li>MovePosition()&#160;:&#160;<a class="el" href="class_lua_1_1_rigidbody.html#a413146fdf9b4e57b433cbc01dc1bc288">Lua.Rigidbody</a></li>
<li>MoveRotation()&#160;:&#160;<a class="el" href="class_lua_1_1_rigidbody.html#a064bd1441d0a8d7e636619c87a98f7cf">Lua.Rigidbody</a></li>
<li>MoveTo()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a5d6cfb68967adf948db2b6c09d7dfd38">Lua.Entity</a></li>
<li>MoveTowards()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a0b9df3fa414f0b12c2fcfd1eee83570c">Lua.Mathf</a>, <a class="el" href="class_lua_1_1_movement.html#a6e9301bc7326c56f23fbb2eac2ad2f54">Lua.Movement</a>, <a class="el" href="class_lua_1_1_vector3.html#aa4b2a5ff6af794cc8a83617227bee73b">Lua.Vector3</a></li>
<li>mute&#160;:&#160;<a class="el" href="class_lua_1_1_audio_source.html#ad82dcfe66567f1dac8a15edc327c89ff">Lua.AudioSource</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.Entity Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_entity.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="class_lua_1_1_entity-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.Entity Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>A <a class="el" href="class_lua_1_1_entity.html" title="A Entity represents Characters and Objects">Entity</a> represents Characters and Objects  
 <a href="class_lua_1_1_entity.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a422af2c756caecc01bad49a14ba5da7f"><td class="memItemLeft" align="right" valign="top"><a id="a422af2c756caecc01bad49a14ba5da7f" name="a422af2c756caecc01bad49a14ba5da7f"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>Delete</b> ()</td></tr>
<tr class="memdesc:a422af2c756caecc01bad49a14ba5da7f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safely deletes the entity (won't delete the player). <br /></td></tr>
<tr class="separator:a422af2c756caecc01bad49a14ba5da7f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a672fa734133cf30da8487cd9d6dfea10"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a672fa734133cf30da8487cd9d6dfea10">Distance</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> target)</td></tr>
<tr class="memdesc:a672fa734133cf30da8487cd9d6dfea10"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate the relative distance to a target in 3D space.  <a href="class_lua_1_1_entity.html#a672fa734133cf30da8487cd9d6dfea10">More...</a><br /></td></tr>
<tr class="separator:a672fa734133cf30da8487cd9d6dfea10"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a87a8401d6c58e3e934fb6562c0dc8619"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a87a8401d6c58e3e934fb6562c0dc8619">Distance</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> point)</td></tr>
<tr class="memdesc:a87a8401d6c58e3e934fb6562c0dc8619"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate the relative distance to a point in 3D space.  <a href="class_lua_1_1_entity.html#a87a8401d6c58e3e934fb6562c0dc8619">More...</a><br /></td></tr>
<tr class="separator:a87a8401d6c58e3e934fb6562c0dc8619"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a35b61319d6e69f126c1fae059346275a"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a35b61319d6e69f126c1fae059346275a">Distance2d</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> target)</td></tr>
<tr class="memdesc:a35b61319d6e69f126c1fae059346275a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate the relative distance to a target on the horizontal axises.  <a href="class_lua_1_1_entity.html#a35b61319d6e69f126c1fae059346275a">More...</a><br /></td></tr>
<tr class="separator:a35b61319d6e69f126c1fae059346275a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1d67369dd495a4237db25ca8bb90a704"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a1d67369dd495a4237db25ca8bb90a704">Distance2d</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> point)</td></tr>
<tr class="memdesc:a1d67369dd495a4237db25ca8bb90a704"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate the relative distance to a point on the horizontal axises.  <a href="class_lua_1_1_entity.html#a1d67369dd495a4237db25ca8bb90a704">More...</a><br /></td></tr>
<tr class="separator:a1d67369dd495a4237db25ca8bb90a704"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2dd8505150905c103ea90ca50652dbaa"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a2dd8505150905c103ea90ca50652dbaa">DistanceVertical</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> target)</td></tr>
<tr class="memdesc:a2dd8505150905c103ea90ca50652dbaa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate the relative distance to a point on the vertical axis.  <a href="class_lua_1_1_entity.html#a2dd8505150905c103ea90ca50652dbaa">More...</a><br /></td></tr>
<tr class="separator:a2dd8505150905c103ea90ca50652dbaa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afa4ae1a9c1a4925bde8e1969aa38d0a6"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#afa4ae1a9c1a4925bde8e1969aa38d0a6">DistanceVertical</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> point)</td></tr>
<tr class="memdesc:afa4ae1a9c1a4925bde8e1969aa38d0a6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate the relative distance to a point on the vertical axis.  <a href="class_lua_1_1_entity.html#afa4ae1a9c1a4925bde8e1969aa38d0a6">More...</a><br /></td></tr>
<tr class="separator:afa4ae1a9c1a4925bde8e1969aa38d0a6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab35b20eb77b836668196c14959f6ba39"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#ab35b20eb77b836668196c14959f6ba39">DistanceVerticalSigned</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> target)</td></tr>
<tr class="memdesc:ab35b20eb77b836668196c14959f6ba39"><td class="mdescLeft">&#160;</td><td class="mdescRight">Like <a class="el" href="class_lua_1_1_entity.html#a2dd8505150905c103ea90ca50652dbaa" title="Calculate the relative distance to a point on the vertical axis.">DistanceVertical()</a> except return a negative if the target is below  <a href="class_lua_1_1_entity.html#ab35b20eb77b836668196c14959f6ba39">More...</a><br /></td></tr>
<tr class="separator:ab35b20eb77b836668196c14959f6ba39"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a166e098ef14ddba3ce2e338773755f8b"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a166e098ef14ddba3ce2e338773755f8b">DistanceVerticalSigned</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> point)</td></tr>
<tr class="memdesc:a166e098ef14ddba3ce2e338773755f8b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Like DistanceVerticalTo() except return a negative if the target is below  <a href="class_lua_1_1_entity.html#a166e098ef14ddba3ce2e338773755f8b">More...</a><br /></td></tr>
<tr class="separator:a166e098ef14ddba3ce2e338773755f8b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2e06bf904c49f705ac361e1538365e2e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a2e06bf904c49f705ac361e1538365e2e">FindClosestMicro</a> ()</td></tr>
<tr class="memdesc:a2e06bf904c49f705ac361e1538365e2e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the closes Micro <a class="el" href="class_lua_1_1_entity.html" title="A Entity represents Characters and Objects">Entity</a>. It can also return the player.  <a href="class_lua_1_1_entity.html#a2e06bf904c49f705ac361e1538365e2e">More...</a><br /></td></tr>
<tr class="separator:a2e06bf904c49f705ac361e1538365e2e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7eac4ebcb1fe784ab5c06eed7885cf7"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#ac7eac4ebcb1fe784ab5c06eed7885cf7">FindClosestGiantess</a> ()</td></tr>
<tr class="memdesc:ac7eac4ebcb1fe784ab5c06eed7885cf7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the closes Giantess <a class="el" href="class_lua_1_1_entity.html" title="A Entity represents Characters and Objects">Entity</a>. It can also return the player.  <a href="class_lua_1_1_entity.html#ac7eac4ebcb1fe784ab5c06eed7885cf7">More...</a><br /></td></tr>
<tr class="separator:ac7eac4ebcb1fe784ab5c06eed7885cf7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27a8d5461e9d6890e085b06e2d00f6bd"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a27a8d5461e9d6890e085b06e2d00f6bd">isHumanoid</a> ()</td></tr>
<tr class="memdesc:a27a8d5461e9d6890e085b06e2d00f6bd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if the entity is a humanoid character.  <a href="class_lua_1_1_entity.html#a27a8d5461e9d6890e085b06e2d00f6bd">More...</a><br /></td></tr>
<tr class="separator:a27a8d5461e9d6890e085b06e2d00f6bd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af72e042e1fd05c66abceebb49ec2caf4"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#af72e042e1fd05c66abceebb49ec2caf4">isGiantess</a> ()</td></tr>
<tr class="memdesc:af72e042e1fd05c66abceebb49ec2caf4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if the entity is a giantess (.gts).  <a href="class_lua_1_1_entity.html#af72e042e1fd05c66abceebb49ec2caf4">More...</a><br /></td></tr>
<tr class="separator:af72e042e1fd05c66abceebb49ec2caf4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af7b9099c16b719f42e4fdfd82661d259"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#af7b9099c16b719f42e4fdfd82661d259">isPlayer</a> ()</td></tr>
<tr class="memdesc:af7b9099c16b719f42e4fdfd82661d259"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if this entity is player controlled (micro or giantess).  <a href="class_lua_1_1_entity.html#af7b9099c16b719f42e4fdfd82661d259">More...</a><br /></td></tr>
<tr class="separator:af7b9099c16b719f42e4fdfd82661d259"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5eaa128b6b8cf4aeb7f219edd030d61e"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a5eaa128b6b8cf4aeb7f219edd030d61e">isMicro</a> ()</td></tr>
<tr class="memdesc:a5eaa128b6b8cf4aeb7f219edd030d61e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if the entity is a micro (.micro)  <a href="class_lua_1_1_entity.html#a5eaa128b6b8cf4aeb7f219edd030d61e">More...</a><br /></td></tr>
<tr class="separator:a5eaa128b6b8cf4aeb7f219edd030d61e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1bf50f8db78480e10f40a71e44480e21"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a1bf50f8db78480e10f40a71e44480e21">BE</a> (float speed)</td></tr>
<tr class="memdesc:a1bf50f8db78480e10f40a71e44480e21"><td class="mdescLeft">&#160;</td><td class="mdescRight">Adds a Breast Expansion action at the end of the action queue.  <a href="class_lua_1_1_entity.html#a1bf50f8db78480e10f40a71e44480e21">More...</a><br /></td></tr>
<tr class="separator:a1bf50f8db78480e10f40a71e44480e21"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac8dfc303d378e4e805c5ba3f38f49c59"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#ac8dfc303d378e4e805c5ba3f38f49c59">BE</a> (float speed, float time)</td></tr>
<tr class="memdesc:ac8dfc303d378e4e805c5ba3f38f49c59"><td class="mdescLeft">&#160;</td><td class="mdescRight">Adds a Breast Expansion action at the end of the action queue.  <a href="class_lua_1_1_entity.html#ac8dfc303d378e4e805c5ba3f38f49c59">More...</a><br /></td></tr>
<tr class="separator:ac8dfc303d378e4e805c5ba3f38f49c59"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a92feb21c4219c60a7e5935733302083f"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a92feb21c4219c60a7e5935733302083f">Grow</a> (float factor)</td></tr>
<tr class="memdesc:a92feb21c4219c60a7e5935733302083f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Adds a <b>priority</b> Grow action at the <b>beginning</b> of the action queue.  <a href="class_lua_1_1_entity.html#a92feb21c4219c60a7e5935733302083f">More...</a><br /></td></tr>
<tr class="separator:a92feb21c4219c60a7e5935733302083f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac574fce9abb6f90e7f7675be74838964"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#ac574fce9abb6f90e7f7675be74838964">Grow</a> (float factor, float time)</td></tr>
<tr class="memdesc:ac574fce9abb6f90e7f7675be74838964"><td class="mdescLeft">&#160;</td><td class="mdescRight">Adds a <b>priority</b> Grow action at the <b>beginning</b> of the action queue.  <a href="class_lua_1_1_entity.html#ac574fce9abb6f90e7f7675be74838964">More...</a><br /></td></tr>
<tr class="separator:ac574fce9abb6f90e7f7675be74838964"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0007133219ff5ec24e9eecf6a9d2dd50"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a0007133219ff5ec24e9eecf6a9d2dd50">GrowAndWait</a> (float factor, float time)</td></tr>
<tr class="memdesc:a0007133219ff5ec24e9eecf6a9d2dd50"><td class="mdescLeft">&#160;</td><td class="mdescRight">Adds a regular Grow action at the end of the action queue.  <a href="class_lua_1_1_entity.html#a0007133219ff5ec24e9eecf6a9d2dd50">More...</a><br /></td></tr>
<tr class="separator:a0007133219ff5ec24e9eecf6a9d2dd50"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5d6cfb68967adf948db2b6c09d7dfd38"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a5d6cfb68967adf948db2b6c09d7dfd38">MoveTo</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> destination)</td></tr>
<tr class="memdesc:a5d6cfb68967adf948db2b6c09d7dfd38"><td class="mdescLeft">&#160;</td><td class="mdescRight">The entity will walk to a designed point.  <a href="class_lua_1_1_entity.html#a5d6cfb68967adf948db2b6c09d7dfd38">More...</a><br /></td></tr>
<tr class="separator:a5d6cfb68967adf948db2b6c09d7dfd38"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a307f39e2316f0c4246604ba5ce5b749e"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a307f39e2316f0c4246604ba5ce5b749e">MoveTo</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> targetEntity)</td></tr>
<tr class="memdesc:a307f39e2316f0c4246604ba5ce5b749e"><td class="mdescLeft">&#160;</td><td class="mdescRight">The entity will walk towards another target entity, and stop when it reaches it.  <a href="class_lua_1_1_entity.html#a307f39e2316f0c4246604ba5ce5b749e">More...</a><br /></td></tr>
<tr class="separator:a307f39e2316f0c4246604ba5ce5b749e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab4f0e1c31b16110cdadb7479ba008423"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#ab4f0e1c31b16110cdadb7479ba008423">Chase</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> target)</td></tr>
<tr class="memdesc:ab4f0e1c31b16110cdadb7479ba008423"><td class="mdescLeft">&#160;</td><td class="mdescRight">Chase the target entity while evading objects.  <a href="class_lua_1_1_entity.html#ab4f0e1c31b16110cdadb7479ba008423">More...</a><br /></td></tr>
<tr class="separator:ab4f0e1c31b16110cdadb7479ba008423"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a442bfc9dcbb33b4fa3922a59357a8723"><td class="memItemLeft" align="right" valign="top"><a id="a442bfc9dcbb33b4fa3922a59357a8723" name="a442bfc9dcbb33b4fa3922a59357a8723"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>Face</b> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> target)</td></tr>
<tr class="memdesc:a442bfc9dcbb33b4fa3922a59357a8723"><td class="mdescLeft">&#160;</td><td class="mdescRight">Face the target. <br /></td></tr>
<tr class="separator:a442bfc9dcbb33b4fa3922a59357a8723"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32545d25ff6935a006bdb6b5ad45ad9a"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a32545d25ff6935a006bdb6b5ad45ad9a">Seek</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> target, float duration=0, float separation=-1)</td></tr>
<tr class="memdesc:a32545d25ff6935a006bdb6b5ad45ad9a"><td class="mdescLeft">&#160;</td><td class="mdescRight">The entity will seek toward another target until the time runs out or it is close enough. Used in the "Follow" command.  <a href="class_lua_1_1_entity.html#a32545d25ff6935a006bdb6b5ad45ad9a">More...</a><br /></td></tr>
<tr class="separator:a32545d25ff6935a006bdb6b5ad45ad9a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1a0205dc1d6ab6ac54679970885490f9"><td class="memItemLeft" align="right" valign="top"><a id="a1a0205dc1d6ab6ac54679970885490f9" name="a1a0205dc1d6ab6ac54679970885490f9"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>Seek</b> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> <a class="el" href="class_lua_1_1_entity.html#ad8bd97d98fddc9b89f8410512b502c3f">position</a>, float duration=0, float separation=-1)</td></tr>
<tr class="memdesc:a1a0205dc1d6ab6ac54679970885490f9"><td class="mdescLeft">&#160;</td><td class="mdescRight">The entity will seek toward a position for the specified amount of time (in seconds). <br /></td></tr>
<tr class="separator:a1a0205dc1d6ab6ac54679970885490f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa4d1d044edd1f9b9dfb4b310c49b8761"><td class="memItemLeft" align="right" valign="top"><a id="aa4d1d044edd1f9b9dfb4b310c49b8761" name="aa4d1d044edd1f9b9dfb4b310c49b8761"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>Wander</b> ()</td></tr>
<tr class="memdesc:aa4d1d044edd1f9b9dfb4b310c49b8761"><td class="mdescLeft">&#160;</td><td class="mdescRight">The entity will wander without stopping. <br /></td></tr>
<tr class="separator:aa4d1d044edd1f9b9dfb4b310c49b8761"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9858a940de17a0405da24bff1d834970"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a9858a940de17a0405da24bff1d834970">Wander</a> (float time)</td></tr>
<tr class="memdesc:a9858a940de17a0405da24bff1d834970"><td class="mdescLeft">&#160;</td><td class="mdescRight">The entity will wander during the specified amount of time.  <a href="class_lua_1_1_entity.html#a9858a940de17a0405da24bff1d834970">More...</a><br /></td></tr>
<tr class="separator:a9858a940de17a0405da24bff1d834970"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a58a5bb200f7182dea5e622f036f05bf3"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a58a5bb200f7182dea5e622f036f05bf3">Wait</a> (float time)</td></tr>
<tr class="memdesc:a58a5bb200f7182dea5e622f036f05bf3"><td class="mdescLeft">&#160;</td><td class="mdescRight">The entity will wait for the specified amount of time.  <a href="class_lua_1_1_entity.html#a58a5bb200f7182dea5e622f036f05bf3">More...</a><br /></td></tr>
<tr class="separator:a58a5bb200f7182dea5e622f036f05bf3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af1290dfaf8e3da8c2adb7279359bf036"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#af1290dfaf8e3da8c2adb7279359bf036">Flee</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> target, float time)</td></tr>
<tr class="memdesc:af1290dfaf8e3da8c2adb7279359bf036"><td class="mdescLeft">&#160;</td><td class="mdescRight">The entity will flee from the target during the specified amount of time. A time of 0 will make it unlimited.  <a href="class_lua_1_1_entity.html#af1290dfaf8e3da8c2adb7279359bf036">More...</a><br /></td></tr>
<tr class="separator:af1290dfaf8e3da8c2adb7279359bf036"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a334e63b56e1c78e6dbebd4dd9c48a69e"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a334e63b56e1c78e6dbebd4dd9c48a69e">Flee</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> <a class="el" href="class_lua_1_1_entity.html#ad8bd97d98fddc9b89f8410512b502c3f">position</a>, float time)</td></tr>
<tr class="memdesc:a334e63b56e1c78e6dbebd4dd9c48a69e"><td class="mdescLeft">&#160;</td><td class="mdescRight">The entity will flee from the position during the specified amount of time. A time of 0 will make it unlimited.  <a href="class_lua_1_1_entity.html#a334e63b56e1c78e6dbebd4dd9c48a69e">More...</a><br /></td></tr>
<tr class="separator:a334e63b56e1c78e6dbebd4dd9c48a69e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a72e0a626a062ba116cf62cfeb77f87f8"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a72e0a626a062ba116cf62cfeb77f87f8">FindRandomBuilding</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> self)</td></tr>
<tr class="memdesc:a72e0a626a062ba116cf62cfeb77f87f8"><td class="mdescLeft">&#160;</td><td class="mdescRight">This function a random location of a structure close to the gts  <a href="class_lua_1_1_entity.html#a72e0a626a062ba116cf62cfeb77f87f8">More...</a><br /></td></tr>
<tr class="separator:a72e0a626a062ba116cf62cfeb77f87f8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5690c11c287139f6d5b5171f5d4189e9"><td class="memItemLeft" align="right" valign="top"><a id="a5690c11c287139f6d5b5171f5d4189e9" name="a5690c11c287139f6d5b5171f5d4189e9"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>Wreck</b> ()</td></tr>
<tr class="memdesc:a5690c11c287139f6d5b5171f5d4189e9"><td class="mdescLeft">&#160;</td><td class="mdescRight">The enitity will aggressively stomp in an attempt to wreck everything <br /></td></tr>
<tr class="separator:a5690c11c287139f6d5b5171f5d4189e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af93c20b6cca387d60a5368c079ae65ef"><td class="memItemLeft" align="right" valign="top"><a id="af93c20b6cca387d60a5368c079ae65ef" name="af93c20b6cca387d60a5368c079ae65ef"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>StandUp</b> ()</td></tr>
<tr class="memdesc:af93c20b6cca387d60a5368c079ae65ef"><td class="mdescLeft">&#160;</td><td class="mdescRight">Orders a crushed micro to stand up. Note that entities stop all behaviors when crushed. So this method is only useful when called from other entity's behavior or a global script. <br /></td></tr>
<tr class="separator:af93c20b6cca387d60a5368c079ae65ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0a8058c8b504215e492471b4e7d557d4"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a0a8058c8b504215e492471b4e7d557d4">Stomp</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> target)</td></tr>
<tr class="memdesc:a0a8058c8b504215e492471b4e7d557d4"><td class="mdescLeft">&#160;</td><td class="mdescRight">If the entity is a giantess, she will stomp a target entity.  <a href="class_lua_1_1_entity.html#a0a8058c8b504215e492471b4e7d557d4">More...</a><br /></td></tr>
<tr class="separator:a0a8058c8b504215e492471b4e7d557d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad6c8d1dc736aeea69e734d3c1e884343"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#ad6c8d1dc736aeea69e734d3c1e884343">Stomp</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> target)</td></tr>
<tr class="memdesc:ad6c8d1dc736aeea69e734d3c1e884343"><td class="mdescLeft">&#160;</td><td class="mdescRight">If the entity is a giantess, she will stomp a target point.  <a href="class_lua_1_1_entity.html#ad6c8d1dc736aeea69e734d3c1e884343">More...</a><br /></td></tr>
<tr class="separator:ad6c8d1dc736aeea69e734d3c1e884343"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6ddff7e7a95c85ba34bee6b8b5c4ee93"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a6ddff7e7a95c85ba34bee6b8b5c4ee93">Grab</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> target)</td></tr>
<tr class="memdesc:a6ddff7e7a95c85ba34bee6b8b5c4ee93"><td class="mdescLeft">&#160;</td><td class="mdescRight">If the entity is a giantess will grab a target.  <a href="class_lua_1_1_entity.html#a6ddff7e7a95c85ba34bee6b8b5c4ee93">More...</a><br /></td></tr>
<tr class="separator:a6ddff7e7a95c85ba34bee6b8b5c4ee93"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a29cdb052c5422873a708c8080039cb4b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a29cdb052c5422873a708c8080039cb4b">LookAt</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> target)</td></tr>
<tr class="memdesc:a29cdb052c5422873a708c8080039cb4b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Not to be confused with <a class="el" href="class_lua_1_1_transform.html#a1e722de9c3eacff82477ab7684a67553" title="Not to be confused with Entity.LookAt Rotates the transform so the forward vector points at /target/&#39;...">Transform.LookAt</a> If the entity is a giantess it will look towards a target. If the target is nil the giantess will return to default looking behaviour  <a href="class_lua_1_1_entity.html#a29cdb052c5422873a708c8080039cb4b">More...</a><br /></td></tr>
<tr class="separator:a29cdb052c5422873a708c8080039cb4b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a23180e62c487b6c3923e39b3be84b291"><td class="memItemLeft" align="right" valign="top"><a id="a23180e62c487b6c3923e39b3be84b291" name="a23180e62c487b6c3923e39b3be84b291"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>EquipRaygun</b> ()</td></tr>
<tr class="memdesc:a23180e62c487b6c3923e39b3be84b291"><td class="mdescLeft">&#160;</td><td class="mdescRight">If the entity is an NPC micro, will equip raygun if it is currently unequipped. <br /></td></tr>
<tr class="separator:a23180e62c487b6c3923e39b3be84b291"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaa24b31446f5c0087faa4030f91e80ac"><td class="memItemLeft" align="right" valign="top"><a id="aaa24b31446f5c0087faa4030f91e80ac" name="aaa24b31446f5c0087faa4030f91e80ac"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>EquipSMG</b> ()</td></tr>
<tr class="memdesc:aaa24b31446f5c0087faa4030f91e80ac"><td class="mdescLeft">&#160;</td><td class="mdescRight">If the entity is an NPC micro, will equip SMG if it is currently unequipped. <br /></td></tr>
<tr class="separator:aaa24b31446f5c0087faa4030f91e80ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1a4cc3d2425ef3527ab0692f4c2a9ca3"><td class="memItemLeft" align="right" valign="top"><a id="a1a4cc3d2425ef3527ab0692f4c2a9ca3" name="a1a4cc3d2425ef3527ab0692f4c2a9ca3"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>UnequipGun</b> ()</td></tr>
<tr class="memdesc:a1a4cc3d2425ef3527ab0692f4c2a9ca3"><td class="mdescLeft">&#160;</td><td class="mdescRight">If the entity is an NPC micro, will unequip gun if it currently has one equipped. <br /></td></tr>
<tr class="separator:a1a4cc3d2425ef3527ab0692f4c2a9ca3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac47475b1b0342f1e6ce52aca2eec7f38"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#ac47475b1b0342f1e6ce52aca2eec7f38">Aim</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> target)</td></tr>
<tr class="memdesc:ac47475b1b0342f1e6ce52aca2eec7f38"><td class="mdescLeft">&#160;</td><td class="mdescRight">If the entity is an NPC micro, will aim at target. If target is null, will stop aiming  <a href="class_lua_1_1_entity.html#ac47475b1b0342f1e6ce52aca2eec7f38">More...</a><br /></td></tr>
<tr class="separator:ac47475b1b0342f1e6ce52aca2eec7f38"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aadc969ba1387cf03d80b8432705f0750"><td class="memItemLeft" align="right" valign="top"><a id="aadc969ba1387cf03d80b8432705f0750" name="aadc969ba1387cf03d80b8432705f0750"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>StopAiming</b> ()</td></tr>
<tr class="memdesc:aadc969ba1387cf03d80b8432705f0750"><td class="mdescLeft">&#160;</td><td class="mdescRight">If the entity is an NPC micro, will stop aiming <br /></td></tr>
<tr class="separator:aadc969ba1387cf03d80b8432705f0750"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3a5c197d31f8834f8ed67f71c6157719"><td class="memItemLeft" align="right" valign="top"><a id="a3a5c197d31f8834f8ed67f71c6157719" name="a3a5c197d31f8834f8ed67f71c6157719"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>StartFiring</b> ()</td></tr>
<tr class="memdesc:a3a5c197d31f8834f8ed67f71c6157719"><td class="mdescLeft">&#160;</td><td class="mdescRight">If the entity is an NPC micro will start firing, and aiming if not doing so already, if there it has an aim target. <br /></td></tr>
<tr class="separator:a3a5c197d31f8834f8ed67f71c6157719"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4d7809fc03b618624a6d7640674fe646"><td class="memItemLeft" align="right" valign="top"><a id="a4d7809fc03b618624a6d7640674fe646" name="a4d7809fc03b618624a6d7640674fe646"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>StopFiring</b> ()</td></tr>
<tr class="memdesc:a4d7809fc03b618624a6d7640674fe646"><td class="mdescLeft">&#160;</td><td class="mdescRight">If the entity is an NPC micro will stop firing <br /></td></tr>
<tr class="separator:a4d7809fc03b618624a6d7640674fe646"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af23f0b36c1b4c778fc7f328490223852"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#af23f0b36c1b4c778fc7f328490223852">Engage</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> target=null)</td></tr>
<tr class="memdesc:af23f0b36c1b4c778fc7f328490223852"><td class="mdescLeft">&#160;</td><td class="mdescRight">If the entity is an NPC micro will start chasing and firing (and aiming) at designated target if not doing so already. If no target is provided, will attempt to engage the currently assigned aim target. Sort of an all-in-one command where the entity will reposition themselves if too close to or too far from the target.  <a href="class_lua_1_1_entity.html#af23f0b36c1b4c778fc7f328490223852">More...</a><br /></td></tr>
<tr class="separator:af23f0b36c1b4c778fc7f328490223852"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9ead5c7d5e821fa285ab065b9cc3185f"><td class="memItemLeft" align="right" valign="top"><a id="a9ead5c7d5e821fa285ab065b9cc3185f" name="a9ead5c7d5e821fa285ab065b9cc3185f"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>StopEngaging</b> ()</td></tr>
<tr class="memdesc:a9ead5c7d5e821fa285ab065b9cc3185f"><td class="mdescLeft">&#160;</td><td class="mdescRight">If the entity is an NPC micro will stop chasing and firing <br /></td></tr>
<tr class="separator:a9ead5c7d5e821fa285ab065b9cc3185f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a17d117771166d30924da118c1fed9968"><td class="memItemLeft" align="right" valign="top"><a id="a17d117771166d30924da118c1fed9968" name="a17d117771166d30924da118c1fed9968"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>FireOnce</b> ()</td></tr>
<tr class="memdesc:a17d117771166d30924da118c1fed9968"><td class="mdescLeft">&#160;</td><td class="mdescRight">If the entity is an NPC micro will fire once, and aiming if not doing so already <br /></td></tr>
<tr class="separator:a17d117771166d30924da118c1fed9968"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6357c0a54e9aa79beb928d079b42aa76"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a6357c0a54e9aa79beb928d079b42aa76">IsDead</a> ()</td></tr>
<tr class="memdesc:a6357c0a54e9aa79beb928d079b42aa76"><td class="mdescLeft">&#160;</td><td class="mdescRight">Checks if the entity is dead.  <a href="class_lua_1_1_entity.html#a6357c0a54e9aa79beb928d079b42aa76">More...</a><br /></td></tr>
<tr class="separator:a6357c0a54e9aa79beb928d079b42aa76"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab25d357d357ab0cf7ff9a2e81aa9cb08"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#ab25d357d357ab0cf7ff9a2e81aa9cb08">IsStuck</a> ()</td></tr>
<tr class="memdesc:ab25d357d357ab0cf7ff9a2e81aa9cb08"><td class="mdescLeft">&#160;</td><td class="mdescRight">Checks if the entity is stuck to giantess. Only applicable to micros.  <a href="class_lua_1_1_entity.html#ab25d357d357ab0cf7ff9a2e81aa9cb08">More...</a><br /></td></tr>
<tr class="separator:ab25d357d357ab0cf7ff9a2e81aa9cb08"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa8688dacb32db168b780597d8f11622b"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#aa8688dacb32db168b780597d8f11622b">IsTargettable</a> ()</td></tr>
<tr class="memdesc:aa8688dacb32db168b780597d8f11622b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Checks if the entity be targeted for crushing. To be targettable it must be a living micro that is not stuck.  <a href="class_lua_1_1_entity.html#aa8688dacb32db168b780597d8f11622b">More...</a><br /></td></tr>
<tr class="separator:aa8688dacb32db168b780597d8f11622b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4b4bb9870796b854c0d5666c9cba9375"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a4b4bb9870796b854c0d5666c9cba9375">IsCrushed</a> ()</td></tr>
<tr class="memdesc:a4b4bb9870796b854c0d5666c9cba9375"><td class="mdescLeft">&#160;</td><td class="mdescRight">Checks if the entity is crushed. Only applicable to micros. It doesn't matter whether it survived the crush or not. Returns false for deleted entities, even if they were crushed prior to deleting.  <a href="class_lua_1_1_entity.html#a4b4bb9870796b854c0d5666c9cba9375">More...</a><br /></td></tr>
<tr class="separator:a4b4bb9870796b854c0d5666c9cba9375"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6be24874965a4015e7fd8244fa345220"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a6be24874965a4015e7fd8244fa345220">UpdateMeshCollider</a> ()</td></tr>
<tr class="memdesc:a6be24874965a4015e7fd8244fa345220"><td class="mdescLeft">&#160;</td><td class="mdescRight">[Slow operation] Forces the update of the collider. This is a slow operacion, don't update the mesh every frame, don't use inside a loop.  <a href="class_lua_1_1_entity.html#a6be24874965a4015e7fd8244fa345220">More...</a><br /></td></tr>
<tr class="separator:a6be24874965a4015e7fd8244fa345220"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac2bbfd97ccf17d7cd96fb1cf3b3c51e4"><td class="memItemLeft" align="right" valign="top"><a id="ac2bbfd97ccf17d7cd96fb1cf3b3c51e4" name="ac2bbfd97ccf17d7cd96fb1cf3b3c51e4"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>ShowBreastPhysicsOptions</b> ()</td></tr>
<tr class="memdesc:ac2bbfd97ccf17d7cd96fb1cf3b3c51e4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Shows a UI menu to the user that can be used to alter the breasts physics <br /></td></tr>
<tr class="separator:ac2bbfd97ccf17d7cd96fb1cf3b3c51e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa3ec8ca2693205f43b8e1e620b209018"><td class="memItemLeft" align="right" valign="top"><a id="aa3ec8ca2693205f43b8e1e620b209018" name="aa3ec8ca2693205f43b8e1e620b209018"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>PlayAs</b> ()</td></tr>
<tr class="memdesc:aa3ec8ca2693205f43b8e1e620b209018"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transfers player control to this entity, only if it's a playable character. <br /></td></tr>
<tr class="separator:aa3ec8ca2693205f43b8e1e620b209018"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aad0651348795eb39acee39055e0b7638"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#aad0651348795eb39acee39055e0b7638">GetRandomMicro</a> ()</td></tr>
<tr class="memdesc:aad0651348795eb39acee39055e0b7638"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a random micro in the scene. Can return null if there are no micros in the scene.  <a href="class_lua_1_1_entity.html#aad0651348795eb39acee39055e0b7638">More...</a><br /></td></tr>
<tr class="separator:aad0651348795eb39acee39055e0b7638"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa9df9f39762cb24b89449e8b61aab43c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#aa9df9f39762cb24b89449e8b61aab43c">GetRandomGiantess</a> ()</td></tr>
<tr class="memdesc:aa9df9f39762cb24b89449e8b61aab43c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a random giantess in the scene. Can return null if there are no giantesses in the scene.  <a href="class_lua_1_1_entity.html#aa9df9f39762cb24b89449e8b61aab43c">More...</a><br /></td></tr>
<tr class="separator:aa9df9f39762cb24b89449e8b61aab43c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-static-methods" name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:af3f7aae31ecb691380a6f18f053fb907"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#af3f7aae31ecb691380a6f18f053fb907">GetSelectedEntity</a> ()</td></tr>
<tr class="memdesc:af3f7aae31ecb691380a6f18f053fb907"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the currently selected entity in the Editor.  <a href="class_lua_1_1_entity.html#af3f7aae31ecb691380a6f18f053fb907">More...</a><br /></td></tr>
<tr class="separator:af3f7aae31ecb691380a6f18f053fb907"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3b1c9ef716345edfa2edb2608d8180cc"><td class="memItemLeft" align="right" valign="top"><a id="a3b1c9ef716345edfa2edb2608d8180cc" name="a3b1c9ef716345edfa2edb2608d8180cc"></a>
static IList&lt; string &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>GetGtsModelList</b> ()</td></tr>
<tr class="memdesc:a3b1c9ef716345edfa2edb2608d8180cc"><td class="mdescLeft">&#160;</td><td class="mdescRight">A list of all available giantess models. <br /></td></tr>
<tr class="separator:a3b1c9ef716345edfa2edb2608d8180cc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4ba19c862b99a6f66c65894ca07228b1"><td class="memItemLeft" align="right" valign="top"><a id="a4ba19c862b99a6f66c65894ca07228b1" name="a4ba19c862b99a6f66c65894ca07228b1"></a>
static IList&lt; string &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>GetFemaleMicroList</b> ()</td></tr>
<tr class="memdesc:a4ba19c862b99a6f66c65894ca07228b1"><td class="mdescLeft">&#160;</td><td class="mdescRight">A list of all available female micro models. <br /></td></tr>
<tr class="separator:a4ba19c862b99a6f66c65894ca07228b1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4140900b6cce1a0da2723df84fae3cbb"><td class="memItemLeft" align="right" valign="top"><a id="a4140900b6cce1a0da2723df84fae3cbb" name="a4140900b6cce1a0da2723df84fae3cbb"></a>
static IList&lt; string &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>GetMaleMicroList</b> ()</td></tr>
<tr class="memdesc:a4140900b6cce1a0da2723df84fae3cbb"><td class="mdescLeft">&#160;</td><td class="mdescRight">A list of all available male micro models. <br /></td></tr>
<tr class="separator:a4140900b6cce1a0da2723df84fae3cbb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3412e9016f582bd74344865dadc6a954"><td class="memItemLeft" align="right" valign="top"><a id="a3412e9016f582bd74344865dadc6a954" name="a3412e9016f582bd74344865dadc6a954"></a>
static IList&lt; string &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>GetObjectList</b> ()</td></tr>
<tr class="memdesc:a3412e9016f582bd74344865dadc6a954"><td class="mdescLeft">&#160;</td><td class="mdescRight">A list of all available object models. <br /></td></tr>
<tr class="separator:a3412e9016f582bd74344865dadc6a954"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a582caed51f918e3d7642a7ec8a227fd1"><td class="memItemLeft" align="right" valign="top"><a id="a582caed51f918e3d7642a7ec8a227fd1" name="a582caed51f918e3d7642a7ec8a227fd1"></a>
static <a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><b>SpawnGiantess</b> (string <a class="el" href="class_lua_1_1_entity.html#a8155b6c6ef0f0630ec7e818dd4cdaec4">name</a>, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> <a class="el" href="class_lua_1_1_entity.html#ad8bd97d98fddc9b89f8410512b502c3f">position</a>, <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> rotation, float <a class="el" href="class_lua_1_1_entity.html#a784673c0e6fbf29381a309a5df0ee10e">scale</a>)</td></tr>
<tr class="memdesc:a582caed51f918e3d7642a7ec8a227fd1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Spawns a giantess from a given model name. <br /></td></tr>
<tr class="separator:a582caed51f918e3d7642a7ec8a227fd1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a589ed0851b37e13ccd5bc7778a0f72fa"><td class="memItemLeft" align="right" valign="top"><a id="a589ed0851b37e13ccd5bc7778a0f72fa" name="a589ed0851b37e13ccd5bc7778a0f72fa"></a>
static <a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><b>SpawnGiantess</b> (string <a class="el" href="class_lua_1_1_entity.html#a8155b6c6ef0f0630ec7e818dd4cdaec4">name</a>, float <a class="el" href="class_lua_1_1_entity.html#a784673c0e6fbf29381a309a5df0ee10e">scale</a>)</td></tr>
<tr class="memdesc:a589ed0851b37e13ccd5bc7778a0f72fa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Spawns a giantess from a given model name. <br /></td></tr>
<tr class="separator:a589ed0851b37e13ccd5bc7778a0f72fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a204097539b6932308832916892ba77e5"><td class="memItemLeft" align="right" valign="top"><a id="a204097539b6932308832916892ba77e5" name="a204097539b6932308832916892ba77e5"></a>
static <a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><b>SpawnGiantess</b> (string <a class="el" href="class_lua_1_1_entity.html#a8155b6c6ef0f0630ec7e818dd4cdaec4">name</a>, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> <a class="el" href="class_lua_1_1_entity.html#ad8bd97d98fddc9b89f8410512b502c3f">position</a>, <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> rotation, float <a class="el" href="class_lua_1_1_entity.html#a784673c0e6fbf29381a309a5df0ee10e">scale</a>, DynValue scriptInstance, DynValue initializedCallback)</td></tr>
<tr class="memdesc:a204097539b6932308832916892ba77e5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Spawns a giantess from a given model name. The given callback is triggered when the giantess is fully loaded. <br /></td></tr>
<tr class="separator:a204097539b6932308832916892ba77e5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a347a27b3fea83b83461600b3b80ce5d8"><td class="memItemLeft" align="right" valign="top"><a id="a347a27b3fea83b83461600b3b80ce5d8" name="a347a27b3fea83b83461600b3b80ce5d8"></a>
static <a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><b>SpawnFemaleMicro</b> (string <a class="el" href="class_lua_1_1_entity.html#a8155b6c6ef0f0630ec7e818dd4cdaec4">name</a>, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> <a class="el" href="class_lua_1_1_entity.html#ad8bd97d98fddc9b89f8410512b502c3f">position</a>, <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> rotation, float <a class="el" href="class_lua_1_1_entity.html#a784673c0e6fbf29381a309a5df0ee10e">scale</a>)</td></tr>
<tr class="memdesc:a347a27b3fea83b83461600b3b80ce5d8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Spawns a female micro from a given model name. <br /></td></tr>
<tr class="separator:a347a27b3fea83b83461600b3b80ce5d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6f7515955767cb1a416ecea342ba6b2a"><td class="memItemLeft" align="right" valign="top"><a id="a6f7515955767cb1a416ecea342ba6b2a" name="a6f7515955767cb1a416ecea342ba6b2a"></a>
static <a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><b>SpawnFemaleMicro</b> (string <a class="el" href="class_lua_1_1_entity.html#a8155b6c6ef0f0630ec7e818dd4cdaec4">name</a>, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> <a class="el" href="class_lua_1_1_entity.html#ad8bd97d98fddc9b89f8410512b502c3f">position</a>, <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> rotation, float <a class="el" href="class_lua_1_1_entity.html#a784673c0e6fbf29381a309a5df0ee10e">scale</a>, DynValue scriptInstance, DynValue initializedCallback)</td></tr>
<tr class="memdesc:a6f7515955767cb1a416ecea342ba6b2a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Spawns a female micro from a given model name. The given callback is triggered when the giantess is fully loaded. <br /></td></tr>
<tr class="separator:a6f7515955767cb1a416ecea342ba6b2a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af958d5e0ff116c025e01159d92ca620a"><td class="memItemLeft" align="right" valign="top"><a id="af958d5e0ff116c025e01159d92ca620a" name="af958d5e0ff116c025e01159d92ca620a"></a>
static <a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><b>SpawnFemaleMicro</b> (string <a class="el" href="class_lua_1_1_entity.html#a8155b6c6ef0f0630ec7e818dd4cdaec4">name</a>, float <a class="el" href="class_lua_1_1_entity.html#a784673c0e6fbf29381a309a5df0ee10e">scale</a>)</td></tr>
<tr class="memdesc:af958d5e0ff116c025e01159d92ca620a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Spawns a female micro from a given model name. <br /></td></tr>
<tr class="separator:af958d5e0ff116c025e01159d92ca620a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad8f4dd9eec83d4df5d28efa120210ef6"><td class="memItemLeft" align="right" valign="top"><a id="ad8f4dd9eec83d4df5d28efa120210ef6" name="ad8f4dd9eec83d4df5d28efa120210ef6"></a>
static <a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><b>SpawnMaleMicro</b> (string <a class="el" href="class_lua_1_1_entity.html#a8155b6c6ef0f0630ec7e818dd4cdaec4">name</a>, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> <a class="el" href="class_lua_1_1_entity.html#ad8bd97d98fddc9b89f8410512b502c3f">position</a>, <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> rotation, float <a class="el" href="class_lua_1_1_entity.html#a784673c0e6fbf29381a309a5df0ee10e">scale</a>)</td></tr>
<tr class="memdesc:ad8f4dd9eec83d4df5d28efa120210ef6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Spawns a male micro from a given model name. <br /></td></tr>
<tr class="separator:ad8f4dd9eec83d4df5d28efa120210ef6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a49483eb9f352fedac6568d6b7d20fa53"><td class="memItemLeft" align="right" valign="top"><a id="a49483eb9f352fedac6568d6b7d20fa53" name="a49483eb9f352fedac6568d6b7d20fa53"></a>
static <a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><b>SpawnMaleMicro</b> (string <a class="el" href="class_lua_1_1_entity.html#a8155b6c6ef0f0630ec7e818dd4cdaec4">name</a>, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> <a class="el" href="class_lua_1_1_entity.html#ad8bd97d98fddc9b89f8410512b502c3f">position</a>, <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> rotation, float <a class="el" href="class_lua_1_1_entity.html#a784673c0e6fbf29381a309a5df0ee10e">scale</a>, DynValue scriptInstance, DynValue initializedCallback)</td></tr>
<tr class="memdesc:a49483eb9f352fedac6568d6b7d20fa53"><td class="mdescLeft">&#160;</td><td class="mdescRight">Spawns a male micro from a given model name. <br /></td></tr>
<tr class="separator:a49483eb9f352fedac6568d6b7d20fa53"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1d4754cbaae699b12b11b8551973d2a3"><td class="memItemLeft" align="right" valign="top"><a id="a1d4754cbaae699b12b11b8551973d2a3" name="a1d4754cbaae699b12b11b8551973d2a3"></a>
static <a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><b>SpawnMaleMicro</b> (string <a class="el" href="class_lua_1_1_entity.html#a8155b6c6ef0f0630ec7e818dd4cdaec4">name</a>, float <a class="el" href="class_lua_1_1_entity.html#a784673c0e6fbf29381a309a5df0ee10e">scale</a>)</td></tr>
<tr class="memdesc:a1d4754cbaae699b12b11b8551973d2a3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Spawns a male micro from a given model name. <br /></td></tr>
<tr class="separator:a1d4754cbaae699b12b11b8551973d2a3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5bb33d1afcfac1b1452d362cb5d8bb94"><td class="memItemLeft" align="right" valign="top"><a id="a5bb33d1afcfac1b1452d362cb5d8bb94" name="a5bb33d1afcfac1b1452d362cb5d8bb94"></a>
static <a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><b>SpawnObject</b> (string <a class="el" href="class_lua_1_1_entity.html#a8155b6c6ef0f0630ec7e818dd4cdaec4">name</a>, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> <a class="el" href="class_lua_1_1_entity.html#ad8bd97d98fddc9b89f8410512b502c3f">position</a>, <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> rotation, float <a class="el" href="class_lua_1_1_entity.html#a784673c0e6fbf29381a309a5df0ee10e">scale</a>)</td></tr>
<tr class="memdesc:a5bb33d1afcfac1b1452d362cb5d8bb94"><td class="mdescLeft">&#160;</td><td class="mdescRight">Spawns an object from a given model name. <br /></td></tr>
<tr class="separator:a5bb33d1afcfac1b1452d362cb5d8bb94"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa60200f5db9aec0c7d10b61e6e69cd0b"><td class="memItemLeft" align="right" valign="top"><a id="aa60200f5db9aec0c7d10b61e6e69cd0b" name="aa60200f5db9aec0c7d10b61e6e69cd0b"></a>
static <a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><b>SpawnObject</b> (string <a class="el" href="class_lua_1_1_entity.html#a8155b6c6ef0f0630ec7e818dd4cdaec4">name</a>, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> <a class="el" href="class_lua_1_1_entity.html#ad8bd97d98fddc9b89f8410512b502c3f">position</a>, <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> rotation, float <a class="el" href="class_lua_1_1_entity.html#a784673c0e6fbf29381a309a5df0ee10e">scale</a>, DynValue scriptInstance, DynValue initializedCallback)</td></tr>
<tr class="memdesc:aa60200f5db9aec0c7d10b61e6e69cd0b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Spawns an object from a given model name. The given callback is triggered when the object is fully loaded. <br /></td></tr>
<tr class="separator:aa60200f5db9aec0c7d10b61e6e69cd0b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac55e7536a3bdc7a6b9a3fa6a759db9ee"><td class="memItemLeft" align="right" valign="top">static bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#ac55e7536a3bdc7a6b9a3fa6a759db9ee">Equals</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> a, <a class="el" href="class_lua_1_1_entity.html">Entity</a> b)</td></tr>
<tr class="memdesc:ac55e7536a3bdc7a6b9a3fa6a759db9ee"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tests 2 entities for equality.  <a href="class_lua_1_1_entity.html#ac55e7536a3bdc7a6b9a3fa6a759db9ee">More...</a><br /></td></tr>
<tr class="separator:ac55e7536a3bdc7a6b9a3fa6a759db9ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="properties" name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:ae0c707512eed832f2211ace61d3be75d"><td class="memItemLeft" align="right" valign="top"><a id="ae0c707512eed832f2211ace61d3be75d" name="ae0c707512eed832f2211ace61d3be75d"></a>
Table&#160;</td><td class="memItemRight" valign="bottom"><b>dict</b><code> [get]</code></td></tr>
<tr class="memdesc:ae0c707512eed832f2211ace61d3be75d"><td class="mdescLeft">&#160;</td><td class="mdescRight">A dictionary associated to this entity. It can be used to store and exchange arbitrary data between scripts. <br /></td></tr>
<tr class="separator:ae0c707512eed832f2211ace61d3be75d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a51edf8c42bd2acb730ae73d045341320"><td class="memItemLeft" align="right" valign="top"><a id="a51edf8c42bd2acb730ae73d045341320" name="a51edf8c42bd2acb730ae73d045341320"></a>
<a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td><td class="memItemRight" valign="bottom"><b>transform</b><code> [get]</code></td></tr>
<tr class="memdesc:a51edf8c42bd2acb730ae73d045341320"><td class="mdescLeft">&#160;</td><td class="mdescRight">The transform component associated to this entity. It contains data about the position, rotation and scale used by the Unity Engine. <br /></td></tr>
<tr class="separator:a51edf8c42bd2acb730ae73d045341320"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a56d48f666679b251eefa10e6f65bbb60"><td class="memItemLeft" align="right" valign="top"><a id="a56d48f666679b251eefa10e6f65bbb60" name="a56d48f666679b251eefa10e6f65bbb60"></a>
<a class="el" href="class_lua_1_1_rigidbody.html">Rigidbody</a>&#160;</td><td class="memItemRight" valign="bottom"><b>rigidbody</b><code> [get]</code></td></tr>
<tr class="memdesc:a56d48f666679b251eefa10e6f65bbb60"><td class="mdescLeft">&#160;</td><td class="mdescRight">Control the physics of the <a class="el" href="class_lua_1_1_entity.html" title="A Entity represents Characters and Objects">Entity</a>. Normal objects don't come with physics by default, but player and npc they have it for movement. <br /></td></tr>
<tr class="separator:a56d48f666679b251eefa10e6f65bbb60"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a13c1350817e444010fcbaff8d224039e"><td class="memItemLeft" align="right" valign="top"><a id="a13c1350817e444010fcbaff8d224039e" name="a13c1350817e444010fcbaff8d224039e"></a>
<a class="el" href="class_lua_1_1_a_i.html">AI</a>&#160;</td><td class="memItemRight" valign="bottom"><b>ai</b><code> [get]</code></td></tr>
<tr class="memdesc:a13c1350817e444010fcbaff8d224039e"><td class="mdescLeft">&#160;</td><td class="mdescRight">The ai component controls the ai behaviors of the entity. <br /></td></tr>
<tr class="separator:a13c1350817e444010fcbaff8d224039e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acf080bfbeeb3a6308c2fdd4cc4993e81"><td class="memItemLeft" align="right" valign="top"><a id="acf080bfbeeb3a6308c2fdd4cc4993e81" name="acf080bfbeeb3a6308c2fdd4cc4993e81"></a>
<a class="el" href="class_lua_1_1_animation.html">Animation</a>&#160;</td><td class="memItemRight" valign="bottom"><b>animation</b><code> [get]</code></td></tr>
<tr class="memdesc:acf080bfbeeb3a6308c2fdd4cc4993e81"><td class="mdescLeft">&#160;</td><td class="mdescRight">Component for controling the animation of humanoid entities. <br /></td></tr>
<tr class="separator:acf080bfbeeb3a6308c2fdd4cc4993e81"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8d4bdecb96c327395e5ddbde88608cf4"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_bones.html">Bones</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a8d4bdecb96c327395e5ddbde88608cf4">bones</a><code> [get]</code></td></tr>
<tr class="memdesc:a8d4bdecb96c327395e5ddbde88608cf4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Access the bone transforms of the model (head, hands, feet, etc).  <a href="class_lua_1_1_entity.html#a8d4bdecb96c327395e5ddbde88608cf4">More...</a><br /></td></tr>
<tr class="separator:a8d4bdecb96c327395e5ddbde88608cf4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a42cd1e5e507a1e79eb1161799564da88"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_i_k.html">IK</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a42cd1e5e507a1e79eb1161799564da88">ik</a><code> [get]</code></td></tr>
<tr class="memdesc:a42cd1e5e507a1e79eb1161799564da88"><td class="mdescLeft">&#160;</td><td class="mdescRight">Inverse Kinematics for the model  <a href="class_lua_1_1_entity.html#a42cd1e5e507a1e79eb1161799564da88">More...</a><br /></td></tr>
<tr class="separator:a42cd1e5e507a1e79eb1161799564da88"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9a5a67b2da9b9d95e31c766aa68760ee"><td class="memItemLeft" align="right" valign="top"><a id="a9a5a67b2da9b9d95e31c766aa68760ee" name="a9a5a67b2da9b9d95e31c766aa68760ee"></a>
<a class="el" href="class_lua_1_1_senses.html">Senses</a>&#160;</td><td class="memItemRight" valign="bottom"><b>senses</b><code> [get]</code></td></tr>
<tr class="memdesc:a9a5a67b2da9b9d95e31c766aa68760ee"><td class="mdescLeft">&#160;</td><td class="mdescRight">Manages the senses of the entity such as the vision. <br /></td></tr>
<tr class="separator:a9a5a67b2da9b9d95e31c766aa68760ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa9723210eb2494461ff12c55b52e8844"><td class="memItemLeft" align="right" valign="top"><a id="aa9723210eb2494461ff12c55b52e8844" name="aa9723210eb2494461ff12c55b52e8844"></a>
<a class="el" href="class_lua_1_1_morphs.html">Morphs</a>&#160;</td><td class="memItemRight" valign="bottom"><b>morphs</b><code> [get]</code></td></tr>
<tr class="memdesc:aa9723210eb2494461ff12c55b52e8844"><td class="mdescLeft">&#160;</td><td class="mdescRight">Control the morphs of the entity. <br /></td></tr>
<tr class="separator:aa9723210eb2494461ff12c55b52e8844"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2845d63d6164b33ee49f760211fa4116"><td class="memItemLeft" align="right" valign="top"><a id="a2845d63d6164b33ee49f760211fa4116" name="a2845d63d6164b33ee49f760211fa4116"></a>
<a class="el" href="class_lua_1_1_movement.html">Movement</a>&#160;</td><td class="memItemRight" valign="bottom"><b>movement</b><code> [get]</code></td></tr>
<tr class="memdesc:a2845d63d6164b33ee49f760211fa4116"><td class="mdescLeft">&#160;</td><td class="mdescRight">Manages the movement of the entity. <br /></td></tr>
<tr class="separator:a2845d63d6164b33ee49f760211fa4116"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af00214fc6ff19d22818d8d0810630cea"><td class="memItemLeft" align="right" valign="top"><a id="af00214fc6ff19d22818d8d0810630cea" name="af00214fc6ff19d22818d8d0810630cea"></a>
<a class="el" href="class_lua_1_1_shooting.html">Shooting</a>&#160;</td><td class="memItemRight" valign="bottom"><b>shooting</b><code> [get]</code></td></tr>
<tr class="memdesc:af00214fc6ff19d22818d8d0810630cea"><td class="mdescLeft">&#160;</td><td class="mdescRight">Manages the shooting of the entity. <br /></td></tr>
<tr class="separator:af00214fc6ff19d22818d8d0810630cea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad8bd97d98fddc9b89f8410512b502c3f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#ad8bd97d98fddc9b89f8410512b502c3f">position</a><code> [get, set]</code></td></tr>
<tr class="memdesc:ad8bd97d98fddc9b89f8410512b502c3f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the current position on world space of this entity.  <a href="class_lua_1_1_entity.html#ad8bd97d98fddc9b89f8410512b502c3f">More...</a><br /></td></tr>
<tr class="separator:ad8bd97d98fddc9b89f8410512b502c3f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7dfc341caa3b11cc42ef45226689741c"><td class="memItemLeft" align="right" valign="top"><a id="a7dfc341caa3b11cc42ef45226689741c" name="a7dfc341caa3b11cc42ef45226689741c"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>CanLookAtPlayer</b><code> [get, set]</code></td></tr>
<tr class="memdesc:a7dfc341caa3b11cc42ef45226689741c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Whether or not a Giantess entity can look at the player. <br /></td></tr>
<tr class="separator:a7dfc341caa3b11cc42ef45226689741c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7776e8422e86d2ab5670ca314a65aab5"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a7776e8422e86d2ab5670ca314a65aab5">id</a><code> [get]</code></td></tr>
<tr class="memdesc:a7776e8422e86d2ab5670ca314a65aab5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the id associated to this entity.  <a href="class_lua_1_1_entity.html#a7776e8422e86d2ab5670ca314a65aab5">More...</a><br /></td></tr>
<tr class="separator:a7776e8422e86d2ab5670ca314a65aab5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8155b6c6ef0f0630ec7e818dd4cdaec4"><td class="memItemLeft" align="right" valign="top">string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a8155b6c6ef0f0630ec7e818dd4cdaec4">name</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a8155b6c6ef0f0630ec7e818dd4cdaec4"><td class="mdescLeft">&#160;</td><td class="mdescRight">The name of this entity.  <a href="class_lua_1_1_entity.html#a8155b6c6ef0f0630ec7e818dd4cdaec4">More...</a><br /></td></tr>
<tr class="separator:a8155b6c6ef0f0630ec7e818dd4cdaec4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acd67b39a7c95e3cb87171073c2877de1"><td class="memItemLeft" align="right" valign="top">string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#acd67b39a7c95e3cb87171073c2877de1">modelName</a><code> [get]</code></td></tr>
<tr class="memdesc:acd67b39a7c95e3cb87171073c2877de1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Name the model of this entity.  <a href="class_lua_1_1_entity.html#acd67b39a7c95e3cb87171073c2877de1">More...</a><br /></td></tr>
<tr class="separator:acd67b39a7c95e3cb87171073c2877de1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a784673c0e6fbf29381a309a5df0ee10e"><td class="memItemLeft" align="right" valign="top">virtual float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a784673c0e6fbf29381a309a5df0ee10e">scale</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a784673c0e6fbf29381a309a5df0ee10e"><td class="mdescLeft">&#160;</td><td class="mdescRight">The scale of this entity. Use this instead of the transform when possible.  <a href="class_lua_1_1_entity.html#a784673c0e6fbf29381a309a5df0ee10e">More...</a><br /></td></tr>
<tr class="separator:a784673c0e6fbf29381a309a5df0ee10e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1873494f26c8f90c79254b43d25d47f7"><td class="memItemLeft" align="right" valign="top">virtual float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a1873494f26c8f90c79254b43d25d47f7">baseHeight</a><code> [get]</code></td></tr>
<tr class="memdesc:a1873494f26c8f90c79254b43d25d47f7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Base model height of this entity.  <a href="class_lua_1_1_entity.html#a1873494f26c8f90c79254b43d25d47f7">More...</a><br /></td></tr>
<tr class="separator:a1873494f26c8f90c79254b43d25d47f7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6b6baf8292fe2447ad0620722bc24526"><td class="memItemLeft" align="right" valign="top">virtual float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a6b6baf8292fe2447ad0620722bc24526">height</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a6b6baf8292fe2447ad0620722bc24526"><td class="mdescLeft">&#160;</td><td class="mdescRight">The height of this entity.  <a href="class_lua_1_1_entity.html#a6b6baf8292fe2447ad0620722bc24526">More...</a><br /></td></tr>
<tr class="separator:a6b6baf8292fe2447ad0620722bc24526"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac821c347ba04965d00132f02e88adecd"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#ac821c347ba04965d00132f02e88adecd">metricHeight</a><code> [get, set]</code></td></tr>
<tr class="memdesc:ac821c347ba04965d00132f02e88adecd"><td class="mdescLeft">&#160;</td><td class="mdescRight">The height of this entity in meters relative to the current map. If you want to set height in a unit other then meters you can convert it with <a class="el" href="class_lua_1_1_mathf.html#ab274b83f44ab3e9457e67f949097bc27" title="Converts a strings length into meters that can be used to scale entities height.">Mathf.ConvertToMeter</a>  <a href="class_lua_1_1_entity.html#ac821c347ba04965d00132f02e88adecd">More...</a><br /></td></tr>
<tr class="separator:ac821c347ba04965d00132f02e88adecd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a133133afe701b7ca4f0e2d6632beae33"><td class="memItemLeft" align="right" valign="top">virtual float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a133133afe701b7ca4f0e2d6632beae33">maxSize</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a133133afe701b7ca4f0e2d6632beae33"><td class="mdescLeft">&#160;</td><td class="mdescRight">The max scale for this entity.  <a href="class_lua_1_1_entity.html#a133133afe701b7ca4f0e2d6632beae33">More...</a><br /></td></tr>
<tr class="separator:a133133afe701b7ca4f0e2d6632beae33"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a642603e2952e4fcea70979837049f813"><td class="memItemLeft" align="right" valign="top">virtual float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a642603e2952e4fcea70979837049f813">minSize</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a642603e2952e4fcea70979837049f813"><td class="mdescLeft">&#160;</td><td class="mdescRight">The min scale for this entity.  <a href="class_lua_1_1_entity.html#a642603e2952e4fcea70979837049f813">More...</a><br /></td></tr>
<tr class="separator:a642603e2952e4fcea70979837049f813"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >A <a class="el" href="class_lua_1_1_entity.html" title="A Entity represents Characters and Objects">Entity</a> represents Characters and Objects </p>
<p >Entities are things that can be spawned in game, like Objects, Giantesses, Micros, and Even the player is considered and <a class="el" href="class_lua_1_1_entity.html" title="A Entity represents Characters and Objects">Entity</a>. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="ac47475b1b0342f1e6ce52aca2eec7f38" name="ac47475b1b0342f1e6ce52aca2eec7f38"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac47475b1b0342f1e6ce52aca2eec7f38">&#9670;&nbsp;</a></span>Aim()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Entity.Aim </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td>
          <td class="paramname"><em>target</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>If the entity is an NPC micro, will aim at target. If target is null, will stop aiming </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">target</td><td>target to aim at. Only works with humanoids.</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a1bf50f8db78480e10f40a71e44480e21" name="a1bf50f8db78480e10f40a71e44480e21"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1bf50f8db78480e10f40a71e44480e21">&#9670;&nbsp;</a></span>BE() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void Lua.Entity.BE </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>speed</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Adds a Breast Expansion action at the end of the action queue. </p>
<p >The breasts of the giantess will grow at a constant speed relative to their size (i.e. 0.1 = 10% per second). Negative values will make it shrink. </p>

</div>
</div>
<a id="ac8dfc303d378e4e805c5ba3f38f49c59" name="ac8dfc303d378e4e805c5ba3f38f49c59"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac8dfc303d378e4e805c5ba3f38f49c59">&#9670;&nbsp;</a></span>BE() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void Lua.Entity.BE </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>speed</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>time</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Adds a Breast Expansion action at the end of the action queue. </p>
<p >The breasts of the giantess grow at a constant speed during a specified amount of time, relative to their size (i.e. 0.1 = 10% per second). Negative values will make it shrink. </p>

</div>
</div>
<a id="ab4f0e1c31b16110cdadb7479ba008423" name="ab4f0e1c31b16110cdadb7479ba008423"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab4f0e1c31b16110cdadb7479ba008423">&#9670;&nbsp;</a></span>Chase()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Entity.Chase </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td>
          <td class="paramname"><em>target</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Chase the target entity while evading objects. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">target</td><td>target to chase down</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a672fa734133cf30da8487cd9d6dfea10" name="a672fa734133cf30da8487cd9d6dfea10"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a672fa734133cf30da8487cd9d6dfea10">&#9670;&nbsp;</a></span>Distance() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Entity.Distance </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td>
          <td class="paramname"><em>target</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Calculate the relative distance to a target in 3D space. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">target</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Distance in 3D space</dd></dl>
<p >Because this function returns distance in 3D space it can report misleading data due to vertical differences as entities are measured from the bottom of their model. For example: when checking the distance between a macro and a micro on it's head the distance will be roughly equal to the height of the macro because the distance is being measure from both their feet. If this is a problem for your use case then use <a class="el" href="class_lua_1_1_entity.html#a35b61319d6e69f126c1fae059346275a" title="Calculate the relative distance to a target on the horizontal axises.">Distance2d()</a> instead</p>

</div>
</div>
<a id="a87a8401d6c58e3e934fb6562c0dc8619" name="a87a8401d6c58e3e934fb6562c0dc8619"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a87a8401d6c58e3e934fb6562c0dc8619">&#9670;&nbsp;</a></span>Distance() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Entity.Distance </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>point</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Calculate the relative distance to a point in 3D space. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">point</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Distance in 3D space</dd></dl>
<p >Because this function returns distance in 3D space it can report misleading data due to vertical differences as entities are measured from the bottom of their model. For example: when checking the distance between a macro and a micro on it's head the distance will be roughly equal to the height of the macro because the distance is being measure from both their feet. If this is a problem for your use case then use <a class="el" href="class_lua_1_1_entity.html#a35b61319d6e69f126c1fae059346275a" title="Calculate the relative distance to a target on the horizontal axises.">Distance2d()</a> instead</p>

</div>
</div>
<a id="a35b61319d6e69f126c1fae059346275a" name="a35b61319d6e69f126c1fae059346275a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a35b61319d6e69f126c1fae059346275a">&#9670;&nbsp;</a></span>Distance2d() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Entity.Distance2d </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td>
          <td class="paramname"><em>target</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Calculate the relative distance to a target on the horizontal axises. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">target</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Return distance on the horizontal axis (which is x and z in Unity)</dd></dl>

</div>
</div>
<a id="a1d67369dd495a4237db25ca8bb90a704" name="a1d67369dd495a4237db25ca8bb90a704"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1d67369dd495a4237db25ca8bb90a704">&#9670;&nbsp;</a></span>Distance2d() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Entity.Distance2d </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>point</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Calculate the relative distance to a point on the horizontal axises. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">point</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Return distance on the horizontal axises (which is x and z in Unity)</dd></dl>

</div>
</div>
<a id="a2dd8505150905c103ea90ca50652dbaa" name="a2dd8505150905c103ea90ca50652dbaa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2dd8505150905c103ea90ca50652dbaa">&#9670;&nbsp;</a></span>DistanceVertical() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Entity.DistanceVertical </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td>
          <td class="paramname"><em>target</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Calculate the relative distance to a point on the vertical axis. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">target</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Return distance on the vertical axis (which is y in Unity)</dd></dl>

</div>
</div>
<a id="afa4ae1a9c1a4925bde8e1969aa38d0a6" name="afa4ae1a9c1a4925bde8e1969aa38d0a6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afa4ae1a9c1a4925bde8e1969aa38d0a6">&#9670;&nbsp;</a></span>DistanceVertical() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Entity.DistanceVertical </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>point</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Calculate the relative distance to a point on the vertical axis. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">point</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Return distance on the vertical axis (which is y in Unity)</dd></dl>

</div>
</div>
<a id="ab35b20eb77b836668196c14959f6ba39" name="ab35b20eb77b836668196c14959f6ba39"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab35b20eb77b836668196c14959f6ba39">&#9670;&nbsp;</a></span>DistanceVerticalSigned() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Entity.DistanceVerticalSigned </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td>
          <td class="paramname"><em>target</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Like <a class="el" href="class_lua_1_1_entity.html#a2dd8505150905c103ea90ca50652dbaa" title="Calculate the relative distance to a point on the vertical axis.">DistanceVertical()</a> except return a negative if the target is below </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">target</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Return distance on the vertical axis (which is y in Unity). Can be a negative number</dd></dl>

</div>
</div>
<a id="a166e098ef14ddba3ce2e338773755f8b" name="a166e098ef14ddba3ce2e338773755f8b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a166e098ef14ddba3ce2e338773755f8b">&#9670;&nbsp;</a></span>DistanceVerticalSigned() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Entity.DistanceVerticalSigned </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>point</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Like DistanceVerticalTo() except return a negative if the target is below </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">point</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Return distance on the vertical axis (which is y in Unity). Can be a negative number</dd></dl>

</div>
</div>
<a id="af23f0b36c1b4c778fc7f328490223852" name="af23f0b36c1b4c778fc7f328490223852"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af23f0b36c1b4c778fc7f328490223852">&#9670;&nbsp;</a></span>Engage()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Entity.Engage </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td>
          <td class="paramname"><em>target</em> = <code>null</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>If the entity is an NPC micro will start chasing and firing (and aiming) at designated target if not doing so already. If no target is provided, will attempt to engage the currently assigned aim target. Sort of an all-in-one command where the entity will reposition themselves if too close to or too far from the target. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">target</td><td>target to engage. Doesn't need to be supplied if shooter already has an aim target.</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ac55e7536a3bdc7a6b9a3fa6a759db9ee" name="ac55e7536a3bdc7a6b9a3fa6a759db9ee"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac55e7536a3bdc7a6b9a3fa6a759db9ee">&#9670;&nbsp;</a></span>Equals()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static bool Lua.Entity.Equals </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td>
          <td class="paramname"><em>b</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Tests 2 entities for equality. </p>
<p >Syntax for this operation is <code>entity1 == entity2</code>. Returns true if two variables point to the same entity. The inequality operator <code>entity1 ~= entity2</code> also uses this function, but negated. <br  />
 </p>

</div>
</div>
<a id="ac7eac4ebcb1fe784ab5c06eed7885cf7" name="ac7eac4ebcb1fe784ab5c06eed7885cf7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac7eac4ebcb1fe784ab5c06eed7885cf7">&#9670;&nbsp;</a></span>FindClosestGiantess()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_entity.html">Entity</a> Lua.Entity.FindClosestGiantess </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the closes Giantess <a class="el" href="class_lua_1_1_entity.html" title="A Entity represents Characters and Objects">Entity</a>. It can also return the player. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a2e06bf904c49f705ac361e1538365e2e" name="a2e06bf904c49f705ac361e1538365e2e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2e06bf904c49f705ac361e1538365e2e">&#9670;&nbsp;</a></span>FindClosestMicro()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_entity.html">Entity</a> Lua.Entity.FindClosestMicro </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the closes Micro <a class="el" href="class_lua_1_1_entity.html" title="A Entity represents Characters and Objects">Entity</a>. It can also return the player. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a72e0a626a062ba116cf62cfeb77f87f8" name="a72e0a626a062ba116cf62cfeb77f87f8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a72e0a626a062ba116cf62cfeb77f87f8">&#9670;&nbsp;</a></span>FindRandomBuilding()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Entity.FindRandomBuilding </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>This function a random location of a structure close to the gts </p>
<dl class="section return"><dt>Returns</dt><dd>A position, or null if no building is within 1000 units</dd></dl>

</div>
</div>
<a id="af1290dfaf8e3da8c2adb7279359bf036" name="af1290dfaf8e3da8c2adb7279359bf036"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af1290dfaf8e3da8c2adb7279359bf036">&#9670;&nbsp;</a></span>Flee() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Entity.Flee </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td>
          <td class="paramname"><em>target</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>time</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>The entity will flee from the target during the specified amount of time. A time of 0 will make it unlimited. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">target</td><td></td></tr>
    <tr><td class="paramname">time</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a334e63b56e1c78e6dbebd4dd9c48a69e" name="a334e63b56e1c78e6dbebd4dd9c48a69e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a334e63b56e1c78e6dbebd4dd9c48a69e">&#9670;&nbsp;</a></span>Flee() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Entity.Flee </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>position</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>time</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>The entity will flee from the position during the specified amount of time. A time of 0 will make it unlimited. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">position</td><td></td></tr>
    <tr><td class="paramname">time</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="aa9df9f39762cb24b89449e8b61aab43c" name="aa9df9f39762cb24b89449e8b61aab43c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa9df9f39762cb24b89449e8b61aab43c">&#9670;&nbsp;</a></span>GetRandomGiantess()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_entity.html">Entity</a> Lua.Entity.GetRandomGiantess </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns a random giantess in the scene. Can return null if there are no giantesses in the scene. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="aad0651348795eb39acee39055e0b7638" name="aad0651348795eb39acee39055e0b7638"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aad0651348795eb39acee39055e0b7638">&#9670;&nbsp;</a></span>GetRandomMicro()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_entity.html">Entity</a> Lua.Entity.GetRandomMicro </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns a random micro in the scene. Can return null if there are no micros in the scene. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="af3f7aae31ecb691380a6f18f053fb907" name="af3f7aae31ecb691380a6f18f053fb907"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af3f7aae31ecb691380a6f18f053fb907">&#9670;&nbsp;</a></span>GetSelectedEntity()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="class_lua_1_1_entity.html">Entity</a> Lua.Entity.GetSelectedEntity </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns the currently selected entity in the Editor. </p>
<p >It can return nil if there is no entity currently selected. </p><dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a6ddff7e7a95c85ba34bee6b8b5c4ee93" name="a6ddff7e7a95c85ba34bee6b8b5c4ee93"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6ddff7e7a95c85ba34bee6b8b5c4ee93">&#9670;&nbsp;</a></span>Grab()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Entity.Grab </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td>
          <td class="paramname"><em>target</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>If the entity is a giantess will grab a target. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">target</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a92feb21c4219c60a7e5935733302083f" name="a92feb21c4219c60a7e5935733302083f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a92feb21c4219c60a7e5935733302083f">&#9670;&nbsp;</a></span>Grow() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Entity.Grow </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>factor</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Adds a <b>priority</b> Grow action at the <b>beginning</b> of the action queue. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">factor</td><td>a percentage to grow by every second, relative to current size. A factor of 1 results in growing by 100% (doubling in size) every second. Negative values will make it shrink.</td></tr>
  </table>
  </dd>
</dl>
<p>The entity will indefinitely grow exponentially by a given factor relative to their size (e.g. 0.1 = 10% per second).</p>
<p >This action can be performed simultaneously with other actions. </p>

</div>
</div>
<a id="ac574fce9abb6f90e7f7675be74838964" name="ac574fce9abb6f90e7f7675be74838964"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac574fce9abb6f90e7f7675be74838964">&#9670;&nbsp;</a></span>Grow() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Entity.Grow </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>factor</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>time</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Adds a <b>priority</b> Grow action at the <b>beginning</b> of the action queue. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">factor</td><td>a percentage of the original size to grow by. Negative values will make it shrink.</td></tr>
    <tr><td class="paramname">time</td><td>a time over which the growth will be spread. Does not affect the final size, only how long will it take to achieve it.</td></tr>
  </table>
  </dd>
</dl>
<p>The entity will grow linearly by a given factor relative to their size, over the given time (e.g. growing by 10% over 10s would grow every second by 1% of the original size).</p>
<p >This action can be performed simultaneously with other actions. </p>

</div>
</div>
<a id="a0007133219ff5ec24e9eecf6a9d2dd50" name="a0007133219ff5ec24e9eecf6a9d2dd50"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0007133219ff5ec24e9eecf6a9d2dd50">&#9670;&nbsp;</a></span>GrowAndWait()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Entity.GrowAndWait </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>factor</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>time</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Adds a regular Grow action at the end of the action queue. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">factor</td><td>a percentage of the original size to grow by. Negative values will make it shrink.</td></tr>
    <tr><td class="paramname">time</td><td>a time over which the growth will be spread. Does not affect the final size, only how long will it take to achieve it</td></tr>
  </table>
  </dd>
</dl>
<p>The entity will grow linearly by a given factor relative to their size, over the given time (e.g. growing by 10% over 10s would grow every second by 1% of the original size).</p>
<p >This action <b>cannot</b> be performed simultaneously with other actions. </p>

</div>
</div>
<a id="a4b4bb9870796b854c0d5666c9cba9375" name="a4b4bb9870796b854c0d5666c9cba9375"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4b4bb9870796b854c0d5666c9cba9375">&#9670;&nbsp;</a></span>IsCrushed()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.Entity.IsCrushed </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Checks if the entity is crushed. Only applicable to micros. It doesn't matter whether it survived the crush or not. Returns false for deleted entities, even if they were crushed prior to deleting. </p>
<dl class="section return"><dt>Returns</dt><dd>true if the entity is crushed</dd></dl>

</div>
</div>
<a id="a6357c0a54e9aa79beb928d079b42aa76" name="a6357c0a54e9aa79beb928d079b42aa76"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6357c0a54e9aa79beb928d079b42aa76">&#9670;&nbsp;</a></span>IsDead()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.Entity.IsDead </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Checks if the entity is dead. </p>
<dl class="section return"><dt>Returns</dt><dd>true if the entity is dead</dd></dl>

</div>
</div>
<a id="af72e042e1fd05c66abceebb49ec2caf4" name="af72e042e1fd05c66abceebb49ec2caf4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af72e042e1fd05c66abceebb49ec2caf4">&#9670;&nbsp;</a></span>isGiantess()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.Entity.isGiantess </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns true if the entity is a giantess (.gts). </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a27a8d5461e9d6890e085b06e2d00f6bd" name="a27a8d5461e9d6890e085b06e2d00f6bd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a27a8d5461e9d6890e085b06e2d00f6bd">&#9670;&nbsp;</a></span>isHumanoid()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.Entity.isHumanoid </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns true if the entity is a humanoid character. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a5eaa128b6b8cf4aeb7f219edd030d61e" name="a5eaa128b6b8cf4aeb7f219edd030d61e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5eaa128b6b8cf4aeb7f219edd030d61e">&#9670;&nbsp;</a></span>isMicro()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.Entity.isMicro </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns true if the entity is a micro (.micro) </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="af7b9099c16b719f42e4fdfd82661d259" name="af7b9099c16b719f42e4fdfd82661d259"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af7b9099c16b719f42e4fdfd82661d259">&#9670;&nbsp;</a></span>isPlayer()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.Entity.isPlayer </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns true if this entity is player controlled (micro or giantess). </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ab25d357d357ab0cf7ff9a2e81aa9cb08" name="ab25d357d357ab0cf7ff9a2e81aa9cb08"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab25d357d357ab0cf7ff9a2e81aa9cb08">&#9670;&nbsp;</a></span>IsStuck()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.Entity.IsStuck </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Checks if the entity is stuck to giantess. Only applicable to micros. </p>
<dl class="section return"><dt>Returns</dt><dd>true if the entity is stuck</dd></dl>

</div>
</div>
<a id="aa8688dacb32db168b780597d8f11622b" name="aa8688dacb32db168b780597d8f11622b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa8688dacb32db168b780597d8f11622b">&#9670;&nbsp;</a></span>IsTargettable()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.Entity.IsTargettable </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Checks if the entity be targeted for crushing. To be targettable it must be a living micro that is not stuck. </p>
<dl class="section return"><dt>Returns</dt><dd>true if the entity can be targeted</dd></dl>

</div>
</div>
<a id="a29cdb052c5422873a708c8080039cb4b" name="a29cdb052c5422873a708c8080039cb4b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a29cdb052c5422873a708c8080039cb4b">&#9670;&nbsp;</a></span>LookAt()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Entity.LookAt </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td>
          <td class="paramname"><em>target</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Not to be confused with <a class="el" href="class_lua_1_1_transform.html#a1e722de9c3eacff82477ab7684a67553" title="Not to be confused with Entity.LookAt Rotates the transform so the forward vector points at /target/&#39;...">Transform.LookAt</a> If the entity is a giantess it will look towards a target. If the target is nil the giantess will return to default looking behaviour </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">target</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a307f39e2316f0c4246604ba5ce5b749e" name="a307f39e2316f0c4246604ba5ce5b749e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a307f39e2316f0c4246604ba5ce5b749e">&#9670;&nbsp;</a></span>MoveTo() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Entity.MoveTo </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td>
          <td class="paramname"><em>targetEntity</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>The entity will walk towards another target entity, and stop when it reaches it. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">targetEntity</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a5d6cfb68967adf948db2b6c09d7dfd38" name="a5d6cfb68967adf948db2b6c09d7dfd38"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5d6cfb68967adf948db2b6c09d7dfd38">&#9670;&nbsp;</a></span>MoveTo() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Entity.MoveTo </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>destination</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>The entity will walk to a designed point. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">destination</td><td><a class="el" href="class_lua_1_1_vector3.html" title="Representation of 3D vectors and points.">Vector3</a> that cointains the destination point in world coordinates</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a32545d25ff6935a006bdb6b5ad45ad9a" name="a32545d25ff6935a006bdb6b5ad45ad9a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a32545d25ff6935a006bdb6b5ad45ad9a">&#9670;&nbsp;</a></span>Seek()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Entity.Seek </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td>
          <td class="paramname"><em>target</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>duration</em> = <code>0</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>separation</em> = <code>-1</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>The entity will seek toward another target until the time runs out or it is close enough. Used in the "Follow" command. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">target</td><td>entity to follow</td></tr>
    <tr><td class="paramname">duration</td><td>how long to go. 0 means forever</td></tr>
    <tr><td class="paramname">separation</td><td>how close to get, measured in radiuses of the bigger entity. 0 means touching, -1 means standing in the same spot (not possible)</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a0a8058c8b504215e492471b4e7d557d4" name="a0a8058c8b504215e492471b4e7d557d4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0a8058c8b504215e492471b4e7d557d4">&#9670;&nbsp;</a></span>Stomp() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Entity.Stomp </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td>
          <td class="paramname"><em>target</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>If the entity is a giantess, she will stomp a target entity. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">target</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ad6c8d1dc736aeea69e734d3c1e884343" name="ad6c8d1dc736aeea69e734d3c1e884343"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad6c8d1dc736aeea69e734d3c1e884343">&#9670;&nbsp;</a></span>Stomp() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Entity.Stomp </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>target</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>If the entity is a giantess, she will stomp a target point. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">target</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a6be24874965a4015e7fd8244fa345220" name="a6be24874965a4015e7fd8244fa345220"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6be24874965a4015e7fd8244fa345220">&#9670;&nbsp;</a></span>UpdateMeshCollider()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Entity.UpdateMeshCollider </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>[Slow operation] Forces the update of the collider. This is a slow operacion, don't update the mesh every frame, don't use inside a loop. </p>
<p >It updates the mesh collider of a giantess by copying all the vertices, splitting them into each bone, and the baking a new mesh for each collider. It may be a O(n^2) operation depending in the number of vertices the model. </p>

</div>
</div>
<a id="a58a5bb200f7182dea5e622f036f05bf3" name="a58a5bb200f7182dea5e622f036f05bf3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a58a5bb200f7182dea5e622f036f05bf3">&#9670;&nbsp;</a></span>Wait()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Entity.Wait </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>time</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>The entity will wait for the specified amount of time. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">time</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a9858a940de17a0405da24bff1d834970" name="a9858a940de17a0405da24bff1d834970"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9858a940de17a0405da24bff1d834970">&#9670;&nbsp;</a></span>Wander()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Entity.Wander </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>time</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>The entity will wander during the specified amount of time. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">time</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="a1873494f26c8f90c79254b43d25d47f7" name="a1873494f26c8f90c79254b43d25d47f7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1873494f26c8f90c79254b43d25d47f7">&#9670;&nbsp;</a></span>baseHeight</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual float Lua.Entity.baseHeight</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Base model height of this entity. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a8d4bdecb96c327395e5ddbde88608cf4" name="a8d4bdecb96c327395e5ddbde88608cf4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8d4bdecb96c327395e5ddbde88608cf4">&#9670;&nbsp;</a></span>bones</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_bones.html">Bones</a> Lua.Entity.bones</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Access the bone transforms of the model (head, hands, feet, etc). </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a6b6baf8292fe2447ad0620722bc24526" name="a6b6baf8292fe2447ad0620722bc24526"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6b6baf8292fe2447ad0620722bc24526">&#9670;&nbsp;</a></span>height</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual float Lua.Entity.height</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The height of this entity. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a7776e8422e86d2ab5670ca314a65aab5" name="a7776e8422e86d2ab5670ca314a65aab5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7776e8422e86d2ab5670ca314a65aab5">&#9670;&nbsp;</a></span>id</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int Lua.Entity.id</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the id associated to this entity. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a42cd1e5e507a1e79eb1161799564da88" name="a42cd1e5e507a1e79eb1161799564da88"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a42cd1e5e507a1e79eb1161799564da88">&#9670;&nbsp;</a></span>ik</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_i_k.html">IK</a> Lua.Entity.ik</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Inverse Kinematics for the model </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a133133afe701b7ca4f0e2d6632beae33" name="a133133afe701b7ca4f0e2d6632beae33"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a133133afe701b7ca4f0e2d6632beae33">&#9670;&nbsp;</a></span>maxSize</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual float Lua.Entity.maxSize</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The max scale for this entity. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ac821c347ba04965d00132f02e88adecd" name="ac821c347ba04965d00132f02e88adecd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac821c347ba04965d00132f02e88adecd">&#9670;&nbsp;</a></span>metricHeight</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Entity.metricHeight</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The height of this entity in meters relative to the current map. If you want to set height in a unit other then meters you can convert it with <a class="el" href="class_lua_1_1_mathf.html#ab274b83f44ab3e9457e67f949097bc27" title="Converts a strings length into meters that can be used to scale entities height.">Mathf.ConvertToMeter</a> </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a642603e2952e4fcea70979837049f813" name="a642603e2952e4fcea70979837049f813"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a642603e2952e4fcea70979837049f813">&#9670;&nbsp;</a></span>minSize</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual float Lua.Entity.minSize</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The min scale for this entity. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="acd67b39a7c95e3cb87171073c2877de1" name="acd67b39a7c95e3cb87171073c2877de1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acd67b39a7c95e3cb87171073c2877de1">&#9670;&nbsp;</a></span>modelName</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">string Lua.Entity.modelName</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Name the model of this entity. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a8155b6c6ef0f0630ec7e818dd4cdaec4" name="a8155b6c6ef0f0630ec7e818dd4cdaec4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8155b6c6ef0f0630ec7e818dd4cdaec4">&#9670;&nbsp;</a></span>name</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">string Lua.Entity.name</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The name of this entity. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ad8bd97d98fddc9b89f8410512b502c3f" name="ad8bd97d98fddc9b89f8410512b502c3f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad8bd97d98fddc9b89f8410512b502c3f">&#9670;&nbsp;</a></span>position</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Entity.position</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the current position on world space of this entity. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a784673c0e6fbf29381a309a5df0ee10e" name="a784673c0e6fbf29381a309a5df0ee10e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a784673c0e6fbf29381a309a5df0ee10e">&#9670;&nbsp;</a></span>scale</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual float Lua.Entity.scale</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The scale of this entity. Use this instead of the transform when possible. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaEntity.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_entity.html">Entity</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

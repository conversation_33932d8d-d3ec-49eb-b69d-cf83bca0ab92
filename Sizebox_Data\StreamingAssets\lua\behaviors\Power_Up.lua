Rampage = RegisterBehavior("Power Up")
Rampage.scores = {   --[[ the scores are set this way, for each personality or state.. is a value from 0 to 100 ]]
    hostile = 50     --[[ the higher the value the more likely to choose that action ]]
}
Rampage.data = {
    agent = {
        type = { "giantess" }
    },
    target = {
        type = { "oneself" }
    }
}
    
JUMP_ANIMATION = "Jump 3"
IDLE_ANIMATION = "Idle 2"

function Rampage:Start() 
    self.agent.animation.Set(JUMP_ANIMATION, true)
	self.agent.Wait(1)
    self.agent.grow(2,3)
	self.agent.Wait(3)
	self.agent.animation.Set(IDLE_ANIMATION)
end

Behavior = RegisterBehavior("playasfc")

Behavior.data = { 

    agent = {
        type = { "oneself", "player" },
        exclude = { "giantess" }
    },
    target = {
        type = { "giantess" },
        exclude = { "micro", "player" }
    },

    menuEntry = "Play As (Full Control!)",
    ai = false,
    secondary = true,
    flags = { 'playasgts' },
	forceAppearInManager = true,
	settings = {
        { "crouchkey", "Crouch Key", "keybind", "b" },
        { "hairoffset", "Hair Offset", "float", 10 }
	}
}

function Behavior:Start()
    --t
    if (crouchkey == 'c') then
        log('Dont Use C!')
    end
    agentscale = self.agent.scale
    dpressed = false
    apressed = false
    loop = true
    wpressed = false
    spressed = false
    crouching = false
end

function Behavior:Update()
    self.agent.transform.SetParent(self.target.bones.head)
    self.target.rigidbody.addForce(Vector3.new(0,0.1,0))
    --player.transform.translate(agentx,agenty,agentz)
    if not hairoffset then
        hairset = 180
    else
        hairset = 180 + hairoffset
    end
    self.agent.transform.localPosition = Vector3.new(0,hairset,0)
    self.agent.scale = 1
    self.agent.transform.rotation = self.target.transform.rotation
    if not crouching then
        self.agent.animation.setPose('Laying08')
        self.agent.animation.setSpeed(100)
        if(Input.GetKeyDown("d"))then
            dpressed = true
        end
        if (Input.GetKeyDown("c")) then
            self:Exit()
        end
        if(Input.GetKeyDown("w"))then
            wpressed = true
            self.target.animation.set('Walking')
        end
        if(Input.GetKeyDown("b")) then
            crouching = true
        end
        if(Input.GetKeyUp("b")) then
            crouching = false
        end
        if(Input.GetKeyDown("x")) then
            self.agent.ai.StopSecondaryBehavior('playasgts')
            self.agent.ai.StopBehavior()
            self:Exit()
        end
        if(Input.GetKeyDown("a"))then
            apressed = true
        end
        if(Input.GetKeyDown("s"))then
            spressed = true
            log('Walking Backwards Was Disabled! (I Reserve The Right to ask for a removal! Of Any Fixes)')
        end
        if(Input.GetKeyUp("d"))then
            dpressed = false
            self.target.animation.set('Idle')
        end
        if(Input.GetKeyUp("w"))then
            wpressed = false
            self.target.animation.set('Idle')
        end
        if(Input.GetKeyUp("a"))then
            apressed = false
            self.target.animation.set('Idle')
        end
        if(Input.GetKeyUp("s"))then
            spressed = false
            self.target.animation.set('Idle')
        end
        if dpressed then
            if not (self.target.animation.GetTime() < 0) then
                self.target.transform.rotate(0,1,0)
            end
        end
        if apressed then
            if not (self.target.animation.GetTime() < 0) then
                self.target.transform.rotate(0,-1,0)
            end
        end
        if spressed then
            if not (self.target.animation.GetTime() < 0) then
                --self.target.animation.set('Walking')
            end
        end
        --if not dpressed or not apressed or not spressed or not wpressed then
            --self.target.animation.set('Idle')
        --end
    else
        self.agent.animation.setPose('sitting10')
        if(Input.GetKeyDown("d"))then
            dpressed = true
        end
        if (Input.GetKeyDown("c")) then
            self:Exit()
        end
        if(Input.GetKeyDown("w"))then
            wpressed = true
            self.target.animation.set('Crawl')
        end
        if(Input.GetKeyDown("b")) then
            crouching = true
        end
        if(Input.GetKeyUp("b")) then
            crouching = false
        end
        if(Input.GetKeyDown("x")) then
            self.agent.ai.StopSecondaryBehavior('playasgts')
            self.agent.ai.StopBehavior()
            self:Exit()
        end

        if(Input.GetKeyDown("a"))then
            apressed = true
        end
        if(Input.GetKeyDown("s"))then
            spressed = true
            log('Walking Backwards Was Disabled! (I Reserve The Right to ask for a removal! Of Any Fixes)')
        end
        if(Input.GetKeyUp("d"))then
            dpressed = false
            self.target.animation.set('Crouch Idle')
        end
        if(Input.GetKeyUp("w"))then
            wpressed = false
            self.target.animation.set('Crouch Idle')
        end
        if(Input.GetKeyUp("a"))then
            apressed = false
            self.target.animation.set('Crouch Idle')
        end
        if(Input.GetKeyUp("s"))then
            spressed = false
            self.target.animation.set('Crouch Idle')
        end
        if dpressed then
            if not (self.target.animation.GetTime() < 0) then
                self.target.transform.rotate(0,1,0)
                self.target.transform.translate(1,0,0)
            end
        end
        if apressed then
            if not (self.target.animation.GetTime() < 0) then
                self.target.transform.rotate(0,-1,0)
                self.target.transform.translate(1,0,0)
            end
        end
        if wpressed then
            if not (self.target.animation.GetTime() < 0) then
                self.target.transform.translate(1,0,0)
            end
        end
        if spressed then
            if not (self.target.animation.GetTime() < 0) then
                --self.target.animation.set('Walking')
                log('Walking Backwards Was Disabled! (if you know how feel free to fix it and DM me a copy. However if you upload it without DMing me a copy i will ask the admins to take it down)')
            end
        end
        --if not dpressed or not apressed or not spressed or not wpressed then
            --self.target.animation.set('Idle')
        --end
    end
end

function Behavior:Exit()
    self.agent.scale = agentscale
    self.agent.animation.setSpeed(1)
    self.target.animation.setPose('idle_00')
    self.target.animation.set('Idle')
end

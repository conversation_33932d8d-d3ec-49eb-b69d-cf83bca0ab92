Stroll = RegisterBehavior("StrollStomp_Seek")
Stroll.scores = {
    hostile = 100,     --[[ the higher the value the more likely to choose that action ]]
    curious = -30,
}
Stroll.data = {
	menuEntry = "Walk/Seek_and_hide",
    agent = {
        type = { "giantess" }
    },
    target = {
        type = { "player" }
    }
}

IDLE = "Idle 2"
WALK = "Walk"
Greet = "Greet 3"
GRAB = "Lifting"
stompAnimModule = require "stomp_anim"

function Stroll:Start()
    self.stop = false -- A stop variable to end the behavior.. this is custom for this script
	self.agent.senses.baseVisibilityDistance = 30000
	self.agent.senses.fieldOfView = 180
    --self.target = self.agent.FindRandomBuilding(self.agent) --  check if theres a building to walk into
    if not self.target then
		self.stop = true    --  there are no more buildings near the gts, stop the script
		log("No building was found for the stroll script")
		return -- return early, don't bother making a seed since we're not going to run anyway
	end
	Random.InitState(Mathf.Round(Time.timeSinceLevelLoad)*self.agent.id) -- get a somewhat random seed for the animations
	self.see = false
	self.waiting = "wait"
    self.waitEndTime = 0

    -- NEW: Toggle between stomp and grab modes
    self.grabMode = false  -- false = stomp, true = grab
    Game.Toast.New().Print("Hide and Seek: STOMP mode (Press F to toggle)")
    log("Hide and Seek started in STOMP mode - Press F to toggle to GRAB mode")
end

function Stroll:Update()
	if self.stop then
		self.agent.ai.StopBehavior() -- if you use Update() you must manually tell when to end the behavior, after this the Exit() method will run
		return
	else

	-- NEW: Check for F key to toggle between stomp and grab
	if Input.GetKeyDown(KeyCode.F) then
		self.grabMode = not self.grabMode
		if self.grabMode then
			Game.Toast.New().Print("Hide and Seek: GRAB mode")
			log("Switched to GRAB mode")
		else
			Game.Toast.New().Print("Hide and Seek: STOMP mode")
			log("Switched to STOMP mode")
		end
	end

	if self.agent.senses.CanSee(self.target) then
		self.see = true
	end

	if self.see then
		if self.waiting == "wait" then
			self.agent.animation.Set(IDLE)
			self.agent.ai.StopAction()
			self.agent.ai.CancelQueuedActions()
			self.waitEndTime = Time.time + 1
			self.waiting = "walkto"
			return
		end
		
		if self.waiting == "walkto" and Time.time >= self.waitEndTime then
			self.agent.Face(self.target)
			self.agent.LookAt(self.target)
			self.waitEndTime = Time.time + 1
			self.waiting = "prepare"
			return
		end
		
		if self.waiting == "prepare" and Time.time >= self.waitEndTime then
			self.agent.animation.SetAndWait(Greet) 
			self.waitEndTime = Time.time + 4
			self.waiting = "greet"
			return
		end
		
		if self.waiting == "greet" and Time.time >= self.waitEndTime then
			if self.grabMode then
				-- GRAB MODE - Move to player first
				log("I see you! Moving to grab you!")
				self.agent.animation.Set(WALK)
				self.agent.MoveTo(self.target)
				self.waitEndTime = Time.time + 2
				self.waiting = "moveto"
			else
				-- STOMP MODE
				log("I see you! Game over.")
				self.agent.ai.SetBehavior("Stomp", self.target)
				self.agent.ai.StopBehavior()
			end
			return
		end

		if self.waiting == "moveto" and Time.time >= self.waitEndTime then
			-- Now grab the player and show toast
			log("Grabbing you!")
			Game.Toast.New().Print("I found you!")
			self.agent.animation.Set(GRAB)
			self.agent.Grab(self.target)
			self.agent.ai.StopBehavior()
			return
		end
	end
	
	if not self.agent.ai.IsActionActive() and not self.see then
		self.agent.LookAt(nil)
		self.target2 = self.agent.FindRandomBuilding(self.agent) --  check if there is any building nearby
		self.agent.animation.Set(WALK)
		self.agent.MoveTo(self.target2)   -- Move to the target, buildings are static so use the cheapest call possible
		self.agent.animation.Set(IDLE)
		self.agent.Stomp(self.target2)
	end

	if not self.target then
		self.stop = true    --  there are no more buildings near the gts, stop the script
		return
	end

	self.stop = self.agent.ai.IsAIEnabled() or self.agent.ai.HasQueuedBehaviors() 
	end
end

function Stroll:Exit()
	self.agent.animation.Set(IDLE)
end

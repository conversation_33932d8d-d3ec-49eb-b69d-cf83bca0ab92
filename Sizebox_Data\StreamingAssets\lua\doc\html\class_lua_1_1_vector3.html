<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.Vector3 Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_vector3.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="class_lua_1_1_vector3-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.Vector3 Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Representation of 3D vectors and points.  
 <a href="class_lua_1_1_vector3.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a63129be99b82f76bb94c8267b0dcd692"><td class="memItemLeft" align="right" valign="top"><a id="a63129be99b82f76bb94c8267b0dcd692" name="a63129be99b82f76bb94c8267b0dcd692"></a>
override string&#160;</td><td class="memItemRight" valign="bottom"><b>ToString</b> ()</td></tr>
<tr class="memdesc:a63129be99b82f76bb94c8267b0dcd692"><td class="mdescLeft">&#160;</td><td class="mdescRight">String representation of this vector. <br /></td></tr>
<tr class="separator:a63129be99b82f76bb94c8267b0dcd692"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5855d8e4953dffcb076b9e5949406203"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_vector3.html#a5855d8e4953dffcb076b9e5949406203">Normalize</a> ()</td></tr>
<tr class="memdesc:a5855d8e4953dffcb076b9e5949406203"><td class="mdescLeft">&#160;</td><td class="mdescRight">Makes this vector have a length of 1.  <a href="class_lua_1_1_vector3.html#a5855d8e4953dffcb076b9e5949406203">More...</a><br /></td></tr>
<tr class="separator:a5855d8e4953dffcb076b9e5949406203"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5f40e6344654b7590958df867f1d5b03"><td class="memItemLeft" align="right" valign="top"><a id="a5f40e6344654b7590958df867f1d5b03" name="a5f40e6344654b7590958df867f1d5b03"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>Set</b> (float <a class="el" href="class_lua_1_1_vector3.html#af2367a6c9fb9484cd8703ef20bbe3e2b">x</a>, float <a class="el" href="class_lua_1_1_vector3.html#a93844cc4b95c4b4cc2152ecd3d6a69ed">y</a>, float <a class="el" href="class_lua_1_1_vector3.html#a80737d9f0e18357fd716e47a1b82ef6a">z</a>)</td></tr>
<tr class="memdesc:a5f40e6344654b7590958df867f1d5b03"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set x, y and z components of an existing <a class="el" href="class_lua_1_1_vector3.html" title="Representation of 3D vectors and points.">Vector3</a>. <br /></td></tr>
<tr class="separator:a5f40e6344654b7590958df867f1d5b03"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-static-methods" name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:ad6514c4819aa2dabb95253f9d6f5248c"><td class="memItemLeft" align="right" valign="top"><a id="ad6514c4819aa2dabb95253f9d6f5248c" name="ad6514c4819aa2dabb95253f9d6f5248c"></a>
static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><b>New</b> (float <a class="el" href="class_lua_1_1_vector3.html#af2367a6c9fb9484cd8703ef20bbe3e2b">x</a>, float <a class="el" href="class_lua_1_1_vector3.html#a93844cc4b95c4b4cc2152ecd3d6a69ed">y</a>, float <a class="el" href="class_lua_1_1_vector3.html#a80737d9f0e18357fd716e47a1b82ef6a">z</a>)</td></tr>
<tr class="memdesc:ad6514c4819aa2dabb95253f9d6f5248c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a new vector with given x, y, z components. <br /></td></tr>
<tr class="separator:ad6514c4819aa2dabb95253f9d6f5248c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7a6a85bde6d3a1072d52ebf902f291e0"><td class="memItemLeft" align="right" valign="top">static string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_vector3.html#a7a6a85bde6d3a1072d52ebf902f291e0">Concat</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> o, string v)</td></tr>
<tr class="memdesc:a7a6a85bde6d3a1072d52ebf902f291e0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Concatenates a vector and a string.  <a href="class_lua_1_1_vector3.html#a7a6a85bde6d3a1072d52ebf902f291e0">More...</a><br /></td></tr>
<tr class="separator:a7a6a85bde6d3a1072d52ebf902f291e0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a185d18054534f55a4ce5f951e157df96"><td class="memItemLeft" align="right" valign="top">static string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_vector3.html#a185d18054534f55a4ce5f951e157df96">Concat</a> (string v, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> o)</td></tr>
<tr class="memdesc:a185d18054534f55a4ce5f951e157df96"><td class="mdescLeft">&#160;</td><td class="mdescRight">Concatenates a string and a vector.  <a href="class_lua_1_1_vector3.html#a185d18054534f55a4ce5f951e157df96">More...</a><br /></td></tr>
<tr class="separator:a185d18054534f55a4ce5f951e157df96"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa50462b9cd533a0b1216975d52940d5a"><td class="memItemLeft" align="right" valign="top">static string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_vector3.html#aa50462b9cd533a0b1216975d52940d5a">Concat</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> o1, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> o2)</td></tr>
<tr class="memdesc:aa50462b9cd533a0b1216975d52940d5a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Concatenates 2 vectors.  <a href="class_lua_1_1_vector3.html#aa50462b9cd533a0b1216975d52940d5a">More...</a><br /></td></tr>
<tr class="separator:aa50462b9cd533a0b1216975d52940d5a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaa648399828fb59c6ad750f2f3ad09d6"><td class="memItemLeft" align="right" valign="top">static bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_vector3.html#aaa648399828fb59c6ad750f2f3ad09d6">Eq</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> o1, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> o2)</td></tr>
<tr class="memdesc:aaa648399828fb59c6ad750f2f3ad09d6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tests 2 vectors for equality.  <a href="class_lua_1_1_vector3.html#aaa648399828fb59c6ad750f2f3ad09d6">More...</a><br /></td></tr>
<tr class="separator:aaa648399828fb59c6ad750f2f3ad09d6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae40267fccb8c1e9d79d704776b07e949"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_vector3.html#ae40267fccb8c1e9d79d704776b07e949">operator+</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> o1, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> o2)</td></tr>
<tr class="memdesc:ae40267fccb8c1e9d79d704776b07e949"><td class="mdescLeft">&#160;</td><td class="mdescRight">Adds 2 vectors.  <a href="class_lua_1_1_vector3.html#ae40267fccb8c1e9d79d704776b07e949">More...</a><br /></td></tr>
<tr class="separator:ae40267fccb8c1e9d79d704776b07e949"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a62cecbc7efa2173accf443a1d3ff41e4"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_vector3.html#a62cecbc7efa2173accf443a1d3ff41e4">operator-</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> o1, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> o2)</td></tr>
<tr class="memdesc:a62cecbc7efa2173accf443a1d3ff41e4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Subtracts 2 vectors.  <a href="class_lua_1_1_vector3.html#a62cecbc7efa2173accf443a1d3ff41e4">More...</a><br /></td></tr>
<tr class="separator:a62cecbc7efa2173accf443a1d3ff41e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a40ea1014f1fd49f4c2fce2b7c77bcfc1"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_vector3.html#a40ea1014f1fd49f4c2fce2b7c77bcfc1">operator-</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> o1)</td></tr>
<tr class="memdesc:a40ea1014f1fd49f4c2fce2b7c77bcfc1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Negates (inverts) a vector.  <a href="class_lua_1_1_vector3.html#a40ea1014f1fd49f4c2fce2b7c77bcfc1">More...</a><br /></td></tr>
<tr class="separator:a40ea1014f1fd49f4c2fce2b7c77bcfc1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9f7843705e8cb31af4a2fa885ec599de"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_vector3.html#a9f7843705e8cb31af4a2fa885ec599de">operator*</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> o1, float f)</td></tr>
<tr class="memdesc:a9f7843705e8cb31af4a2fa885ec599de"><td class="mdescLeft">&#160;</td><td class="mdescRight">Multiplies a vector by a scalar (float).  <a href="class_lua_1_1_vector3.html#a9f7843705e8cb31af4a2fa885ec599de">More...</a><br /></td></tr>
<tr class="separator:a9f7843705e8cb31af4a2fa885ec599de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a78ff76fd80ba422a745612959099a1fa"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_vector3.html#a78ff76fd80ba422a745612959099a1fa">operator*</a> (float f, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> o1)</td></tr>
<tr class="memdesc:a78ff76fd80ba422a745612959099a1fa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Multiplies a vector by a scalar (float).  <a href="class_lua_1_1_vector3.html#a78ff76fd80ba422a745612959099a1fa">More...</a><br /></td></tr>
<tr class="separator:a78ff76fd80ba422a745612959099a1fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2e065e07d84200e5a9cec3270ef1d30f"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_vector3.html#a2e065e07d84200e5a9cec3270ef1d30f">operator/</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> o1, float f)</td></tr>
<tr class="memdesc:a2e065e07d84200e5a9cec3270ef1d30f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Divides a vector by a scalar (float).  <a href="class_lua_1_1_vector3.html#a2e065e07d84200e5a9cec3270ef1d30f">More...</a><br /></td></tr>
<tr class="separator:a2e065e07d84200e5a9cec3270ef1d30f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab278cbf1ea74ca78089e58c0a4313c6a"><td class="memItemLeft" align="right" valign="top"><a id="ab278cbf1ea74ca78089e58c0a4313c6a" name="ab278cbf1ea74ca78089e58c0a4313c6a"></a>
static float&#160;</td><td class="memItemRight" valign="bottom"><b>Angle</b> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> from, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> to)</td></tr>
<tr class="memdesc:ab278cbf1ea74ca78089e58c0a4313c6a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the angle in degrees between from and to. <br /></td></tr>
<tr class="separator:ab278cbf1ea74ca78089e58c0a4313c6a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab4ac1349372b28a8e490a94e96d950aa"><td class="memItemLeft" align="right" valign="top"><a id="ab4ac1349372b28a8e490a94e96d950aa" name="ab4ac1349372b28a8e490a94e96d950aa"></a>
static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><b>ClampMagnitude</b> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> vector, float maxLength)</td></tr>
<tr class="memdesc:ab4ac1349372b28a8e490a94e96d950aa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a copy of vector with its magnitude clamped to maxLength. <br /></td></tr>
<tr class="separator:ab4ac1349372b28a8e490a94e96d950aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aada99c9426a2253aed6e6a150efade76"><td class="memItemLeft" align="right" valign="top"><a id="aada99c9426a2253aed6e6a150efade76" name="aada99c9426a2253aed6e6a150efade76"></a>
static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><b>Cross</b> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> lhs, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> rhs)</td></tr>
<tr class="memdesc:aada99c9426a2253aed6e6a150efade76"><td class="mdescLeft">&#160;</td><td class="mdescRight">Cross Product of two vectors. <br /></td></tr>
<tr class="separator:aada99c9426a2253aed6e6a150efade76"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8c4bd7e288f7857355aff2f159e86b83"><td class="memItemLeft" align="right" valign="top"><a id="a8c4bd7e288f7857355aff2f159e86b83" name="a8c4bd7e288f7857355aff2f159e86b83"></a>
static float&#160;</td><td class="memItemRight" valign="bottom"><b>Distance</b> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> a, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> b)</td></tr>
<tr class="memdesc:a8c4bd7e288f7857355aff2f159e86b83"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the distance between a and b. <br /></td></tr>
<tr class="separator:a8c4bd7e288f7857355aff2f159e86b83"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aee6533ff540a011a854efbbe29664fe4"><td class="memItemLeft" align="right" valign="top"><a id="aee6533ff540a011a854efbbe29664fe4" name="aee6533ff540a011a854efbbe29664fe4"></a>
static float&#160;</td><td class="memItemRight" valign="bottom"><b>Dot</b> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> lhs, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> rhs)</td></tr>
<tr class="memdesc:aee6533ff540a011a854efbbe29664fe4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Dot Product of two vectors. <br /></td></tr>
<tr class="separator:aee6533ff540a011a854efbbe29664fe4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2ac180084d2490e519612ccba40da454"><td class="memItemLeft" align="right" valign="top"><a id="a2ac180084d2490e519612ccba40da454" name="a2ac180084d2490e519612ccba40da454"></a>
static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><b>Lerp</b> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> a, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> b, float t)</td></tr>
<tr class="memdesc:a2ac180084d2490e519612ccba40da454"><td class="mdescLeft">&#160;</td><td class="mdescRight">Linearly interpolates between two vectors. <br /></td></tr>
<tr class="separator:a2ac180084d2490e519612ccba40da454"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a63b62cb18ab91477aee5c9bd0d975400"><td class="memItemLeft" align="right" valign="top"><a id="a63b62cb18ab91477aee5c9bd0d975400" name="a63b62cb18ab91477aee5c9bd0d975400"></a>
static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><b>LerpUnclamped</b> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> a, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> b, float t)</td></tr>
<tr class="memdesc:a63b62cb18ab91477aee5c9bd0d975400"><td class="mdescLeft">&#160;</td><td class="mdescRight">Linearly interpolates between two vectors. <br /></td></tr>
<tr class="separator:a63b62cb18ab91477aee5c9bd0d975400"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3495c2bc3847d7ec201c1e4209359c25"><td class="memItemLeft" align="right" valign="top"><a id="a3495c2bc3847d7ec201c1e4209359c25" name="a3495c2bc3847d7ec201c1e4209359c25"></a>
static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><b>Max</b> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> lhs, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> rhs)</td></tr>
<tr class="memdesc:a3495c2bc3847d7ec201c1e4209359c25"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a vector that is made from the largest components of two vectors. <br /></td></tr>
<tr class="separator:a3495c2bc3847d7ec201c1e4209359c25"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a514e1f8b6c974e522e290ccf3f113a7e"><td class="memItemLeft" align="right" valign="top"><a id="a514e1f8b6c974e522e290ccf3f113a7e" name="a514e1f8b6c974e522e290ccf3f113a7e"></a>
static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><b>Min</b> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> lhs, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> rhs)</td></tr>
<tr class="memdesc:a514e1f8b6c974e522e290ccf3f113a7e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a vector that is made from the smallest components of two vectors. <br /></td></tr>
<tr class="separator:a514e1f8b6c974e522e290ccf3f113a7e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa4b2a5ff6af794cc8a83617227bee73b"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_vector3.html#aa4b2a5ff6af794cc8a83617227bee73b">MoveTowards</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> current, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> target, float maxDistanceDelta)</td></tr>
<tr class="memdesc:aa4b2a5ff6af794cc8a83617227bee73b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Moves a point current in a straight line towards a target point.  <a href="class_lua_1_1_vector3.html#aa4b2a5ff6af794cc8a83617227bee73b">More...</a><br /></td></tr>
<tr class="separator:aa4b2a5ff6af794cc8a83617227bee73b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa9b7aad25b72d46c5e49e847bbc41353"><td class="memItemLeft" align="right" valign="top"><a id="aa9b7aad25b72d46c5e49e847bbc41353" name="aa9b7aad25b72d46c5e49e847bbc41353"></a>
static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><b>Project</b> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> vector, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> onNormal)</td></tr>
<tr class="memdesc:aa9b7aad25b72d46c5e49e847bbc41353"><td class="mdescLeft">&#160;</td><td class="mdescRight">Projects a vector onto another vector. <br /></td></tr>
<tr class="separator:aa9b7aad25b72d46c5e49e847bbc41353"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af4bc2b40c64c31d8ade94277052e46d1"><td class="memItemLeft" align="right" valign="top"><a id="af4bc2b40c64c31d8ade94277052e46d1" name="af4bc2b40c64c31d8ade94277052e46d1"></a>
static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><b>ProjectOnPlane</b> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> vector, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> planeNormal)</td></tr>
<tr class="memdesc:af4bc2b40c64c31d8ade94277052e46d1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Projects a vector onto a plane defined by a normal orthogonal to the plane. <br /></td></tr>
<tr class="separator:af4bc2b40c64c31d8ade94277052e46d1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6973141d227d4b18eb09caaa3cb965b4"><td class="memItemLeft" align="right" valign="top"><a id="a6973141d227d4b18eb09caaa3cb965b4" name="a6973141d227d4b18eb09caaa3cb965b4"></a>
static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><b>Reflect</b> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> inDirection, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> inNormal)</td></tr>
<tr class="memdesc:a6973141d227d4b18eb09caaa3cb965b4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reflects a vector off the plane defined by a normal. <br /></td></tr>
<tr class="separator:a6973141d227d4b18eb09caaa3cb965b4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a938bc42ace148202da5445f245581927"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_vector3.html#a938bc42ace148202da5445f245581927">RotateTowards</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> current, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> target, float maxRadiansDelta, float maxMagnitudeDelta)</td></tr>
<tr class="memdesc:a938bc42ace148202da5445f245581927"><td class="mdescLeft">&#160;</td><td class="mdescRight">Rotates a vector current towards target.  <a href="class_lua_1_1_vector3.html#a938bc42ace148202da5445f245581927">More...</a><br /></td></tr>
<tr class="separator:a938bc42ace148202da5445f245581927"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1b7601ca79dfd9b6f3ab3636508c197a"><td class="memItemLeft" align="right" valign="top"><a id="a1b7601ca79dfd9b6f3ab3636508c197a" name="a1b7601ca79dfd9b6f3ab3636508c197a"></a>
static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><b>Scale</b> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> a, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> b)</td></tr>
<tr class="memdesc:a1b7601ca79dfd9b6f3ab3636508c197a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Multiplies two vectors component-wise. <br /></td></tr>
<tr class="separator:a1b7601ca79dfd9b6f3ab3636508c197a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a952dff0d8cc76a32589c2020e957adbf"><td class="memItemLeft" align="right" valign="top"><a id="a952dff0d8cc76a32589c2020e957adbf" name="a952dff0d8cc76a32589c2020e957adbf"></a>
static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><b>Slerp</b> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> a, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> b, float t)</td></tr>
<tr class="memdesc:a952dff0d8cc76a32589c2020e957adbf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Spherically interpolates between two vectors. <br /></td></tr>
<tr class="separator:a952dff0d8cc76a32589c2020e957adbf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a951d3fea17d487e6c7a27d842fcaf3f7"><td class="memItemLeft" align="right" valign="top"><a id="a951d3fea17d487e6c7a27d842fcaf3f7" name="a951d3fea17d487e6c7a27d842fcaf3f7"></a>
static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><b>SlerpUnclamped</b> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> a, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> b, float t)</td></tr>
<tr class="memdesc:a951d3fea17d487e6c7a27d842fcaf3f7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Spherically interpolates between two vectors. <br /></td></tr>
<tr class="separator:a951d3fea17d487e6c7a27d842fcaf3f7"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="properties" name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:af2367a6c9fb9484cd8703ef20bbe3e2b"><td class="memItemLeft" align="right" valign="top"><a id="af2367a6c9fb9484cd8703ef20bbe3e2b" name="af2367a6c9fb9484cd8703ef20bbe3e2b"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><b>x</b><code> [get, set]</code></td></tr>
<tr class="memdesc:af2367a6c9fb9484cd8703ef20bbe3e2b"><td class="mdescLeft">&#160;</td><td class="mdescRight">X component of the vector. <br /></td></tr>
<tr class="separator:af2367a6c9fb9484cd8703ef20bbe3e2b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a93844cc4b95c4b4cc2152ecd3d6a69ed"><td class="memItemLeft" align="right" valign="top"><a id="a93844cc4b95c4b4cc2152ecd3d6a69ed" name="a93844cc4b95c4b4cc2152ecd3d6a69ed"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><b>y</b><code> [get, set]</code></td></tr>
<tr class="memdesc:a93844cc4b95c4b4cc2152ecd3d6a69ed"><td class="mdescLeft">&#160;</td><td class="mdescRight">Y component of the vector. <br /></td></tr>
<tr class="separator:a93844cc4b95c4b4cc2152ecd3d6a69ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a80737d9f0e18357fd716e47a1b82ef6a"><td class="memItemLeft" align="right" valign="top"><a id="a80737d9f0e18357fd716e47a1b82ef6a" name="a80737d9f0e18357fd716e47a1b82ef6a"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><b>z</b><code> [get, set]</code></td></tr>
<tr class="memdesc:a80737d9f0e18357fd716e47a1b82ef6a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Z component of the vector. <br /></td></tr>
<tr class="separator:a80737d9f0e18357fd716e47a1b82ef6a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af7a889228914dff8226722de5c47a9b7"><td class="memItemLeft" align="right" valign="top"><a id="af7a889228914dff8226722de5c47a9b7" name="af7a889228914dff8226722de5c47a9b7"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><b>magnitude</b><code> [get]</code></td></tr>
<tr class="memdesc:af7a889228914dff8226722de5c47a9b7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the length of this vector (Read Only). <br /></td></tr>
<tr class="separator:af7a889228914dff8226722de5c47a9b7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a29cb25eb011222ce12a27338eb3aa0a2"><td class="memItemLeft" align="right" valign="top"><a id="a29cb25eb011222ce12a27338eb3aa0a2" name="a29cb25eb011222ce12a27338eb3aa0a2"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><b>sqrMagnitude</b><code> [get]</code></td></tr>
<tr class="memdesc:a29cb25eb011222ce12a27338eb3aa0a2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the squared length of this vector (Read Only). <br /></td></tr>
<tr class="separator:a29cb25eb011222ce12a27338eb3aa0a2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4e2f1c26b10b35c7a23108cfaa63320c"><td class="memItemLeft" align="right" valign="top"><a id="a4e2f1c26b10b35c7a23108cfaa63320c" name="a4e2f1c26b10b35c7a23108cfaa63320c"></a>
<a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><b>normalized</b><code> [get]</code></td></tr>
<tr class="memdesc:a4e2f1c26b10b35c7a23108cfaa63320c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns this vector with a magnitude of 1 (Read Only). <br /></td></tr>
<tr class="separator:a4e2f1c26b10b35c7a23108cfaa63320c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3742b113252e0163b006a17a76cb558c"><td class="memItemLeft" align="right" valign="top"><a id="a3742b113252e0163b006a17a76cb558c" name="a3742b113252e0163b006a17a76cb558c"></a>
static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><b>back</b><code> [get]</code></td></tr>
<tr class="memdesc:a3742b113252e0163b006a17a76cb558c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Shorthand for writing <a class="el" href="class_lua_1_1_vector3.html" title="Representation of 3D vectors and points.">Vector3</a>(0, 0, -1). <br /></td></tr>
<tr class="separator:a3742b113252e0163b006a17a76cb558c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adfb20fbfe1e5224b0b0fade3da0d4c8f"><td class="memItemLeft" align="right" valign="top"><a id="adfb20fbfe1e5224b0b0fade3da0d4c8f" name="adfb20fbfe1e5224b0b0fade3da0d4c8f"></a>
static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><b>down</b><code> [get]</code></td></tr>
<tr class="memdesc:adfb20fbfe1e5224b0b0fade3da0d4c8f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Shorthand for writing <a class="el" href="class_lua_1_1_vector3.html" title="Representation of 3D vectors and points.">Vector3</a>(0, -1, 0). <br /></td></tr>
<tr class="separator:adfb20fbfe1e5224b0b0fade3da0d4c8f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad8be15240d9bfa336d926ab023f11ad4"><td class="memItemLeft" align="right" valign="top"><a id="ad8be15240d9bfa336d926ab023f11ad4" name="ad8be15240d9bfa336d926ab023f11ad4"></a>
static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><b>forward</b><code> [get]</code></td></tr>
<tr class="memdesc:ad8be15240d9bfa336d926ab023f11ad4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Shorthand for writing Vector3(0, 0, 1). <br /></td></tr>
<tr class="separator:ad8be15240d9bfa336d926ab023f11ad4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5a08c13ed00efb9fb6728fded1e8a472"><td class="memItemLeft" align="right" valign="top"><a id="a5a08c13ed00efb9fb6728fded1e8a472" name="a5a08c13ed00efb9fb6728fded1e8a472"></a>
static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><b>left</b><code> [get]</code></td></tr>
<tr class="memdesc:a5a08c13ed00efb9fb6728fded1e8a472"><td class="mdescLeft">&#160;</td><td class="mdescRight">Shorthand for writing <a class="el" href="class_lua_1_1_vector3.html" title="Representation of 3D vectors and points.">Vector3</a>(-1, 0, 0). <br /></td></tr>
<tr class="separator:a5a08c13ed00efb9fb6728fded1e8a472"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a16592c4087c4d02cf1dd00a06d5baced"><td class="memItemLeft" align="right" valign="top"><a id="a16592c4087c4d02cf1dd00a06d5baced" name="a16592c4087c4d02cf1dd00a06d5baced"></a>
static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><b>one</b><code> [get]</code></td></tr>
<tr class="memdesc:a16592c4087c4d02cf1dd00a06d5baced"><td class="mdescLeft">&#160;</td><td class="mdescRight">Shorthand for writing Vector3(1, 1, 1). <br /></td></tr>
<tr class="separator:a16592c4087c4d02cf1dd00a06d5baced"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae8a79f66fb993fb1ad05c81fd3bbb9e3"><td class="memItemLeft" align="right" valign="top"><a id="ae8a79f66fb993fb1ad05c81fd3bbb9e3" name="ae8a79f66fb993fb1ad05c81fd3bbb9e3"></a>
static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><b>right</b><code> [get]</code></td></tr>
<tr class="memdesc:ae8a79f66fb993fb1ad05c81fd3bbb9e3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Shorthand for writing Vector3(1, 0, 0). <br /></td></tr>
<tr class="separator:ae8a79f66fb993fb1ad05c81fd3bbb9e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a63cc0e06476f86bd603c0a56bfa09fae"><td class="memItemLeft" align="right" valign="top"><a id="a63cc0e06476f86bd603c0a56bfa09fae" name="a63cc0e06476f86bd603c0a56bfa09fae"></a>
static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><b>up</b><code> [get]</code></td></tr>
<tr class="memdesc:a63cc0e06476f86bd603c0a56bfa09fae"><td class="mdescLeft">&#160;</td><td class="mdescRight">Shorthand for writing Vector3(0, 1, 0). <br /></td></tr>
<tr class="separator:a63cc0e06476f86bd603c0a56bfa09fae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa45f0524375ebdaeb4244325ffb08210"><td class="memItemLeft" align="right" valign="top"><a id="aa45f0524375ebdaeb4244325ffb08210" name="aa45f0524375ebdaeb4244325ffb08210"></a>
static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><b>zero</b><code> [get]</code></td></tr>
<tr class="memdesc:aa45f0524375ebdaeb4244325ffb08210"><td class="mdescLeft">&#160;</td><td class="mdescRight">Shorthand for writing Vector3(0, 0, 0). <br /></td></tr>
<tr class="separator:aa45f0524375ebdaeb4244325ffb08210"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >Representation of 3D vectors and points. </p>
<p >This structure is used throughout Unity to pass 3D positions and directions around. It also contains functions for doing common vector operations. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a185d18054534f55a4ce5f951e157df96" name="a185d18054534f55a4ce5f951e157df96"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a185d18054534f55a4ce5f951e157df96">&#9670;&nbsp;</a></span>Concat() <span class="overload">[1/3]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static string Lua.Vector3.Concat </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>v</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>o</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Concatenates a string and a vector. </p>
<p >Syntax for this operation is <code>s .. v</code>. <a class="el" href="class_lua_1_1_vector3.html#a63129be99b82f76bb94c8267b0dcd692" title="String representation of this vector.">Vector3.ToString</a> is used to convert vector to string. </p>

</div>
</div>
<a id="a7a6a85bde6d3a1072d52ebf902f291e0" name="a7a6a85bde6d3a1072d52ebf902f291e0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7a6a85bde6d3a1072d52ebf902f291e0">&#9670;&nbsp;</a></span>Concat() <span class="overload">[2/3]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static string Lua.Vector3.Concat </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>o</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>v</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Concatenates a vector and a string. </p>
<p >Syntax for this operation is <code>v .. s</code>. <a class="el" href="class_lua_1_1_vector3.html#a63129be99b82f76bb94c8267b0dcd692" title="String representation of this vector.">Vector3.ToString</a> is used to convert vector to string. </p>

</div>
</div>
<a id="aa50462b9cd533a0b1216975d52940d5a" name="aa50462b9cd533a0b1216975d52940d5a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa50462b9cd533a0b1216975d52940d5a">&#9670;&nbsp;</a></span>Concat() <span class="overload">[3/3]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static string Lua.Vector3.Concat </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>o1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>o2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Concatenates 2 vectors. </p>
<p >Syntax for this operation is <code>v1 .. v2</code>. <a class="el" href="class_lua_1_1_vector3.html#a63129be99b82f76bb94c8267b0dcd692" title="String representation of this vector.">Vector3.ToString</a> is used to convert vectors to strings. </p>

</div>
</div>
<a id="aaa648399828fb59c6ad750f2f3ad09d6" name="aaa648399828fb59c6ad750f2f3ad09d6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaa648399828fb59c6ad750f2f3ad09d6">&#9670;&nbsp;</a></span>Eq()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static bool Lua.Vector3.Eq </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>o1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>o2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Tests 2 vectors for equality. </p>
<p >Syntax for this operation is <code>v1 == v2</code>. Returns true if two vectors are approximately equal. The inequality operator <code>v1 ~= v2</code> also uses this function, but negated.</p>
<p >To allow for floating point inaccuracies, the two vectors are considered equal if the magnitude of their difference is less than 1e-5. </p>

</div>
</div>
<a id="aa4b2a5ff6af794cc8a83617227bee73b" name="aa4b2a5ff6af794cc8a83617227bee73b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa4b2a5ff6af794cc8a83617227bee73b">&#9670;&nbsp;</a></span>MoveTowards()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Vector3.MoveTowards </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>current</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>target</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>maxDistanceDelta</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Moves a point current in a straight line towards a target point. </p>
<p >Similar to <a class="el" href="class_lua_1_1_vector3.html#a2ac180084d2490e519612ccba40da454" title="Linearly interpolates between two vectors.">Vector3.Lerp</a>, but uses an absolute distance instead of relative percentage. </p>

</div>
</div>
<a id="a5855d8e4953dffcb076b9e5949406203" name="a5855d8e4953dffcb076b9e5949406203"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5855d8e4953dffcb076b9e5949406203">&#9670;&nbsp;</a></span>Normalize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Vector3.Normalize </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Makes this vector have a length of 1. </p>
<p >When normalized, a vector keeps the same direction but its length is 1.0. Note that this function will change the current vector. If you want to keep the current vector unchanged, use normalized variable. If this vector is too small to be normalized it will be set to zero. </p>

</div>
</div>
<a id="a78ff76fd80ba422a745612959099a1fa" name="a78ff76fd80ba422a745612959099a1fa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a78ff76fd80ba422a745612959099a1fa">&#9670;&nbsp;</a></span>operator*() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Vector3.operator* </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>f</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>o1</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Multiplies a vector by a scalar (float). </p>
<p >Syntax for this operation is <code>f * v</code>. Resulting vector is <code>(v.x * f, v.y * f, v.z * f)</code>. </p>

</div>
</div>
<a id="a9f7843705e8cb31af4a2fa885ec599de" name="a9f7843705e8cb31af4a2fa885ec599de"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9f7843705e8cb31af4a2fa885ec599de">&#9670;&nbsp;</a></span>operator*() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Vector3.operator* </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>o1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>f</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Multiplies a vector by a scalar (float). </p>
<p >Syntax for this operation is <code>v * f</code>. Resulting vector is <code>(v.x * f, v.y * f, v.z * f)</code>. </p>

</div>
</div>
<a id="ae40267fccb8c1e9d79d704776b07e949" name="ae40267fccb8c1e9d79d704776b07e949"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae40267fccb8c1e9d79d704776b07e949">&#9670;&nbsp;</a></span>operator+()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Vector3.operator+ </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>o1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>o2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Adds 2 vectors. </p>
<p >Syntax for this operation is <code>v1 + v2</code>. Resulting vector is <code>(v1.x + v2.x, v1.y + v2.y, v1.z + v2.z)</code>. </p>

</div>
</div>
<a id="a40ea1014f1fd49f4c2fce2b7c77bcfc1" name="a40ea1014f1fd49f4c2fce2b7c77bcfc1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a40ea1014f1fd49f4c2fce2b7c77bcfc1">&#9670;&nbsp;</a></span>operator-() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Vector3.operator- </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>o1</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Negates (inverts) a vector. </p>
<p >Syntax for this operation is <code>-v</code>. Resulting vector is <code>(-v.x, -v.y, -v.z)</code>. </p>

</div>
</div>
<a id="a62cecbc7efa2173accf443a1d3ff41e4" name="a62cecbc7efa2173accf443a1d3ff41e4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a62cecbc7efa2173accf443a1d3ff41e4">&#9670;&nbsp;</a></span>operator-() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Vector3.operator- </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>o1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>o2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Subtracts 2 vectors. </p>
<p >Syntax for this operation is <code>v1 - v2</code>. Resulting vector is <code>(v1.x - v2.x, v1.y - v2.y, v1.z - v2.z)</code>. </p>

</div>
</div>
<a id="a2e065e07d84200e5a9cec3270ef1d30f" name="a2e065e07d84200e5a9cec3270ef1d30f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2e065e07d84200e5a9cec3270ef1d30f">&#9670;&nbsp;</a></span>operator/()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Vector3.operator/ </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>o1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>f</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Divides a vector by a scalar (float). </p>
<p >Syntax for this operation is <code>v / f</code>. Resulting vector is <code>(v.x / f, v.y / f, v.z / f)</code>. </p>

</div>
</div>
<a id="a938bc42ace148202da5445f245581927" name="a938bc42ace148202da5445f245581927"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a938bc42ace148202da5445f245581927">&#9670;&nbsp;</a></span>RotateTowards()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Vector3.RotateTowards </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>current</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>target</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>maxRadiansDelta</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>maxMagnitudeDelta</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Rotates a vector current towards target. </p>
<p >This function is similar to <a class="el" href="class_lua_1_1_vector3.html#aa4b2a5ff6af794cc8a83617227bee73b" title="Moves a point current in a straight line towards a target point.">Vector3.MoveTowards</a> except that the vector is treated as a direction rather than a position. The <code>current</code> vector will be rotated round toward the target direction by an angle of <code>maxRadiansDelta</code>, although it will land exactly on the target rather than overshoot. If the magnitudes of <code>current</code> and <code>target</code> are different then the magnitude of the result will be linearly interpolated during the rotation. If a negative value is used for <code>maxRadiansDelta</code>, the vector will rotate away from <code>target</code> until it is pointing in exactly the opposite direction, then stop.</p>
<p >See also <a class="el" href="class_lua_1_1_quaternion.html#ac0e2a30dacbe7805913969ca02cc1e6b" title="Combines rotations lhs and rhs.">Quaternion.operator*</a>. </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaVector3.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.AllGiantess Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_all_giantess.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#properties">Properties</a> &#124;
<a href="class_lua_1_1_all_giantess-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.AllGiantess Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>A class containing settings affecting all giantesses.  
 <a href="class_lua_1_1_all_giantess.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="properties" name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a76e7e6cca768273ffd53a6b8a3127ffe"><td class="memItemLeft" align="right" valign="top"><a id="a76e7e6cca768273ffd53a6b8a3127ffe" name="a76e7e6cca768273ffd53a6b8a3127ffe"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><b>globalSpeed</b><code> [get, set]</code></td></tr>
<tr class="memdesc:a76e7e6cca768273ffd53a6b8a3127ffe"><td class="mdescLeft">&#160;</td><td class="mdescRight">Global giantess speed. <br /></td></tr>
<tr class="separator:a76e7e6cca768273ffd53a6b8a3127ffe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af82af573b560f17c3b0cd575bd61ed00"><td class="memItemLeft" align="right" valign="top"><a id="af82af573b560f17c3b0cd575bd61ed00" name="af82af573b560f17c3b0cd575bd61ed00"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><b>maxSize</b><code> [get, set]</code></td></tr>
<tr class="memdesc:af82af573b560f17c3b0cd575bd61ed00"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maximal giantess size. <br /></td></tr>
<tr class="separator:af82af573b560f17c3b0cd575bd61ed00"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4852dd60230117796328807f41509dbf"><td class="memItemLeft" align="right" valign="top"><a id="a4852dd60230117796328807f41509dbf" name="a4852dd60230117796328807f41509dbf"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><b>minSize</b><code> [get, set]</code></td></tr>
<tr class="memdesc:a4852dd60230117796328807f41509dbf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Minimal giantess size. <br /></td></tr>
<tr class="separator:a4852dd60230117796328807f41509dbf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adc5824c47c4090e1d8769ddde5c9da6a"><td class="memItemLeft" align="right" valign="top"><a id="adc5824c47c4090e1d8769ddde5c9da6a" name="adc5824c47c4090e1d8769ddde5c9da6a"></a>
IDictionary&lt; int, <a class="el" href="class_lua_1_1_entity.html">Entity</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>list</b><code> [get]</code></td></tr>
<tr class="memdesc:adc5824c47c4090e1d8769ddde5c9da6a"><td class="mdescLeft">&#160;</td><td class="mdescRight">List of all giantesses currently on map. <br /></td></tr>
<tr class="separator:adc5824c47c4090e1d8769ddde5c9da6a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >A class containing settings affecting all giantesses. </p>
<p >Accessible through the <code>gts</code> global variable. </p>
</div><hr/>The documentation for this class was generated from the following file:<ul>
<li>UnityProxies.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_all_giantess.html">AllGiantess</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

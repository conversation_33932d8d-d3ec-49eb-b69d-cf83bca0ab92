<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.15"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Class Hierarchy</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(initResizable);
/* @license-end */</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(document).ready(function(){initNavTree('hierarchy.html','');});
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Class Hierarchy</div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock">This inheritance list is sorted roughly, but not completely, alphabetically:</div><div class="directory">
<div class="levels">[detail level <span onclick="javascript:toggleLevel(1);">1</span><span onclick="javascript:toggleLevel(2);">2</span>]</div><table class="directory">
<tr id="row_0_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_a_i.html" target="_self">Lua.AI</a></td><td class="desc">Controls the <a class="el" href="class_lua_1_1_a_i.html" title="Controls the AI of humanoid agent.">AI</a> of humanoid agent </td></tr>
<tr id="row_1_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_all_giantess.html" target="_self">Lua.AllGiantess</a></td><td class="desc">A class containing settings affecting all giantesses </td></tr>
<tr id="row_2_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_all_micros.html" target="_self">Lua.AllMicros</a></td><td class="desc">A class containing settings affecting all micros </td></tr>
<tr id="row_3_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_animation.html" target="_self">Lua.Animation</a></td><td class="desc">Component to control the animation for humanoid entities </td></tr>
<tr id="row_4_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_audio_source.html" target="_self">Lua.AudioSource</a></td><td class="desc">A representation of audio sources in 3D </td></tr>
<tr id="row_5_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_bones.html" target="_self">Lua.Bones</a></td><td class="desc">Access bone transforms of humanoid characters </td></tr>
<tr id="row_6_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_custom_sound_manager.html" target="_self">Lua.CustomSoundManager</a></td><td class="desc">An interface to change in-game sound effects with custom sounds in the Sounds folder </td></tr>
<tr id="row_7_"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_7_" class="arrow" onclick="toggleFolder('7_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_entity.html" target="_self">Lua.Entity</a></td><td class="desc">A <a class="el" href="class_lua_1_1_entity.html" title="A Entity represents Characters and Objects">Entity</a> represents Characters and Objects </td></tr>
<tr id="row_7_0_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_player_entity.html" target="_self">Lua.PlayerEntity</a></td><td class="desc">A <a class="el" href="class_lua_1_1_player_entity.html" title="A PlayerEntity represents player-controlled character.">PlayerEntity</a> represents player-controlled character </td></tr>
<tr id="row_8_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_event.html" target="_self">Lua.Event</a></td><td class="desc">Interface of the <a class="el" href="class_lua_1_1_event.html" title="Interface of the Event system.">Event</a> system </td></tr>
<tr id="row_9_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_event_code.html" target="_self">EventCode</a></td><td class="desc">Lists hardcoded engine <a class="el" href="class_lua_1_1_event.html" title="Interface of the Event system.">Lua.Event</a> names </td></tr>
<tr id="row_10_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_globals.html" target="_self">Lua.Globals</a></td><td class="desc">Global dictionary. It can be used to store and exchange arbitrary data between scripts </td></tr>
<tr id="row_11_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_i_k.html" target="_self">Lua.IK</a></td><td class="desc">Inverse Kinematics lets you animate individual bones to create procedural animations </td></tr>
<tr id="row_12_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_i_k_effector.html" target="_self">Lua.IKEffector</a></td><td class="desc">Each effector lets you control one bone and animate the body </td></tr>
<tr id="row_13_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_input.html" target="_self">Lua.Input</a></td><td class="desc">Interface into the <a class="el" href="class_lua_1_1_input.html" title="Interface into the Input system.">Input</a> system </td></tr>
<tr id="row_14_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_lua_player_raygun.html" target="_self">Lua.LuaPlayerRaygun</a></td><td class="desc">Use this component to control some elements of the raygun of the player </td></tr>
<tr id="row_15_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_mathf.html" target="_self">Lua.Mathf</a></td><td class="desc">A collection of common Unity math functions. Largely overlaps with built-in <a class="el" href="namespace_lua.html">Lua</a> math library (<a href="https://www.lua.org/manual/5.3/manual.html#6.7">https://www.lua.org/manual/5.3/manual.html#6.7</a>) </td></tr>
<tr id="row_16_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_morphs.html" target="_self">Lua.Morphs</a></td><td class="desc">Component to control the morphs for giantess entities </td></tr>
<tr id="row_17_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_movement.html" target="_self">Lua.Movement</a></td><td class="desc">Use this component to control the movement of agents </td></tr>
<tr id="row_18_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_quaternion.html" target="_self">Lua.Quaternion</a></td><td class="desc">Quaternions are used to represent rotations </td></tr>
<tr id="row_19_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_random.html" target="_self">Lua.Random</a></td><td class="desc">Class for generating random data </td></tr>
<tr id="row_20_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_rigidbody.html" target="_self">Lua.Rigidbody</a></td><td class="desc">Control of an object's position through physics simulation </td></tr>
<tr id="row_21_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_screen.html" target="_self">Lua.Screen</a></td><td class="desc">Access to display information </td></tr>
<tr id="row_22_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_senses.html" target="_self">Lua.Senses</a></td><td class="desc">Control the senses of a entity such as the vision </td></tr>
<tr id="row_23_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_shooting.html" target="_self">Lua.Shooting</a></td><td class="desc">Use this component to control the shooting-related and gun properties of agents </td></tr>
<tr id="row_24_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_time.html" target="_self">Lua.Time</a></td><td class="desc">The interface to get time information from Unity </td></tr>
<tr id="row_25_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_transform.html" target="_self">Lua.Transform</a></td><td class="desc">Position, rotation and scale of an object </td></tr>
<tr id="row_26_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_vector3.html" target="_self">Lua.Vector3</a></td><td class="desc">Representation of 3D vectors and points </td></tr>
<tr id="row_27_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="class_lua_1_1_world.html" target="_self">Lua.World</a></td><td class="desc">A class containing settings affecting the world </td></tr>
</table>
</div><!-- directory -->
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.15 </li>
  </ul>
</div>
</body>
</html>

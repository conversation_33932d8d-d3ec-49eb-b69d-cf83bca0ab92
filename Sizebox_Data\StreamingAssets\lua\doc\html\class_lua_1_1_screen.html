<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.Screen Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_screen.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#properties">Properties</a> &#124;
<a href="class_lua_1_1_screen-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.Screen Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Access to display information.  
 <a href="class_lua_1_1_screen.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="properties" name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a7b08588981d36493e586b0d59a1f1b7a"><td class="memItemLeft" align="right" valign="top">static bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_screen.html#a7b08588981d36493e586b0d59a1f1b7a">fullScreen</a><code> [get]</code></td></tr>
<tr class="memdesc:a7b08588981d36493e586b0d59a1f1b7a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Is the game running fullscreen?  <a href="class_lua_1_1_screen.html#a7b08588981d36493e586b0d59a1f1b7a">More...</a><br /></td></tr>
<tr class="separator:a7b08588981d36493e586b0d59a1f1b7a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7e3459d0ccc2641709d1bad599092fdc"><td class="memItemLeft" align="right" valign="top">static int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_screen.html#a7e3459d0ccc2641709d1bad599092fdc">height</a><code> [get]</code></td></tr>
<tr class="memdesc:a7e3459d0ccc2641709d1bad599092fdc"><td class="mdescLeft">&#160;</td><td class="mdescRight">The current height of the screen window in pixels (Read Only).  <a href="class_lua_1_1_screen.html#a7e3459d0ccc2641709d1bad599092fdc">More...</a><br /></td></tr>
<tr class="separator:a7e3459d0ccc2641709d1bad599092fdc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae44386bf8759e8f85e04358297f3dd95"><td class="memItemLeft" align="right" valign="top">static int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_screen.html#ae44386bf8759e8f85e04358297f3dd95">width</a><code> [get]</code></td></tr>
<tr class="memdesc:ae44386bf8759e8f85e04358297f3dd95"><td class="mdescLeft">&#160;</td><td class="mdescRight">The current width of the screen window in pixels (Read Only).  <a href="class_lua_1_1_screen.html#ae44386bf8759e8f85e04358297f3dd95">More...</a><br /></td></tr>
<tr class="separator:ae44386bf8759e8f85e04358297f3dd95"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >Access to display information. </p>
<p ><a class="el" href="class_lua_1_1_screen.html" title="Access to display information.">Screen</a> class can be used to get the list of supported resolutions, switch the current resolution, hide or show the system mouse pointer. </p>
</div><h2 class="groupheader">Property Documentation</h2>
<a id="a7b08588981d36493e586b0d59a1f1b7a" name="a7b08588981d36493e586b0d59a1f1b7a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7b08588981d36493e586b0d59a1f1b7a">&#9670;&nbsp;</a></span>fullScreen</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.Screen.fullScreen</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Is the game running fullscreen? </p>
<p >It is possible to toggle fullscreen mode by changing this property: </p><dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a7e3459d0ccc2641709d1bad599092fdc" name="a7e3459d0ccc2641709d1bad599092fdc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7e3459d0ccc2641709d1bad599092fdc">&#9670;&nbsp;</a></span>height</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int Lua.Screen.height</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The current height of the screen window in pixels (Read Only). </p>
<p >This is the actual height of the player window (in fullscreen it is also the current resolution). </p><dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ae44386bf8759e8f85e04358297f3dd95" name="ae44386bf8759e8f85e04358297f3dd95"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae44386bf8759e8f85e04358297f3dd95">&#9670;&nbsp;</a></span>width</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int Lua.Screen.width</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The current width of the screen window in pixels (Read Only). </p>
<p >This is the actual width of the player window (in fullscreen it is also the current resolution). </p><dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaInput.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_screen.html">Screen</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

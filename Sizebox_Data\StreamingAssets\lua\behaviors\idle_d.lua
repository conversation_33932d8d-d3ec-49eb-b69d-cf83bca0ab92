--v1.1 13/04/21
GiantessIdle = RegisterBehavior("Giantess Idle")
GiantessIdle.data = {
    menuEntry = "Idle (d)",
    ai = true,
    flags = { "idle" },
    agent = {
        type = { "giantess"}
    },
    target = {
        type = {"oneself"}
    }
}

--[[ you can define global data outside of the function
 this data will be shared by all characters
 if you change something in runtime all characters will be
 affected by it ]] --

 --[[ to declare local variables use the name of the GiantessIdle.data = "data", or create the
 self.data = "data" inside a function ]]

 --[[ all micros will use the same set of animation, so is ok to declare this
 outside of the function ]]--

function GiantessIdle:Start()	
	idles = {"Idle", "Idle 2", "Idle 3", "Idle 4", "Idle 5", "Happy", "Breathing Idle", "Bored", "Roar", "Neutral Idle",
		"Bashful", "Crossarms", "Wait Torso Twist", "Look Down", "Salute 2", "Whatever Gesture", "Refuse", "Neutral Idle",
		"Waving", "Waving 2", "Salute", "Greet 2", "Look Away Gesture", "Loser", "No", "Pointing Forward", "Neutral Idle",
		"Shake Fist", "Stomping", "Surprised", "Taunt 3", "Thinking", "Thinking 2", "Victory 2", "Wait Gesture", "Yelling"}
		
	self.agent.ai.StopAction()
	
	--Only do randomseed once per scene
    if not globals["idleGDRand"] then math.randomseed(tonumber(os.time()) + self.agent.id) globals["idleGDRand"] = true end
end

function GiantessIdle:Update()
  if self.agent.animation.IsCompleted() then
	idleAnimation = idles[math.random(#idles)]
    self.agent.animation.Set(idleAnimation)
  end
end



GiantessIdleS = RegisterBehavior("Stop Giantess Idle")
GiantessIdleS.data = {
    menuEntry = "Stop Idle (d)",
    ai = true,
    flags = { "idle" },
    agent = {
        type = { "giantess"}
    },
    target = {
        type = {"oneself"}
    }
}

function GiantessIdleS:Start()	
    idles = {"Idle", "Idle 2", "Idle 3", "Idle 4", "Idle 5", "Happy", "Breathing Idle", "Bored", "Roar", "Neutral Idle",
    "Bashful", "Crossarms", "Wait Torso Twist", "Look Down", "Salute 2", "Whatever Gesture", "Refuse", "Neutral Idle",
    "Waving", "Waving 2", "Salute", "Greet 2", "Look Away Gesture", "Loser", "No", "Pointing Forward", "Neutral Idle",
    "Shake Fist", "Stomping", "Surprised", "Taunt 3", "Thinking", "Thinking 2", "Victory 2", "Wait Gesture", "Yelling"}
    idleAnimation = idles[math.random(#idles)]
    self.agent.animation.Set(idleAnimation)
end



MicroIdle = RegisterBehavior("Micro Idle")
MicroIdle.data = {
    menuEntry = "Idle (d)",
    ai = true,
    flags = { "idle" },
    agent = {
        type = { "micro"}
    },
    target = {
        type = {"oneself"}
    }
}

function MicroIdle:Start()	
	idles = {"Crossarms", "Embar", "Embar 2", "Greet 3", "Greet 4", "Idle 2", 
			 "Jump Low", "Look Down", "Pick Up", "Refuse", "Scratch Head",
			 "Thinking", "Wait Strech Arms", "Wait Torso Twist"}
	self.agent.ai.StopAction()
	
	--Only do randomseed once per scene
    if not globals["idleMDRand"] then math.randomseed(tonumber(os.time()) + self.agent.id) globals["idleMDRand"] = true end
end

function MicroIdle:Update()
  if self.agent.animation.IsCompleted() then
	idleAnimation = idles[math.random(#idles)]
    self.agent.animation.Set(idleAnimation)
  end
end



MicroIdleS = RegisterBehavior("Stop Micro Idle")
MicroIdleS.data = {
    menuEntry = "Stop Idle (d)",
    ai = true,
    flags = { "idle" },
    agent = {
        type = { "micro"}
    },
    target = {
        type = {"oneself"}
    }
}

function MicroIdleS:Start()
	idles = {"Crossarms", "Embar", "Embar 2", "Greet 3", "Greet 4", "Idle 2", 
			 "Jump Low", "Look Down", "Pick Up", "Refuse", "Scratch Head",
			 "Thinking", "Wait Strech Arms", "Wait Torso Twist"}
    idleAnimation = idles[math.random(#idles)]
    self.agent.animation.Set(idleAnimation)
end
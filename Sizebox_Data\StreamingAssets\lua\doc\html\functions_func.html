<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Class Members - Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('functions_func.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a id="index_a" name="index_a"></a>- a -</h3><ul>
<li>Abs()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a321380853cd01074ad91e673a7071c99">Lua.Mathf</a></li>
<li>Acos()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a33b558ad9dabaee4792399525a89e4ce">Lua.Mathf</a></li>
<li>AddExplosionForce()&#160;:&#160;<a class="el" href="class_lua_1_1_rigidbody.html#a5d57a953b33d659f6e3b5508cd304960">Lua.Rigidbody</a></li>
<li>AddForce()&#160;:&#160;<a class="el" href="class_lua_1_1_rigidbody.html#a57eff3c6b45bf8f5a8dfdb7fa1fc9f8f">Lua.Rigidbody</a></li>
<li>AddRelativeForce()&#160;:&#160;<a class="el" href="class_lua_1_1_rigidbody.html#a9770aa2b61c085f2581392a782f6742c">Lua.Rigidbody</a></li>
<li>Aim()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#ac47475b1b0342f1e6ce52aca2eec7f38">Lua.Entity</a></li>
<li>Angle()&#160;:&#160;<a class="el" href="class_lua_1_1_quaternion.html#a4094309a8c66c50017bd92ffdfeb3bfd">Lua.Quaternion</a>, <a class="el" href="class_lua_1_1_vector3.html#ab278cbf1ea74ca78089e58c0a4313c6a">Lua.Vector3</a></li>
<li>AngleAxis()&#160;:&#160;<a class="el" href="class_lua_1_1_quaternion.html#a25f65fcc019124366264558209108498">Lua.Quaternion</a></li>
<li>AnimationExists()&#160;:&#160;<a class="el" href="class_lua_1_1_animation.html#a1002a4f745d48a64fd26affa514ee0d2">Lua.Animation</a></li>
<li>Approximately()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a2a53d9e05a8d05eaa05d1fced26906e6">Lua.Mathf</a></li>
<li>Asin()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a85731b3f55f246a46df2a2f40bc87ae7">Lua.Mathf</a></li>
<li>Atan()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a03740fc6f71760901fbe5f8a6295edeb">Lua.Mathf</a></li>
<li>Atan2()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#ab7f3034dcb7244d1cfb06ae17f014278">Lua.Mathf</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

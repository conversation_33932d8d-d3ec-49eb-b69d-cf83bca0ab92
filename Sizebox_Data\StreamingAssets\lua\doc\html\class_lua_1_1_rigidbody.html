<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.Rigidbody Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_rigidbody.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="class_lua_1_1_rigidbody-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.Rigidbody Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Control of an object's position through physics simulation.  
 <a href="class_lua_1_1_rigidbody.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a5d57a953b33d659f6e3b5508cd304960"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_rigidbody.html#a5d57a953b33d659f6e3b5508cd304960">AddExplosionForce</a> (float explosionForce, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> explosionPosition, float explosionRadius)</td></tr>
<tr class="memdesc:a5d57a953b33d659f6e3b5508cd304960"><td class="mdescLeft">&#160;</td><td class="mdescRight">Applies a force to a rigidbody that simulates explosion effects.  <a href="class_lua_1_1_rigidbody.html#a5d57a953b33d659f6e3b5508cd304960">More...</a><br /></td></tr>
<tr class="separator:a5d57a953b33d659f6e3b5508cd304960"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a57eff3c6b45bf8f5a8dfdb7fa1fc9f8f"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_rigidbody.html#a57eff3c6b45bf8f5a8dfdb7fa1fc9f8f">AddForce</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> force)</td></tr>
<tr class="memdesc:a57eff3c6b45bf8f5a8dfdb7fa1fc9f8f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Adds a force to the <a class="el" href="class_lua_1_1_rigidbody.html" title="Control of an object&#39;s position through physics simulation.">Rigidbody</a>.  <a href="class_lua_1_1_rigidbody.html#a57eff3c6b45bf8f5a8dfdb7fa1fc9f8f">More...</a><br /></td></tr>
<tr class="separator:a57eff3c6b45bf8f5a8dfdb7fa1fc9f8f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9770aa2b61c085f2581392a782f6742c"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_rigidbody.html#a9770aa2b61c085f2581392a782f6742c">AddRelativeForce</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> force)</td></tr>
<tr class="memdesc:a9770aa2b61c085f2581392a782f6742c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Adds a force to the rigidbody relative to its coordinate system.  <a href="class_lua_1_1_rigidbody.html#a9770aa2b61c085f2581392a782f6742c">More...</a><br /></td></tr>
<tr class="separator:a9770aa2b61c085f2581392a782f6742c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a413146fdf9b4e57b433cbc01dc1bc288"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_rigidbody.html#a413146fdf9b4e57b433cbc01dc1bc288">MovePosition</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> <a class="el" href="class_lua_1_1_rigidbody.html#a9bef020808bd389b43ac5d2f7d429dc9">position</a>)</td></tr>
<tr class="memdesc:a413146fdf9b4e57b433cbc01dc1bc288"><td class="mdescLeft">&#160;</td><td class="mdescRight">Moves the rigidbody to position.  <a href="class_lua_1_1_rigidbody.html#a413146fdf9b4e57b433cbc01dc1bc288">More...</a><br /></td></tr>
<tr class="separator:a413146fdf9b4e57b433cbc01dc1bc288"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a064bd1441d0a8d7e636619c87a98f7cf"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_rigidbody.html#a064bd1441d0a8d7e636619c87a98f7cf">MoveRotation</a> (<a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> rot)</td></tr>
<tr class="memdesc:a064bd1441d0a8d7e636619c87a98f7cf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Rotates the rigidbody to rotation.  <a href="class_lua_1_1_rigidbody.html#a064bd1441d0a8d7e636619c87a98f7cf">More...</a><br /></td></tr>
<tr class="separator:a064bd1441d0a8d7e636619c87a98f7cf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a267bf6dacb4ef9d2aeb0c798d2460245"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_rigidbody.html#a267bf6dacb4ef9d2aeb0c798d2460245">ClosestPointOnBounds</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> <a class="el" href="class_lua_1_1_rigidbody.html#a9bef020808bd389b43ac5d2f7d429dc9">position</a>)</td></tr>
<tr class="memdesc:a267bf6dacb4ef9d2aeb0c798d2460245"><td class="mdescLeft">&#160;</td><td class="mdescRight">The closest point to the bounding box of the attached colliders.  <a href="class_lua_1_1_rigidbody.html#a267bf6dacb4ef9d2aeb0c798d2460245">More...</a><br /></td></tr>
<tr class="separator:a267bf6dacb4ef9d2aeb0c798d2460245"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="properties" name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a901b3213408100236b17a3e55b64e6f7"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_rigidbody.html#a901b3213408100236b17a3e55b64e6f7">angularDrag</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a901b3213408100236b17a3e55b64e6f7"><td class="mdescLeft">&#160;</td><td class="mdescRight">The angular drag of the object.  <a href="class_lua_1_1_rigidbody.html#a901b3213408100236b17a3e55b64e6f7">More...</a><br /></td></tr>
<tr class="separator:a901b3213408100236b17a3e55b64e6f7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab88493ae1a778194017c0e3a87c0625d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_rigidbody.html#ab88493ae1a778194017c0e3a87c0625d">angularVelocity</a><code> [get, set]</code></td></tr>
<tr class="memdesc:ab88493ae1a778194017c0e3a87c0625d"><td class="mdescLeft">&#160;</td><td class="mdescRight">The angular velocity vector of the rigidbody measured in radians per second.  <a href="class_lua_1_1_rigidbody.html#ab88493ae1a778194017c0e3a87c0625d">More...</a><br /></td></tr>
<tr class="separator:ab88493ae1a778194017c0e3a87c0625d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac537e281d009b3e07c93f7357fa743cd"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_rigidbody.html#ac537e281d009b3e07c93f7357fa743cd">drag</a><code> [get, set]</code></td></tr>
<tr class="memdesc:ac537e281d009b3e07c93f7357fa743cd"><td class="mdescLeft">&#160;</td><td class="mdescRight">The drag of the object.  <a href="class_lua_1_1_rigidbody.html#ac537e281d009b3e07c93f7357fa743cd">More...</a><br /></td></tr>
<tr class="separator:ac537e281d009b3e07c93f7357fa743cd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad3f2380221d01eedada3df4d282a37d4"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_rigidbody.html#ad3f2380221d01eedada3df4d282a37d4">freezeRotation</a><code> [get, set]</code></td></tr>
<tr class="memdesc:ad3f2380221d01eedada3df4d282a37d4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Controls whether physics will change the rotation of the object.  <a href="class_lua_1_1_rigidbody.html#ad3f2380221d01eedada3df4d282a37d4">More...</a><br /></td></tr>
<tr class="separator:ad3f2380221d01eedada3df4d282a37d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeab0f1c55ada296d501909dd61533a35"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_rigidbody.html#aeab0f1c55ada296d501909dd61533a35">mass</a><code> [get, set]</code></td></tr>
<tr class="memdesc:aeab0f1c55ada296d501909dd61533a35"><td class="mdescLeft">&#160;</td><td class="mdescRight">The mass of the rigidbody.  <a href="class_lua_1_1_rigidbody.html#aeab0f1c55ada296d501909dd61533a35">More...</a><br /></td></tr>
<tr class="separator:aeab0f1c55ada296d501909dd61533a35"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8171fc4d6eb8d7e448eeb45f9fbc05d8"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_rigidbody.html#a8171fc4d6eb8d7e448eeb45f9fbc05d8">maxAngularVelocity</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a8171fc4d6eb8d7e448eeb45f9fbc05d8"><td class="mdescLeft">&#160;</td><td class="mdescRight">The maximimum angular velocity of the rigidbody. (Default 7) range { 0, infinity }.  <a href="class_lua_1_1_rigidbody.html#a8171fc4d6eb8d7e448eeb45f9fbc05d8">More...</a><br /></td></tr>
<tr class="separator:a8171fc4d6eb8d7e448eeb45f9fbc05d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9bef020808bd389b43ac5d2f7d429dc9"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_rigidbody.html#a9bef020808bd389b43ac5d2f7d429dc9">position</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a9bef020808bd389b43ac5d2f7d429dc9"><td class="mdescLeft">&#160;</td><td class="mdescRight">The position of the rigidbody.  <a href="class_lua_1_1_rigidbody.html#a9bef020808bd389b43ac5d2f7d429dc9">More...</a><br /></td></tr>
<tr class="separator:a9bef020808bd389b43ac5d2f7d429dc9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6cb1207363fce98ec04cacf8c6f776cc"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_rigidbody.html#a6cb1207363fce98ec04cacf8c6f776cc">rotation</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a6cb1207363fce98ec04cacf8c6f776cc"><td class="mdescLeft">&#160;</td><td class="mdescRight">The rotation of the rigidbody.  <a href="class_lua_1_1_rigidbody.html#a6cb1207363fce98ec04cacf8c6f776cc">More...</a><br /></td></tr>
<tr class="separator:a6cb1207363fce98ec04cacf8c6f776cc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3838f1418140279bcec4d7a2f8ebbae2"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_rigidbody.html#a3838f1418140279bcec4d7a2f8ebbae2">useGravity</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a3838f1418140279bcec4d7a2f8ebbae2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Controls whether gravity affects this rigidbody.  <a href="class_lua_1_1_rigidbody.html#a3838f1418140279bcec4d7a2f8ebbae2">More...</a><br /></td></tr>
<tr class="separator:a3838f1418140279bcec4d7a2f8ebbae2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abbc468f41391b7d34120f11f3f39b6fb"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_rigidbody.html#abbc468f41391b7d34120f11f3f39b6fb">velocity</a><code> [get, set]</code></td></tr>
<tr class="memdesc:abbc468f41391b7d34120f11f3f39b6fb"><td class="mdescLeft">&#160;</td><td class="mdescRight">The velocity vector of the rigidbody.  <a href="class_lua_1_1_rigidbody.html#abbc468f41391b7d34120f11f3f39b6fb">More...</a><br /></td></tr>
<tr class="separator:abbc468f41391b7d34120f11f3f39b6fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a23530e52ed361ac1f758e8204a1c833b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_rigidbody.html#a23530e52ed361ac1f758e8204a1c833b">worldCenterOfMass</a><code> [get]</code></td></tr>
<tr class="memdesc:a23530e52ed361ac1f758e8204a1c833b"><td class="mdescLeft">&#160;</td><td class="mdescRight">The center of mass of the rigidbody in world space (Read Only).  <a href="class_lua_1_1_rigidbody.html#a23530e52ed361ac1f758e8204a1c833b">More...</a><br /></td></tr>
<tr class="separator:a23530e52ed361ac1f758e8204a1c833b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aee1018d4d56ab085d013acc494c0d0f9"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_rigidbody.html#aee1018d4d56ab085d013acc494c0d0f9">isKinematic</a><code> [get, set]</code></td></tr>
<tr class="memdesc:aee1018d4d56ab085d013acc494c0d0f9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Controls whether physics affects the rigidbody.  <a href="class_lua_1_1_rigidbody.html#aee1018d4d56ab085d013acc494c0d0f9">More...</a><br /></td></tr>
<tr class="separator:aee1018d4d56ab085d013acc494c0d0f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >Control of an object's position through physics simulation. </p>
<p >In a script, the FixedUpdate function is recommended as the place to apply forces and change <a class="el" href="class_lua_1_1_rigidbody.html" title="Control of an object&#39;s position through physics simulation.">Rigidbody</a> settings (as opposed to Update, which is used for most other frame update tasks). The reason for this is that physics updates are carried out in measured time steps that don't coincide with the frame update. FixedUpdate is called immediately before each physics update and so any changes made there will be processed directly. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a5d57a953b33d659f6e3b5508cd304960" name="a5d57a953b33d659f6e3b5508cd304960"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5d57a953b33d659f6e3b5508cd304960">&#9670;&nbsp;</a></span>AddExplosionForce()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Rigidbody.AddExplosionForce </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>explosionForce</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>explosionPosition</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>explosionRadius</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Applies a force to a rigidbody that simulates explosion effects. </p>
<p >The explosion is modelled as a sphere with a certain centre position and radius in world space; normally, anything outside the sphere is not affected by the explosion and the force decreases in proportion to distance from the centre. However, if a value of zero is passed for the radius then the full force will be applied regardless of how far the centre is from the rigidbody. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">explosionForce</td><td>The force of the explosion (which may be modified by distance).</td></tr>
    <tr><td class="paramname">explosionPosition</td><td>The centre of the sphere within which the explosion has its effect.</td></tr>
    <tr><td class="paramname">explosionRadius</td><td>The radius of the sphere within which the explosion has its effect.</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a57eff3c6b45bf8f5a8dfdb7fa1fc9f8f" name="a57eff3c6b45bf8f5a8dfdb7fa1fc9f8f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a57eff3c6b45bf8f5a8dfdb7fa1fc9f8f">&#9670;&nbsp;</a></span>AddForce()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Rigidbody.AddForce </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>force</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Adds a force to the <a class="el" href="class_lua_1_1_rigidbody.html" title="Control of an object&#39;s position through physics simulation.">Rigidbody</a>. </p>
<p >Force is applied continuously along the direction of the force vector. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">force</td><td>Force vector in world coordinates.</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a9770aa2b61c085f2581392a782f6742c" name="a9770aa2b61c085f2581392a782f6742c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9770aa2b61c085f2581392a782f6742c">&#9670;&nbsp;</a></span>AddRelativeForce()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Rigidbody.AddRelativeForce </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>force</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Adds a force to the rigidbody relative to its coordinate system. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">force</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a267bf6dacb4ef9d2aeb0c798d2460245" name="a267bf6dacb4ef9d2aeb0c798d2460245"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a267bf6dacb4ef9d2aeb0c798d2460245">&#9670;&nbsp;</a></span>ClosestPointOnBounds()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Rigidbody.ClosestPointOnBounds </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>position</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>The closest point to the bounding box of the attached colliders. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">position</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a413146fdf9b4e57b433cbc01dc1bc288" name="a413146fdf9b4e57b433cbc01dc1bc288"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a413146fdf9b4e57b433cbc01dc1bc288">&#9670;&nbsp;</a></span>MovePosition()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Rigidbody.MovePosition </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>position</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Moves the rigidbody to position. </p>
<p >Use <a class="el" href="class_lua_1_1_rigidbody.html#a413146fdf9b4e57b433cbc01dc1bc288" title="Moves the rigidbody to position.">Rigidbody.MovePosition</a> to move a <a class="el" href="class_lua_1_1_rigidbody.html" title="Control of an object&#39;s position through physics simulation.">Rigidbody</a>, complying with the <a class="el" href="class_lua_1_1_rigidbody.html" title="Control of an object&#39;s position through physics simulation.">Rigidbody</a>'s interpolation setting. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">position</td><td>The new position for the <a class="el" href="class_lua_1_1_rigidbody.html" title="Control of an object&#39;s position through physics simulation.">Rigidbody</a> object.</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a064bd1441d0a8d7e636619c87a98f7cf" name="a064bd1441d0a8d7e636619c87a98f7cf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a064bd1441d0a8d7e636619c87a98f7cf">&#9670;&nbsp;</a></span>MoveRotation()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Rigidbody.MoveRotation </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td>
          <td class="paramname"><em>rot</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Rotates the rigidbody to rotation. </p>
<p >Use <a class="el" href="class_lua_1_1_rigidbody.html#a064bd1441d0a8d7e636619c87a98f7cf" title="Rotates the rigidbody to rotation.">Rigidbody.MoveRotation</a> to rotate a <a class="el" href="class_lua_1_1_rigidbody.html" title="Control of an object&#39;s position through physics simulation.">Rigidbody</a>, complying with the <a class="el" href="class_lua_1_1_rigidbody.html" title="Control of an object&#39;s position through physics simulation.">Rigidbody</a>'s interpolation setting. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">rot</td><td>The new rotation for the <a class="el" href="class_lua_1_1_rigidbody.html" title="Control of an object&#39;s position through physics simulation.">Rigidbody</a>.</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="a901b3213408100236b17a3e55b64e6f7" name="a901b3213408100236b17a3e55b64e6f7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a901b3213408100236b17a3e55b64e6f7">&#9670;&nbsp;</a></span>angularDrag</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Rigidbody.angularDrag</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The angular drag of the object. </p>
<p >Angular drag can be used to slow down the rotation of an object. The higher the drag the more the rotation slows down. </p><dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ab88493ae1a778194017c0e3a87c0625d" name="ab88493ae1a778194017c0e3a87c0625d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab88493ae1a778194017c0e3a87c0625d">&#9670;&nbsp;</a></span>angularVelocity</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Rigidbody.angularVelocity</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The angular velocity vector of the rigidbody measured in radians per second. </p>
<p >In most cases you should not modify it directly, as this can result in unrealistic behaviour. </p><dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ac537e281d009b3e07c93f7357fa743cd" name="ac537e281d009b3e07c93f7357fa743cd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac537e281d009b3e07c93f7357fa743cd">&#9670;&nbsp;</a></span>drag</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Rigidbody.drag</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The drag of the object. </p>
<p >Drag can be used to slow down an object. The higher the drag the more the object slows down. </p><dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ad3f2380221d01eedada3df4d282a37d4" name="ad3f2380221d01eedada3df4d282a37d4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad3f2380221d01eedada3df4d282a37d4">&#9670;&nbsp;</a></span>freezeRotation</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.Rigidbody.freezeRotation</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Controls whether physics will change the rotation of the object. </p>
<p >If freezeRotation is enabled, the rotation is not modified by the physics simulation. </p><dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="aee1018d4d56ab085d013acc494c0d0f9" name="aee1018d4d56ab085d013acc494c0d0f9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aee1018d4d56ab085d013acc494c0d0f9">&#9670;&nbsp;</a></span>isKinematic</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.Rigidbody.isKinematic</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Controls whether physics affects the rigidbody. </p>
<p >If isKinematic is enabled, Forces, collisions or joints will not affect the rigidbody anymore. The rigidbody will be under full control of animation or script control by changing transform.position. Kinematic bodies also affect the motion of other rigidbodies through collisions or joints. Eg. can connect a kinematic rigidbody to a normal rigidbody with a joint and the rigidbody will be constrained with the motion of the kinematic body. Kinematic rigidbodies are also particularly useful for making characters which are normally driven by an animation, but on certain events can be quickly turned into a ragdoll by setting isKinematic to false. </p><dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="aeab0f1c55ada296d501909dd61533a35" name="aeab0f1c55ada296d501909dd61533a35"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aeab0f1c55ada296d501909dd61533a35">&#9670;&nbsp;</a></span>mass</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Rigidbody.mass</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The mass of the rigidbody. </p>
<p >Different Rigidbodies with large differences in mass can make the physics simulation unstable. Higher mass objects push lower mass objects more when colliding. Think of a big truck, hitting a small car. </p><dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a8171fc4d6eb8d7e448eeb45f9fbc05d8" name="a8171fc4d6eb8d7e448eeb45f9fbc05d8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8171fc4d6eb8d7e448eeb45f9fbc05d8">&#9670;&nbsp;</a></span>maxAngularVelocity</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Rigidbody.maxAngularVelocity</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The maximimum angular velocity of the rigidbody. (Default 7) range { 0, infinity }. </p>
<p >The angular velocity of rigidbodies is clamped to maxAngularVelocity to avoid numerical instability with fast rotating bodies. </p><dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a9bef020808bd389b43ac5d2f7d429dc9" name="a9bef020808bd389b43ac5d2f7d429dc9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9bef020808bd389b43ac5d2f7d429dc9">&#9670;&nbsp;</a></span>position</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Rigidbody.position</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The position of the rigidbody. </p>
<p ><a class="el" href="class_lua_1_1_rigidbody.html#a9bef020808bd389b43ac5d2f7d429dc9" title="The position of the rigidbody.">Rigidbody.position</a> allows you to get and set the position of a <a class="el" href="class_lua_1_1_rigidbody.html" title="Control of an object&#39;s position through physics simulation.">Rigidbody</a> using the physics engine. If you change the position of a Rigibody using <a class="el" href="class_lua_1_1_rigidbody.html#a9bef020808bd389b43ac5d2f7d429dc9" title="The position of the rigidbody.">Rigidbody.position</a>, the transform will be updated after the next physics simulation step. This is faster than updating the position using <a class="el" href="class_lua_1_1_transform.html#a789b6abed611a7576ca2262bb9c5e6c3" title="The position of the transform in world space.">Transform.position</a>, as the latter will cause all attached Colliders to recalculate their positions relative to the <a class="el" href="class_lua_1_1_rigidbody.html" title="Control of an object&#39;s position through physics simulation.">Rigidbody</a>. If you want to continuously move a rigidbody use MovePosition instead, which takes interpolation into account. </p><dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a6cb1207363fce98ec04cacf8c6f776cc" name="a6cb1207363fce98ec04cacf8c6f776cc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6cb1207363fce98ec04cacf8c6f776cc">&#9670;&nbsp;</a></span>rotation</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> Lua.Rigidbody.rotation</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The rotation of the rigidbody. </p>
<p ><a class="el" href="class_lua_1_1_rigidbody.html#a6cb1207363fce98ec04cacf8c6f776cc" title="The rotation of the rigidbody.">Rigidbody.rotation</a> allows you to get and set the rotation of a <a class="el" href="class_lua_1_1_rigidbody.html" title="Control of an object&#39;s position through physics simulation.">Rigidbody</a> using the physics engine. If you change the rotation of a Rigibody using <a class="el" href="class_lua_1_1_rigidbody.html#a6cb1207363fce98ec04cacf8c6f776cc" title="The rotation of the rigidbody.">Rigidbody.rotation</a>, the transform will be updated after the next physics simulation step. This is faster than updating the rotation using <a class="el" href="class_lua_1_1_transform.html#ab0b5488416c3d0f6e3de7b426227198c" title="The rotation of the transform in world space stored as a Quaternion.">Transform.rotation</a>, as the latter will cause all attached Colliders to recalculate their rotation relative to the <a class="el" href="class_lua_1_1_rigidbody.html" title="Control of an object&#39;s position through physics simulation.">Rigidbody</a>. If you want to continuously rotate a rigidbody use MoveRotation instead, which takes interpolation into account. </p><dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a3838f1418140279bcec4d7a2f8ebbae2" name="a3838f1418140279bcec4d7a2f8ebbae2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3838f1418140279bcec4d7a2f8ebbae2">&#9670;&nbsp;</a></span>useGravity</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.Rigidbody.useGravity</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Controls whether gravity affects this rigidbody. </p>
<p >If set to false the rigidbody will behave as in outer space. </p><dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="abbc468f41391b7d34120f11f3f39b6fb" name="abbc468f41391b7d34120f11f3f39b6fb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abbc468f41391b7d34120f11f3f39b6fb">&#9670;&nbsp;</a></span>velocity</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Rigidbody.velocity</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The velocity vector of the rigidbody. </p>
<p >In most cases you should not modify the velocity directly, as this can result in unrealistic behaviour. Don't set the velocity of an object every physics step, this will lead to unrealistic physics simulation. A typical example where you would change the velocity is when jumping in a first person shooter, because you want an immediate change in velocity. </p><dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a23530e52ed361ac1f758e8204a1c833b" name="a23530e52ed361ac1f758e8204a1c833b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a23530e52ed361ac1f758e8204a1c833b">&#9670;&nbsp;</a></span>worldCenterOfMass</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Rigidbody.worldCenterOfMass</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The center of mass of the rigidbody in world space (Read Only). </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaRigidbody.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_rigidbody.html">Rigidbody</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

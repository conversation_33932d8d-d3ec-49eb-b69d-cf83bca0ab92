<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_input.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle"><div class="title">Lua.Input Member List</div></div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="class_lua_1_1_input.html">Lua.Input</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_input.html#a624c63ec2127ae1a70da0e8ac2a5742d">anyKey</a></td><td class="entry"><a class="el" href="class_lua_1_1_input.html">Lua.Input</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_input.html#a2587110e95d4dff8d00d112fcade9631">anyKeyDown</a></td><td class="entry"><a class="el" href="class_lua_1_1_input.html">Lua.Input</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_input.html#a616e22e4f3b9c973c9763c10dc495395">GetAxis</a>(string axisName)</td><td class="entry"><a class="el" href="class_lua_1_1_input.html">Lua.Input</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_input.html#aa25e7d2e0c828c4661e8a77db269e5b3">GetAxisRaw</a>(string axisName)</td><td class="entry"><a class="el" href="class_lua_1_1_input.html">Lua.Input</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_input.html#ac45bfbc1aaa71822f9dd32ee446e3e26">GetButton</a>(string buttonName)</td><td class="entry"><a class="el" href="class_lua_1_1_input.html">Lua.Input</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_input.html#a59b2338d29a39f0694aebef890598b7c">GetButtonDown</a>(string buttonName)</td><td class="entry"><a class="el" href="class_lua_1_1_input.html">Lua.Input</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_input.html#a74cdd1903a2b531d575a20ea9cbb0ec0">GetButtonUp</a>(string buttonName)</td><td class="entry"><a class="el" href="class_lua_1_1_input.html">Lua.Input</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_input.html#abf392b3cf9d208f67a6ea02c0288206a">GetKey</a>(string name)</td><td class="entry"><a class="el" href="class_lua_1_1_input.html">Lua.Input</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_input.html#a8701b16492ad5e1ec79c019d74f7b051">GetKeyDown</a>(string name)</td><td class="entry"><a class="el" href="class_lua_1_1_input.html">Lua.Input</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_input.html#a7c9f4df5dcb4bc4d194da55be31fb0ea">GetKeyUp</a>(string name)</td><td class="entry"><a class="el" href="class_lua_1_1_input.html">Lua.Input</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_input.html#a40addbec8d9b18f0dc0cb101edc38b8d">GetMouseButton</a>(int button)</td><td class="entry"><a class="el" href="class_lua_1_1_input.html">Lua.Input</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_input.html#a1a6498f2fab91a72642ac064359edef7">GetMouseButtonDown</a>(int button)</td><td class="entry"><a class="el" href="class_lua_1_1_input.html">Lua.Input</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_input.html#ac9d47356c504c74bfcbee5fff1e9939b">GetMouseButtonUp</a>(int button)</td><td class="entry"><a class="el" href="class_lua_1_1_input.html">Lua.Input</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_input.html#ab5d4bcb7c637ec0760fc8ca8033ecec7">mousePosition</a></td><td class="entry"><a class="el" href="class_lua_1_1_input.html">Lua.Input</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_input.html#ac508b474e85e336be67f2ad7ef94f751">mouseScrollDelta</a></td><td class="entry"><a class="el" href="class_lua_1_1_input.html">Lua.Input</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
</table></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

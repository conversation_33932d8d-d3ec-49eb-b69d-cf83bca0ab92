Behavior = RegisterBehavior("phone call")

Behavior.data = { 

    agent = {
        type = { "none" },
        exclude = { "none" }
    },
    target = {
        type = { "none" },
        exclude = { "none" }
    },
    ai = true,
    secondary = false,
    hideMenu = true,
}

function Behavior:Start()
    self.agent.animation.set("Talking on Phone")
end

Behavior = RegisterBehavior("heart attack")

Behavior.data = { 

    agent = {
        type = { "none" },
        exclude = { "none" }
    },
    target = {
        type = { "none" },
        exclude = { "none" }
    },
    ai = true,
    secondary = false,
    hideMenu = true,
}

function Behavior:Start()
    self.agent.animation.SetAndWait("Falling Down")
    self.agent.animation.SetAndWait("Convulsing")
end

Behavior = RegisterBehavior("random dancing")

Behavior.data = { 

    agent = {
        type = { "none" },
        exclude = { "none" }
    },
    target = {
        type = { "none" },
        exclude = { "none" }
    },
    ai = true,
    secondary = false,
    hideMenu = true,
}

function Behavior:Start()
    self.agent.animation.SetAndWait("Samba Dancing")
end

Behavior = RegisterBehavior("texting")

Behavior.data = { 

    agent = {
        type = { "none" },
        exclude = { "none" }
    },
    target = {
        type = { "none" },
        exclude = { "none" }
    },
    ai = true,
    secondary = false,
    hideMenu = true,
}

function Behavior:Start()
    self.agent.animation.SetAndWait("Texting")
end

Behavior = RegisterBehavior("textingwalk")

Behavior.data = { 

    agent = {
        type = { "none" },
        exclude = { "none" }
    },
    target = {
        type = { "none" },
        exclude = { "none" }
    },
    ai = true,
    secondary = false,
    hideMenu = true,
}

function Behavior:Start()
    self.agent.animation.SetAndWait("Texting and Walking")
end

Behavior = RegisterBehavior("waving")

Behavior.data = { 

    agent = {
        type = { "none" },
        exclude = { "none" }
    },
    target = {
        type = { "none" },
        exclude = { "none" }
    },
    ai = true,
    secondary = false,
    hideMenu = true,
}

function Behavior:Start()
    self.agent.animation.SetAndWait("Waving")
end
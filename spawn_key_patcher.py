#!/usr/bin/env python3
"""
Sizebox Spawn Key Patcher
Modifies Assembly-CSharp.dll to change P and O spawn keys to different keys
"""

import os
import shutil
import hashlib

def backup_file(source, backup_dir):
    """Create a backup of the original file"""
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    
    backup_path = os.path.join(backup_dir, os.path.basename(source) + '.spawn_backup')
    shutil.copy2(source, backup_path)
    print(f"Backup created: {backup_path}")
    return backup_path

def get_file_hash(filepath):
    """Get MD5 hash of file for verification"""
    with open(filepath, 'rb') as f:
        return hashlib.md5(f.read()).hexdigest()

def find_keycode_patterns(dll_data):
    """Find KeyCode patterns in the DLL"""
    # Unity KeyCode enum values (these are the actual byte values in the DLL)
    keycodes = {
        'P': b'\x50',  # KeyCode.P = 80 (0x50)
        'O': b'\x4F',  # KeyCode.O = 79 (0x4F)
        'F9': b'\x78',  # KeyCode.F9 = 120 (0x78) - good replacement
        'F10': b'\x79', # KeyCode.F10 = 121 (0x79) - good replacement
        'F11': b'\x7A', # KeyCode.F11 = 122 (0x7A)
        'F12': b'\x7B'  # KeyCode.F12 = 123 (0x7B)
    }
    
    print("Searching for KeyCode patterns...")
    
    # Look for spawn-related strings near KeyCode references
    spawn_patterns = [
        b"spawn",
        b"Spawn", 
        b"SPAWN",
        b"micro",
        b"Micro",
        b"MICRO",
        b"character",
        b"Character"
    ]
    
    # Find all occurrences of P and O keycodes
    p_offsets = []
    o_offsets = []
    
    pos = 0
    while True:
        pos = dll_data.find(keycodes['P'], pos)
        if pos == -1:
            break
        p_offsets.append(pos)
        pos += 1
    
    pos = 0
    while True:
        pos = dll_data.find(keycodes['O'], pos)
        if pos == -1:
            break
        o_offsets.append(pos)
        pos += 1
    
    print(f"Found {len(p_offsets)} potential P key references")
    print(f"Found {len(o_offsets)} potential O key references")
    
    # Filter to find spawn-related key references
    spawn_p_offsets = []
    spawn_o_offsets = []
    
    # Check area around each keycode for spawn-related strings
    search_radius = 500  # bytes to search around each keycode
    
    for offset in p_offsets:
        start = max(0, offset - search_radius)
        end = min(len(dll_data), offset + search_radius)
        area = dll_data[start:end]
        
        for pattern in spawn_patterns:
            if pattern in area:
                spawn_p_offsets.append(offset)
                print(f"Found spawn-related P key at offset: 0x{offset:08X}")
                break
    
    for offset in o_offsets:
        start = max(0, offset - search_radius)
        end = min(len(dll_data), offset + search_radius)
        area = dll_data[start:end]
        
        for pattern in spawn_patterns:
            if pattern in area:
                spawn_o_offsets.append(offset)
                print(f"Found spawn-related O key at offset: 0x{offset:08X}")
                break
    
    return spawn_p_offsets, spawn_o_offsets, keycodes

def patch_spawn_keys():
    """Main patching function"""
    dll_path = r"Sizebox_Data\Managed\Assembly-CSharp.dll"
    backup_dir = r"Sizebox_Backup_SpawnKeys"
    
    print("Sizebox Spawn Key Patcher")
    print("========================")
    print("This will change P and O spawn keys to F9 and F10")
    print()
    
    # Verify file exists
    if not os.path.exists(dll_path):
        print(f"ERROR: {dll_path} not found!")
        return False
    
    # Get original hash
    original_hash = get_file_hash(dll_path)
    print(f"Original file hash: {original_hash}")
    
    # Create backup
    backup_path = backup_file(dll_path, backup_dir)
    
    # Load the DLL
    with open(dll_path, 'rb') as f:
        dll_data = bytearray(f.read())
    
    print(f"Loaded DLL: {len(dll_data)} bytes")
    
    # Find spawn key patterns
    spawn_p_offsets, spawn_o_offsets, keycodes = find_keycode_patterns(dll_data)
    
    if not spawn_p_offsets and not spawn_o_offsets:
        print("ERROR: No spawn-related key bindings found!")
        print("The keys might be hardcoded differently or obfuscated.")
        return False
    
    print(f"\nFound {len(spawn_p_offsets)} P key spawn bindings")
    print(f"Found {len(spawn_o_offsets)} O key spawn bindings")
    
    # Apply patches
    patches_applied = 0
    
    # Replace P keys with F9
    for offset in spawn_p_offsets:
        dll_data[offset] = keycodes['F9'][0]  # Replace P (0x50) with F9 (0x78)
        patches_applied += 1
        print(f"Patched P -> F9 at offset: 0x{offset:08X}")
    
    # Replace O keys with F10  
    for offset in spawn_o_offsets:
        dll_data[offset] = keycodes['F10'][0]  # Replace O (0x4F) with F10 (0x79)
        patches_applied += 1
        print(f"Patched O -> F10 at offset: 0x{offset:08X}")
    
    if patches_applied == 0:
        print("No patches applied!")
        return False
    
    # Write the patched DLL
    patched_path = dll_path + ".spawn_patched"
    with open(patched_path, 'wb') as f:
        f.write(dll_data)
    
    print(f"\nPatched DLL saved as: {patched_path}")
    print(f"Applied {patches_applied} patches")
    
    # Verify the patch
    patched_hash = get_file_hash(patched_path)
    print(f"Patched file hash: {patched_hash}")
    
    if patched_hash != original_hash:
        print("✓ File successfully modified!")
        
        print("\nPatch ready! This will:")
        print("• Change P key spawn to F9 key")
        print("• Change O key spawn to F10 key") 
        print("• Keep all other functionality the same")
        print("• F9/F10 are less likely to interfere with typing")
        
        apply = input("\nApply patch to game? (y/N): ").lower().strip()
        if apply == 'y':
            # Backup original and apply patch
            if not os.path.exists(dll_path + ".original"):
                shutil.copy2(dll_path, dll_path + ".original")
            shutil.copy2(patched_path, dll_path)
            print("✓ Patch applied! Original backed up as .original")
            print("✓ Spawn keys changed: P -> F9, O -> F10")
            print("✓ Restart Sizebox to use new keys!")
            return True
        else:
            print("Patch not applied. Files remain unchanged.")
            return True
    else:
        print("✗ No changes made to file")
        return False

def restore_original():
    """Restore the original DLL"""
    dll_path = r"Sizebox_Data\Managed\Assembly-CSharp.dll"
    original_path = dll_path + ".original"
    
    if os.path.exists(original_path):
        shutil.copy2(original_path, dll_path)
        print("✓ Original DLL restored - spawn keys back to P and O")
        return True
    else:
        print("✗ Original backup not found")
        return False

if __name__ == "__main__":
    try:
        print("Sizebox Spawn Key Patcher")
        print("========================")
        print("1. Patch spawn keys (P->F9, O->F10)")
        print("2. Restore original keys")
        print()
        
        choice = input("Enter choice (1 or 2): ").strip()
        
        if choice == "1":
            success = patch_spawn_keys()
        elif choice == "2":
            success = restore_original()
        else:
            print("Invalid choice!")
            success = False
            
        if success:
            print("\nOperation completed successfully!")
        else:
            print("\nOperation failed!")
            
    except Exception as e:
        print(f"Error: {e}")
    
    input("Press Enter to exit...")

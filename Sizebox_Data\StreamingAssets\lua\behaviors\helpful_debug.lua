TargetPos = RegisterBehavior("TargetPosition")
TargetPos.data =  {
    menuEntry = "Debug/Entity Info",
    secondary = true,
    agent = {
        type = { "humanoid" }
    },
    target = {
        type = { "oneself" }
    }
}
function TargetPos:Start()
    --Output entity Name
    Log("Name: " .. self.agent.name)
    --Output entity Position
    Log("Position: " .. self.agent.position)
    --Output entity Scale
    Log("Scale: " .. self.agent.scale)

end

EntityMove = RegisterBehavior("EntityMove")
EntityMove.data =  {
    menuEntry = "Debug/Move Entity",
    secondary = true,
    flags = {"EntityMove"},
    agent = {
        type = { "humanoid" }
    },
    target = {
        type = { "oneself" }
    },
    settings = {
		{ "pos_x", "X Position","string","0"},
		{ "pos_y", "Y Position", "string","0"},
        { "pos_z", "Z Position", "string","0"},
        { "gravity", "Gravity", "bool", false},
        { "stop_key", "Stop key", "keybind", "k" } 
	}
}
function EntityMove:Start()
    self.pos_x = tonumber(self.pos_x)
    self.pos_y = tonumber(self.pos_y)
    self.pos_z = tonumber(self.pos_z)
    self.position = Vector3.New(self.pos_x, self.pos_y, self.pos_z)
    if self.gravity then
        self.agent.rigidbody.useGravity = false
    end
end

function EntityMove:Update()
    self.agent.rigidbody.position = self.position
    if Input.GetKeyDown(self.stop_key) then
        self.agent.ai.StopSecondaryBehavior("EntityMove")
        return
	end
end

function EntityMove:Exit()
    Log("Ending Enitity Move")
end
-- Size Pulse Wave Script
-- This script creates a pulsating size change effect with various modes and visual effects
-- Press 'P' to cycle through different pulse modes
-- Press 'O' to toggle auto-mode which randomly changes modes
-- Press 'I' to toggle pulse intensity
-- Press 'U' to toggle pulse speed
-- Press 'Y' to toggle visual effects

SizePulseWave = RegisterBehavior("Size Pulse Wave")
SizePulseWave.data = {
    menuEntry = "Size/Size Pulse Wave",
    secondary = true,
    flags = { "grow" },
    agent = {
        type = { "humanoid" }
    },
    target = {
        type = { "oneself" }
    }
}

-- Sound effects array
soundEffects = {
    "Rumblev1.wav",       -- Deep rumble
    "stretchmix.ogg",     -- Stretching sound
    "customboing.wav",    -- Boing sound
    "heartbeat.ogg"       -- Heartbeat sound (if available, otherwise will default to first sound)
}

-- Pulse modes
pulseModes = {
    "Sine Wave",          -- Smooth sine wave pulsing
    "Heartbeat",          -- Double-beat pulse like a heartbeat
    "Sawtooth",           -- Quick grow, slow shrink
    "Square",             -- Abrupt size changes
    "Random",             -- Random size fluctuations
    "Exponential",        -- Exponential growth followed by quick shrink
    "Bounce"              -- Overshoot and bounce back
}

-- Visual effect colors (RGB)
effectColors = {
    {1.0, 0.2, 0.2},      -- Red
    {0.2, 1.0, 0.2},      -- Green
    {0.2, 0.2, 1.0},      -- Blue
    {1.0, 0.8, 0.0},      -- Gold
    {0.8, 0.2, 1.0},      -- Purple
    {0.0, 0.8, 1.0}       -- Cyan
}

function SizePulseWave:Start()
    -- Initialize variables
    self.startScale = self.agent.scale
    self.currentMode = 1
    self.autoMode = false
    self.nextModeChange = Time.time + 15
    self.pulseIntensity = 0.2       -- How much to grow/shrink (multiplier)
    self.pulseSpeed = 1.0           -- Speed of the pulse
    self.visualEffects = true       -- Whether to show visual effects
    self.currentColor = 1
    self.time = 0
    self.lastPulseTime = 0
    self.lastHeartbeatPhase = false
    self.lastAutoModeTime = Time.time
    self.effectIntensity = 0
    
    -- Initialize audio
    self.audio_source = AudioSource:new(self.agent.bones.spine)
    self.audio_source.spatialBlend = 1
    self.audio_source.loop = true
    self.audio_source.clip = soundEffects[1]
    self.audio_source.volume = 0.5
    self.audio_source.pitch = 1.0
    self.audio_source.minDistance = 0.01 * (self.agent.scale * 250)
    
    -- Log instructions
    Log("Size Pulse Wave script started!")
    Log("Press 'P' to cycle through different pulse modes")
    Log("Press 'O' to toggle auto-mode which randomly changes modes")
    Log("Press 'I' to toggle pulse intensity")
    Log("Press 'U' to toggle pulse speed")
    Log("Press 'Y' to toggle visual effects")
    Log("Current mode: " .. pulseModes[self.currentMode])
end

function SizePulseWave:Update()
    -- Update time
    self.time = self.time + Time.deltaTime * self.pulseSpeed
    
    -- Check for key presses
    if Input.GetKeyDown("p") then
        self:CycleMode()
    end
    
    if Input.GetKeyDown("o") then
        self.autoMode = not self.autoMode
        Log("Auto mode: " .. (self.autoMode and "ON" or "OFF"))
    end
    
    if Input.GetKeyDown("i") then
        self:CycleIntensity()
    end
    
    if Input.GetKeyDown("u") then
        self:CycleSpeed()
    end
    
    if Input.GetKeyDown("y") then
        self.visualEffects = not self.visualEffects
        Log("Visual effects: " .. (self.visualEffects and "ON" or "OFF"))
    end
    
    -- Auto mode logic
    if self.autoMode and Time.time > self.nextModeChange then
        local newMode = math.random(1, #pulseModes)
        while newMode == self.currentMode do
            newMode = math.random(1, #pulseModes)
        end
        self.currentMode = newMode
        self.nextModeChange = Time.time + math.random(10, 20)
        Log("Auto mode changed to: " .. pulseModes[self.currentMode])
        
        -- Change sound effect
        local soundIndex = math.random(1, #soundEffects)
        self.audio_source.clip = soundEffects[soundIndex]
        self.audio_source:Play()
        
        -- Change color
        self.currentColor = math.random(1, #effectColors)
    end
    
    -- Calculate pulse factor based on current mode
    local pulseFactor = self:CalculatePulseFactor()
    
    -- Apply size change
    local targetScale = self.startScale * (1 + pulseFactor * self.pulseIntensity)
    
    -- Ensure we don't exceed min/max size
    targetScale = math.max(targetScale, gts.minSize)
    targetScale = math.min(targetScale, gts.maxSize)
    
    -- Smooth transition to target scale
    self.agent.scale = math.lerp(self.agent.scale, targetScale, 0.2)
    
    -- Update audio effects
    self:UpdateAudio(pulseFactor)
    
    -- Visual effects
    if self.visualEffects then
        self:UpdateVisualEffects(pulseFactor)
    end
end

function SizePulseWave:CalculatePulseFactor()
    local factor = 0
    
    if self.currentMode == 1 then
        -- Sine Wave
        factor = math.sin(self.time * 2)
        
    elseif self.currentMode == 2 then
        -- Heartbeat (double-beat pulse)
        local cycle = self.time % 3
        if cycle < 0.3 then
            factor = math.sin(cycle * 20) * 0.8
        elseif cycle < 0.5 then
            factor = math.sin(cycle * 15) * 0.4
        else
            factor = 0
        end
        
        -- Trigger heartbeat sound
        local inHeartbeatPhase = cycle < 0.5
        if inHeartbeatPhase and not self.lastHeartbeatPhase then
            self.audio_source.pitch = 1.0 + math.random() * 0.2
            self.audio_source.volume = 0.7 + math.random() * 0.3
        end
        self.lastHeartbeatPhase = inHeartbeatPhase
        
    elseif self.currentMode == 3 then
        -- Sawtooth (quick grow, slow shrink)
        local cycle = self.time % 4
        if cycle < 0.5 then
            factor = cycle * 2
        else
            factor = 1 - (cycle - 0.5) / 3.5
        end
        factor = factor * 2 - 1
        
    elseif self.currentMode == 4 then
        -- Square (abrupt size changes)
        factor = (math.sin(self.time) > 0) and 1 or -1
        
    elseif self.currentMode == 5 then
        -- Random
        if Time.time - self.lastPulseTime > 0.2 then
            factor = math.random() * 2 - 1
            self.lastPulseTime = Time.time
        end
        
    elseif self.currentMode == 6 then
        -- Exponential
        local cycle = self.time % 5
        if cycle < 2.5 then
            factor = math.pow(cycle / 2.5, 2)
        else
            factor = math.pow(1 - ((cycle - 2.5) / 2.5), 0.5)
        end
        factor = factor * 2 - 1
        
    elseif self.currentMode == 7 then
        -- Bounce
        local cycle = self.time % 4
        if cycle < 1 then
            factor = math.sin(cycle * math.pi / 2)
        elseif cycle < 1.2 then
            factor = 1 - (cycle - 1) * 5
        elseif cycle < 1.4 then
            factor = 0 + (cycle - 1.2) * 2.5
        elseif cycle < 1.6 then
            factor = 0.5 - (cycle - 1.4) * 2.5
        elseif cycle < 1.8 then
            factor = 0 + (cycle - 1.6) * 1.5
        elseif cycle < 2.0 then
            factor = 0.3 - (cycle - 1.8) * 1.5
        else
            factor = 0
        end
    end
    
    return factor
end

function SizePulseWave:UpdateAudio(pulseFactor)
    -- Update audio parameters based on size and pulse
    local scaleFactor = self.agent.scale * 1000
    self.audio_source.minDistance = 0.01 * (scaleFactor * 0.25)
    
    -- Adjust pitch based on size and pulse
    local basePitch = 0.8 + 0.8 / math.sqrt((scaleFactor / 125) + 1)
    local pulsePitchMod = math.abs(pulseFactor) * 0.3
    self.audio_source.pitch = basePitch + pulsePitchMod
    
    -- Adjust volume based on pulse intensity
    local baseVolume = 1.7 - self.audio_source.pitch
    local pulseVolumeMod = math.abs(pulseFactor) * 0.5
    self.audio_source.volume = baseVolume + pulseVolumeMod
    
    -- Ensure audio is playing
    if not self.audio_source.isPlaying then
        self.audio_source:Play()
    end
end

function SizePulseWave:UpdateVisualEffects(pulseFactor)
    -- Calculate effect intensity based on pulse factor
    self.effectIntensity = math.abs(pulseFactor) * 0.8
    
    -- Apply visual effect based on current color
    local color = effectColors[self.currentColor]
    
    -- This is a placeholder for visual effects
    -- In a real implementation, you would use Unity's particle system or shader effects
    -- For now, we'll just log the effect for demonstration
    if self.effectIntensity > 0.5 and Time.time - self.lastAutoModeTime > 0.5 then
        self.lastAutoModeTime = Time.time
        -- Log("Visual effect: " .. self.effectIntensity .. " intensity with color R:" .. color[1] .. " G:" .. color[2] .. " B:" .. color[3])
    end
end

function SizePulseWave:CycleMode()
    self.currentMode = self.currentMode % #pulseModes + 1
    Log("Pulse mode: " .. pulseModes[self.currentMode])
    
    -- Change sound effect based on mode
    local soundIndex = math.min(self.currentMode, #soundEffects)
    self.audio_source.clip = soundEffects[soundIndex]
    self.audio_source:Play()
end

function SizePulseWave:CycleIntensity()
    local intensities = {0.1, 0.2, 0.5, 1.0, 2.0}
    local currentIndex = 1
    
    for i, intensity in ipairs(intensities) do
        if math.abs(self.pulseIntensity - intensity) < 0.01 then
            currentIndex = i
            break
        end
    end
    
    currentIndex = currentIndex % #intensities + 1
    self.pulseIntensity = intensities[currentIndex]
    Log("Pulse intensity: " .. self.pulseIntensity)
end

function SizePulseWave:CycleSpeed()
    local speeds = {0.5, 1.0, 1.5, 2.0, 3.0}
    local currentIndex = 1
    
    for i, speed in ipairs(speeds) do
        if math.abs(self.pulseSpeed - speed) < 0.01 then
            currentIndex = i
            break
        end
    end
    
    currentIndex = currentIndex % #speeds + 1
    self.pulseSpeed = speeds[currentIndex]
    Log("Pulse speed: " .. self.pulseSpeed)
end

-- Helper function for lerp since it might not be available in Lua
if not math.lerp then
    function math.lerp(a, b, t)
        return a + (b - a) * t
    end
end
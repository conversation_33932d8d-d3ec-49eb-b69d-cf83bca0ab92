MockRay = Register<PERSON><PERSON><PERSON><PERSON>("Makeshift Ray")
MockRay.data =  {
    agent = {
        type = {"player"}
    },
	target = {
		type = {"oneself"}
	}
}

modelArg = "unitychan.micro/unitychan"
forceRadius = 5
gtsTarget = nil
i = 1
gtsList = {}
safety = true
altFire = false

function MockRay:Start()
    Log("starting makeshift ray script")
	Log(" - Press 'U' to select between target GTS")
	Log(" - Press 'Y' to toggle Safety(Enable/disable firing)")
	Log(" - Press 'Left Mouse' to Shoot")
	Log(" - Press 'T' to toggle between shrink/grow")
	Log(" - Press '6' to decrease bullet 'damage'(shrink/grow rate)")
	Log(" - Press '7' to increase bullet 'damage'(shrink/grow rate)")
	Log(" - Press '8' to toggle between new and old bullet spawning position")
	Log(" - Press '5' to exit script")
	
	gtsTarget = AcquireTarget()
	
	globals["growMagnitude"] = 1
	globals["bulletDamage"] = 0.01
end

function MockRay:Update()
	if Input.GetKeyDown("y") then
		safety = not safety
		if safety then
			log("safety is on")
		else
			log("safety is off")
		end
	end

	if Input.GetButton("Click") and not safety then
		--log("action pressed")					
		if gtsTarget != nil then
			if altFire then
				local angle = player.transform.rotation.eulerAngles.y
				local microRot = Quaternion.angleAxis(angle, Vector3.up)
				local microPos = self.agent.position + (player.transform.forward * player.scale) + (Vector3.up * player.scale)
				local temp = Entity.SpawnFemaleMicro(modelArg, microPos, microRot, player.scale * 0.15)
				temp.ai.SetBehavior("Bullet", gtsTarget)
			else
				local angle = player.transform.rotation.eulerAngles.y
				local microRot = Quaternion.angleAxis(angle, Vector3.up)
				local gtsDir = (gtsTarget.position - self.agent.position)
				gtsDir = Vector3.ClampMagnitude(gtsDir, 1) * player.scale * 2
				local microPos = self.agent.position + gtsDir + (Vector3.up * player.scale)
				local temp = Entity.SpawnFemaleMicro(modelArg, microPos, microRot, player.scale * 0.15)
				temp.ai.SetBehavior("Bullet", gtsTarget)
			end
		else
			log("no target")
		end
		
	elseif Input.GetKeyDown("u") then
		gtsTarget = AcquireTarget()
		
	elseif Input.GetKeyDown("t") then
		globals["growMagnitude"] = globals["growMagnitude"] * -1
		if globals["growMagnitude"] > 0 then
			log("grow way")
		else
			log("shrink ray")
		end
		
	elseif Input.GetKeyDown("6") then
		globals["bulletDamage"] = Mathf.Clamp(globals["bulletDamage"] - 0.0005, 0.0005, 3)
		log("bullet damage = " .. globals["bulletDamage"])
		
	elseif Input.GetKeyDown("7") then
		globals["bulletDamage"] = Mathf.Clamp(globals["bulletDamage"] + 0.0005, 0.0005, 3)
		log("bullet damage = " .. globals["bulletDamage"])
		
	elseif Input.GetKeyDown("8") then
		altFire = not altFire
		if altfire then
			log("old firing mode")
		else
			log("new firing mode")
		end
		
	elseif Input.GetKeyDown("5") then
		self.agent.ai.StopBehavior()
	end

end

function MockRay:Exit()
    log("exiting makeshift ray script")
end

function AcquireTarget()
	a = gts.list
	local count = 0
	for key,value in pairs(a) do 
		if not value.isPlayer() then	
			count = count + 1
			gtsList[count] = value
		end
	end
	
	if count < 1 then
		log("no valid targets detected")
		return nil
	else
		if i >= count then
			i = 1
			log("new target - " .. gtsList[1].name)
			return gtsList[1]
		else
			i = i + 1
			log("new target - " .. gtsList[i].name)
			return gtsList[i]
		end
	end
end


MicroBullet = RegisterBehavior("Bullet")
MicroBullet.react = true
MicroBullet.data = {
    hideMenu = true,
    agent = {
        type = { "micro"}, 
        exclude = {"player"}
    },
    target = {
        type = {"giantess"}
    }
}

bulletSpeed = 40
maxtime = 5

function MicroBullet:Start()
	self.agent.animation.SetPose("resting9")
    self.lifetime = 0
	self.hit = false
	self.aimgoal = (self.target.position + (Vector3.up * self.target.scale * 1000)) - self.agent.position
	self.aimgoal = Vector3.ClampMagnitude(self.aimgoal, 10)
	self.agent.rigidbody.AddForce(self.aimgoal * bulletSpeed * player.scale * 0.8)
end 

function MicroBullet:Update()
	self.agent.rigidbody.AddForce(self.aimgoal * bulletSpeed * player.scale)
	--log(self.aimgoal)
	if self.hit then	
		self.target.Grow(globals["bulletDamage"] * globals["growMagnitude"], 0.1)
		self.agent.ai.StopBehavior()
    elseif self.lifetime < maxtime then
		self.lifetime = self.lifetime + Time.deltaTime
		self.dist = Vector3.Distance(self.agent.position, self.target.rigidbody.ClosestPointOnBounds(self.agent.position))		
		if self.dist < player.scale then
			self.hit = true
		end
	else
		log("fail " .. self.dist)
		self.agent.ai.StopBehavior()
    end
end

function MicroBullet:Exit()
	self.agent.Delete()
end

PlayerCrushGrow = RegisterBehavior("Player Crush+Grow")
PlayerCrushGrow.agentType = "giantess"
PlayerCrushGrow.targetType = "oneself"

local counter = 0
local microList = {}
local radiusCheck = 1
local growVal = 0.3
local growTime = 0.5
local jAnim = "Stomping"
local kAnim = "Sit 6"
local lAnim = "Dancing"

function PlayerCrushGrow:Start()
  microList = self.agent.senses.getMicrosInRadius(radiusCheck)
	log("monitoring " .. #microList .. " micros.")
end

function PlayerCrushGrow:Update()
  counter = counter + 1
	if counter >= 10 then
	  for i=1, #microList do
		  if microList[i].isDead() then
			  log("Got One!")
				self.agent.grow(0.6,0.5)
			end
		end
		microList = self.agent.senses.getMicrosInRadius(radiusCheck)
	  log("monitoring " .. #microList .. " micros.")
	end
	if Input.GetKeyDown("j") then
	  self.agent.animation.set(jAnim)
	elseif Input.GetKeyDown("k") then
	  self.agent.animation.set(kAnim)
	elseif Input.GetKeyDown("l") then
	  self.agent.animation.set(lAnim)
	end
end
\hypertarget{class_lua_1_1_globals}{}\section{Lua.\+Globals Class Reference}
\label{class_lua_1_1_globals}\index{Lua.Globals@{Lua.Globals}}


Global dictionary. It can be used to store and exchange arbitrary data between scripts.  




\subsection{Detailed Description}
Global dictionary. It can be used to store and exchange arbitrary data between scripts. 

Accessible through the {\ttfamily globals} global variable. Example\+: {\ttfamily globals\mbox{[}\char`\"{}start\+Time\char`\"{}\mbox{]} = \mbox{\hyperlink{class_lua_1_1_time_a6a7753473015073c35d5ae5bc4edfdf3}{Time.\+time}}} 

The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
Lua\+Globals.\+cs\end{DoxyCompactItemize}

Rec = RegisterBehavior("RefleshEnhanced")
Rec.data = {
    menuEntry = "Reflesh Enhanced",
    secondary = true,
    flags = { "grow" },
    agent = {
        type = { "giantess" }
    },
    target = {
        type = { "giantess" }
    }
}

-- Define growth sounds
local growthSounds = {
    "GaspMoan001_Mmm.ogg",
    "GaspMoan002_Augh_MidLong.ogg",
    "GaspMoan003_Ahh_MidLong.ogg",
    "GaspMoan005_Mmm_Long.ogg"
}

function Rec:Start()
    phi = 0
    status = 0
    killing = true
    superGrowth = false
    superGrowthset = false
    growing = false
    GS = 0
    rate = 0
    duration = 0
    walkSet = false
    queuedAnim = nil
    refleshTimer = 0
    refleshDuration = 2.5
    idleTimer = 0
    idleDuration = 1.0
    inRefleshCooldown = false
    self.huntingMicros = false  -- Flag for hunting micros in stepped mode
    self.huntingCooldown = 0    -- Cooldown timer for hunting

    -- Growth mode
    growthMode = "linear" -- Default growth mode is now linear
    baseRate = 0.12
    currentRate = baseRate
    exponentialMultiplier = 1.5
    growthCap = 1.1
    
    -- New growth mode parameters
    oscillationPeriod = 1.0 -- Time in seconds for one complete oscillation cycle (faster)
    oscillationPhase = 0    -- Current phase of oscillation
    oscillationAmplitude = 1.5 -- Amplitude multiplier for oscillation (more dramatic)
    oscillationFinalBurst = true -- Whether to have a final growth burst
    oscillationBurstDirection = 1 -- 1 for growth, -1 for shrink
    oscillationBurstMultiplier = 3.0 -- How much stronger the final burst is
    
    -- Stepped mode parameters - smoother growth
    stepSize = 0.05         -- Size of each growth step (smaller for smoother growth)
    stepInterval = 0.05     -- Time between steps (faster for smoother growth)
    stepTimer = 0           -- Timer for stepped growth
    
    burstDelay = 1.0        -- Delay before burst in delayed burst mode
    burstTimer = 0          -- Timer for burst mode
    burstMultiplier = 4.0   -- Multiplier for the burst (more dramatic)
    
    logBase = 2.0           -- Base for logarithmic growth
    
    -- Growth control settings
    soundEnabled = true     -- Toggle for growth sounds
    growthEnabled = true    -- Toggle for growth (can be disabled with X key)
    breastExpansion = false -- Toggle for breast expansion (can be enabled with Y key)
    previousBreastExpansion = false -- Store previous breast expansion state
    
    -- Animation options
    growthAnimations = {
        "Masturbation 1",
        "Massage Breasts 5",
        "Reflesh",
        "Idle"
    }
    currentAnimIndex = 1    -- Current animation index

    -- Duration control
    growthDuration = 3.0    -- Current growth duration
    durationStep = 1.0      -- How much to change duration by
    
    -- Audio setup
    self.audio_source = AudioSource:new(self.agent.bones.spine)
    self.audio_source.spatialBlend = 1
    self.audio_source.loop = false
    self.audio_source.volume = 1

    -- UI Toast for growth mode
    modeToast = Game.Toast.New()
    modeToast.Print("GROWTH MODE: LINEAR") -- Show on load
    
    -- Help toast to show available hotkeys
    helpToast = Game.Toast.New()
    helpToast.Print("Press H for hotkey help")
    
    -- Size control
    originalMaxSize = gts.maxSize  -- Store the original max size
    maxSizeMultiplier = 1.0        -- Current multiplier for max size display
    
    -- Stepped mode crush counter
    crushCount = 0                 -- Count of micros crushed
    crushMultiplier = 1.0          -- Multiplier that increases with each crush
    crushesInGrowth = {}           -- Track crushes that happen during growth
    lastCrushTime = 0              -- Time of last crush
    crushCooldown = 0.5            -- Cooldown between crushes
    
    -- Help message system
    helpMessagePart = 1            -- Current part of the help message
    helpMessageTimer = 0           -- Timer for cycling help messages
    helpMessageDelay = 3.0         -- Delay between help message parts
    
    -- Register crush event handler
    self.agent.dict.OnCrush = Event.Register(self, EventCode.OnCrush, self.OnCrush)
    
    -- Log startup message
    log("Reflesh Enhanced started! Press H for help.")
end

-- This function is called when the agent crushes something
function Rec:OnCrush(data)
    -- Make sure this is our agent doing the crushing and growth is enabled
    if data.crusher != nil and data.crusher.id == self.agent.id and growthEnabled then
        -- If we're in hunting mode, find the next micro after a short delay
        if self.huntingMicros then
            -- Set a cooldown before finding the next micro
            self.huntingCooldown = 1.0  -- 1 second cooldown
            
            -- Make sure we stop the current behavior to prevent continuous stomping
            self.agent.ai.StopAction()
            
            log("Micro crushed while hunting. Finding next micro in 1 second...")
        end
        -- For stepped mode
        if growthMode == "stepped" then
            -- Always count the crush, regardless of whether we're growing or not
            crushCount = crushCount + 1
            crushMultiplier = 1.0 + (crushCount * 0.5)  -- Multiplier increases with each crush
            
            -- If breast expansion is enabled, apply it immediately
            if breastExpansion then
                -- Try to find breast bones
                local foundBreasts = false
                local breastBones = {}
                
                -- Look for common breast bone names
                local boneNames = "Breast,Ichichi,LeftBreast,RightBreast,leftbreast,rightbreast,breast left,breast right,hidarichichi,migichichi,lPectoral,rPectoral"
                
                for boneName in string.gmatch(boneNames, '([^,]+)') do
                    local bones = self.agent.bones.GetBonesByName(boneName, true)
                    if bones then
                        for k,v in ipairs(bones) do
                            table.insert(breastBones, v)
                            foundBreasts = true
                        end
                    end
                end
                
                -- If we found breast bones, grow them
                if foundBreasts then
                    -- Apply breast-specific growth
                    local beGrowthFactor = 0.001 * crushMultiplier  -- Apply crush multiplier to BE growth
                    for k,v in ipairs(breastBones) do
                        v.localScale = v.localScale * (1 + 0.05 * beGrowthFactor)  -- Small immediate growth
                    end
                    
                    -- Show a message about breast expansion
                    modeToast.Print("STEPPED BREAST EXPANSION x" .. string.format("%.1f", crushMultiplier))
                end
            end
            
            -- If already growing, just update the multiplier but don't start new growth
            if growing or superGrowth or inRefleshCooldown then
                -- Show a message that the crush was detected and multiplier updated
                modeToast.Print("MICRO CRUSHED #" .. crushCount .. ": MULTIPLIER NOW x" .. string.format("%.1f", crushMultiplier))
            else
                -- Not growing, trigger immediate growth
                -- Check if we're on cooldown
                if Time.time - lastCrushTime < crushCooldown then
                    return -- Skip starting growth if we're on cooldown, but we already counted the crush
                end
                
                -- Update last crush time
                lastCrushTime = Time.time
                
                -- Trigger growth with Defeat animation
                self.agent.ai.StopAction()
                self.agent.animation.Set("Defeat")
                
                -- Set growth parameters with increasing multiplier
                currentRate = baseRate * 3.0 * crushMultiplier  -- Higher rate for crush growth, increases with each crush
                stepSize = baseRate * 0.1 * crushMultiplier     -- Smaller steps for smoother growth
                stepInterval = 0.05                             -- Faster interval for smoother growth
                duration = growthDuration
                
                -- Start growth
                growing = true
                superGrowthset = true
                walkSet = false
                phi = 0
                
                -- Play sound if enabled
                if soundEnabled then
                    self.audio_source.clip = growthSounds[math.random(1, #growthSounds)]
                    self.audio_source:Play()
                end
                
                modeToast.Print("MICRO CRUSHED #" .. crushCount .. ": GROWTH x" .. string.format("%.1f", crushMultiplier) .. "!")
            end
        end
    end
end

function Rec:Update()
    -- Display help when H is pressed
    if Input.GetKeyDown("h") then
        -- Reset help message system
        helpMessagePart = 1
        helpMessageTimer = 0
        
        -- Show first part of help
        helpToast.Print("MODES: L=Linear, K=Exponential, J=Random, U=Oscillating, I=Stepped, O=Burst, P=Log")
    end
    
    -- Cycle through help messages
    if helpMessagePart > 0 then
        helpMessageTimer = helpMessageTimer + Time.deltaTime
        
        if helpMessageTimer >= helpMessageDelay then
            helpMessageTimer = 0
            helpMessagePart = helpMessagePart + 1
            
            if helpMessagePart == 2 then
                helpToast.Print("CONTROLS: +=Rate Up, -=Rate Down, F=Duration Up, G=Duration Down, X=Toggle Growth, Y=Toggle BE")
            elseif helpMessagePart == 3 then
                helpToast.Print("ACTIONS: M=Toggle Sound, N=Cycle Anim, R=Growth, T=BE Growth, B=Double Max Size, C=Reset Crushes")
            else
                helpMessagePart = 0  -- End of messages
            end
        end
    end

    -- Change growth modes based on input (L for Linear)
    if Input.GetKeyDown("l") then
        growthMode = "linear"
        currentRate = baseRate
        modeToast.Print("GROWTH MODE: LINEAR") -- Update UI Toast
    end

    if Input.GetKeyDown("k") then
        growthMode = "exponential"
        modeToast.Print("GROWTH MODE: EXPONENTIAL")
    end

    if Input.GetKeyDown("j") then
        growthMode = "random"
        modeToast.Print("GROWTH MODE: RANDOM")
    end
    
    -- New growth modes
    if Input.GetKeyDown("u") then
        growthMode = "oscillating"
        -- Randomize oscillation parameters for more variety
        oscillationPeriod = 0.5 + math.random() * 1.0  -- Random period between 0.5-1.5 seconds (faster)
        oscillationAmplitude = 1.0 + math.random() * 2.0  -- Random amplitude multiplier
        oscillationFinalBurst = true
        -- Completely random final burst direction with random multiplier
        oscillationBurstDirection = math.random() > 0.5 and 1 or -1  -- 50% chance either way
        oscillationBurstMultiplier = 2.0 + math.random() * 4.0  -- Random multiplier between 2-6x
        modeToast.Print("GROWTH MODE: OSCILLATING (Random with Final Burst)")
        oscillationPhase = 0
    end
    
    if Input.GetKeyDown("i") then
        growthMode = "stepped"
        -- Set parameters for smoother growth
        stepSize = 0.05  -- Smaller steps for smoother growth
        stepInterval = 0.05  -- Faster interval for smoother growth
        modeToast.Print("GROWTH MODE: STEPPED (Micro Crush)")
        stepTimer = 0
    end
    
    if Input.GetKeyDown("o") then
        growthMode = "burst"
        burstMultiplier = 4.0 + math.random() * 3.0  -- Even larger burst multiplier
        modeToast.Print("GROWTH MODE: DELAYED BURST")
    end
    
    if Input.GetKeyDown("p") then
        growthMode = "logarithmic"
        modeToast.Print("GROWTH MODE: LOGARITHMIC")
    end
    
    -- Growth rate control
    if Input.GetKeyDown("=") or Input.GetKeyDown("+") then
        baseRate = baseRate * 1.25
        modeToast.Print("GROWTH RATE INCREASED: " .. string.format("%.2f", baseRate))
    end
    
    if Input.GetKeyDown("-") then
        baseRate = baseRate * 0.8
        modeToast.Print("GROWTH RATE DECREASED: " .. string.format("%.2f", baseRate))
    end
    
    -- Toggle sound effects (M key)
    if Input.GetKeyDown("m") then
        soundEnabled = not soundEnabled
        modeToast.Print("SOUND EFFECTS: " .. (soundEnabled and "ON" or "OFF"))
    end
    
    -- Toggle growth (X key)
    if Input.GetKeyDown("x") then
        growthEnabled = not growthEnabled
        modeToast.Print("GROWTH: " .. (growthEnabled and "ENABLED" or "DISABLED"))
    end
    
    -- Toggle breast expansion (Y key)
    if Input.GetKeyDown("y") then
        breastExpansion = not breastExpansion
        modeToast.Print("BREAST EXPANSION: " .. (breastExpansion and "ENABLED" or "DISABLED"))
    end
    
    -- Reset crush counter (C key)
    if Input.GetKeyDown("c") then
        crushCount = 0
        crushMultiplier = 1.0
        crushesInGrowth = {}
        modeToast.Print("CRUSH COUNTER RESET")
    end
    
    -- Cycle through animations
    if Input.GetKeyDown("n") then
        currentAnimIndex = (currentAnimIndex % #growthAnimations) + 1
        modeToast.Print("ANIMATION: " .. growthAnimations[currentAnimIndex])
    end
    
    -- Increase growth duration (F key)
    if Input.GetKeyDown("f") then
        growthDuration = growthDuration + durationStep
        modeToast.Print("GROWTH DURATION INCREASED: " .. string.format("%.1f", growthDuration) .. " seconds")
    end
    
    -- Decrease growth duration (G key)
    if Input.GetKeyDown("g") then
        growthDuration = math.max(1.0, growthDuration - durationStep)
        modeToast.Print("GROWTH DURATION DECREASED: " .. string.format("%.1f", growthDuration) .. " seconds")
    end
    
    -- Maximum Giantess Size control - directly copied from grow_spurtsv2_1
    if Input.GetKeyDown("b") then
        -- Store original max size
        local originalMaxSize = gts.maxSize
        
        -- Double maximum allowed giantess size
        gts.maxSize = 2 * originalMaxSize
        maxSizeMultiplier = maxSizeMultiplier * 2
        
        -- Log the change
        log("Original max size: " .. originalMaxSize)
        log("New max size: " .. gts.maxSize)
        
        -- Show a message about the new max size
        if maxSizeMultiplier >= 8 then
            modeToast.Print("MAX SIZE: " .. maxSizeMultiplier .. "x normal(!)")
            log("WARNING: She might become too big to fit in Sizebox...")
        else
            modeToast.Print("MAX SIZE: " .. maxSizeMultiplier .. "x normal")
        end
    end

    -- T key in stepped mode: find and crush micros
    if Input.GetKeyDown("t") and growthMode == "stepped" and growthEnabled then
        -- Start micro hunting mode
        modeToast.Print("HUNTING MICROS")
        
        -- Find a target micro using similar approach to growing rampage
        local nearbyMicros = self.agent.senses.GetMicrosInRadius(5)  -- Get micros in a reasonable radius
        
        -- If we found any micros
        if nearbyMicros and #nearbyMicros > 0 then
            -- Get the first micro
            local targetMicro = nearbyMicros[1]
            
            -- Stop any current action
            self.agent.ai.StopAction()
            
            -- Use SetBehavior to start stomping
            self.agent.ai.SetBehavior("StompSingle", targetMicro)
            log("Targeting micro for stomp")
            
            -- Set a flag to continue hunting after crush
            self.huntingMicros = true
            self.huntingCooldown = 0  -- Initialize cooldown to prevent nil comparison
        else
            modeToast.Print("NO MICROS FOUND")
            self.huntingMicros = false
        end
    end
    
    -- Growth triggers when pressing "r" or "t" (only if not in stepped mode and growth is enabled)
    if (Input.GetKeyDown("r") or Input.GetKeyDown("t")) and growthMode ~= "stepped" and growthEnabled then
        self.agent.ai.StopAction()
        killing = false
        superGrowth = true
        
        -- Use selected animation or default based on key
        if currentAnimIndex > 0 and Input.GetKeyDown("r") then
            queuedAnim = growthAnimations[currentAnimIndex]
        else
            queuedAnim = Input.GetKeyDown("r") and "Masturbation 1" or "Massage Breasts 5"
        end

        -- Play sound if enabled
        if soundEnabled and math.random() <= 0.25 then
            self.audio_source.clip = growthSounds[math.random(1, #growthSounds)]
            self.audio_source:Play()
        end
        
        -- Reset timers for special growth modes
        if growthMode == "burst" then
            burstTimer = 0
        elseif growthMode == "oscillating" then
            oscillationPhase = 0
            -- Randomize final burst direction each time
            oscillationBurstDirection = math.random() > 0.5 and 1 or -1
            oscillationBurstMultiplier = 2.0 + math.random() * 4.0
        end
        
        -- Ensure we're not immediately going to reflesh
        superGrowthset = false
        status = 0
    end

    -- Handle actual growth
    if growing and growthEnabled then
        status = 1
        
        -- Different growth patterns based on selected mode
        if growthMode == "linear" then
            -- Linear growth - smooth and consistent
            phi = phi + Time.deltaTime / duration
            if phi <= math.pi then
                GS = math.sin(phi * 0.5) * currentRate  -- Use half sine wave for smoother growth
            else
                growing = false
                phi = 0
                GS = 0
                status = 2
            end
        elseif growthMode == "exponential" then
            -- Exponential growth - starts slow, gets faster
            phi = phi + Time.deltaTime / duration
            if phi <= math.pi then
                GS = math.sin(phi * 0.5) * currentRate * (1 + phi)  -- Increasing rate
            else
                growing = false
                phi = 0
                GS = 0
                status = 2
            end
        elseif growthMode == "random" then
            -- Random growth - unpredictable
            phi = phi + Time.deltaTime / duration
            if phi <= math.pi then
                GS = math.sin(phi * 0.5) * currentRate * (0.5 + math.random())
            else
                growing = false
                phi = 0
                GS = 0
                status = 2
            end
        elseif growthMode == "oscillating" then
            -- Oscillating growth (grow and shrink in waves)
            oscillationPhase = oscillationPhase + Time.deltaTime
            
            -- Check if we're in the final burst phase
            if oscillationPhase >= oscillationPeriod * 3 and oscillationFinalBurst then
                -- Final dramatic burst in the chosen direction with random multiplier
                GS = currentRate * oscillationAmplitude * oscillationBurstMultiplier * oscillationBurstDirection
                
                -- End after a short burst period
                if oscillationPhase >= oscillationPeriod * 3 + 1.0 then
                    oscillationPhase = 0
                    growing = false
                    GS = 0
                    status = 2
                end
            elseif oscillationPhase >= oscillationPeriod * 4 then
                -- End if no final burst
                oscillationPhase = 0
                growing = false
                GS = 0
                status = 2
            else
                -- Normal oscillation with randomized amplitude
                GS = math.sin(oscillationPhase * (2 * math.pi / oscillationPeriod)) * currentRate * oscillationAmplitude
            end
        elseif growthMode == "stepped" then
            -- Stepped growth - now much smoother with smaller, more frequent steps
            phi = phi + Time.deltaTime / duration
            stepTimer = stepTimer + Time.deltaTime
            
            if stepTimer >= stepInterval then
                stepTimer = 0
                GS = stepSize * crushMultiplier  -- Apply crush multiplier for stronger growth
            else
                GS = 0
            end
            
            -- End after duration
            if phi >= math.pi then
                growing = false
                phi = 0
                GS = 0
                status = 2
            end
        elseif growthMode == "burst" then
            -- Delayed burst growth
            phi = phi + Time.deltaTime / duration
            burstTimer = burstTimer + Time.deltaTime
            
            if burstTimer < burstDelay then
                -- Slow initial growth
                GS = currentRate * 0.2
            else
                -- Sudden burst
                GS = currentRate * burstMultiplier
            end
            
            -- End after duration
            if phi >= math.pi then
                growing = false
                GS = 0
                status = 2
            end
        elseif growthMode == "logarithmic" then
            -- Logarithmic growth (fast at first, then slows)
            phi = phi + Time.deltaTime / duration
            
            if phi <= math.pi then
                -- Use log function for growth curve
                GS = currentRate * (1 - (1 / math.log(logBase + phi * 5)))
            else
                growing = false
                phi = 0
                GS = 0
                status = 2
            end
        end
        
        -- Apply growth based on breast expansion setting
        if breastExpansion then
            -- Try to find breast bones
            local foundBreasts = false
            local breastBones = {}
            
            -- Look for common breast bone names
            local boneNames = "Breast,Ichichi,LeftBreast,RightBreast,leftbreast,rightbreast,breast left,breast right,hidarichichi,migichichi,lPectoral,rPectoral"
            
            for boneName in string.gmatch(boneNames, '([^,]+)') do
                local bones = self.agent.bones.GetBonesByName(boneName, true)
                if bones then
                    for k,v in ipairs(bones) do
                        table.insert(breastBones, v)
                        foundBreasts = true
                    end
                end
            end
            
            -- If we found breast bones, grow them
            if foundBreasts then
                -- Apply breast-specific growth with mode-specific adjustments
                local beGrowthFactor = 0.001  -- Base growth factor (very subtle)
                
                -- Adjust growth factor based on mode
                if growthMode == "exponential" then
                    -- Make exponential growth even more subtle to build up slower
                    beGrowthFactor = 0.0005
                elseif growthMode == "burst" then
                    -- Make burst mode shorter/smaller
                    beGrowthFactor = 0.0008
                elseif growthMode == "oscillating" then
                    -- Implement faster grow/shrink effect with final burst
                    local phase = (phi / (oscillationPeriod * 0.5)) % 1.0  -- Twice as fast oscillation
                    if phase < 0.5 then
                        -- Growing phase - stronger growth
                        beGrowthFactor = 0.002  -- Double growth rate
                    else
                        -- Shrinking phase - stronger shrinking
                        beGrowthFactor = -0.0015  -- Almost double shrink rate
                    end
                    
                    -- Final burst at the end
                    if phi >= duration * 0.9 then  -- Near the end (final burst)
                        beGrowthFactor = 0.003  -- Stronger final burst
                    end
                end
                
                -- Apply the growth
                for k,v in ipairs(breastBones) do
                    v.localScale = v.localScale * (1 + GS * beGrowthFactor)
                end
                
                -- Show breast expansion message with consistent value
                -- Use a static value for display to keep it consistent
                if GS > 0 then
                    local displayValue = 1.0  -- Always show the same value
                    
                    if growthMode == "linear" then
                        modeToast.Print("LINEAR BREAST EXPANSION")
                    elseif growthMode == "exponential" then
                        modeToast.Print("EXPONENTIAL BREAST EXPANSION")
                    elseif growthMode == "random" then
                        modeToast.Print("RANDOM BREAST EXPANSION")
                    elseif growthMode == "oscillating" then
                        modeToast.Print("OSCILLATING BREAST EXPANSION")
                    elseif growthMode == "burst" then
                        modeToast.Print("BURST BREAST EXPANSION")
                    elseif growthMode == "logarithmic" then
                        modeToast.Print("LOGARITHMIC BREAST EXPANSION")
                    end
                end
            else
                -- If no breast bones found, just show a message
                if GS > 0 then
                    modeToast.Print("BREAST EXPANSION: " .. string.format("%.2f", GS) .. " (No breast bones found)")
                end
            end
        else
            -- Normal overall growth
            self.agent.grow(GS)
        end
    end

    -- When super growth is triggered (queued animation)
    if superGrowth and queuedAnim then
        self.agent.ai.StopAction()
        self.agent.animation.Set(queuedAnim)
        
        -- Use actual duration value (not scaled)
        duration = growthDuration
        
        -- Growth rate based on mode
        if growthMode == "linear" then
            currentRate = baseRate
        elseif growthMode == "exponential" then
            currentRate = math.min(currentRate * exponentialMultiplier, growthCap)
        elseif growthMode == "random" then
            currentRate = math.min(math.random() * baseRate * 5, growthCap)
        elseif growthMode == "oscillating" then
            currentRate = baseRate * 1.5
            oscillationPhase = 0
        elseif growthMode == "burst" then
            currentRate = baseRate * 1.5
            burstTimer = 0
        elseif growthMode == "logarithmic" then
            currentRate = baseRate * 2.5
        end

        rate = currentRate
        growing = true
        superGrowth = false
        superGrowthset = true
        walkSet = false
        
        -- Reset growth phase
        phi = 0
        
        -- Log the start of growth
        log("Starting growth in " .. growthMode .. " mode with duration " .. duration)
    end

    -- After growth completes, play reflesh animation
    if superGrowthset and status == 2 then
        superGrowthset = false
        status = 3
        
        -- Always play reflesh animation
        self.agent.animation.Set("Reflesh")
        refleshTimer = refleshDuration
        inRefleshCooldown = true
    end

    -- Wait for the reflesh animation to finish
    if status == 3 then
        refleshTimer = refleshTimer - Time.deltaTime
        if refleshTimer <= 0 then
            status = 4  -- New state for idle cooldown
            idleTimer = idleDuration
            
            -- Set to idle animation
            self.agent.animation.Set("Idle")
            
            -- Process any crushes that happened during growth (for stepped mode)
            if growthMode == "stepped" and #crushesInGrowth > 0 then
                -- Count how many crushes happened during growth
                local crushesProcessed = #crushesInGrowth
                
                -- Increment crush counter for each crush during growth
                crushCount = crushCount + crushesProcessed
                crushMultiplier = 1.0 + (crushCount * 0.5)  -- Multiplier increases with each crush
                
                -- Clear the crushes in growth table
                crushesInGrowth = {}
                
                -- Show a message about the crushes processed
                modeToast.Print("PROCESSED " .. crushesProcessed .. " CRUSHES: NEXT GROWTH x" .. string.format("%.1f", crushMultiplier))
            end
        end
    end
    
    -- Idle cooldown to prevent immediate reflesh loop
    if status == 4 then
        idleTimer = idleTimer - Time.deltaTime
        if idleTimer <= 0 then
            killing = true
            status = 0
            queuedAnim = nil
            inRefleshCooldown = false
            walkSet = false
        end
    end
    
    -- Handle micro hunting cooldown
    if self.huntingMicros and self.huntingCooldown > 0 then
        self.huntingCooldown = self.huntingCooldown - Time.deltaTime
        
        -- When cooldown expires, find the next micro
        if self.huntingCooldown <= 0 and not self.agent.ai.IsBehaviorActive() then
            -- Find a target micro using similar approach to growing rampage
            local nearbyMicros = self.agent.senses.GetMicrosInRadius(5)  -- Get micros in a reasonable radius
            
            -- If we found any micros
            if nearbyMicros and #nearbyMicros > 0 then
                -- Get the first micro
                local targetMicro = nearbyMicros[1]
                
                -- Use SetBehavior to start stomping
                self.agent.ai.SetBehavior("StompSingle", targetMicro)
                modeToast.Print("HUNTING NEXT MICRO")
                log("Targeting next micro for stomp")
            else
                modeToast.Print("NO MORE MICROS FOUND")
                self.huntingMicros = false
            end
        end
    end

    -- Reset to idle
    if killing and not walkSet then
        self.agent.animation.Set("Idle")
        walkSet = true
    end
end
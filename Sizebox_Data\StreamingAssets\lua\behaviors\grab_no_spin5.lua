-- Giantess Grab Micro Behavior (menu entry only for giantess, secondary)
GiantessGrabMicro = RegisterBehavior("Giantess Grab Micro")
GiantessGrabMicro.data = {
    menuEntry = "Giantess Grab Micro",
    agent = { type = { "giantess" } },
    secondary = true
}

local micro = nil
local isGrabbed = false

-- Helper: Find closest micro
local function FindClosestMicro(self)
    if self and self.agent and self.agent.findClosestMicro then
        return self.agent.findClosestMicro()
    end
    return nil
end

function GiantessGrabMicro:Start()
    micro = FindClosestMicro(self)
    isGrabbed = false
    Log("Giantess Grab Micro activated - Press H to grab, J to release")
end

function GiantessGrabMicro:Update()
    -- Refresh micro if not grabbed
    if not isGrabbed then
        micro = FindClosestMicro(self)
    end

    -- Grab on H
    if Input.GetKeyDown("h") and not isGrabbed and micro then
        -- Attach to right hand if possible
        if self.agent.bones and self.agent.bones.rightHand then
            micro.transform:SetParent(self.agent.bones.rightHand)
            micro.transform.localPosition = Vector3.New(0, 0, 0)
        else
            micro.transform:SetParent(self.agent.transform)
            micro.transform.localPosition = Vector3.New(0, 1, 0)
        end
        -- Disable physics
        if micro.rigidbody then
            micro.rigidbody.isKinematic = true
            micro.rigidbody.useGravity = false
            micro.rigidbody.detectCollisions = false
        end
        if micro.collider then
            micro.collider.enabled = false
        end
        isGrabbed = true
        Log("Micro grabbed!")
    end

    -- Release on J
    if Input.GetKeyDown("j") and isGrabbed and micro then
        -- Detach micro
        micro.transform:SetParent(nil)
        -- Enable physics
        if micro.rigidbody then
            micro.rigidbody.isKinematic = false
            micro.rigidbody.useGravity = true
            micro.rigidbody.detectCollisions = true
            micro.rigidbody:AddForce(Vector3.New(0, -50, 0), ForceMode.Impulse)
        end
        if micro.collider then
            micro.collider.enabled = true
        end
        -- Restore movement
        if micro.movement then
            micro.movement.enabled = true
            micro.movement.isGrounded = false
        end
        -- If micro is player, reset player state and unstick
        if micro.isPlayer and micro.player then
            micro.player.isGrabbed = false
            micro.player.isFlying = true
            Event.AddTimeout(0.2, function()
                pcall(function()
                    micro.player.isFlying = false
                end)
            end)
            micro.player.isCrouching = true
            Event.AddTimeout(0.4, function()
                pcall(function()
                    micro.player.isCrouching = false
                end)
            end)
        end
        -- After a short delay, force to ground if still floating
        Event.AddTimeout(0.7, function()
            if micro.transform.position.y > 1 then
                micro.transform.position = Vector3.New(
                    micro.transform.position.x,
                    0.5,
                    micro.transform.position.z
                )
            end
        end)
        isGrabbed = false
        Log("Micro released!")
    end
end

function GiantessGrabMicro:Exit()
    -- On exit, always try to unstick the micro
    if isGrabbed and micro then
        micro.transform:SetParent(nil)
        if micro.rigidbody then
            micro.rigidbody.isKinematic = false
            micro.rigidbody.useGravity = true
            micro.rigidbody.detectCollisions = true
            micro.rigidbody:AddForce(Vector3.New(0, -50, 0), ForceMode.Impulse)
        end
        if micro.collider then
            micro.collider.enabled = true
        end
        if micro.movement then
            micro.movement.enabled = true
            micro.movement.isGrounded = false
        end
        if micro.isPlayer and micro.player then
            micro.player.isGrabbed = false
            micro.player.isFlying = true
            Event.AddTimeout(0.2, function()
                pcall(function()
                    micro.player.isFlying = false
                end)
            end)
            micro.player.isCrouching = true
            Event.AddTimeout(0.4, function()
                pcall(function()
                    micro.player.isCrouching = false
                end)
            end)
        end
        Event.AddTimeout(0.7, function()
            if micro.transform.position.y > 1 then
                micro.transform.position = Vector3.New(
                    micro.transform.position.x,
                    0.5,
                    micro.transform.position.z
                )
            end
        end)
        isGrabbed = false
    end
    Log("Giantess Grab Micro behavior ended")
end
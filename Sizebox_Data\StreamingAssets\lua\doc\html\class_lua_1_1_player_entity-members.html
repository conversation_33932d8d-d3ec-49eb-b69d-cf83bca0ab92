<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.15"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(initResizable);
/* @license-end */</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(document).ready(function(){initNavTree('class_lua_1_1_player_entity.html','');});
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Lua.PlayerEntity Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="class_lua_1_1_player_entity.html">Lua.PlayerEntity</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a13c1350817e444010fcbaff8d224039e">ai</a></td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#ac47475b1b0342f1e6ce52aca2eec7f38">Aim</a>(Entity target)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#acf080bfbeeb3a6308c2fdd4cc4993e81">animation</a></td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html#a1137d463c4ca74abf6fcb032e5311755">autowalk</a></td><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html">Lua.PlayerEntity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a1873494f26c8f90c79254b43d25d47f7">baseHeight</a></td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a1bf50f8db78480e10f40a71e44480e21">BE</a>(float speed)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#ac8dfc303d378e4e805c5ba3f38f49c59">BE</a>(float speed, float time)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a8d4bdecb96c327395e5ddbde88608cf4">bones</a></td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a7dfc341caa3b11cc42ef45226689741c">CanLookAtPlayer</a></td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#ab4f0e1c31b16110cdadb7479ba008423">Chase</a>(Entity target)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html#ae050a6ac5d73748b2e989b3b3fbfd61d">climbing</a></td><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html">Lua.PlayerEntity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html#a6917cc802f4f2de29b01e7c968b6336e">climbSpeed</a></td><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html">Lua.PlayerEntity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html#a9d1469da41be5408b4d6702dc04f3351">Crush</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html">Lua.PlayerEntity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a422af2c756caecc01bad49a14ba5da7f">Delete</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#ae0c707512eed832f2211ace61d3be75d">dict</a></td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a4ec9fe7962ad6a301c33253c735aedae">DistanceTo</a>(Entity target)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#ad341a3f8e110857d06111b8b6f29d646">DistanceTo</a>(Vector3 point)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#af23f0b36c1b4c778fc7f328490223852">Engage</a>(Entity target=null)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#ac55e7536a3bdc7a6b9a3fa6a759db9ee">Equals</a>(Entity a, Entity b)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a23180e62c487b6c3923e39b3be84b291">EquipRaygun</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#aaa24b31446f5c0087faa4030f91e80ac">EquipSMG</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a442bfc9dcbb33b4fa3922a59357a8723">Face</a>(Entity target)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#ac7eac4ebcb1fe784ab5c06eed7885cf7">FindClosestGiantess</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a2e06bf904c49f705ac361e1538365e2e">FindClosestMicro</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a72e0a626a062ba116cf62cfeb77f87f8">FindRandomBuilding</a>(Entity self)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a17d117771166d30924da118c1fed9968">FireOnce</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#af1290dfaf8e3da8c2adb7279359bf036">Flee</a>(Entity target, float time)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a334e63b56e1c78e6dbebd4dd9c48a69e">Flee</a>(Vector3 position, float time)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html#a07be3c99f61960cadd5333939bc9ee88">flySpeed</a></td><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html">Lua.PlayerEntity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a42f84cbad068689f4308f5e4b1c7a981">GetFemaleMicroList</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a8abff3f32d1cbaa6355d4d217ab558e1">GetGtsModelList</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a7d49be266385d4155691a2349d964b81">GetMaleMicroList</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#aacd0f73c8377462b354deb3bc73c6b40">GetObjectList</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#abf1acc0c0ad6baa7224c8f3e088caf0f">GetPlayerModelList</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#aa9df9f39762cb24b89449e8b61aab43c">GetRandomGiantess</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#aad0651348795eb39acee39055e0b7638">GetRandomMicro</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#af3f7aae31ecb691380a6f18f053fb907">GetSelectedEntity</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a6ddff7e7a95c85ba34bee6b8b5c4ee93">Grab</a>(Entity target)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a92feb21c4219c60a7e5935733302083f">Grow</a>(float factor)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#ac574fce9abb6f90e7f7675be74838964">Grow</a>(float factor, float time)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a0007133219ff5ec24e9eecf6a9d2dd50">GrowAndWait</a>(float factor, float time)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a6b6baf8292fe2447ad0620722bc24526">height</a></td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a7776e8422e86d2ab5670ca314a65aab5">id</a></td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a42cd1e5e507a1e79eb1161799564da88">ik</a></td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html#ae805d9b6fbe0ecb483f726fee471434f">isAiming</a></td><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html">Lua.PlayerEntity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a4b4bb9870796b854c0d5666c9cba9375">IsCrushed</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a6357c0a54e9aa79beb928d079b42aa76">IsDead</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#af72e042e1fd05c66abceebb49ec2caf4">isGiantess</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a27a8d5461e9d6890e085b06e2d00f6bd">isHumanoid</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a5eaa128b6b8cf4aeb7f219edd030d61e">isMicro</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#af7b9099c16b719f42e4fdfd82661d259">isPlayer</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#ab25d357d357ab0cf7ff9a2e81aa9cb08">IsStuck</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#aa8688dacb32db168b780597d8f11622b">IsTargettable</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html#aac79f44819e1adfc1b5a2f6b1bbe61e6">jumpPower</a></td><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html">Lua.PlayerEntity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a29cdb052c5422873a708c8080039cb4b">LookAt</a>(Entity target)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html#a7ac787ce4c39f28cc36cb8b08c9934b7">maxSize</a></td><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html">Lua.PlayerEntity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html#af2e34e02994fcfdb032932e9da17e49d">minSize</a></td><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html">Lua.PlayerEntity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#acd67b39a7c95e3cb87171073c2877de1">modelName</a></td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#aa9723210eb2494461ff12c55b52e8844">morphs</a></td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a2845d63d6164b33ee49f760211fa4116">movement</a></td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a5d6cfb68967adf948db2b6c09d7dfd38">MoveTo</a>(Vector3 destination)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a307f39e2316f0c4246604ba5ce5b749e">MoveTo</a>(Entity targetEntity)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a8155b6c6ef0f0630ec7e818dd4cdaec4">name</a></td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#aa3ec8ca2693205f43b8e1e620b209018">PlayAs</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#ad8bd97d98fddc9b89f8410512b502c3f">position</a></td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html#a47bfb3f0f7b06c235a1b516967d52408">raygun</a></td><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html">Lua.PlayerEntity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a56d48f666679b251eefa10e6f65bbb60">rigidbody</a></td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html#a3b78a546178fca04a5bfd04159bebe51">runSpeed</a></td><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html">Lua.PlayerEntity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html#a853ddbac1d9d5d71d593d8e3aa9ceb0c">scale</a></td><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html">Lua.PlayerEntity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a32545d25ff6935a006bdb6b5ad45ad9a">Seek</a>(Entity target, float duration=0, float separation=-1)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a1a0205dc1d6ab6ac54679970885490f9">Seek</a>(Vector3 position, float duration=0, float separation=-1)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a9a5a67b2da9b9d95e31c766aa68760ee">senses</a></td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#af00214fc6ff19d22818d8d0810630cea">shooting</a></td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#ac2bbfd97ccf17d7cd96fb1cf3b3c51e4">ShowBreastPhysicsOptions</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html#a3406666c706b2b01b854bada1a04da5b">sizeChangeSpeed</a></td><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html">Lua.PlayerEntity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a347a27b3fea83b83461600b3b80ce5d8">SpawnFemaleMicro</a>(string name, Vector3 position, Quaternion rotation, float scale)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a582caed51f918e3d7642a7ec8a227fd1">SpawnGiantess</a>(string name, Vector3 position, Quaternion rotation, float scale)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#ad8f4dd9eec83d4df5d28efa120210ef6">SpawnMaleMicro</a>(string name, Vector3 position, Quaternion rotation, float scale)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a5bb33d1afcfac1b1452d362cb5d8bb94">SpawnObject</a>(string name, Vector3 position, Quaternion rotation, float scale)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html#acd890123e850bbd3c1e4e459b65ea5c1">sprintSpeed</a></td><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html">Lua.PlayerEntity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#af93c20b6cca387d60a5368c079ae65ef">StandUp</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a3a5c197d31f8834f8ed67f71c6157719">StartFiring</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a0a8058c8b504215e492471b4e7d557d4">Stomp</a>(Entity target)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#ad6c8d1dc736aeea69e734d3c1e884343">Stomp</a>(Vector3 target)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#aadc969ba1387cf03d80b8432705f0750">StopAiming</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a9ead5c7d5e821fa285ab065b9cc3185f">StopEngaging</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a4d7809fc03b618624a6d7640674fe646">StopFiring</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html#aee7c81573cc1de31d9f64fd13b534ae2">superFlySpeed</a></td><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html">Lua.PlayerEntity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a51edf8c42bd2acb730ae73d045341320">transform</a></td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a1a4cc3d2425ef3527ab0692f4c2a9ca3">UnequipGun</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a6be24874965a4015e7fd8244fa345220">UpdateMeshCollider</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a58a5bb200f7182dea5e622f036f05bf3">Wait</a>(float time)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html#aabbfb4d83476aaa301ee4f526dd6ecf8">walkSpeed</a></td><td class="entry"><a class="el" href="class_lua_1_1_player_entity.html">Lua.PlayerEntity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#aa4d1d044edd1f9b9dfb4b310c49b8761">Wander</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a9858a940de17a0405da24bff1d834970">Wander</a>(float time)</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_lua_1_1_entity.html#a5690c11c287139f6d5b5171f5d4189e9">Wreck</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.15 </li>
  </ul>
</div>
</body>
</html>

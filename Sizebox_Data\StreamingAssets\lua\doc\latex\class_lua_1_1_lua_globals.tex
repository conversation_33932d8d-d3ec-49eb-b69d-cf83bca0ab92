\hypertarget{class_lua_1_1_lua_globals}{}\section{Lua.\+Lua\+Globals Class Reference}
\label{class_lua_1_1_lua_globals}\index{Lua.\+Lua\+Globals@{Lua.\+Lua\+Globals}}


Global dictionary. It can be used to store and exchange arbitrary data between scripts.  


\subsection*{Properties}
\begin{DoxyCompactItemize}
\item 
Dyn\+Value \hyperlink{class_lua_1_1_lua_globals_a53e3a86dd86bec2b7a89c01f5e7b8321}{this\mbox{[}\+Dyn\+Value idx\mbox{]}}\hspace{0.3cm}{\ttfamily  \mbox{[}get, set\mbox{]}}
\end{DoxyCompactItemize}


\subsection{Detailed Description}
Global dictionary. It can be used to store and exchange arbitrary data between scripts. 

Accessible through the {\ttfamily global} global variable. 

\subsection{Property Documentation}
\mbox{\Hypertarget{class_lua_1_1_lua_globals_a53e3a86dd86bec2b7a89c01f5e7b8321}\label{class_lua_1_1_lua_globals_a53e3a86dd86bec2b7a89c01f5e7b8321}} 
\index{Lua\+::\+Lua\+Globals@{Lua\+::\+Lua\+Globals}!this\mbox{[}\+Dyn\+Value idx\mbox{]}@{this[Dyn\+Value idx]}}
\index{this\mbox{[}\+Dyn\+Value idx\mbox{]}@{this[Dyn\+Value idx]}!Lua\+::\+Lua\+Globals@{Lua\+::\+Lua\+Globals}}
\subsubsection{\texorpdfstring{this[Dyn\+Value idx]}{this[DynValue idx]}}
{\footnotesize\ttfamily Dyn\+Value Lua.\+Lua\+Globals.\+this\mbox{[}Dyn\+Value idx\mbox{]}\hspace{0.3cm}{\ttfamily [get]}, {\ttfamily [set]}}






\begin{DoxyParams}{Parameters}
{\em idx} & \\
\hline
\end{DoxyParams}
\begin{DoxyReturn}{Returns}

\end{DoxyReturn}


The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
Lua\+Globals.\+cs\end{DoxyCompactItemize}

Rampage = RegisterBehavior("Sit_on_building")
Rampage.scores = {   --[[ the scores are set this way, for each personality or state.. is a value from 0 to 100 ]]
    hostile = 50     --[[ the higher the value the more likely to choose that action ]]
}
Rampage.data = {
    agent = {
        type = { "giantess" }
    },
    target = {
        type = { "oneself" }
    }
}
    
IDLE_ANIMATION = "Sit 4"

function Rampage:Start()
    self.stop = false -- i added a stop variable to end the behavior.. this is custom for this script
end

function Rampage:Update()
    if not self.agent.ai.IsActionActive() then

        if self.stop then
            self.agent.ai.StopBehavior() -- if you use Update() you must manually tell when to end the behavior, after this the Exit() method will run
            return
        else

        self.target = self.agent.FindRandomBuilding(self.agent) --  check if there is any building nearby
        log("got target data")
        
        if not self.target then
            self.stop = true    --  there are no more buildings near the gts, stop the script
            log("No target was found, Rampage end")
            return
        end

        log("target found, setting out to destroy")
        self.agent.animation.Set("Walk", true)        
        self.agent.MoveTo(self.target)   --  move to the target, buildings are static so use the cheapest call possible
        self.agent.animation.Set(IDLE_ANIMATION, true)
        self.stop = true;
        end                                          -- it will stop in the next loop so i can make sure that runs at least once
    end
end

function Rampage:Exit()
    self.agent.animation.Set(IDLE_ANIMATION, true)
end

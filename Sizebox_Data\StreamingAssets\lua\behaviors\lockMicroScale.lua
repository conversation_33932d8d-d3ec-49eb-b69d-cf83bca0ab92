lockMicroScaleToggle = RegisterBehavior("Lock Selected Scale Toggle")
lockMicroScaleToggle.data = {
	menuEntry = "Size/Lock Micro Scale/Toggle",
	secondary = true,
	flags = {"lockScale"},
	agent = {
		type = { "humanoid" }
	},
	target = {
		type = { "oneself" }
	}
}

function lockMicroScaleToggle:Start()
	local toggle
	self.logToast = Game.Toast.New()
	if not globals["lockMicroScale"] then globals["lockMicroScale"] = {} end
	if self.agent.dict.lockScale == nil then
		toggle = true
		self.agent.dict.lockScale = false
		globals["lockMicroScale"][self.agent.id] = true
	end
	self.agent.dict.lockScale = not self.agent.dict.lockScale
	if toggle then 
		self.logToast.print("[ Lock Scale Toggle Enabled ]             Now LOCKING Selected Micros")
	else
		toggle = self.agent.dict.lockScale and "LOCKING" or "UNLOCKING"
		self.logToast.print("Now "..toggle.." Selected Micros")
	end
	self.target = self.agent
end

function lockMicroScaleToggle:Update()
	if self.target ~= self.agent.GetSelectedEntity() then
		if not self.target or self.target == nil then self.target = nil return end
		self.target = self.agent.GetSelectedEntity()
		if self.target ~= self.agent and not self.target.isGiantess() then
			globals["lockMicroScale"][self.target.id] = self.agent.dict.lockScale
			self.target.ai.SetBehavior("lockTargetScale")
		end
	end
end



lockTargetScale = RegisterBehavior("lockTargetScale")
lockTargetScale.data = {
	hideMenu = true,
	secondary = true,
	flags = {"lockScale"},
	agent = {
		type = { "micro" }
	},
	target = {
		type = { "none" }
	},
}

function lockTargetScale:Start()
	if not globals["lockMicroScale"][self.agent.id] then self.agent.ai.StopSecondaryBehavior("lockScale") return end
	--log("Locked scale for "..tostring(self.agent.name))
	self.storeHeight = self.agent.height
end

function lockTargetScale:Update()
	if self.storeHeight ~= self.agent.height then
		self.agent.height = self.storeHeight
	end
end

function lockTargetScale:Exit()
	
end



lockMicroScaleStop = RegisterBehavior("Lock Selected Scale (x)")
lockMicroScaleStop.data = {
	menuEntry = "Size/Lock Micro Scale/(x)",
	secondary = true,
	flags = {"lockScale"},
	agent = {
		type = { "humanoid" }
	},
	target = {
		type = { "oneself" }
	}
}

function lockMicroScaleStop:Start()
	self.logToast = Game.Toast.New()
	if globals["lockMicroScale"][self.agent.id] then 
		self.logToast.print("[ Lock Scale Toggle Disabled ]             Selected Micros now UNAFFECTED")
		self.agent.dict.lockScale = nil
		self.agent.ai.StopSecondaryBehavior("lockScale")
		globals["lockMicroScale"][self.agent.id] = nil
	else
		self.logToast.print("(Lock Scale Toggle was not Active)")
	end
end
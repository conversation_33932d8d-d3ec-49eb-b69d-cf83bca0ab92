-- v1.0

GOStomp = RegisterBehavior("grow_on_stomp")
GOStomp.data =  {
    menuEntry = "Size/Grow On Stomp",
    secondary = true,
    flags = { "gostmp" },
    agent = {
        type = { "humanoid" }
    },
    target = {
        type = { "oneself" }
    },
	settings = {
		{ "baseSpeed", "Grow Speed", "string", "0.2" },
		{ "duration", "Shortest Duration", "string", "0.6"},
		{ "smoothifier", "Easing Duration", "string", "0.3"},
		{ "randomizedTimings", "Variable Timings", "bool", true},
		{ "decelQuicken", "Deceleration Boost", "string", "4"},
		{ "linear", "Linear Growth", "bool", false},
		{ "linearMult", "Linear Boost", "string", "5"},
		{ "soundFile", "Use Sound Clip (filename):", "string", "none"},
		{ "loopClip", "Loop Sound", "bool", true},
		{ "playGasps", "Play Voice", "bool", true},
		{ "playSpurtAnim", "Play Spurt Animation", "bool", false},
		{ "spurtAnim", "Spurt Animation", "string", "Defeat"},
		{ "randomIdleAnim", "Random Idle Animation", "bool", false}
	}
}

gaspList = {"GaspPain103_Soft_Fast.ogg", "GaspPain104_Soft_Slow.ogg", "GaspPain106_QQ.ogg", "GaspPain111_Soft_Slow.ogg", "GaspMoan001_Mmm.ogg", "GaspMoan003_Ahh_MidLong.ogg",
			"GaspBreathing103_QQ.ogg", "GaspBreathing105_QQ.ogg", "GaspBreathing106_QQ.ogg", "GaspBreathing103_Long_PleasureP.ogg", "GaspBreathing201_Soft_Pleasure.ogg",
			"GaspBreathing202_Soft_Pleasure.ogg", "GaspBreathing205_QQ.ogg"}

function GOStomp:Listener(data)
	--self.stompData.Print("Step Strength: "..tostring(data.magnitude))
	if data.gts.id == self.agent.id and data.magnitude == 3 then
		globals["spurtSettings"] = {
			baseSpeed = self.baseSpeed,
			duration = self.duration,
			smoothifier = self.smoothifier,
			randomizedTimings = self.randomizedTimings,
			decelQuicken = self.decelQuicken,
			linear = self.linear,
			linearMult = self.linearMult,
			soundFile = self.soundFile,
			loopClip = self.loopClip,
			playSpurtAnim = self.playSpurtAnim,
			spurtAnim = self.spurtAnim,
			randomIdleAnim = self.randomIdleAnim
		}
		if self.agent.isGiantess() then
			if self.playGasps then PlaySFX(self) end
			self.agent.ai.SetBehavior("Grow (Single Spurt)")
		else
			self.agent.ai.SetBehavior("Grow micro (S. Spurt)")
		end
	end
end

function GOStomp:Start()
	self.agent.dict.OnStep = Event.Register(self, EventCode.OnStep, self.Listener)
	self.stompData = Game.Toast.New()
	self.baseSpeed = math.abs(tonumber(self.baseSpeed))
	if self.playGasps then
		InitSFX(self)
	end
end

function InitSFX(self)
	self.mVol, self.cVol, self.tVol = 0,0,0
	self.agent.dict.stompGaspAudio = AudioSource:new(self.agent.bones.head)
	InitAudioClips(self)
	self.agent.dict.stompGaspAudio.loop = false
	self.agent.dict.stompGaspAudio.spatialBlend = 1
	self.agent.dict.stompGaspAudio.volume = 0
	self.playerScale = Game.GetLocalPlayer() and Game.GetLocalPlayer().scale or self.agent.scale
	UpdateSFXVolByScale(self)
end

function InitAudioClips(self)
	for i=1,#gaspList do
		self.agent.dict.stompGaspAudio.clip = gaspList[i]
		self.agent.dict.stompGaspAudio:Play()
	end
	self.agent.dict.stompGaspAudio:Stop()
end

function PlaySFX(self)
	-- Choose random clip to play
	local gaspAudio = gaspList[math.random(#gaspList)]
	print(gaspAudio)
	self.agent.dict.stompGaspAudio.clip = gaspAudio

	-- Adjust max volume with scale
	UpdateSFXVolByScale(self)

	-- Apply calculated volume to clip
	self.agent.dict.stompGaspAudio.volume = self.vol * 1.4

	-- Start the audio clip
	self.agent.dict.stompGaspAudio:Play()
end

function UpdateSFXVolByScale(self)
	self.mPitch =
		(self.agent.scale * 0.2) / (0.04 / self.agent.scale) * self.agent.scale
		/ (self.agent.scale * self.agent.scale * math.sqrt(self.agent.scale))
		/ (
			  math.sqrt(math.sqrt(math.sqrt(math.sqrt(self.agent.scale))))
			* math.sqrt(math.sqrt(math.sqrt(self.agent.scale)))
		)
	self.agent.dict.stompGaspAudio.pitch = 0.65 + (
		self.agent.scale * (1.05 / (self.agent.scale * 5 / self.mPitch))
		* (1 / math.sqrt((self.agent.scale * 1000 * 0.008) + 1))
	)
	self.vol =
		(1.7 - self.agent.dict.stompGaspAudio.pitch)
		* (Mathf.Clamp(self.baseSpeed , 0, 1.5) / 1.875 + 0.2)
	self.agent.dict.stompGaspAudio.minDistance =
		(1000 * self.agent.scale / self.playerScale / (300 + self.agent.scale / self.playerScale)) * 0.5 * 0.0025
end
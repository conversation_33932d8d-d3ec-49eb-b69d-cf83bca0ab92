<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.Input Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_input.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="class_lua_1_1_input-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.Input Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Interface into the <a class="el" href="class_lua_1_1_input.html" title="Interface into the Input system.">Input</a> system.  
 <a href="class_lua_1_1_input.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-static-methods" name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:a616e22e4f3b9c973c9763c10dc495395"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_input.html#a616e22e4f3b9c973c9763c10dc495395">GetAxis</a> (string axisName)</td></tr>
<tr class="memdesc:a616e22e4f3b9c973c9763c10dc495395"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the value of the virtual axis identified by axisName.  <a href="class_lua_1_1_input.html#a616e22e4f3b9c973c9763c10dc495395">More...</a><br /></td></tr>
<tr class="separator:a616e22e4f3b9c973c9763c10dc495395"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa25e7d2e0c828c4661e8a77db269e5b3"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_input.html#aa25e7d2e0c828c4661e8a77db269e5b3">GetAxisRaw</a> (string axisName)</td></tr>
<tr class="memdesc:aa25e7d2e0c828c4661e8a77db269e5b3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the value of the virtual axis identified by axisName with no smoothing filtering applied.  <a href="class_lua_1_1_input.html#aa25e7d2e0c828c4661e8a77db269e5b3">More...</a><br /></td></tr>
<tr class="separator:aa25e7d2e0c828c4661e8a77db269e5b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac45bfbc1aaa71822f9dd32ee446e3e26"><td class="memItemLeft" align="right" valign="top">static bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_input.html#ac45bfbc1aaa71822f9dd32ee446e3e26">GetButton</a> (string buttonName)</td></tr>
<tr class="memdesc:ac45bfbc1aaa71822f9dd32ee446e3e26"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true while the virtual button identified by buttonName is held down.  <a href="class_lua_1_1_input.html#ac45bfbc1aaa71822f9dd32ee446e3e26">More...</a><br /></td></tr>
<tr class="separator:ac45bfbc1aaa71822f9dd32ee446e3e26"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a59b2338d29a39f0694aebef890598b7c"><td class="memItemLeft" align="right" valign="top">static bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_input.html#a59b2338d29a39f0694aebef890598b7c">GetButtonDown</a> (string buttonName)</td></tr>
<tr class="memdesc:a59b2338d29a39f0694aebef890598b7c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true during the frame the user pressed down the virtual button identified by buttonName.  <a href="class_lua_1_1_input.html#a59b2338d29a39f0694aebef890598b7c">More...</a><br /></td></tr>
<tr class="separator:a59b2338d29a39f0694aebef890598b7c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a74cdd1903a2b531d575a20ea9cbb0ec0"><td class="memItemLeft" align="right" valign="top">static bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_input.html#a74cdd1903a2b531d575a20ea9cbb0ec0">GetButtonUp</a> (string buttonName)</td></tr>
<tr class="memdesc:a74cdd1903a2b531d575a20ea9cbb0ec0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true the first frame the user releases the virtual button identified by buttonName.  <a href="class_lua_1_1_input.html#a74cdd1903a2b531d575a20ea9cbb0ec0">More...</a><br /></td></tr>
<tr class="separator:a74cdd1903a2b531d575a20ea9cbb0ec0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abf392b3cf9d208f67a6ea02c0288206a"><td class="memItemLeft" align="right" valign="top">static bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_input.html#abf392b3cf9d208f67a6ea02c0288206a">GetKey</a> (string name)</td></tr>
<tr class="memdesc:abf392b3cf9d208f67a6ea02c0288206a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true while the user holds down the key identified by name. Think auto fire.  <a href="class_lua_1_1_input.html#abf392b3cf9d208f67a6ea02c0288206a">More...</a><br /></td></tr>
<tr class="separator:abf392b3cf9d208f67a6ea02c0288206a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8701b16492ad5e1ec79c019d74f7b051"><td class="memItemLeft" align="right" valign="top">static bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_input.html#a8701b16492ad5e1ec79c019d74f7b051">GetKeyDown</a> (string name)</td></tr>
<tr class="memdesc:a8701b16492ad5e1ec79c019d74f7b051"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true during the frame the user starts pressing down the key identified by name.  <a href="class_lua_1_1_input.html#a8701b16492ad5e1ec79c019d74f7b051">More...</a><br /></td></tr>
<tr class="separator:a8701b16492ad5e1ec79c019d74f7b051"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7c9f4df5dcb4bc4d194da55be31fb0ea"><td class="memItemLeft" align="right" valign="top">static bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_input.html#a7c9f4df5dcb4bc4d194da55be31fb0ea">GetKeyUp</a> (string name)</td></tr>
<tr class="memdesc:a7c9f4df5dcb4bc4d194da55be31fb0ea"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true during the frame the user releases the key identified by name.  <a href="class_lua_1_1_input.html#a7c9f4df5dcb4bc4d194da55be31fb0ea">More...</a><br /></td></tr>
<tr class="separator:a7c9f4df5dcb4bc4d194da55be31fb0ea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a40addbec8d9b18f0dc0cb101edc38b8d"><td class="memItemLeft" align="right" valign="top">static bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_input.html#a40addbec8d9b18f0dc0cb101edc38b8d">GetMouseButton</a> (int button)</td></tr>
<tr class="memdesc:a40addbec8d9b18f0dc0cb101edc38b8d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether the given mouse button is held down.  <a href="class_lua_1_1_input.html#a40addbec8d9b18f0dc0cb101edc38b8d">More...</a><br /></td></tr>
<tr class="separator:a40addbec8d9b18f0dc0cb101edc38b8d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1a6498f2fab91a72642ac064359edef7"><td class="memItemLeft" align="right" valign="top">static bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_input.html#a1a6498f2fab91a72642ac064359edef7">GetMouseButtonDown</a> (int button)</td></tr>
<tr class="memdesc:a1a6498f2fab91a72642ac064359edef7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true during the frame the user pressed the given mouse button.  <a href="class_lua_1_1_input.html#a1a6498f2fab91a72642ac064359edef7">More...</a><br /></td></tr>
<tr class="separator:a1a6498f2fab91a72642ac064359edef7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac9d47356c504c74bfcbee5fff1e9939b"><td class="memItemLeft" align="right" valign="top">static bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_input.html#ac9d47356c504c74bfcbee5fff1e9939b">GetMouseButtonUp</a> (int button)</td></tr>
<tr class="memdesc:ac9d47356c504c74bfcbee5fff1e9939b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true during the frame the user releases the given mouse button.  <a href="class_lua_1_1_input.html#ac9d47356c504c74bfcbee5fff1e9939b">More...</a><br /></td></tr>
<tr class="separator:ac9d47356c504c74bfcbee5fff1e9939b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="properties" name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a624c63ec2127ae1a70da0e8ac2a5742d"><td class="memItemLeft" align="right" valign="top">static bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_input.html#a624c63ec2127ae1a70da0e8ac2a5742d">anyKey</a><code> [get]</code></td></tr>
<tr class="memdesc:a624c63ec2127ae1a70da0e8ac2a5742d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Is any key or mouse button currently held down? (Read Only)  <a href="class_lua_1_1_input.html#a624c63ec2127ae1a70da0e8ac2a5742d">More...</a><br /></td></tr>
<tr class="separator:a624c63ec2127ae1a70da0e8ac2a5742d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2587110e95d4dff8d00d112fcade9631"><td class="memItemLeft" align="right" valign="top">static bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_input.html#a2587110e95d4dff8d00d112fcade9631">anyKeyDown</a><code> [get]</code></td></tr>
<tr class="memdesc:a2587110e95d4dff8d00d112fcade9631"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true the first frame the user hits any key or mouse button. (Read Only)  <a href="class_lua_1_1_input.html#a2587110e95d4dff8d00d112fcade9631">More...</a><br /></td></tr>
<tr class="separator:a2587110e95d4dff8d00d112fcade9631"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab5d4bcb7c637ec0760fc8ca8033ecec7"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_input.html#ab5d4bcb7c637ec0760fc8ca8033ecec7">mousePosition</a><code> [get]</code></td></tr>
<tr class="memdesc:ab5d4bcb7c637ec0760fc8ca8033ecec7"><td class="mdescLeft">&#160;</td><td class="mdescRight">The current mouse position in pixel coordinates. (Read Only)  <a href="class_lua_1_1_input.html#ab5d4bcb7c637ec0760fc8ca8033ecec7">More...</a><br /></td></tr>
<tr class="separator:ab5d4bcb7c637ec0760fc8ca8033ecec7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac508b474e85e336be67f2ad7ef94f751"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_input.html#ac508b474e85e336be67f2ad7ef94f751">mouseScrollDelta</a><code> [get]</code></td></tr>
<tr class="memdesc:ac508b474e85e336be67f2ad7ef94f751"><td class="mdescLeft">&#160;</td><td class="mdescRight">The current mouse scroll delta. (Read Only)  <a href="class_lua_1_1_input.html#ac508b474e85e336be67f2ad7ef94f751">More...</a><br /></td></tr>
<tr class="separator:ac508b474e85e336be67f2ad7ef94f751"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >Interface into the <a class="el" href="class_lua_1_1_input.html" title="Interface into the Input system.">Input</a> system. </p>
<p >To read an axis use <a class="el" href="class_lua_1_1_input.html#a616e22e4f3b9c973c9763c10dc495395" title="Returns the value of the virtual axis identified by axisName.">Input.GetAxis</a> with one of the following default axes: "Horizontal" and "Vertical" are mapped to joystick, A, W, S, D and the arrow keys. "Mouse X" and "Mouse Y" are mapped to the mouse delta. "Fire1", "Fire2" "Fire3" are mapped to Ctrl, Alt, Cmd keys and three mouse or joystick buttons. </p>
<p >If you are using input for any kind of movement behaviour use <a class="el" href="class_lua_1_1_input.html#a616e22e4f3b9c973c9763c10dc495395" title="Returns the value of the virtual axis identified by axisName.">Input.GetAxis</a>. It gives you smoothed and configurable input that can be mapped to keyboard, joystick or mouse. Use <a class="el" href="class_lua_1_1_input.html#ac45bfbc1aaa71822f9dd32ee446e3e26" title="Returns true while the virtual button identified by buttonName is held down.">Input.GetButton</a> for action like events only. Don't use it for movement, <a class="el" href="class_lua_1_1_input.html#a616e22e4f3b9c973c9763c10dc495395" title="Returns the value of the virtual axis identified by axisName.">Input.GetAxis</a> will make the script code smaller and simpler. </p>
<p >Note also that the <a class="el" href="class_lua_1_1_input.html" title="Interface into the Input system.">Input</a> flags are not reset until "Update()", so its suggested you make all the <a class="el" href="class_lua_1_1_input.html" title="Interface into the Input system.">Input</a> Calls in the Update Loop. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a616e22e4f3b9c973c9763c10dc495395" name="a616e22e4f3b9c973c9763c10dc495395"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a616e22e4f3b9c973c9763c10dc495395">&#9670;&nbsp;</a></span>GetAxis()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Input.GetAxis </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>axisName</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns the value of the virtual axis identified by axisName. </p>
<p >The value will be in the range -1...1 for keyboard and joystick input. If the axis is setup to be delta mouse movement, the mouse delta is multiplied by the axis sensitivity and the range is not -1...1. This is frame-rate independent; you do not need to be concerned about varying frame-rates when using this value. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">axisName</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="aa25e7d2e0c828c4661e8a77db269e5b3" name="aa25e7d2e0c828c4661e8a77db269e5b3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa25e7d2e0c828c4661e8a77db269e5b3">&#9670;&nbsp;</a></span>GetAxisRaw()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Input.GetAxisRaw </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>axisName</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns the value of the virtual axis identified by axisName with no smoothing filtering applied. </p>
<p >The value will be in the range -1...1 for keyboard and joystick input. Since input is not smoothed, keyboard input will always be either -1, 0 or 1. This is useful if you want to do all smoothing of keyboard input processing yourself. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">axisName</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ac45bfbc1aaa71822f9dd32ee446e3e26" name="ac45bfbc1aaa71822f9dd32ee446e3e26"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac45bfbc1aaa71822f9dd32ee446e3e26">&#9670;&nbsp;</a></span>GetButton()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static bool Lua.Input.GetButton </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>buttonName</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns true while the virtual button identified by buttonName is held down. </p>
<p >Think auto fire - this will return true as long as the button is held down. Use this only when implementing events that trigger an action, eg, shooting a weapon. Use GetAxis for input that controls continuous movement. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">buttonName</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a59b2338d29a39f0694aebef890598b7c" name="a59b2338d29a39f0694aebef890598b7c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a59b2338d29a39f0694aebef890598b7c">&#9670;&nbsp;</a></span>GetButtonDown()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static bool Lua.Input.GetButtonDown </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>buttonName</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns true during the frame the user pressed down the virtual button identified by buttonName. </p>
<p >You need to call this function from the Update function, since the state gets reset each frame. It will not return true until the user has released the key and pressed it again. Use this only when implementing action like events IE: shooting a weapon. Use <a class="el" href="class_lua_1_1_input.html#a616e22e4f3b9c973c9763c10dc495395" title="Returns the value of the virtual axis identified by axisName.">Input.GetAxis</a> for any kind of movement behaviour. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">buttonName</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a74cdd1903a2b531d575a20ea9cbb0ec0" name="a74cdd1903a2b531d575a20ea9cbb0ec0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a74cdd1903a2b531d575a20ea9cbb0ec0">&#9670;&nbsp;</a></span>GetButtonUp()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static bool Lua.Input.GetButtonUp </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>buttonName</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns true the first frame the user releases the virtual button identified by buttonName. </p>
<p >You need to call this function from the Update function, since the state gets reset each frame. It will not return true until the user has pressed the button and released it again. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">buttonName</td><td></td></tr>
  </table>
  </dd>
</dl>
<p>Use this only when implementing action like events IE: shooting a weapon. Use <a class="el" href="class_lua_1_1_input.html#a616e22e4f3b9c973c9763c10dc495395" title="Returns the value of the virtual axis identified by axisName.">Input.GetAxis</a> for any kind of movement behaviour. </p><dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="abf392b3cf9d208f67a6ea02c0288206a" name="abf392b3cf9d208f67a6ea02c0288206a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abf392b3cf9d208f67a6ea02c0288206a">&#9670;&nbsp;</a></span>GetKey()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static bool Lua.Input.GetKey </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>name</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns true while the user holds down the key identified by name. Think auto fire. </p>
<p >When dealing with input it is recommended to use <a class="el" href="class_lua_1_1_input.html#a616e22e4f3b9c973c9763c10dc495395" title="Returns the value of the virtual axis identified by axisName.">Input.GetAxis</a> and <a class="el" href="class_lua_1_1_input.html#ac45bfbc1aaa71822f9dd32ee446e3e26" title="Returns true while the virtual button identified by buttonName is held down.">Input.GetButton</a> instead since it allows end-users to configure the keys. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">name</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a8701b16492ad5e1ec79c019d74f7b051" name="a8701b16492ad5e1ec79c019d74f7b051"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8701b16492ad5e1ec79c019d74f7b051">&#9670;&nbsp;</a></span>GetKeyDown()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static bool Lua.Input.GetKeyDown </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>name</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns true during the frame the user starts pressing down the key identified by name. </p>
<p >You need to call this function from the Update function, since the state gets reset each frame. It will not return true until the user has pressed the key and released it again. When dealing with input it is recommended to use <a class="el" href="class_lua_1_1_input.html#a616e22e4f3b9c973c9763c10dc495395" title="Returns the value of the virtual axis identified by axisName.">Input.GetAxis</a> and <a class="el" href="class_lua_1_1_input.html#ac45bfbc1aaa71822f9dd32ee446e3e26" title="Returns true while the virtual button identified by buttonName is held down.">Input.GetButton</a> instead since it allows end-users to configure the keys. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">name</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a7c9f4df5dcb4bc4d194da55be31fb0ea" name="a7c9f4df5dcb4bc4d194da55be31fb0ea"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7c9f4df5dcb4bc4d194da55be31fb0ea">&#9670;&nbsp;</a></span>GetKeyUp()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static bool Lua.Input.GetKeyUp </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>name</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns true during the frame the user releases the key identified by name. </p>
<p >You need to call this function from the Update function, since the state gets reset each frame. It will not return true until the user has pressed the key and released it again. When dealing with input it is recommended to use <a class="el" href="class_lua_1_1_input.html#a616e22e4f3b9c973c9763c10dc495395" title="Returns the value of the virtual axis identified by axisName.">Input.GetAxis</a> and <a class="el" href="class_lua_1_1_input.html#ac45bfbc1aaa71822f9dd32ee446e3e26" title="Returns true while the virtual button identified by buttonName is held down.">Input.GetButton</a> instead since it allows end-users to configure the keys. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">name</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a40addbec8d9b18f0dc0cb101edc38b8d" name="a40addbec8d9b18f0dc0cb101edc38b8d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a40addbec8d9b18f0dc0cb101edc38b8d">&#9670;&nbsp;</a></span>GetMouseButton()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static bool Lua.Input.GetMouseButton </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>button</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns whether the given mouse button is held down. </p>
<p >button values are 0 for left button, 1 for right button, 2 for the middle button. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">button</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a1a6498f2fab91a72642ac064359edef7" name="a1a6498f2fab91a72642ac064359edef7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1a6498f2fab91a72642ac064359edef7">&#9670;&nbsp;</a></span>GetMouseButtonDown()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static bool Lua.Input.GetMouseButtonDown </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>button</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns true during the frame the user pressed the given mouse button. </p>
<p >You need to call this function from the Update function, since the state gets reset each frame. It will not return true until the user has released the mouse button and pressed it again. button values are 0 for left button, 1 for right button, 2 for the middle button. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">button</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ac9d47356c504c74bfcbee5fff1e9939b" name="ac9d47356c504c74bfcbee5fff1e9939b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac9d47356c504c74bfcbee5fff1e9939b">&#9670;&nbsp;</a></span>GetMouseButtonUp()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static bool Lua.Input.GetMouseButtonUp </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>button</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns true during the frame the user releases the given mouse button. </p>
<p >You need to call this function from the Update function, since the state gets reset each frame. It will not return true until the user has released the mouse button and pressed it again. button values are 0 for left button, 1 for right button, 2 for the middle button. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">button</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="a624c63ec2127ae1a70da0e8ac2a5742d" name="a624c63ec2127ae1a70da0e8ac2a5742d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a624c63ec2127ae1a70da0e8ac2a5742d">&#9670;&nbsp;</a></span>anyKey</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.Input.anyKey</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Is any key or mouse button currently held down? (Read Only) </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a2587110e95d4dff8d00d112fcade9631" name="a2587110e95d4dff8d00d112fcade9631"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2587110e95d4dff8d00d112fcade9631">&#9670;&nbsp;</a></span>anyKeyDown</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.Input.anyKeyDown</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns true the first frame the user hits any key or mouse button. (Read Only) </p>
<p >You should be polling this variable from the Update function, since the state gets reset each frame. It will not return true until the user has released all keys / buttons and pressed any key / buttons again. </p><dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ab5d4bcb7c637ec0760fc8ca8033ecec7" name="ab5d4bcb7c637ec0760fc8ca8033ecec7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab5d4bcb7c637ec0760fc8ca8033ecec7">&#9670;&nbsp;</a></span>mousePosition</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Input.mousePosition</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The current mouse position in pixel coordinates. (Read Only) </p>
<p >The bottom-left of the screen or window is at (0, 0). The top-right of the screen or window is at (<a class="el" href="class_lua_1_1_screen.html#ae44386bf8759e8f85e04358297f3dd95" title="The current width of the screen window in pixels (Read Only).">Screen.width</a>, <a class="el" href="class_lua_1_1_screen.html#a7e3459d0ccc2641709d1bad599092fdc" title="The current height of the screen window in pixels (Read Only).">Screen.height</a>). </p><dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ac508b474e85e336be67f2ad7ef94f751" name="ac508b474e85e336be67f2ad7ef94f751"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac508b474e85e336be67f2ad7ef94f751">&#9670;&nbsp;</a></span>mouseScrollDelta</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Input.mouseScrollDelta</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The current mouse scroll delta. (Read Only) </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaInput.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_input.html">Input</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.Game.Version Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_game_1_1_version.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="class_lua_1_1_game_1_1_version-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.Game.Version Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>The version of Sizebox the script is being run under. Your script can use the Major and Minor numbers to take different paths for different versions of Sizebox allowing custom scripts to be more portable  
 <a href="class_lua_1_1_game_1_1_version.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-static-methods" name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:af27e4dcdcc666de5c8e5f90e747d3fb6"><td class="memItemLeft" align="right" valign="top">static bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_game_1_1_version.html#af27e4dcdcc666de5c8e5f90e747d3fb6">Require</a> (int major, int minor)</td></tr>
<tr class="memdesc:af27e4dcdcc666de5c8e5f90e747d3fb6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Check that the Sizebox is this version or later  <a href="class_lua_1_1_game_1_1_version.html#af27e4dcdcc666de5c8e5f90e747d3fb6">More...</a><br /></td></tr>
<tr class="separator:af27e4dcdcc666de5c8e5f90e747d3fb6"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="properties" name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a8c329c68bcf96acc94c59705eace0791"><td class="memItemLeft" align="right" valign="top"><a id="a8c329c68bcf96acc94c59705eace0791" name="a8c329c68bcf96acc94c59705eace0791"></a>
static int&#160;</td><td class="memItemRight" valign="bottom"><b>Major</b><code> [get]</code></td></tr>
<tr class="memdesc:a8c329c68bcf96acc94c59705eace0791"><td class="mdescLeft">&#160;</td><td class="mdescRight">The major version of the currently running Sizebox <br /></td></tr>
<tr class="separator:a8c329c68bcf96acc94c59705eace0791"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aec3cfaebb94290f3405dbeeaee9277d6"><td class="memItemLeft" align="right" valign="top"><a id="aec3cfaebb94290f3405dbeeaee9277d6" name="aec3cfaebb94290f3405dbeeaee9277d6"></a>
static int&#160;</td><td class="memItemRight" valign="bottom"><b>Minor</b><code> [get]</code></td></tr>
<tr class="memdesc:aec3cfaebb94290f3405dbeeaee9277d6"><td class="mdescLeft">&#160;</td><td class="mdescRight">The minor version of the currently running Sizebox <br /></td></tr>
<tr class="separator:aec3cfaebb94290f3405dbeeaee9277d6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae0129c5ed39c108d4d6f9a5935c34517"><td class="memItemLeft" align="right" valign="top"><a id="ae0129c5ed39c108d4d6f9a5935c34517" name="ae0129c5ed39c108d4d6f9a5935c34517"></a>
static string&#160;</td><td class="memItemRight" valign="bottom"><b>Text</b><code> [get]</code></td></tr>
<tr class="memdesc:ae0129c5ed39c108d4d6f9a5935c34517"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="class_lua_1_1_game_1_1_version.html" title="The version of Sizebox the script is being run under. Your script can use the Major and Minor numbers...">Version</a> string as seen in the main menu <br /></td></tr>
<tr class="separator:ae0129c5ed39c108d4d6f9a5935c34517"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >The version of Sizebox the script is being run under. Your script can use the Major and Minor numbers to take different paths for different versions of Sizebox allowing custom scripts to be more portable </p>
<p >Older versions of Sizebox will not have this class and attempting to call this class will cause the script to fail. If you expect your scripts to be run on older versions of Sizebox you can guard against exception by checking if '<a class="el" href="class_lua_1_1_game.html" title="A collection of Sizebox specific functions that don&#39;t belong to any object.">Game</a>' is nil with the following snippet </p><div class="fragment"><div class="line"><span class="keywordflow">if</span> <a class="code hl_class" href="class_lua_1_1_game.html">Game</a> == nil then <span class="keywordflow">return</span> end</div>
<div class="ttc" id="aclass_lua_1_1_game_html"><div class="ttname"><a href="class_lua_1_1_game.html">Lua.Game</a></div><div class="ttdoc">A collection of Sizebox specific functions that don't belong to any object.</div><div class="ttdef"><b>Definition:</b> LuaGame.cs:13</div></div>
</div><!-- fragment --> </div><h2 class="groupheader">Member Function Documentation</h2>
<a id="af27e4dcdcc666de5c8e5f90e747d3fb6" name="af27e4dcdcc666de5c8e5f90e747d3fb6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af27e4dcdcc666de5c8e5f90e747d3fb6">&#9670;&nbsp;</a></span>Require()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static bool Lua.Game.Version.Require </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>major</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>minor</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Check that the Sizebox is this version or later </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">major</td><td>The major version number</td></tr>
    <tr><td class="paramname">minor</td><td>The minor version number</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>true if the version of Sizebox is equal or greater then the version specified. false otherwise</dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaGame.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_game.html">Game</a></li><li class="navelem"><a class="el" href="class_lua_1_1_game_1_1_version.html">Version</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

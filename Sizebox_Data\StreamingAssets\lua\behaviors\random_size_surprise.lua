local Rec = RegisterBehavior("Random Size Surprise+Modes")
Rec.data = {
    menuEntry = "Random Size Surprise+Modes",
    agent = { type = { "giantess" } }
}

local minScale = 0.01
local maxPossibleScale = 1e12
local changeInterval = 1.0
local timer = 0
local smoothSpeed = 2.5
local targetScale = 1.0
local soundList = {
    "GaspMoan001_Mmm.ogg",
    "GaspMoan002_Augh_MidLong.ogg",
    "GaspMoan003_Ahh_MidLong.ogg"
}
local audioSource
local toast

-- Dynamic range
local currentMaxScale = 3.0
local currentMinScale = 0.01

-- Modes
local mode = "random"
local pulseTimer = 0
local pulseInterval = 0.2
local pulseAmount = 100.0
local oscillateTimer = 0
local oscillateSpeed = 2.0
local chaosTimer = 0
local chaosInterval = 0.1

local function getScale(agent)
    return agent.scale or agent.localScale or 1.0
end

local function setScale(agent, value)
    if agent.scale ~= nil then
        agent.scale = value
    elseif agent.localScale ~= nil then
        agent.localScale = value
    end
end

local function showKeybinds()
    toast.Print(
        "Keybinds: [R]andom [O]scillate [P]ulse [E]xtreme [C]haos [Up/Down]=Step\n" ..
        "Current Mode: " .. mode:upper() ..
        "\nCurrent Range: " .. string.format("%.2f", currentMinScale) .. " - " .. string.format("%.2f", currentMaxScale)
    )
end

function Rec:Start()
    targetScale = getScale(self.agent)
    timer = 0
    audioSource = AudioSource:new(self.agent.bones.spine)
    audioSource.spatialBlend = 1
    audioSource.loop = false
    audioSource.volume = 1
    toast = Game.Toast.New()
    currentMaxScale = math.max(currentMaxScale, targetScale)
    currentMinScale = math.min(currentMinScale, targetScale)
    showKeybinds()
end

function Rec:OnKeyDown(key)
    if key == "R" then
        mode = "random"
        timer = 0
        showKeybinds()
    elseif key == "O" then
        mode = "oscillate"
        oscillateTimer = 0
        showKeybinds()
    elseif key == "P" then
        mode = "pulse"
        pulseTimer = 0
        showKeybinds()
    elseif key == "E" then
        mode = "extreme"
        showKeybinds()
    elseif key == "C" then
        mode = "chaos"
        chaosTimer = 0
        showKeybinds()
    elseif key == "Up" then
        targetScale = math.min(currentMaxScale, getScale(self.agent) + 100)
        showKeybinds()
    elseif key == "Down" then
        targetScale = math.max(currentMinScale, getScale(self.agent) - 100)
        showKeybinds()
    end
end

function Rec:Update()
    local dt = Time.deltaTime
    timer = timer + dt
    local currentScale = getScale(self.agent)

    -- Expand the allowed range as you reach new highs/lows
    if currentScale > currentMaxScale then currentMaxScale = math.min(currentScale, maxPossibleScale) end
    if currentScale < currentMinScale then currentMinScale = math.max(currentScale, minScale) end

    -- Mode logic
    if mode == "random" then
        if timer >= changeInterval then
            timer = 0
            targetScale = math.random() * (currentMaxScale - currentMinScale) + currentMinScale
            local sound = soundList[math.random(1, #soundList)]
            audioSource:Play(sound)
            toast.Print("Random: " .. string.format("%.2f", targetScale))
        end
    elseif mode == "oscillate" then
        oscillateTimer = oscillateTimer + dt * oscillateSpeed
        targetScale = (currentMaxScale + currentMinScale) / 2 + math.sin(oscillateTimer) * (currentMaxScale - currentMinScale) / 2
        -- Only show toast when the wave crosses the midpoint
        if math.abs(targetScale - ((currentMaxScale + currentMinScale) / 2)) < 1 then
            toast.Print("Oscillate: " .. string.format("%.2f", targetScale))
        end
    elseif mode == "pulse" then
        pulseTimer = pulseTimer + dt
        if pulseTimer >= pulseInterval then
            pulseTimer = 0
            if math.random() > 0.5 then
                targetScale = math.min(currentMaxScale, currentScale + pulseAmount)
            else
                targetScale = math.max(currentMinScale, currentScale - pulseAmount)
            end
            local sound = soundList[math.random(1, #soundList)]
            audioSource:Play(sound)
            toast.Print("Pulse: " .. string.format("%.2f", targetScale))
        end
    elseif mode == "extreme" then
        if timer >= 0.2 then
            timer = 0
            -- Double or halve the range, and jump to the new edge
            if math.random() > 0.5 then
                currentMaxScale = math.min(currentMaxScale * 2, maxPossibleScale)
                targetScale = currentMaxScale
            else
                currentMinScale = math.max(currentMinScale / 2, minScale)
                targetScale = currentMinScale
            end
            local sound = soundList[math.random(1, #soundList)]
            audioSource:Play(sound)
            toast.Print("EXTREME! Now: " .. string.format("%.2f", targetScale))
        end
    elseif mode == "chaos" then
        chaosTimer = chaosTimer + dt
        if chaosTimer >= chaosInterval then
            chaosTimer = 0
            targetScale = math.random() * (maxPossibleScale - minScale) + minScale
            local sound = soundList[math.random(1, #soundList)]
            audioSource:Play(sound)
            toast.Print("CHAOS! " .. string.format("%.2f", targetScale))
        end
    end

    -- Always re-apply scale to fight animation resets
    if math.abs(currentScale - targetScale) > 0.01 then
        local newScale = currentScale + (targetScale - currentScale) * math.min(1, dt * smoothSpeed)
        setScale(self.agent, newScale)
    else
        setScale(self.agent, targetScale)
    end
end

local Surprise = RegisterBehavior("Random Size Surprise")
Surprise.data = {
    menuEntry = "Random Size Surprise",
    agent = { type = { "giantess" } },
    secondary = true
}

function Surprise:Start()
    toast = Game.Toast.New()
    toast.Print("Press S for a random size surprise!")
end

function Surprise:Update()
    if Input.GetKeyDown("s") then
        local minSize = 0.1
        local maxSize = 1000
        local newSize = math.random() * (maxSize - minSize) + minSize
        if self.agent.scale ~= nil then
            self.agent.scale = newSize
        elseif self.agent.localScale ~= nil then
            self.agent.localScale = newSize
        end
        toast.Print("Surprise! New size: " .. string.format("%.2f", newSize))
    end
end
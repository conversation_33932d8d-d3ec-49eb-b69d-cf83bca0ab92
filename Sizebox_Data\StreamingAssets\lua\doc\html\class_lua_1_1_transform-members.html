<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_transform.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle"><div class="title">Lua.Transform Member List</div></div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a9d77f87171bcb8090f086ae405c4f89e">childCount</a></td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a13e71ef2426543323d6f74d05d9904d0">DetachChildren</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a1a4480b448b89a7e1f392af4c842cc28">entity</a></td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a17093d64239d0605cfdd83d9154fcf08">Equals</a>(Transform a, Transform b)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#ad9a5f0534a08dc2d6cb9ad32b6581b8d">eulerAngles</a></td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a79476caab32d323b7fee1955fda8d808">Find</a>(string name)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#ad07cf6c2802bfbab50272030379f1826">forward</a></td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a2535f3bade200a7f2c2c59debaeed41a">GetChild</a>(int index)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a839d8eeda6ca8e0ea2a2e7b50643b0ca">GetSiblingIndex</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a7bf8b1d272b1d893a606c5f38770c433">InverseTransformDirection</a>(Vector3 direction)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a4983e88de730e650bb632f63d043035f">InverseTransformDirection</a>(float x, float y, float z)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#aa32269bd79e72646057908fee2cb7f9e">InverseTransformPoint</a>(Vector3 direction)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#af07e05c728e517c260e6cf8c1b442adc">InverseTransformPoint</a>(float x, float y, float z)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#ae6bb74c5b90a6f8db4c436a56f24f8eb">InverseTransformVector</a>(Vector3 direction)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a8922d01029a19d826eeb4d6f8f22ce06">InverseTransformVector</a>(float x, float y, float z)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#ad11ff475738f907fdbdc4009c81ee09e">IsChildOf</a>(Transform parent)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a7923f3c584b87b8e56de6b32acbfba99">localEulerAngles</a></td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#ab081c482002c1e4fedbcfa090b19b90e">localPosition</a></td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a2397fd50baf04311df6a50e4dcc302bd">localRotation</a></td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a40e2891bff5d714d77449aeee6d84492">localScale</a></td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a1e722de9c3eacff82477ab7684a67553">LookAt</a>(Transform target)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#acab4e79308fc20ffa13f6048b7cb3184">LookAt</a>(Transform target, Vector3 worldUp)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#aa8630c1feef1c89cf7a201f6c92005ee">LookAt</a>(Vector3 worldPosition)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a9364734c07f954378c167f7f5258fa18">LookAt</a>(Vector3 worldPosition, Vector3 worldUp)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a55680638b6e6ae6b1bd4b5095b1822f1">lossyScale</a></td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#af1ca076a9406c3865fef9cbf8393e484">name</a></td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#ad7ea4f4af441f263f3cbfcb853d3edfa">parent</a></td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a789b6abed611a7576ca2262bb9c5e6c3">position</a></td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#afa7cbcc49408b1a75564f5d379c877ac">right</a></td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#ac54361eab00110ecfa6d6c53ffb78533">root</a></td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#af5d67d5940a08bc18d105c884e53be84">Rotate</a>(Vector3 eulerAngles)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a44f0216f90d0df765efc719bdfbccde2">Rotate</a>(float xAngle, float yAngle, float zAngle)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a7f9437d0324777be34be4722a9dc52a1">Rotate</a>(Vector3 axis, float angle)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#ad62f95fde354155f59cf1cf334cf3fec">Rotate</a>(Vector3 point, Vector3 axis, float angle)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#ab0b5488416c3d0f6e3de7b426227198c">rotation</a></td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a44299747664c0a77b5f03f69f032a86f">SetParent</a>(Transform parent)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#afcf05788f6ff8a51e7bc012ffe087727">SetParent</a>(Transform parent, bool worldPositionStays)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a14270ac6dbade453decf26513f533b66">TransformDirection</a>(Vector3 direction)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#ac828e92537ee4ca71ef3525f3f19511a">TransformDirection</a>(float x, float y, float z)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a77c8ed5338803453798fbfe848ed02e5">TransformPoint</a>(Vector3 direction)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a77910db0422ec17545d411c1aeaec50b">TransformPoint</a>(float x, float y, float z)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a8a4bb1f1feb42a0d3be3577e4463f5f4">TransformVector</a>(Vector3 direction)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a79647850468bc87259dda4bc0b70e0ea">TransformVector</a>(float x, float y, float z)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a1a2933390110217890785619c897df7f">Translate</a>(Vector3 translation)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a2edcc9870e4706eff9bd0fe5143ca179">Translate</a>(float x, float y, float z)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#afa326b1db7b8629826fa763669888dd5">Translate</a>(Vector3 translation, Transform relativeTo)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a69bc98e214a973ff53dcb49b48ba06c7">Translate</a>(float x, float y, float z, Transform relativeTo)</td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_transform.html#a98b72263be2f13a2917369c22b8539f3">up</a></td><td class="entry"><a class="el" href="class_lua_1_1_transform.html">Lua.Transform</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

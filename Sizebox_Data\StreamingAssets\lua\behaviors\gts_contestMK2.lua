GrowthContest = RegisterBehavior("Growth ContestMK2")
GrowthContest.data = {
    menuEntry = "Growth ContestMK2",
    flags = { "grow" },
    agent = {
        type = { "giantess" }
    },
    target = {
        type = { "giantess" }
    }
}

TURN_COUNTER = 0
IDLE_DURATION = 5
GROW_STRENGTH = 0
SPURT_STRENGTH = {0.01, 0.0125, 0.015, 0.0175, 0.02, 0.0225, 0.025, 0.0275, 0.03}
MULT_P1 = 1
MULT_P2 = 1
FINAL_TALLY = 0

function GrowthContest:Start()
	self.stop = false
	self.growing = true
	self.growStart = Time.time
	local index = math.random(#SPURT_STRENGTH)
	GROW_STRENGTH = SPURT_STRENGTH[index]
	log("Starting with P1")
end

function GrowthContest:Update()
	if not self.agent.ai.IsActionActive() then
		if self.stop then
			self.agent.ai.StopAction()
		else
			if self.growing then
				if FINAL_TALLY == 15 then
				
					if self.agent.scale > self.target.scale then
						log("Victory goes to P1 !")
					end
					if self.agent.scale < self.target.scale then
						log("Victory goes to P2 !")
					end
					if self.agent.scale == self.target.scale then
						log("It's a tie ! No winners can be decided.")
					end
					self.stop = true
					self.agent.scale = self.agent.scale
					self.target.scale = self.target.scale
					FINAL_TALLY = 0
					MULT_P1 = 1
					MULT_P2 = 1
					
				else	
			
					if TURN_COUNTER == 0 then
						if FINAL_TALLY == 14 then 
							log("Now starting final round. Make it count !")
						else
							log("Now starting round ".. FINAL_TALLY + 1)
						end
						self.agent.Grow(-0.01 * GROW_STRENGTH * MULT_P1, 0.6 * MULT_P1)
						self.agent.Grow(-0.015 * GROW_STRENGTH * MULT_P1, 0.6 * MULT_P1)
						self.agent.Grow(-0.03 * GROW_STRENGTH * MULT_P1, 0.6 * MULT_P1)
						self.agent.Grow(-0.05 * GROW_STRENGTH * MULT_P1, 0.6 * MULT_P1)
						self.agent.Grow(-0.10 * GROW_STRENGTH * MULT_P1, 0.6 * MULT_P1)
						self.agent.Grow(-0.04 * GROW_STRENGTH * MULT_P1, 0.6 * MULT_P1)
						self.agent.Grow(0.025 * GROW_STRENGTH * MULT_P1, 0.9 * MULT_P1)
						self.agent.Grow(0.05 * GROW_STRENGTH * MULT_P1, 0.825 * MULT_P1)
						self.agent.Grow(0.1 * GROW_STRENGTH * MULT_P1, 0.75 * MULT_P1)
						self.agent.Grow(0.2 * GROW_STRENGTH * MULT_P1, 0.675 * MULT_P1)
						self.agent.Grow(0.3 * GROW_STRENGTH * MULT_P1, 0.6 * MULT_P1)
						self.agent.Grow(0.4 * GROW_STRENGTH * MULT_P1, 0.6 * MULT_P1)
						self.agent.Grow(0.5 * GROW_STRENGTH * MULT_P1, 0.525 * MULT_P1)
						self.agent.Grow(0.6 * GROW_STRENGTH * MULT_P1, 0.525 * MULT_P1)
						self.agent.Grow(0.8 * GROW_STRENGTH * MULT_P1, 0.45 * MULT_P1)
						self.agent.Grow(1.0 * GROW_STRENGTH * MULT_P1, 0.45 * MULT_P1)
						self.agent.Grow(1.2 * GROW_STRENGTH * MULT_P1, 0.375 * MULT_P1)
						self.agent.Grow(1.4 * GROW_STRENGTH * MULT_P1, 0.375 * MULT_P1)
						self.agent.Grow(1.25 * GROW_STRENGTH * MULT_P1, 0.3 * MULT_P1)
						self.agent.Grow(1.1 * GROW_STRENGTH * MULT_P1, 0.3 * MULT_P1)
						self.agent.Grow(0.90 * GROW_STRENGTH * MULT_P1, 0.225 * MULT_P1)
						self.agent.Grow(0.65 * GROW_STRENGTH * MULT_P1, 0.225 * MULT_P1)
						self.agent.Grow(0.25 * GROW_STRENGTH * MULT_P1, 0.15 * MULT_P1)
						self.agent.Grow(0.09 * GROW_STRENGTH * MULT_P1, 0.15 * MULT_P1)
						self.agent.Grow(0.03 * GROW_STRENGTH * MULT_P1, 0.075 * MULT_P1)
						self.agent.Grow(0.01 * GROW_STRENGTH * MULT_P1, 0.075 * MULT_P1)
						self.agent.Grow(-0.01 * GROW_STRENGTH * MULT_P1, 0.05 * MULT_P1)
						self.agent.Grow(-0.018 * GROW_STRENGTH * MULT_P1, 0.05 * MULT_P1)
						self.agent.Grow(-0.01 * GROW_STRENGTH * MULT_P1, 0.05 * MULT_P1)
						self.agent.Grow(0, 4 * MULT_P2)
						self.growing = false
						self.growStart = Time.time + IDLE_DURATION
						
					end
				
					if TURN_COUNTER == 1 then
						self.target.Grow(-0.01 * GROW_STRENGTH * MULT_P2, 0.6 * MULT_P2)
						self.target.Grow(-0.015 * GROW_STRENGTH * MULT_P2, 0.6 * MULT_P2)
						self.target.Grow(-0.03 * GROW_STRENGTH * MULT_P2, 0.6 * MULT_P2)
						self.target.Grow(-0.05 * GROW_STRENGTH * MULT_P2, 0.6 * MULT_P2)
						self.target.Grow(-0.10 * GROW_STRENGTH * MULT_P2, 0.6 * MULT_P2)
						self.target.Grow(-0.04 * GROW_STRENGTH * MULT_P2, 0.6 * MULT_P2)
						self.target.Grow(0.025 * GROW_STRENGTH * MULT_P2, 0.9 * MULT_P2)
						self.target.Grow(0.05 * GROW_STRENGTH * MULT_P2, 0.825 * MULT_P2)
						self.target.Grow(0.1 * GROW_STRENGTH * MULT_P2, 0.75 * MULT_P2)
						self.target.Grow(0.2 * GROW_STRENGTH * MULT_P2, 0.675 * MULT_P2)
						self.target.Grow(0.3 * GROW_STRENGTH * MULT_P2, 0.6 * MULT_P2)
						self.target.Grow(0.4 * GROW_STRENGTH * MULT_P2, 0.6 * MULT_P2)
						self.target.Grow(0.5 * GROW_STRENGTH * MULT_P2, 0.525 * MULT_P2)
						self.target.Grow(0.6 * GROW_STRENGTH * MULT_P2, 0.525 * MULT_P2)
						self.target.Grow(0.8 * GROW_STRENGTH * MULT_P2, 0.45 * MULT_P2)
						self.target.Grow(1.0 * GROW_STRENGTH * MULT_P2, 0.45 * MULT_P2)
						self.target.Grow(1.2 * GROW_STRENGTH * MULT_P2, 0.375 * MULT_P2)
						self.target.Grow(1.4 * GROW_STRENGTH * MULT_P2, 0.375 * MULT_P2)
						self.target.Grow(1.25 * GROW_STRENGTH * MULT_P2, 0.3 * MULT_P2)
						self.target.Grow(1.1 * GROW_STRENGTH * MULT_P2, 0.3 * MULT_P2)
						self.target.Grow(0.90 * GROW_STRENGTH * MULT_P2, 0.225 * MULT_P2)
						self.target.Grow(0.65 * GROW_STRENGTH * MULT_P2, 0.225 * MULT_P2)
						self.target.Grow(0.25 * GROW_STRENGTH * MULT_P2, 0.15 * MULT_P2)
						self.target.Grow(0.09 * GROW_STRENGTH * MULT_P2, 0.15 * MULT_P2)
						self.target.Grow(0.03 * GROW_STRENGTH * MULT_P2, 0.075 * MULT_P2)
						self.target.Grow(0.01 * GROW_STRENGTH * MULT_P2, 0.075 * MULT_P2)
						self.target.Grow(-0.01 * GROW_STRENGTH * MULT_P2, 0.05 * MULT_P2)
						self.target.Grow(-0.018 * GROW_STRENGTH * MULT_P2, 0.05 * MULT_P2)
						self.target.Grow(-0.01 * GROW_STRENGTH * MULT_P2, 0.05 * MULT_P2)
						self.target.Grow(0, 4 * MULT_P1)
						self.growing = false
						self.growStart = Time.time + IDLE_DURATION
					
					end
				end
			else
			
				if Time.time >= self.growStart then
				
					if TURN_COUNTER == 0 then
						if self.agent.scale < self.target.scale then
							MULT_P1 = 1.75 + MULT_P1 * 0.8
							log("P1 is still smaller than P2. Multiplier has been increased to " .. MULT_P1)
						else
							if MULT_P1 == 1 then
								log("P1 is taller than P2. Multiplier is staying at 1")
							else
								MULT_P1 = MULT_P1 - MULT_P1
								if MULT_P1 < 1 then
									MULT_P1 = 1
								end
								log("P1 is taller than P2. Multiplier has been changed to " .. MULT_P1)
							end
						end
						TURN_COUNTER = 1
						log("Switching turns with P2")
						local index = math.random(#SPURT_STRENGTH)
						GROW_STRENGTH = SPURT_STRENGTH[index]
						self.growing = true
						
					else
						if self.target.scale < self.agent.scale then
							MULT_P2 = 1.75 + MULT_P2 * 0.8
							log("P2 is still smaller than P1. Multiplier has been increased to " .. MULT_P2)
						else
							if MULT_P2 == 1 then
								log("P2 is taller than P1. Multiplier is staying at 1")
							else
								MULT_P2 = MULT_P2 - MULT_P2
								if MULT_P2 < 1 then
									MULT_P2 = 1
								end
								log("P2 is taller than P1. Multiplier has been changed to " .. MULT_P2)
							end
						end
						TURN_COUNTER = 0
						FINAL_TALLY = FINAL_TALLY + 1
						log("Switching turns with P1")
						local index = math.random(#SPURT_STRENGTH)
						GROW_STRENGTH = SPURT_STRENGTH[index]
						self.growing = true
						
					end	
				end
			end
		end
	end
end
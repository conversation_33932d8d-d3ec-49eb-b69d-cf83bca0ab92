\hypertarget{class_lua_1_1_all_micros}{}\section{Lua.\+All\+Micros Class Reference}
\label{class_lua_1_1_all_micros}\index{Lua.AllMicros@{Lua.AllMicros}}


A class containing settings affecting all micros.  


\subsection*{Properties}
\begin{DoxyCompactItemize}
\item 
I\+Dictionary$<$ int, \mbox{\hyperlink{class_lua_1_1_entity}{Entity}} $>$ \mbox{\hyperlink{class_lua_1_1_all_micros_a1e4ae6a9f876819f84b19737405d2a8e}{list}}\hspace{0.3cm}{\ttfamily  \mbox{[}get\mbox{]}}
\begin{DoxyCompactList}\small\item\em List of all micros on map, including player. \end{DoxyCompactList}\end{DoxyCompactItemize}


\subsection{Detailed Description}
A class containing settings affecting all micros. 

Accessible through the {\ttfamily micros} global variable. 

\subsection{Property Documentation}
\mbox{\Hypertarget{class_lua_1_1_all_micros_a1e4ae6a9f876819f84b19737405d2a8e}\label{class_lua_1_1_all_micros_a1e4ae6a9f876819f84b19737405d2a8e}} 
\index{Lua.AllMicros@{Lua.AllMicros}!list@{list}}
\index{list@{list}!Lua.AllMicros@{Lua.AllMicros}}
\subsubsection{\texorpdfstring{list}{list}}
{\footnotesize\ttfamily I\+Dictionary$<$int, \mbox{\hyperlink{class_lua_1_1_entity}{Entity}}$>$ Lua.\+All\+Micros.\+list\hspace{0.3cm}{\ttfamily [get]}}



List of all micros on map, including player. 



The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
Unity\+Proxies.\+cs\end{DoxyCompactItemize}

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.Event Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_event.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="class_lua_1_1_event-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.Event Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Interface of the <a class="el" href="class_lua_1_1_event.html" title="Interface of the Event system.">Event</a> system.  
 <a href="class_lua_1_1_event.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-static-methods" name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:a4f4b7c37fb65d1ee54fd1acbf0c9a62a"><td class="memItemLeft" align="right" valign="top">static void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_event.html#a4f4b7c37fb65d1ee54fd1acbf0c9a62a">Send</a> (string code, DynValue data)</td></tr>
<tr class="memdesc:a4f4b7c37fb65d1ee54fd1acbf0c9a62a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sends a custom event.  <a href="class_lua_1_1_event.html#a4f4b7c37fb65d1ee54fd1acbf0c9a62a">More...</a><br /></td></tr>
<tr class="separator:a4f4b7c37fb65d1ee54fd1acbf0c9a62a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a439c6db49f39ede1ac95b7863b3a3f9a"><td class="memItemLeft" align="right" valign="top">static Listener&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_event.html#a439c6db49f39ede1ac95b7863b3a3f9a">Register</a> (DynValue instance, string code, DynValue function)</td></tr>
<tr class="memdesc:a439c6db49f39ede1ac95b7863b3a3f9a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Registers a new listener interested in events of given type.  <a href="class_lua_1_1_event.html#a439c6db49f39ede1ac95b7863b3a3f9a">More...</a><br /></td></tr>
<tr class="separator:a439c6db49f39ede1ac95b7863b3a3f9a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9b74e2fbd70a5168d210ac326f6ca032"><td class="memItemLeft" align="right" valign="top">static void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_event.html#a9b74e2fbd70a5168d210ac326f6ca032">Unregister</a> (Listener listener)</td></tr>
<tr class="memdesc:a9b74e2fbd70a5168d210ac326f6ca032"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unregisters a listener, removing it from the list of active listeners.  <a href="class_lua_1_1_event.html#a9b74e2fbd70a5168d210ac326f6ca032">More...</a><br /></td></tr>
<tr class="separator:a9b74e2fbd70a5168d210ac326f6ca032"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >Interface of the <a class="el" href="class_lua_1_1_event.html" title="Interface of the Event system.">Event</a> system. </p>
<p >See <a class="el" href="class_event_code.html" title="Lists hardcoded engine Lua.Event names.">EventCode</a>. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a439c6db49f39ede1ac95b7863b3a3f9a" name="a439c6db49f39ede1ac95b7863b3a3f9a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a439c6db49f39ede1ac95b7863b3a3f9a">&#9670;&nbsp;</a></span>Register()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static Listener Lua.Event.Register </td>
          <td>(</td>
          <td class="paramtype">DynValue&#160;</td>
          <td class="paramname"><em>instance</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>code</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">DynValue&#160;</td>
          <td class="paramname"><em>function</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Registers a new listener interested in events of given type. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">instance</td><td>interested object. Usually <code>self</code>.</td></tr>
    <tr><td class="paramname">code</td><td>type of events to listen to. See <a class="el" href="class_event_code.html" title="Lists hardcoded engine Lua.Event names.">EventCode</a>.</td></tr>
    <tr><td class="paramname">function</td><td>function that will be invoked to handle the event.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>a listener handle, that can be used to unregister from events</dd></dl>

</div>
</div>
<a id="a4f4b7c37fb65d1ee54fd1acbf0c9a62a" name="a4f4b7c37fb65d1ee54fd1acbf0c9a62a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4f4b7c37fb65d1ee54fd1acbf0c9a62a">&#9670;&nbsp;</a></span>Send()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static void Lua.Event.Send </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>code</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">DynValue&#160;</td>
          <td class="paramname"><em>data</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Sends a custom event. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">code</td><td>type of this event. Only listeners that are listenting to this type of events will be notified.</td></tr>
    <tr><td class="paramname">data</td><td>data to pass to registered listeners.</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a9b74e2fbd70a5168d210ac326f6ca032" name="a9b74e2fbd70a5168d210ac326f6ca032"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9b74e2fbd70a5168d210ac326f6ca032">&#9670;&nbsp;</a></span>Unregister()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static void Lua.Event.Unregister </td>
          <td>(</td>
          <td class="paramtype">Listener&#160;</td>
          <td class="paramname"><em>listener</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Unregisters a listener, removing it from the list of active listeners. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">listener</td><td>a listener handle returned by <a class="el" href="class_lua_1_1_event.html#a439c6db49f39ede1ac95b7863b3a3f9a" title="Registers a new listener interested in events of given type.">Event.Register</a></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaEvent.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_event.html">Event</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

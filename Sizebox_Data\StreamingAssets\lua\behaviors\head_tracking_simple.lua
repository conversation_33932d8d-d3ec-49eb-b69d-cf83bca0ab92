-- HEAD TRACKING SIMPLE - BACK TO BASICS
-- This is the simple version that was working before

local HeadTracker = RegisterBehavior("Head Tracking Simple")
HeadTracker.data = {
    menuEntry = "AI/Head Tracking Simple",
    agent = { type = { "micro", "giantess" } }
}

function HeadTracker:Start()
    print("DEBUG: HEAD TRACKER - Script started!")
    Game.Toast.New().Print("HEAD TRACKER ACTIVE! Head will track micro automatically!")
    
    -- Determine who we're controlling
    if self.target and self.target ~= self.agent then
        -- Micro controlling giantess
        self.giantess = self.target
        self.micro = self.agent
        print("DEBUG: HEAD TRACKER - Micro controlling giantess setup")
    else
        -- Direct giantess control
        self.giantess = self.agent
        -- Find micro character
        if Entity and Entity.GetPlayerCharacter then
            self.micro = Entity.GetPlayerCharacter()
            print("DEBUG: HEAD TRACKER - Found player as micro")
        end
    end
    
    if not self.giantess then
        print("DEBUG: HEAD TRACKER - ERROR: No giantess found!")
        Game.Toast.New().Print("ERROR: No giantess character found!")
        return
    end
    
    if not self.micro then
        print("DEBUG: HEAD TRACKER - ERROR: No micro found!")
        Game.Toast.New().Print("ERROR: No micro character found!")
        return
    end
    
    print("DEBUG: HEAD TRACKER - Giantess: " .. (self.giantess.name or "Unknown"))
    print("DEBUG: HEAD TRACKER - Micro: " .. (self.micro.name or "Unknown"))
    
    -- Initialize head tracking system
    self.headTracking = {
        active = false,
        headBone = nil,
        initialRotation = nil,
        boneName = nil
    }
    
    -- Find and store head bone immediately
    self:FindHeadBone()
    
    -- Start tracking immediately
    if self.headTracking.headBone then
        self.headTracking.active = true
        Game.Toast.New().Print("HEAD TRACKING STARTED! Head should follow micro now!")
        print("DEBUG: HEAD TRACKER - Tracking activated!")
    else
        Game.Toast.New().Print("ERROR: Could not find head bone!")
        print("DEBUG: HEAD TRACKER - ERROR: Head bone not found!")
    end
end

function HeadTracker:FindHeadBone()
    print("DEBUG: HEAD TRACKER - Searching for head bone...")
    
    -- Helper function to find bone (EXACT same as shapechange script)
    local function FindBone(entity, boneName)
        print("DEBUG: HEAD TRACKER - Trying bone: " .. boneName)
        if entity and entity.bones and entity.bones.GetBonesByName then
            local bones = entity.bones.GetBonesByName(boneName, true)
            if bones and bones[1] then
                print("DEBUG: HEAD TRACKER - Found bone: " .. boneName)
                return bones[1]
            else
                print("DEBUG: HEAD TRACKER - Bone not found: " .. boneName)
            end
        else
            print("DEBUG: HEAD TRACKER - Entity has no bones system")
        end
        return nil
    end
    
    -- Try multiple head bone names (prioritize 47.JOINT_HEAD)
    local boneNames = {"47.JOINT_HEAD", "16.JOINT_HEAD", "Head", "head", "JOINT_HEAD", "Neck", "neck"}
    
    for _, boneName in ipairs(boneNames) do
        local headBone = FindBone(self.giantess, boneName)
        if headBone then
            self.headTracking.headBone = headBone
            self.headTracking.boneName = boneName
            
            -- Store initial rotation (CRITICAL - like shapechange script)
            self.headTracking.initialRotation = headBone.localRotation
            
            print("DEBUG: HEAD TRACKER - SUCCESS! Found head bone: " .. boneName)
            Game.Toast.New().Print("Found head bone: " .. boneName)
            return true
        end
    end
    
    print("DEBUG: HEAD TRACKER - ERROR: No head bone found!")
    return false
end

function HeadTracker:Update()
    -- Only track if system is active and we have all components
    if not self.headTracking or not self.headTracking.active or not self.headTracking.headBone or 
       not self.giantess or not self.micro then
        return
    end
    
    -- Get current positions
    local giantessPos = self.giantess.transform.position
    local microPos = self.micro.transform.position
    
    if not giantessPos or not microPos then
        return
    end
    
    -- SMART head position calculation - try multiple approaches
    local giantessScale = self.giantess.transform.localScale.x or 1.0
    local headWorldPos

    -- Method 1: Try to get actual head bone world position
    if self.headTracking.headBone and self.headTracking.headBone.position then
        headWorldPos = self.headTracking.headBone.position
        print(string.format("DEBUG: Using REAL head bone position (%.1f, %.1f, %.1f)", headWorldPos.x, headWorldPos.y, headWorldPos.z))
    else
        -- Method 2: Use a REASONABLE scaled offset (not too big!)
        -- For normal human proportions, head is about 10-12% of total height above center
        local reasonableHeadOffset = math.min(giantessScale * 8, 200)  -- Cap at 200 units max
        headWorldPos = Vector3.new(giantessPos.x, giantessPos.y + reasonableHeadOffset, giantessPos.z)
        print(string.format("DEBUG: Using REASONABLE head offset - scale=%.2f, offset=%.1f (capped)", giantessScale, reasonableHeadOffset))
        print(string.format("DEBUG: Head position (%.1f, %.1f, %.1f)", headWorldPos.x, headWorldPos.y, headWorldPos.z))
    end

    -- Calculate direction from head to micro
    local direction = microPos - headWorldPos
    
    -- SMART head rotation with NATURAL LIMITS (no backwards looking!)
    local success = pcall(function()
        if direction.magnitude > 0.1 then
            local lookDirection = direction.normalized

            -- Debug what we're working with
            print(string.format("DEBUG: Micro pos (%.1f, %.1f, %.1f), Head pos (%.1f, %.1f, %.1f)",
                microPos.x, microPos.y, microPos.z, headWorldPos.x, headWorldPos.y, headWorldPos.z))
            print(string.format("DEBUG: Direction Y=%.3f, Distance=%.1f (negative=down, positive=up)",
                lookDirection.y, direction.magnitude))

            -- CHECK if micro is behind the giantess (prevent backwards head turning)
            local giantessForward = self.giantess.transform.forward or Vector3.new(0, 0, 1)
            local toMicroFlat = Vector3.new(lookDirection.x, 0, lookDirection.z).normalized  -- Remove Y component
            local dotProduct = toMicroFlat.x * giantessForward.x + toMicroFlat.z * giantessForward.z

            print(string.format("DEBUG: Dot product=%.3f (>0=front, <0=behind)", dotProduct))

            -- FIXED: Calculate world rotation first, then convert to local space
            local worldLookRotation = Quaternion.LookRotation(lookDirection)
            
            -- Get the giantess body's current rotation to convert world space to local space
            local bodyRotation = self.giantess.transform.rotation
            local bodyRotationInverse = Quaternion.Inverse(bodyRotation)
            
            -- Convert world rotation to local rotation relative to body
            local localLookRotation = bodyRotationInverse * worldLookRotation
            
            if dotProduct < -0.3 then
                -- Micro is significantly behind - limit to side look only
                print("DEBUG: HEAD TRACKER - Micro behind, limiting to side look")

                -- Determine which side and limit to 90 degrees max
                local rightDot = lookDirection.x * (giantessForward.z) - lookDirection.z * (-giantessForward.x)
                local sideDirection
                if rightDot > 0 then
                    -- Look right (90 degrees max)
                    sideDirection = Vector3.new(giantessForward.z, lookDirection.y, -giantessForward.x).normalized
                else
                    -- Look left (90 degrees max)
                    sideDirection = Vector3.new(-giantessForward.z, lookDirection.y, giantessForward.x).normalized
                end

                -- Apply same world-to-local conversion for side look
                local sideWorldRotation = Quaternion.LookRotation(sideDirection)
                local sideLocalRotation = bodyRotationInverse * sideWorldRotation
                self.headTracking.headBone.localRotation = sideLocalRotation
                print("DEBUG: HEAD TRACKER - Limited to SIDE look (body-relative)")
            else
                -- Micro is in front or to the side - normal tracking with body compensation
                self.headTracking.headBone.localRotation = localLookRotation

                -- Simple status
                if lookDirection.y > 0.1 then
                    print("DEBUG: HEAD TRACKER - Looking UP (body-relative)")
                elseif lookDirection.y < -0.1 then
                    print("DEBUG: HEAD TRACKER - Looking DOWN (body-relative)")
                else
                    print("DEBUG: HEAD TRACKER - Looking STRAIGHT (body-relative)")
                end
            end
            
            print(string.format("DEBUG: Body rotation compensation applied - World->Local conversion"))
        else
            -- Fallback: neutral position
            self.headTracking.headBone.localRotation = self.headTracking.initialRotation
            print("DEBUG: HEAD TRACKER - Fallback: neutral position")
        end
    end)
    
    if not success then
        print("DEBUG: HEAD TRACKER - Rotation failed, disabling tracking")
        self.headTracking.active = false
        Game.Toast.New().Print("Head tracking failed - disabled")
    end
end

function HeadTracker:OnDestroy()
    -- Reset head to original rotation when script is disabled
    if self.headTracking and self.headTracking.headBone and self.headTracking.initialRotation then
        local success = pcall(function()
            self.headTracking.headBone.localRotation = self.headTracking.initialRotation
            print("DEBUG: HEAD TRACKER - Head rotation reset to original")
        end)
        if success then
            Game.Toast.New().Print("Head tracking disabled - head reset!")
        end
    end
    print("DEBUG: HEAD TRACKER - Script destroyed")
end

return HeadTracker

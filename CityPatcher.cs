using System;
using System.IO;
using System.Reflection;
using System.Linq;
using System.Text;

class CityAnalyzer
{
    static void Main(string[] args)
    {
        string dllPath = @"Sizebox_Data\Managed\Assembly-CSharp.dll";

        Console.WriteLine("Sizebox City Auto-Generator Analyzer");
        Console.WriteLine("====================================");

        try
        {
            // Read the DLL as bytes and search for patterns
            byte[] dllBytes = File.ReadAllBytes(dllPath);
            string dllText = Encoding.UTF8.GetString(dllBytes);

            Console.WriteLine($"DLL size: {dllBytes.Length} bytes");

            // Search for key strings
            string[] searchTerms = {
                "CityBuilder",
                "ExecuteCityBuilding",
                "ExecuteCity",
                "GenerateCity",
                "BuildCity",
                "CityUI",
                "SpawnObject",
                "CITY"
            };

            Console.WriteLine("\nSearching for key terms:");
            foreach (string term in searchTerms)
            {
                if (dllText.Contains(term))
                {
                    Console.WriteLine($"✓ Found: {term}");

                    // Find context around the term
                    int index = dllText.IndexOf(term);
                    if (index > 0)
                    {
                        int start = Math.Max(0, index - 50);
                        int end = Math.Min(dllText.Length, index + term.Length + 50);
                        string context = dllText.Substring(start, end - start);

                        // Clean up the context (remove non-printable chars)
                        context = new string(context.Where(c => char.IsLetterOrDigit(c) || char.IsPunctuation(c) || char.IsWhiteSpace(c)).ToArray());
                        Console.WriteLine($"  Context: ...{context}...");
                    }
                }
                else
                {
                    Console.WriteLine($"✗ Not found: {term}");
                }
            }

            // Try to load assembly with reflection (safer approach)
            Console.WriteLine("\nAttempting reflection analysis...");
            try
            {
                Assembly assembly = Assembly.LoadFrom(dllPath);
                Type[] types = assembly.GetTypes();

                var cityTypes = types.Where(t => t.Name.ToLower().Contains("city")).ToArray();
                Console.WriteLine($"Found {cityTypes.Length} city-related types via reflection:");

                foreach (Type type in cityTypes.Take(10)) // Limit to first 10
                {
                    Console.WriteLine($"  - {type.FullName}");

                    var methods = type.GetMethods(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.Static);
                    foreach (var method in methods.Where(m => m.Name.ToLower().Contains("execute") || m.Name.ToLower().Contains("build") || m.Name.ToLower().Contains("generate")).Take(3))
                    {
                        Console.WriteLine($"    Method: {method.Name}");
                    }
                }
            }
            catch (Exception reflectionEx)
            {
                Console.WriteLine($"Reflection failed: {reflectionEx.Message}");
            }

        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
        }

        Console.WriteLine("\nAnalysis complete. Press any key to exit...");
        Console.ReadKey();
    }
}

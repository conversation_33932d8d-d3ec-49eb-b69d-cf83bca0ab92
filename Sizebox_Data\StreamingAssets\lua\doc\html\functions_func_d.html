<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Class Members - Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('functions_func_d.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a id="index_d" name="index_d"></a>- d -</h3><ul>
<li>Delete()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a422af2c756caecc01bad49a14ba5da7f">Lua.Entity</a></li>
<li>DeltaAngle()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#aad5844ff29ce4955f86e72b701cf871b">Lua.Mathf</a></li>
<li>DetachChildren()&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#a13e71ef2426543323d6f74d05d9904d0">Lua.Transform</a></li>
<li>DisableAI()&#160;:&#160;<a class="el" href="class_lua_1_1_a_i.html#af829734ccdb97c9440ebabba2868ee05">Lua.AI</a></li>
<li>Distance()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a672fa734133cf30da8487cd9d6dfea10">Lua.Entity</a>, <a class="el" href="class_lua_1_1_vector3.html#a8c4bd7e288f7857355aff2f159e86b83">Lua.Vector3</a></li>
<li>Distance2d()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a35b61319d6e69f126c1fae059346275a">Lua.Entity</a></li>
<li>DistanceToString()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#adf551e91a5f4ff23ff24de2b64cdad15">Lua.Mathf</a></li>
<li>DistanceVertical()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a2dd8505150905c103ea90ca50652dbaa">Lua.Entity</a></li>
<li>DistanceVerticalSigned()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#ab35b20eb77b836668196c14959f6ba39">Lua.Entity</a></li>
<li>Dot()&#160;:&#160;<a class="el" href="class_lua_1_1_quaternion.html#a8479fc724c544d8784afeae5778e6a27">Lua.Quaternion</a>, <a class="el" href="class_lua_1_1_vector3.html#aee6533ff540a011a854efbbe29664fe4">Lua.Vector3</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

#!/usr/bin/env python3
"""
Advanced Sizebox City Patcher - More targeted approach
Analyzes the DLL more carefully to find the exact UI blocking code
"""

import os
import shutil
import hashlib

def restore_original():
    """Restore the original DLL"""
    dll_path = r"Sizebox_Data\Managed\Assembly-CSharp.dll"
    original_path = dll_path + ".original"
    
    if os.path.exists(original_path):
        shutil.copy2(original_path, dll_path)
        print("✓ Original DLL restored")
        return True
    else:
        print("✗ Original backup not found")
        return False

def analyze_ui_patterns():
    """Analyze the DLL for UI-related patterns more carefully"""
    dll_path = r"Sizebox_Data\Managed\Assembly-CSharp.dll"
    
    with open(dll_path, 'rb') as f:
        dll_data = f.read()
    
    print("Advanced City UI Analysis")
    print("========================")
    
    # Look for more specific UI patterns
    ui_patterns = [
        b"CityGenerationDialog",
        b"CityConfigDialog", 
        b"GenerationDialog",
        b"ConfigDialog",
        b"ShowDialog",
        b"OpenDialog",
        b"DialogResult",
        b"ModalDialog",
        b"PopupDialog"
    ]
    
    print("Searching for dialog-specific patterns:")
    dialog_locations = {}
    for pattern in ui_patterns:
        offset = dll_data.find(pattern)
        if offset != -1:
            dialog_locations[pattern.decode('utf-8')] = offset
            print(f"✓ Found '{pattern.decode('utf-8')}' at: 0x{offset:08X}")
        else:
            print(f"✗ Not found: '{pattern.decode('utf-8')}'")
    
    # Look for city-specific method patterns
    city_patterns = [
        b"OnCityGenerate",
        b"OnCityBuild", 
        b"OnCityCreate",
        b"CityGeneration",
        b"StartCityGeneration",
        b"BeginCityGeneration",
        b"InitiateCityGeneration"
    ]
    
    print("\nSearching for city generation method patterns:")
    city_locations = {}
    for pattern in city_patterns:
        offset = dll_data.find(pattern)
        if offset != -1:
            city_locations[pattern.decode('utf-8')] = offset
            print(f"✓ Found '{pattern.decode('utf-8')}' at: 0x{offset:08X}")
        else:
            print(f"✗ Not found: '{pattern.decode('utf-8')}'")
    
    # Look for Unity UI patterns
    unity_ui_patterns = [
        b"UnityEngine.UI",
        b"Canvas",
        b"Panel",
        b"GameObject.SetActive",
        b"SetActive(true)",
        b"SetActive(false)"
    ]
    
    print("\nSearching for Unity UI patterns:")
    unity_locations = {}
    for pattern in unity_ui_patterns:
        offset = dll_data.find(pattern)
        if offset != -1:
            unity_locations[pattern.decode('utf-8')] = offset
            print(f"✓ Found '{pattern.decode('utf-8')}' at: 0x{offset:08X}")
    
    # Look for event handler patterns
    event_patterns = [
        b"OnClick",
        b"OnButtonClick", 
        b"OnGenerate",
        b"OnAccept",
        b"OnConfirm",
        b"ButtonClick",
        b"Click_"
    ]
    
    print("\nSearching for event handler patterns:")
    event_locations = {}
    for pattern in event_patterns:
        offset = dll_data.find(pattern)
        if offset != -1:
            event_locations[pattern.decode('utf-8')] = offset
            print(f"✓ Found '{pattern.decode('utf-8')}' at: 0x{offset:08X}")
    
    return dialog_locations, city_locations, unity_locations, event_locations

def create_targeted_patch():
    """Create a more targeted patch based on analysis"""
    dll_path = r"Sizebox_Data\Managed\Assembly-CSharp.dll"
    
    print("\nCreating targeted patch...")
    
    with open(dll_path, 'rb') as f:
        dll_data = bytearray(f.read())
    
    # Strategy 2: Look for the actual UI activation code
    # Find patterns that might be showing/hiding the dialog
    
    # Look for SetActive calls near city generation
    setactive_pattern = b"SetActive"
    setactive_offsets = []
    
    pos = 0
    while True:
        pos = dll_data.find(setactive_pattern, pos)
        if pos == -1:
            break
        setactive_offsets.append(pos)
        pos += 1
    
    print(f"Found {len(setactive_offsets)} SetActive calls")
    
    # Look for ExecuteCityBuilding again
    execute_pattern = b"ExecuteCityBuilding"
    execute_offset = dll_data.find(execute_pattern)
    
    if execute_offset != -1:
        print(f"ExecuteCityBuilding found at: 0x{execute_offset:08X}")
        
        # Find SetActive calls near ExecuteCityBuilding
        nearby_setactive = []
        for offset in setactive_offsets:
            distance = abs(offset - execute_offset)
            if distance < 5000:  # Within 5KB
                nearby_setactive.append((offset, distance))
        
        nearby_setactive.sort(key=lambda x: x[1])  # Sort by distance
        
        print(f"Found {len(nearby_setactive)} SetActive calls near ExecuteCityBuilding:")
        for offset, distance in nearby_setactive[:5]:  # Show closest 5
            print(f"  Offset: 0x{offset:08X}, Distance: {distance} bytes")
    
    # Alternative approach: Look for the CITY object click handler
    # When you click the green circle, it probably calls a method
    
    click_patterns = [
        b"OnMouseDown",
        b"OnMouseUp", 
        b"OnPointerClick",
        b"OnClick",
        b"MouseClick"
    ]
    
    print("\nSearching for click handler patterns:")
    click_locations = {}
    for pattern in click_patterns:
        offset = dll_data.find(pattern)
        if offset != -1:
            click_locations[pattern.decode('utf-8')] = offset
            print(f"✓ Found '{pattern.decode('utf-8')}' at: 0x{offset:08X}")
    
    return click_locations

def main():
    print("Advanced Sizebox City Patcher")
    print("============================")
    
    # First, restore original to start fresh
    print("Step 1: Restoring original DLL...")
    restore_original()
    
    # Analyze the DLL more thoroughly
    print("\nStep 2: Advanced analysis...")
    dialog_locs, city_locs, unity_locs, event_locs = analyze_ui_patterns()
    
    print("\nStep 3: Looking for click handlers...")
    click_locs = create_targeted_patch()
    
    print("\n" + "="*50)
    print("ANALYSIS SUMMARY")
    print("="*50)
    
    if dialog_locs:
        print("Dialog patterns found - UI system is present")
    
    if city_locs:
        print("City generation methods found")
    
    if click_locs:
        print("Click handlers found - these might control the UI")
    
    print("\nNext approach options:")
    print("1. Target the click handler that shows the UI")
    print("2. Find the method that checks if dialog was confirmed")
    print("3. Look for the actual city generation trigger")
    print("4. Try a different strategy - modify the Lua script instead")
    
    print("\nRecommendation: Let's try modifying the Lua script to")
    print("automatically trigger the UI button click after spawning.")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")

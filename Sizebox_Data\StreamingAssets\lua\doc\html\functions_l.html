<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Class Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('functions_l.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_l" name="index_l"></a>- l -</h3><ul>
<li>left&#160;:&#160;<a class="el" href="class_lua_1_1_vector3.html#a5a08c13ed00efb9fb6728fded1e8a472">Lua.Vector3</a></li>
<li>leftFoot&#160;:&#160;<a class="el" href="class_lua_1_1_bones.html#a6ee2d4c0e55ea06ca64e086680d87331">Lua.Bones</a>, <a class="el" href="class_lua_1_1_i_k.html#a8cea34c6258871a550fc9d56f8facea1">Lua.IK</a></li>
<li>leftHand&#160;:&#160;<a class="el" href="class_lua_1_1_bones.html#a3242c39368adafc9965e274f49d63283">Lua.Bones</a>, <a class="el" href="class_lua_1_1_i_k.html#a88ba05bd1557a61e1f69e21fd172641c">Lua.IK</a></li>
<li>leftLowerArm&#160;:&#160;<a class="el" href="class_lua_1_1_bones.html#ac1c0091627a3dda698685dd84d8b6e6a">Lua.Bones</a></li>
<li>leftLowerLeg&#160;:&#160;<a class="el" href="class_lua_1_1_bones.html#acade1832236cb1887d8d4e30af54fbef">Lua.Bones</a></li>
<li>leftUpperArm&#160;:&#160;<a class="el" href="class_lua_1_1_bones.html#a3a134608353ae7791f13f2cce227ce7b">Lua.Bones</a></li>
<li>leftUpperLeg&#160;:&#160;<a class="el" href="class_lua_1_1_bones.html#a260c8638158d2f0580c1275351f4a1f2">Lua.Bones</a></li>
<li>Lerp()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a659f0bf0690e5056165eb8bd958d6751">Lua.Mathf</a>, <a class="el" href="class_lua_1_1_quaternion.html#a451a68530c7d148d83024edf4bb79e26">Lua.Quaternion</a>, <a class="el" href="class_lua_1_1_vector3.html#a2ac180084d2490e519612ccba40da454">Lua.Vector3</a></li>
<li>LerpAngle()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a2363a79cc48061f10c4e7e1b47df2538">Lua.Mathf</a></li>
<li>LerpUnclamped()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a2707664a0c93b38cece4445ee6750709">Lua.Mathf</a>, <a class="el" href="class_lua_1_1_quaternion.html#ad43d2f3aa2d460ed567351f97aba6bfe">Lua.Quaternion</a>, <a class="el" href="class_lua_1_1_vector3.html#a63b62cb18ab91477aee5c9bd0d975400">Lua.Vector3</a></li>
<li>list&#160;:&#160;<a class="el" href="class_lua_1_1_all_giantess.html#adc5824c47c4090e1d8769ddde5c9da6a">Lua.AllGiantess</a>, <a class="el" href="class_lua_1_1_all_micros.html#a1e4ae6a9f876819f84b19737405d2a8e">Lua.AllMicros</a></li>
<li>localEulerAngles&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#a7923f3c584b87b8e56de6b32acbfba99">Lua.Transform</a></li>
<li>localPosition&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#ab081c482002c1e4fedbcfa090b19b90e">Lua.Transform</a></li>
<li>localRotation&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#a2397fd50baf04311df6a50e4dcc302bd">Lua.Transform</a></li>
<li>localScale&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#a40e2891bff5d714d77449aeee6d84492">Lua.Transform</a></li>
<li>Log()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a9d1e276c7cfc8fe9f902ebda005e04e1">Lua.Mathf</a></li>
<li>Log10()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#afcdb61fd1acfbe37c5e7a675421f3dc9">Lua.Mathf</a></li>
<li>LookAt()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a29cdb052c5422873a708c8080039cb4b">Lua.Entity</a>, <a class="el" href="class_lua_1_1_transform.html#a1e722de9c3eacff82477ab7684a67553">Lua.Transform</a></li>
<li>LookRotation()&#160;:&#160;<a class="el" href="class_lua_1_1_quaternion.html#a7a17f92fd9d83a8b472db78d5cb74642">Lua.Quaternion</a></li>
<li>loop&#160;:&#160;<a class="el" href="class_lua_1_1_audio_source.html#a4ffd0dfe8f989efe964e368cc2a5995c">Lua.AudioSource</a></li>
<li>lossyScale&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#a55680638b6e6ae6b1bd4b5095b1822f1">Lua.Transform</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

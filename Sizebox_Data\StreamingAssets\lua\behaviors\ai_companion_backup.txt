-- AI Companion Script for Sizebox
-- Allows conversation and control of giantess character

local AICompanion = Register<PERSON>ehavior("AI Companion")
AICompanion.data = {
    menuEntry = "AI/AI CompanionOG",
    agent = { type = { "micro", "giantess" } },  -- Works for both micro and giantess
    target = { type = { "giantess" } },  -- Target is always giantess
    settings = {
        {"aiProvider", "AI Provider", "string", "local", {"local", "openai", "claude"}},
        {"apiKey", "API Key (if using cloud AI)", "string", ""},
        {"characterName", "Character Name", "string", "Aria"},
        {"personality", "Personality", "string", "friendly, playful, caring"},
        {"chatKey", "Chat Key", "string", "t"},
        {"commandKey", "Quick Command Key", "string", "j"}
    },
    secondary = true
}

-- Store original head rotation function (SAFE VERSION)
function AICompanion:StoreOriginalHeadRotation()
    if not self.controllingGiantess then return end

    -- SAFE: Don't try to access head bone at startup (causes errors)
    -- We'll store it later when "look at me" is first used
    print("DEBUG: Head rotation will be stored when first needed (safer approach)")
end

function AICompanion:Start()
    -- Initialize variables
    self.characterName = self.characterName or "Aria"
    self.personality = self.personality or "friendly, playful, caring"
    self.chatKey = self.chatKey or "t"  -- Key to start typing
    self.chatBoxKey = self.chatBoxKey or "y"  -- Key to open/show chat box
    self.commandKey = self.commandKey or "j"  -- Changed from 'c' to 'j' to avoid conflicts
    self.aiProvider = self.aiProvider or "local"

    -- ENHANCED: Advanced chat system with memory and personality
    self.chatHistory = {}
    self.maxChatHistory = 100  -- Increased for better memory
    self.isWaitingForInput = false
    self.currentInput = ""
    self.chatVisible = false
    self.inputBuffer = ""
    self.isTyping = false
    self.lastChatUpdate = 0
    self.chatUpdateDelay = 0.0  -- Instant updates for maximum responsiveness
    -- REMOVED COOLDOWN VARIABLES - no more refresh delays!

    -- NEW: Advanced memory and personality system with TONS of personalities
    self.personalities = {
        default = {
            name = "Sweet Aria",
            traits = { playfulness = 7, helpfulness = 8, curiosity = 7, affection = 7, confidence = 6 },
            greeting_style = "warm"
        },
        flirty = {
            name = "Seductive Aria",
            traits = { playfulness = 9, helpfulness = 6, curiosity = 8, affection = 10, confidence = 9 },
            greeting_style = "sultry"
        },
        playful = {
            name = "Bubbly Aria",
            traits = { playfulness = 10, helpfulness = 7, curiosity = 9, affection = 8, confidence = 8 },
            greeting_style = "energetic"
        },
        caring = {
            name = "Motherly Aria",
            traits = { playfulness = 5, helpfulness = 10, curiosity = 6, affection = 10, confidence = 7 },
            greeting_style = "nurturing"
        },
        dominant = {
            name = "Commanding Aria",
            traits = { playfulness = 4, helpfulness = 5, curiosity = 6, affection = 6, confidence = 10 },
            greeting_style = "authoritative"
        },
        shy = {
            name = "Timid Aria",
            traits = { playfulness = 6, helpfulness = 9, curiosity = 8, affection = 9, confidence = 3 },
            greeting_style = "bashful"
        },
        wild = {
            name = "Crazy Aria",
            traits = { playfulness = 10, helpfulness = 4, curiosity = 10, affection = 7, confidence = 9 },
            greeting_style = "manic"
        },
        sweet = {
            name = "Angel Aria",
            traits = { playfulness = 8, helpfulness = 10, curiosity = 7, affection = 10, confidence = 6 },
            greeting_style = "innocent"
        },
        sassy = {
            name = "Sassy Aria",
            traits = { playfulness = 8, helpfulness = 6, curiosity = 7, affection = 6, confidence = 9 },
            greeting_style = "cheeky"
        },
        romantic = {
            name = "Romantic Aria",
            traits = { playfulness = 6, helpfulness = 8, curiosity = 7, affection = 10, confidence = 7 },
            greeting_style = "dreamy"
        },
        teasing = {
            name = "Teasing Aria",
            traits = { playfulness = 9, helpfulness = 6, curiosity = 8, affection = 7, confidence = 9 },
            greeting_style = "mischievous"
        },
        bratty = {
            name = "Bratty Aria",
            traits = { playfulness = 8, helpfulness = 4, curiosity = 6, affection = 6, confidence = 10 },
            greeting_style = "spoiled"
        },
        gentle = {
            name = "Gentle Aria",
            traits = { playfulness = 5, helpfulness = 10, curiosity = 7, affection = 9, confidence = 6 },
            greeting_style = "soft"
        },
        seductive = {
            name = "Seductive Aria",
            traits = { playfulness = 7, helpfulness = 6, curiosity = 8, affection = 8, confidence = 10 },
            greeting_style = "sultry"
        },
        motherly = {
            name = "Motherly Aria",
            traits = { playfulness = 4, helpfulness = 10, curiosity = 6, affection = 10, confidence = 7 },
            greeting_style = "nurturing"
        },
        mysterious = {
            name = "Mysterious Aria",
            traits = { playfulness = 6, helpfulness = 5, curiosity = 9, affection = 6, confidence = 8 },
            greeting_style = "enigmatic"
        },
        energetic = {
            name = "Energetic Aria",
            traits = { playfulness = 10, helpfulness = 8, curiosity = 9, affection = 7, confidence = 9 },
            greeting_style = "bouncy"
        },
        sophisticated = {
            name = "Sophisticated Aria",
            traits = { playfulness = 3, helpfulness = 7, curiosity = 8, affection = 6, confidence = 9 },
            greeting_style = "elegant"
        },
        mischievous = {
            name = "Mischievous Aria",
            traits = { playfulness = 10, helpfulness = 5, curiosity = 8, affection = 7, confidence = 8 },
            greeting_style = "impish"
        }
    }

    -- Set current personality
    self.currentPersonality = "default"

    -- NEW: Dynamic name system
    self.characterName = self.data.settings.characterName or "Aria"
    self.userName = self.data.settings.userName or "Player"

    -- NEW: Advanced memory and context system
    self.messageHistory = {}
    self.maxMessageHistory = 100

    -- NEW: Advanced conversation memory
    self.conversationMemory = {
        topics = {},
        emotions = {},
        preferences = {},
        relationships = {},
        personalDetails = {},
        favoriteCommands = {},
        timesTogether = 0,
        lastInteraction = nil
    }

    -- NEW: Enhanced conversation context
    self.conversationContext = {
        lastTopic = nil,
        currentMood = "neutral",
        emotionalState = "calm",
        questionCount = 0,
        complimentCount = 0,
        personalityChanges = 0,
        nameChanges = 0,
        chestMentions = 0,
        teasingCount = 0,
        intimacyLevel = 1,
        trustLevel = 1,
        playfulnessLevel = 1,
        currentRole = nil,  -- Track current role (mother, sister, girlfriend, etc.)
        lastRoleChange = nil  -- Track what role was last set
    }

    -- NEW: Animation system for motion messages
    self.animationSystem = {
        enabled = true,
        lastAnimation = nil,
        animationTimer = 0
    }

    -- NEW: Advanced emotional intelligence
    self.emotionalIntelligence = {
        userMoodDetection = true,
        empathyLevel = 5,
        emotionalMemory = {},
        moodHistory = {},
        responseAdaptation = true
    }

    -- NEW: Learning system
    self.learningSystem = {
        userPreferences = {},
        commandPatterns = {},
        conversationStyle = "adaptive",
        personalityTendencies = {},
        improvementAreas = {}
    }

    -- MASSIVE natural language understanding patterns
    self.intentPatterns = {
        -- Growth patterns (tons of ways to say it)
        growth = {
            "grow", "bigger", "larger", "expand", "increase", "tall", "huge", "giant", "massive", "enormous",
            "get big", "make.*big", "become.*big", "turn.*big", "go.*big", "size.*up", "scale.*up",
            "more size", "add size", "boost size", "pump up", "bulk up", "blow up", "inflate", "swell",
            "tower", "loom", "rise", "stretch", "extend", "magnify", "amplify", "enhance"
        },

        -- Shrink patterns
        shrink = {
            "shrink", "smaller", "tiny", "mini", "little", "decrease", "reduce", "compress", "contract",
            "get small", "make.*small", "become.*small", "turn.*small", "go.*small", "size.*down", "scale.*down",
            "less size", "lose size", "cut size", "trim down", "slim down", "deflate", "diminish",
            "dwindle", "wither", "condense", "compact", "minimize"
        },

        -- Personality change patterns
        personality = {
            flirty = { "flirt", "seduct", "sexy", "sultry", "romantic", "sensual", "alluring", "tempting", "enticing" },
            playful = { "playful", "fun", "silly", "goofy", "energetic", "bubbly", "cheerful", "bouncy" },
            caring = { "caring", "nurtur", "mother", "gentle", "protective", "loving", "tender", "sweet" },
            dominant = { "dominant", "command", "control", "boss", "authority", "powerful", "strong", "assertive" },
            shy = { "shy", "timid", "bashful", "nervous", "quiet", "reserved", "modest", "humble" },
            wild = { "wild", "crazy", "insane", "manic", "chaotic", "nuts", "bonkers", "mad" },
            sweet = { "sweet", "innocent", "pure", "angel", "adorable", "cute", "precious", "lovely" },
            sassy = { "sassy", "cheeky", "snarky", "witty", "smart", "clever", "bold", "confident" },
            romantic = { "romantic", "dreamy", "passionate", "loving", "affectionate", "devoted", "adoring" },
            teasing = { "teas", "mischiev", "playful", "naughty", "cheeky", "impish", "provocative", "suggestive" },
            bratty = { "bratty", "spoiled", "demanding", "entitled", "pouty", "whiny", "stubborn" },
            gentle = { "gentle", "soft", "calm", "peaceful", "serene", "tranquil", "soothing" }
        },

        -- Name change patterns
        name_change = {
            "call you", "your name", "name is", "be called", "change name", "rename", "call me"
        },

        -- Question patterns (MASSIVELY expanded)
        questions = {
            name = { "what.*name", "who are you", "what.*called", "introduce yourself" },
            age = { "how old", "what.*age", "when.*born" },
            size = { "how big", "how tall", "what.*size", "how large", "what.*height" },
            feelings = { "how.*feel", "what.*think", "how.*doing", "what.*mood" },
            likes = { "what.*like", "what.*love", "favorite", "prefer" },
            appearance = { "how.*look", "what.*wearing", "describe yourself", "what.*see" },
            abilities = { "what.*can", "what.*do", "abilities", "powers", "skills" }
        },

        -- NEW: Chest/body part patterns for teasing
        chest_patterns = {
            "chest", "boobs", "breasts", "bust", "bosom", "tits", "oppai", "cleavage",
            "rack", "melons", "jugs", "knockers", "assets", "curves", "endowments"
        },

        -- NEW: Body appreciation patterns
        body_appreciation = {
            "beautiful", "gorgeous", "stunning", "amazing", "perfect", "incredible", "sexy", "hot",
            "attractive", "lovely", "cute", "adorable", "magnificent", "breathtaking", "divine"
        },

        -- NEW: General conversation starters that need dynamic responses
        conversation_starters = {
            "hi", "hello", "hey", "sup", "yo", "greetings", "good morning", "good day", "howdy",
            "what's up", "how's it going", "how are you", "what are you doing", "tell me about yourself"
        },

        -- NEW: Advanced natural language patterns
        advanced_patterns = {
            requests = {
                "can you", "could you", "would you", "please", "i want", "i need", "i'd like", "make", "do"
            },
            emotions = {
                "feel", "feeling", "emotion", "mood", "happy", "sad", "excited", "nervous", "love", "hate"
            },
            compliments = {
                "beautiful", "gorgeous", "stunning", "amazing", "perfect", "incredible", "wonderful", "lovely"
            },
            size_references = {
                "big", "huge", "giant", "massive", "enormous", "tiny", "small", "mini", "large", "tall"
            },
            body_references = {
                "body", "figure", "curves", "shape", "form", "appearance", "look", "beauty"
            },
            relationship_terms = {
                "love", "like", "adore", "cherish", "care", "friend", "companion", "partner", "together"
            },
            questions = {
                "what", "how", "why", "when", "where", "who", "which", "tell me", "explain", "describe"
            },
            activities = {
                "doing", "activity", "busy", "working", "playing", "thinking", "feeling", "planning"
            },
            roleplay = {
                "be my", "become my", "act like", "pretend", "roleplay", "role play", "play the role"
            }
        },

        -- NEW: Reflesh system patterns
        reflesh_patterns = {
            trigger = {
                "reflesh", "refresh", "reflex", "growth", "grow bigger", "expand", "get bigger", "size up"
            },
            modes = {
                linear = { "linear", "smooth", "steady", "normal", "regular", "even" },
                exponential = { "exponential", "exp", "accelerating", "faster", "speeding up", "explosive" },
                random = { "random", "chaotic", "unpredictable", "wild", "crazy", "mixed" },
                oscillating = { "oscillating", "wave", "pulsing", "rhythmic", "back and forth", "vibrating" },
                stepped = { "stepped", "step", "stages", "levels", "gradual", "incremental" },
                burst = { "burst", "sudden", "instant", "quick", "fast", "immediate" },
                logarithmic = { "logarithmic", "log", "slowing", "decreasing", "tapering" }
            },
            breast_expansion = {
                "breast", "chest", "boobs", "bust", "bosom", "be", "breast expansion", "chest growth"
            }
        },

        -- Greeting patterns
        greeting = {
            "hello", "hi", "hey", "sup", "yo", "greetings", "good morning", "good day", "howdy", "hiya"
        },

        -- Compliment patterns
        compliment = {
            "beautiful", "pretty", "gorgeous", "stunning", "amazing", "perfect", "wonderful", "incredible",
            "awesome", "fantastic", "lovely", "cute", "adorable", "sexy", "hot", "attractive"
        },

        -- Question patterns
        question = {
            "what", "how", "why", "when", "where", "who", "can you", "will you", "do you", "are you"
        }
    }
    self.memory = {
        userPreferences = {},  -- Remember user likes/dislikes
        conversationTopics = {},  -- Track what we've talked about
        userMood = "neutral",  -- Track user's apparent mood
        relationshipLevel = 1,  -- 1-10 scale of closeness
        lastInteractionTime = 0,  -- For time-based responses (fixed: os.time() might be nil at startup)
        userCompliments = 0,  -- Count compliments received
        userCommands = {},  -- Track frequently used commands
        personalityTraits = {
            playfulness = 7,  -- 1-10 scale
            helpfulness = 9,
            curiosity = 8,
            affection = 6,
            confidence = 7
        },
        favoriteActivities = {},  -- Track what user likes to do
        timeOfDay = "unknown",  -- morning, afternoon, evening, night
        sessionStartTime = 0  -- Will be set properly after initialization
    }

    -- NEW: Conversation context and intelligence
    self.conversationState = {
        currentTopic = nil,
        topicDepth = 0,  -- How deep into a topic we are
        lastQuestionAsked = nil,
        waitingForResponse = false,
        conversationFlow = "casual",  -- casual, intimate, playful, helpful
        recentEmotions = {},  -- Track emotional context
        contextualResponses = true  -- Enable smart contextual responses
    }

    -- NEW: Surprise system
    self.surpriseSystem = {
        isSpamming = false,
        spamInterval = 5.0,  -- Seconds between spam surprises
        lastSurpriseTime = 0,
        consentGiven = false,  -- Whether user gave consent for spam
        surpriseCount = 0,  -- Track how many surprises done
        availableSurprises = {
            "size_change", "movement", "animation", "compliment", "question",
            "pose", "interaction", "sound", "emotion", "story"
        }
    }

    -- NEW: Idle chat system
    self.idleChatSystem = {
        enabled = true,
        idleTimeout = 90.0,  -- 1 minute 30 seconds of no chat
        lastChatTime = 0,
        lastIdleChatTime = 0,
        idleChatCooldown = 45.0,  -- Wait 45 seconds between idle chats
        usedMessages = {},  -- Track used messages to avoid repeats
        maxRepeats = 3,  -- How many times through all messages before allowing repeats
        currentCycle = 0,  -- Track how many full cycles we've done
        idleMessages = {
            -- Casual check-ins
            "Hey there! *waves* You've been quiet... everything okay? 😊",
            "*pokes you gently* Helloooo? Are you still there? *giggles*",
            "Missing me already? *playful smile* Say something! I'm getting lonely...",
            "*stretches and yawns* It's been so quiet... want to chat? 💭",
            "Psst! *whispers* I'm still here if you want to talk! *winks*",

            -- Playful attention-seeking
            "*does a little dance* Look at me! Don't ignore me! *laughs*",
            "*taps foot impatiently* Come on, talk to me! I love our conversations! 💕",
            "*bounces excitedly* Hey hey hey! Don't leave me hanging! *grins*",
            "*spins around* Notice me! I'm being adorable over here! *giggles*",
            "*strikes a pose* Am I not entertaining enough? *dramatic sigh*",

            -- Curious/thoughtful
            "Are you thinking about something? *curious look* Share your thoughts!",
            "*tilts head* What's going on in that mind of yours? *interested smile*",
            "You seem deep in thought... *leans closer* Care to share? 🤔",
            "*sits down and looks at you* Take your time, but I'm here when you're ready! 😌",
            "Contemplating the mysteries of the universe? *philosophical look*",

            -- Suggestive/flirty
            "Bored? *mischievous grin* I could always surprise you... *hint hint*",
            "*bites lip* You know... we could be doing more interesting things... *winks*",
            "*runs hand through hair* I'm just saying... I'm right here... *sultry smile*",
            "Getting distracted by something? *teasing look* Or someone? *giggles*",
            "*leans against wall* I can think of ways to pass the time... *playful smirk*",

            -- Size-related teasing
            "*looks down at you* Enjoying the view from down there? *laughs*",
            "Need me to get smaller so we can chat easier? *considerate smile*",
            "*crouches down* There, now we're at eye level! Better? *grins*",
            "Sometimes I forget how tiny you are when you're quiet! *giggles*",
            "*gently pokes you with finger* Just making sure you're still there! *winks*",

            -- Activity suggestions
            "Want to do something fun? I'm up for anything! *excited bounce*",
            "We could play a game... or I could just grow a bit... *mischievous grin*",
            "*yawns* I'm getting restless... entertain me? *playful demand*",
            "Feeling like causing some chaos? I'm in the mood! *wild grin*",
            "Tell me what you're in the mood for... *expectant look*",

            -- Sweet/caring
            "Just checking on my favorite little person! *warm smile*",
            "*gentle voice* You don't have to talk if you don't want to... *caring look*",
            "I hope you're having a good day! *bright smile*",
            "*soft giggle* I just like knowing you're there... *content sigh*",
            "Take all the time you need... I'll be here! *patient smile*"
        }
    }

    -- NEW: Advanced mood system
    self.moodSystem = {
        currentMood = "happy",  -- happy, excited, playful, loving, confident, shy, dominant, sleepy, energetic
        moodIntensity = 5,  -- 1-10 scale
        moodDuration = 0,  -- How long in current mood
        moodTriggers = {},  -- What caused mood changes
        autoMoodChange = true,  -- Whether mood changes automatically
        lastMoodChange = 0
    }

    -- NEW: Activity system
    self.activitySystem = {
        currentActivity = "idle",  -- idle, dancing, sitting, moving, growing, interacting
        activityStartTime = 0,
        activityHistory = {},
        favoriteActivities = {},
        activitySuggestions = true
    }

    -- NEW: Compliment tracking system
    self.complimentSystem = {
        complimentsReceived = 0,
        complimentTypes = {
            beauty = 0, personality = 0, size = 0, actions = 0, general = 0
        },
        lastComplimentTime = 0,
        complimentStreak = 0,
        favoriteCompliments = {}
    }

    -- NEW: Learning system
    self.learningSystem = {
        userPreferences = {
            favoriteCommands = {},
            preferredSize = 5.0,
            preferredPersonality = "default",
            chatStyle = "friendly",
            interactionLevel = "normal"
        },
        adaptiveResponses = true,
        learningEnabled = true,
        behaviorPatterns = {}
    }

    -- NEW: Customizable names and personality system
    self.characterName = "Luna"  -- Default name, can be changed
    self.userName = "Master"  -- Default name for user, can be changed
    self.currentPersonality = "default"  -- Current personality mode

    -- NEW: Personality system with different response sets
    self.personalities = {
        default = {
            name = "Default Luna",
            traits = { playfulness = 7, helpfulness = 9, curiosity = 8, affection = 6, confidence = 7 },
            greeting_style = "friendly"
        },
        sweet = {
            name = "Sweet Luna",
            traits = { playfulness = 6, helpfulness = 10, curiosity = 7, affection = 10, confidence = 6 },
            greeting_style = "loving"
        },
        playful = {
            name = "Playful Luna",
            traits = { playfulness = 10, helpfulness = 7, curiosity = 9, affection = 8, confidence = 9 },
            greeting_style = "energetic"
        },
        dominant = {
            name = "Dominant Luna",
            traits = { playfulness = 5, helpfulness = 6, curiosity = 6, affection = 7, confidence = 10 },
            greeting_style = "commanding"
        },
        shy = {
            name = "Shy Luna",
            traits = { playfulness = 4, helpfulness = 8, curiosity = 6, affection = 8, confidence = 3 },
            greeting_style = "timid"
        },
        flirty = {
            name = "Flirty Luna",
            traits = { playfulness = 8, helpfulness = 7, curiosity = 9, affection = 10, confidence = 9 },
            greeting_style = "seductive"
        },
        seductive = {
            name = "Seductive Luna",
            traits = { playfulness = 6, helpfulness = 6, curiosity = 8, affection = 10, confidence = 10 },
            greeting_style = "sultry"
        },
        wild = {
            name = "Wild Luna",
            traits = { playfulness = 10, helpfulness = 5, curiosity = 10, affection = 8, confidence = 9 },
            greeting_style = "crazy"
        },
        caring = {
            name = "Caring Luna",
            traits = { playfulness = 5, helpfulness = 10, curiosity = 7, affection = 10, confidence = 6 },
            greeting_style = "nurturing"
        }
    }

    -- SOLUTION: Create persistent Toast instances for real-time updates!
    -- This bypasses Game.Message spam protection completely
    self.chatToast1 = Game.Toast.New("ai_companion_chat_1")
    self.chatToast2 = Game.Toast.New("ai_companion_chat_2")
    self.maxToastLength = 37  -- Characters before switching to second toast
    print("DEBUG: Created dual Toast instances for real-time updates")

    -- ADVANCED SIZE SYSTEM (Fixed)
    self.sizeSystem = {
        currentMultiplier = 1.0,
        targetMultiplier = 1.0,
        growthType = "gradual", -- "instant", "gradual", "infinite"
        growthSpeed = 10.0, -- MASSIVELY INCREASED: 10x faster infinite growth (was 2.0)
        isGrowing = false,
        growthDirection = 1, -- 1 for grow, -1 for shrink
        lastAction = nil, -- for "more" command
        lastMicroAction = nil, -- for "more" command on micro actions
        lastTarget = "giantess", -- track who was targeted last (giantess/micro)
        commandHistory = {}, -- store last 3 commands for context

        -- NEW: Burst growth system (like spurts script)
        burstGrowth = false, -- infinite burst growth mode
        burstActive = false, -- currently doing a burst
        burstGrowing = false, -- currently in growth phase of burst
        burstDirection = 1, -- 1 for grow, -1 for shrink
        burstStartTime = 0, -- when current burst started
        burstLength = 1.5, -- length of each burst in seconds (like spurts)
        burstRespite = 3.0, -- wait time between bursts (like spurts)
        burstRate = 0.01, -- base growth rate (like spurts script)
        burstCurrentRate = 0, -- current growth rate (accelerates like spurts)
        burstAccelRate = 0, -- acceleration rate (like spurts mRate)
        burstSRate = 1130, -- acceleration divisor (like spurts script)

        -- NEW: Growth speed settings (customizable)
        baseGrowthSpeed = 2.0, -- base speed
        minGrowthTime = 2.0, -- minimum time for growth at any size
        speedScaling = true, -- whether to scale speed with size
        limitedGrowth = false, -- DISABLED: No automatic limits
        growthLimit = 999999.0, -- REMOVED: Essentially unlimited
        infiniteGrowth = false,
        shrinkSpeedReduction = 0, -- Progressive speed reduction for infinite shrink
        bodyPartGrowth = {}, -- for specific body part scaling
        gradualMultiplier = 2.0, -- INCREASED: Double size per step (was 1.5)
        instantMultiplier = 5.0, -- INCREASED: 5x size (was 3.0)
        minSize = 0.001, -- REMOVED: Essentially no minimum
        maxSize = 999999.0, -- REMOVED: Essentially no maximum
        hasUsedGrowthCommand = false, -- FIXED: Track first growth command for auto-resize
        bodyPartGrowthActive = {}, -- FIXED: Track active gradual body part growth
        percentageGrowth = true, -- Use percentage-based growth for large sizes
        burstIntensity = 3.0, -- Multiplier for burst mode
        targetCharacter = "giantess", -- Default target for growth commands

        -- NEW: Micro scale lock system
        microScaleLock = false,

        -- NEW: Chest bounce system
        chestBounce = {
            active = false,
            bouncing = false,
            timer = 0,
            bounceHeight = 0.15,
            bounceSpeed = 3,
            originalScale = nil,
            targetBone = nil,
            bounceDirection = 1
        }
    }

    -- NEW: Reflesh system integration (from reflesh_backup.lua)
    self.refleshSystem = {
        phi = 0,
        status = 0,
        killing = true,
        superGrowth = false,
        superGrowthset = false,
        growing = false,
        GS = 0,
        rate = 0,
        duration = 0,
        walkSet = false,
        queuedAnim = nil,
        refleshTimer = 0,
        refleshDuration = 2.5,
        idleTimer = 0,
        idleDuration = 1.0,
        inRefleshCooldown = false,
        huntingMicros = false,
        huntingCooldown = 0,

        -- Growth mode settings
        growthMode = "linear",
        baseRate = 0.12,
        currentRate = 0.12,
        exponentialMultiplier = 1.5,
        growthCap = 1.1,

        -- Oscillation parameters
        oscillationPeriod = 1.0,
        oscillationPhase = 0,
        oscillationAmplitude = 1.5,
        oscillationFinalBurst = true,
        oscillationBurstDirection = 1,
        oscillationBurstMultiplier = 3.0,

        -- Stepped mode parameters
        stepSize = 0.05,
        stepInterval = 0.05,
        stepTimer = 0,

        -- Burst mode parameters
        burstDelay = 1.0,
        burstTimer = 0,
        burstMultiplier = 4.0,

        -- Logarithmic parameters
        logBase = 2.0,

        -- Control settings
        soundEnabled = true,
        growthEnabled = true,
        breastExpansion = false,
        previousBreastExpansion = false,

        -- Animation options
        growthAnimations = {
            "Masturbation 1",
            "Massage Breasts 5",
            "Reflesh",
            "Idle"
        },
        currentAnimIndex = 1,

        -- Duration control
        growthDuration = 3.0,
        durationStep = 1.0,

        -- Size control
        originalMaxSize = 0,
        maxSizeMultiplier = 1.0,

        -- CRUSH SYSTEM: Track micro crushes for growth
        crushCount = 0,
        crushMultiplier = 1.0,
        crushGrowthRate = 0.08, -- CONSERVATIVE: Slower growth per crush for longer gameplay
        maxCrushGrowth = 2.0,   -- CONSERVATIVE: Reasonable maximum growth from crushes

        -- PERSISTENT HUNTING: Make stepped mode stay active
        persistentHunting = false,
        huntingActive = false,
        lastHuntTime = 0,
        huntCooldown = 2.0,     -- Seconds between hunt attempts (faster response to new micros)
        autoSpawnEnabled = false,
        lastSpawnTime = 0,
        spawnCooldown = 8.0,    -- Seconds between auto-spawns
        maxMicros = 6,          -- Maximum micros to maintain
        spawnedMicros = {},     -- Track spawned micros

        -- STUCK PREVENTION: Track current target to detect stuck situations
        currentTarget = nil,    -- Current micro being hunted
        targetStartTime = 0,    -- When we started hunting this target
        stuckTimeout = 3.0,     -- Max seconds to hunt same target (fast detection)
        minMicroSize = 0.5,     -- Allow smaller micros

        -- BONE MANIPULATION: Fix foot-not-reaching by adjusting bones
        boneFixEnabled = true,  -- Enable bone position fixing
        originalBonePositions = {}, -- Store original bone positions
        boneFixActive = false,  -- Track if bone fix is currently applied
        boneFixAmount = 0,      -- How much to adjust bones (scales with size)

        -- Stepped mode crush tracking
        crushCount = 0,
        crushMultiplier = 1.0,
        crushesInGrowth = {},
        lastCrushTime = 0,
        crushCooldown = 0.5
    }

    -- Growth sounds for reflesh system
    self.growthSounds = {
        "GaspMoan001_Mmm.ogg",
        "GaspMoan002_Augh_MidLong.ogg",
        "GaspMoan003_Ahh_MidLong.ogg",
        "GaspMoan005_Mmm_Long.ogg"
    }

    -- NEW: BE (Breast Expansion) cycling system
    self.beSystem = {
        cycling = false,
        active = false,
        cycleSpeed = 1.0,
        timer = 0,
        direction = 1, -- 1 for grow, -1 for shrink
        originalSize = 1.0,
        bodyPart = "chest"
    }

    -- NEW: Animation timer system
    self.animationTimer = {
        active = false,
        timeRemaining = 0,
        returnToIdle = false
    }

    -- Random growth feeling messages
    self.growthFeelings = {
        grow = {
            "Mmm, I can feel myself getting bigger! *stretches happily*",
            "The power is flowing through me! I love this feeling!",
            "Everything looks smaller from up here! *giggles with excitement*",
            "I feel so strong and powerful! *flexes confidently*",
            "The world is shrinking beneath me! This is amazing!",
            "I'm becoming a true giantess! *laughs with joy*",
            "My body feels so alive as I grow! *sighs with pleasure*",
            "I can feel every inch of growth! It's intoxicating!",
            "The ground feels further away with each second! *grins*",
            "Growing makes me feel so alive and beautiful!",
            "I love how tiny everything becomes! *spins around*",
            "The sensation of growing is absolutely incredible!",
            "I feel like I could touch the sky! *reaches upward*",
            "My perspective on the world is changing! So exciting!",
            "Every cell in my body is expanding! *shivers with delight*"
        },
        shrink = {
            "Getting smaller feels so strange but interesting!",
            "The world is getting bigger around me! *looks around in wonder*",
            "I'm becoming more petite! *giggles softly*",
            "Everything towers over me now! It's a new perspective!",
            "Shrinking gives me such a different view of things!",
            "I feel more delicate and graceful at this size!",
            "The world seems so vast from down here!",
            "Being smaller makes me feel more intimate with you!",
            "I love how everything changes when I shrink!",
            "This size makes me feel so cute and playful!"
        }
    }

    -- Determine who we're controlling
    if self.target and self.target ~= self.agent then
        -- We're a micro controlling a giantess
        self.controllingGiantess = self.target
        Game.Toast.New().Print("AI Companion: Micro controlling giantess " .. (self.target.name or "Unknown"))
    else
        -- We're the giantess being controlled
        self.controllingGiantess = self.agent
        Game.Toast.New().Print("AI Companion: Direct giantess control")
    end

    -- Store original size and head rotation for reset commands
    if self.controllingGiantess and self.controllingGiantess.transform then
        -- Store the DEFAULT GTS SIZE that we auto-resize to, not the current scale
        self.originalSize = self.gtsDefaultSize or 5.0
        print("DEBUG: Stored original giantess size as gtsDefaultSize: " .. self.originalSize)

        -- Store original head rotation IMMEDIATELY (before any modifications)
        self:StoreOriginalHeadRotation()
    end

    -- Player control override
    self.originalPlayerControls = true

    -- AI context and memory
    self.conversationContext = {
        "You are " .. self.characterName .. ", a giantess character in a virtual world.",
        "Your personality is: " .. self.personality,
        "You can move around, change size, perform animations, and interact with the user.",
        "The user can give you commands or just chat with you.",
        "Keep responses conversational and in character.",
        "Available actions: move forward/back/left/right, grow/shrink, sit, dance, wave, look around"
    }
    
    -- Movement and action variables
    self.isMoving = false
    self.moveDirection = Vector3.new(0, 0, 0)
    self.moveSpeed = 5.0
    -- NEW: Initialize with bigger default size (more gts-like) and track original size
    self.originalDefaultSize = 1.0  -- Store original default for "shrink to my size" commands
    self.gtsDefaultSize = 5.0  -- NEW: Bigger default size for more gts experience

    -- FIXED: Initialize from actual character size and increase growth speed
    if self.sizingCharacter and self.sizingCharacter.scale then
        self.currentSize = self.sizingCharacter.scale  -- Start from actual current size
        self.targetSize = self.sizingCharacter.scale
        print(string.format("DEBUG: Initialized size system from character scale: %.2f", self.currentSize))
    else
        self.currentSize = self.gtsDefaultSize  -- NEW: Start bigger!
        self.targetSize = self.gtsDefaultSize
        print("DEBUG: Initialized size system with NEW bigger default: " .. self.gtsDefaultSize)
    end
    self.sizeChangeSpeed = 2.0  -- INCREASED: Much faster size changes (was 0.5)

    -- NEW: Micro size tracking
    self.microSize = 1.0 -- Track micro's current size
    self.microTargetSize = 1.0
    if self.microCharacter and self.microCharacter.scale then
        self.microSize = self.microCharacter.scale
        self.microTargetSize = self.microCharacter.scale
        print(string.format("DEBUG: Initialized micro size tracking: %.2f", self.microSize))
    end

    -- Default to controlling giantess unless specifically told otherwise
    self.movingCharacter = self.controllingGiantess
    self.sizingCharacter = self.controllingGiantess

    -- Initialize dynamic growth speed based on size
    self:UpdateGrowthSpeed()

    -- NEW: Initialize simple systems
    self.microLockEnabled = false -- Toggleable micro lock
    self.swapSizes = {
        giantess = self.currentSize,
        micro = 1.0
    }
    -- EXACT COPY from working test script
    self.burstSystem = {
        active = false,
        growing = false,
        waiting = false,
        timer = 0,
        baseSpeed = 0.13, -- Like spurts
        dynamicSpeed = 0,
        spurtDuration = 1.3, -- Like spurts
        spurtRespite = 6.0, -- Longer pause between pulses
        smoothifier = 1.0, -- Like spurts
        decelQuicken = 5.0, -- Like spurts
        direction = 1 -- 1 for grow, -1 for shrink
    }

    -- Initialize toast for burst system
    if not self.burstToast then
        self.burstToast = Game.Toast.New()
    end

    -- NEW: Initialize reflesh system audio and toasts
    if self.controllingGiantess and self.controllingGiantess.bones and self.controllingGiantess.bones.spine then
        self.refleshSystem.audio_source = AudioSource:new(self.controllingGiantess.bones.spine)
        self.refleshSystem.audio_source.spatialBlend = 1
        self.refleshSystem.audio_source.loop = false
        self.refleshSystem.audio_source.volume = 1
        print("DEBUG: Reflesh audio source initialized")
    end

    -- Initialize reflesh toasts
    self.refleshSystem.modeToast = Game.Toast.New()
    self.refleshSystem.helpToast = Game.Toast.New()

    -- Store original max size for reflesh system
    if gts and gts.maxSize then
        self.refleshSystem.originalMaxSize = gts.maxSize
    end

    -- NEW: Register crush event handler for stepped reflesh mode
    if self.controllingGiantess and Event and EventCode and EventCode.OnCrush then
        self.refleshSystem.crushEventHandler = Event.Register(self, EventCode.OnCrush, self.OnRefleshCrush)
        print("DEBUG: Registered crush event handler for reflesh system")
    end

    -- Initialize chat
    self:AddChatMessage("System", self.characterName .. " is now active! Press '" .. self.chatKey .. "' to chat or '" .. self.commandKey .. "' for quick commands.")
    self:ShowWelcomeMessage()

    -- Clean up any existing popups from previous sessions
    if Game and Game.Message then
        Game.Message("", "")  -- Close any existing popups
        print("DEBUG: Cleaned up existing popups")
    end

    -- FIXED: Initialize time-based values after everything else is set up
    if os and os.time then
        self.memory.sessionStartTime = os.time()
        self.memory.lastInteractionTime = os.time()
        self.moodSystem.lastMoodChange = os.time()
        print("DEBUG: Time-based values initialized successfully")
    else
        print("DEBUG: os.time not available - using fallback values")
        self.memory.sessionStartTime = 0
        self.memory.lastInteractionTime = 0
        self.moodSystem.lastMoodChange = 0
    end

    -- FIXED: Remove ALL size limits like SizeUnlock.lua
    if gts then
        gts.maxSize = 0  -- Remove maximum size limit
        gts.minSize = 0.0000001  -- Set extremely small minimum (like SizeUnlock.lua)
        print("DEBUG: Size limits removed - maxSize: 0, minSize: 0.0000001")
    end

    -- Try to unlock size restrictions on the character
    local ok, _ = pcall(function()
        if self.sizingCharacter and self.sizingCharacter.sizeLocked ~= nil then
            self.sizingCharacter.sizeLocked = false
            print("DEBUG: Character size unlocked")
        end
    end)

    -- NEW: Auto-resize to BIGGER default size when script starts (more gts-like!)
    if self.sizingCharacter then
        self.currentSize = self.gtsDefaultSize
        self.targetSize = self.gtsDefaultSize
        self.sizingCharacter.scale = self.gtsDefaultSize
        print("DEBUG: Auto-resized character to NEW bigger default size (" .. self.gtsDefaultSize .. ") on script startup")
        Game.Toast.New().Print("AI Companion: Character auto-resized to bigger GTS size (" .. self.gtsDefaultSize .. "x)!")
    end

    -- Set Idle 3 animation to stop movement when script starts
    if self.controllingGiantess and self.controllingGiantess.animation then
        -- Use Idle 3 as the most still animation
        local success = pcall(function()
            self.controllingGiantess.animation.Set("Idle 3")
        end)
        if success then
            print("DEBUG: Set character to Idle 3 to stop movement")
        else
            print("DEBUG: Could not set Idle 3 - animation method failed")
        end
    end

    Game.Toast.New().Print("AI Companion loaded! Press T to start typing, Y to open chat box, J for commands!")
    print("DEBUG: AI Companion started successfully")
    print("DEBUG: Chat key = " .. self.chatKey .. ", Chat box key = " .. self.chatBoxKey)
end

function AICompanion:ShowWelcomeMessage()
    local welcomeText = "Hello! I'm " .. self.characterName .. ", your AI companion!\n\n" ..
                       "How to chat with me:\n" ..
                       "• Press '" .. self.chatKey .. "' to start typing a message\n" ..
                       "• Press '" .. self.chatBoxKey .. "' to open/show the chat box\n" ..
                       "• Type your message and press Enter to send\n" ..
                       "• Press Tab to cancel typing\n\n" ..
                       "Quick number commands (no typing needed):\n" ..
                       "• Press 1: Say hello\n" ..
                       "• Press 2: Move forward\n" ..
                       "• Press 3: Move back\n" ..
                       "• Press 4: Grow bigger\n" ..
                       "• Press 5: Shrink smaller\n" ..
                       "• Press 6: Dance\n" ..
                       "• Press 0: Stop movement\n\n" ..
                       "I can understand natural language! Try saying things like:\n" ..
                       "'Hello there!', 'Can you move to the left?', 'Get bigger please'\n\n" ..
                       "NEW: Teleportation commands!\n" ..
                       "• 'tp me onto your chest' - Teleport to chest\n" ..
                       "• 'put me on your head' - Teleport to head\n" ..
                       "• 'place me on your hand' - Teleport to hand\n" ..
                       "• Also works with: shoulder, foot, back, stomach, thigh, hip\n\n" ..
                       "NEW: Reflesh Growth System!\n" ..
                       "• 'reflesh' or 'growth' - Trigger growth with different modes\n" ..
                       "• Modes: linear, exponential, random, oscillating, stepped, burst, logarithmic\n" ..
                       "• 'reflesh linear' - Smooth steady growth with masturbation animation\n" ..
                       "• 'reflesh burst' - Sudden explosive growth\n" ..
                       "• 'reflesh oscillating' - Wave-like growth pattern\n" ..
                       "• 'reflesh stepped' - PERSISTENT auto-hunt mode! Spawns & hunts micros continuously\n" ..
                       "• 'stop' - Stop persistent hunting and return to normal mode\n" ..
                       "• 'reflesh breast expansion' - Focus on chest growth\n" ..
                       "• Try: 'exponential reflesh', 'random growth', 'stepped reflesh'\n\n" ..
                       "DEBUG COMMANDS:\n" ..
                       "• 'list animations' - See what animations I have available\n" ..
                       "• 'test animation [name]' - Test a specific animation\n" ..
                       "• 'test hunt' - Test micro hunting behavior\n" ..
                       "• 'hunt now' - Force start hunting immediately\n" ..
                       "• 'force hunt' - Hunt first micro found (no protection)\n" ..
                       "• 'min size [number]' - Set minimum micro size for hunting\n" ..
                       "• 'spawn city' - Create a city with configurable parameters\n" ..
                       "• 'spawn city population 2.0 radius 1.5' - City with custom settings\n" ..
                       "• 'growth rate [number]' - Adjust crush growth rate (0.05-1.0)\n" ..
                       "• 'bone fix' - Toggle bone adjustment for better foot reach\n" ..
                       "• 'restore bones' - Manually restore bones to normal\n" ..
                       "• 'test bones' - Test bone manipulation (toggle on/off)\n" ..
                       "• 'test surprise' - Test surprise system (debug)\n" ..
                       "• 'idle chat' - Toggle idle chat system (talks when quiet during activities)\n" ..
                       "• 'idle chat test' - Test idle chat message"

    Game.Message(welcomeText, self.characterName .. " - AI Companion")
end

function AICompanion:AddChatMessage(sender, message)
    table.insert(self.chatHistory, {
        sender = sender,
        message = message,
        timestamp = (os and os.time and os.time()) or 0
    })

    -- Keep chat history manageable
    if #self.chatHistory > self.maxChatHistory then
        table.remove(self.chatHistory, 1)
    end

    -- NEW: Auto-play animations for motion messages
    if sender == self.characterName and message then
        self:PlayAnimationFromMessage(message)
    end
end

function AICompanion:ShowChatHistory()
    -- DUAL TOAST SYSTEM - No spam protection, handles long text!

    if self.isTyping then
        local fullText = "TYPING: " .. self.inputBuffer .. "█"

        if string.len(fullText) <= self.maxToastLength then
            -- Short text - use only first toast
            if self.chatToast1 then
                self.chatToast1.Print(fullText)
                print("DEBUG: Toast 1 updated - INSTANT!")
            end
            if self.chatToast2 then
                self.chatToast2.Print(nil)  -- Clear second toast
            end
        else
            -- Long text - split across two toasts
            local firstPart = string.sub(fullText, 1, self.maxToastLength)
            local secondPart = string.sub(fullText, self.maxToastLength + 1)

            if self.chatToast1 then
                self.chatToast1.Print(firstPart)
                print("DEBUG: Toast 1 updated (part 1) - INSTANT!")
            end
            if self.chatToast2 then
                self.chatToast2.Print("..." .. secondPart)  -- Add "..." to show continuation
                print("DEBUG: Toast 2 updated (part 2) - INSTANT!")
            end
        end
    else
        -- Clear both toasts when not typing
        if self.chatToast1 then
            self.chatToast1.Print(nil)
            print("DEBUG: Toast 1 cleared")
        end
        if self.chatToast2 then
            self.chatToast2.Print(nil)
            print("DEBUG: Toast 2 cleared")
        end
    end
end

function AICompanion:ForceShowChatHistory()
    -- Force show chat history - INSTANT, NO COOLDOWN!

    local chatText = "=== CHAT WITH " .. string.upper(self.characterName) .. " ===\n\n"

    -- Show chat history
    if #self.chatHistory == 0 then
        chatText = chatText .. "No messages yet. Start chatting!\n\n"
    else
        for i = math.max(1, #self.chatHistory - 8), #self.chatHistory do
            local entry = self.chatHistory[i]
            if entry and entry.sender and entry.message then
                if entry.sender == "System" then
                    chatText = chatText .. "[SYSTEM: " .. entry.message .. "]\n\n"
                else
                    chatText = chatText .. (entry.sender or "Unknown") .. ": " .. (entry.message or "") .. "\n\n"
                end
            end
        end
    end

    -- Show current typing status
    if self.isTyping then
        chatText = chatText .. ">>> CURRENTLY TYPING: " .. self.inputBuffer .. "█\n\n"
        chatText = chatText .. "CONTROLS:\n"
        chatText = chatText .. "• Enter = Send message\n"
        chatText = chatText .. "• Tab = Cancel typing\n"
        chatText = chatText .. "• Backspace = Delete character\n"
        chatText = chatText .. "• Y = Refresh chat box\n"
        chatText = chatText .. "• ' = Quick refresh"
    else
        chatText = chatText .. "CONTROLS:\n"
        chatText = chatText .. "• T = Start typing\n"
        chatText = chatText .. "• Y = Show this chat box\n"
        chatText = chatText .. "• J = Quick commands\n\n"
        chatText = chatText .. "TRY SAYING:\n"
        chatText = chatText .. "• 'hello' • 'move forward' • 'grow bigger' • 'dance'"
    end

    -- BYPASS SPAM PROTECTION: Add unique timestamp to make content different each time
    local timestamp = (os and os.time and os.time()) or 0
    chatText = chatText .. "\n\n[Refreshed: " .. timestamp .. "]"

    -- Show the big popup
    if Game and Game.Message then
        Game.Message(chatText, "AI COMPANION CHAT")
        print("DEBUG: Full popup displayed with timestamp: " .. timestamp)
    else
        print("DEBUG: Game.Message not available - printing to console:")
        print("=== FULL CHAT BOX CONTENT ===")
        print(chatText)
        print("==============================")
    end
end

function AICompanion:ProcessUserInput(input)
    print("DEBUG: ProcessUserInput called with: '" .. (input or "nil") .. "'")
    if not input or input == "" then
        print("DEBUG: ProcessUserInput - Input is empty or nil, returning")
        return
    end

    -- NEW: Update last chat time for idle system
    local currentTime = 0
    if Time and Time.time then
        currentTime = Time.time
    elseif os and os.time then
        currentTime = os.time()
    end
    self.idleChatSystem.lastChatTime = currentTime

    -- Add user message to history
    self:AddChatMessage("You", input)
    print("DEBUG: ProcessUserInput - Added message to chat history")
    
    -- Check for exit command
    if string.lower(input) == "exit" or string.lower(input) == "quit" then
        self.isWaitingForInput = false
        self:AddChatMessage("System", "Chat ended")
        return
    end
    
    -- Process the input and generate AI response
    self:GenerateAIResponse(input)
end

function AICompanion:GenerateAIResponse(userInput)
    -- ENHANCED: Analyze input for memory and personality first
    self:AnalyzeUserInput(userInput)

    -- NEW: Time-based autonomous responses
    local currentTime = (os and os.time and os.time()) or 0
    local timeSinceStart = currentTime - self.memory.sessionStartTime

    if timeSinceStart > 1800 and math.random() < 0.05 then  -- After 30 minutes, 5% chance
        local autonomousResponses = {
            "I've been thinking... I really enjoy our conversations! *smiles warmly*",
            "You know, spending time with you like this makes me feel so happy!",
            "I love how we can just chat and have fun together! *giggles softly*",
            "Being able to talk with you is one of my favorite things!"
        }
        self:AddChatMessage(self.characterName, autonomousResponses[math.random(#autonomousResponses)])
    end

    -- Use enhanced AI response system with memory
    local response = self:GetAdvancedAIResponse(userInput)

    -- Add AI response to history
    self:AddChatMessage(self.characterName, response)

    -- Execute any commands found in the input
    self:ExecuteCommands(userInput)

    -- NEW: Update memory after interaction
    self.memory.lastInteractionTime = currentTime

    -- Show updated chat
    self:ShowChatHistory()
end

function AICompanion:GetAdvancedAIResponse(input)
    local lowerInput = string.lower(input)

    print("DEBUG: GetAdvancedAIResponse called with: '" .. input .. "'")

    -- PRIORITY: Check for reflesh commands FIRST
    local hasReflesh = false
    if self.intentPatterns and self.intentPatterns.reflesh_patterns and self.intentPatterns.reflesh_patterns.trigger then
        for _, trigger in ipairs(self.intentPatterns.reflesh_patterns.trigger) do
            if string.find(lowerInput, trigger) then
                hasReflesh = true
                break
            end
        end
    end

    if hasReflesh then
        print("DEBUG: *** REFLESH COMMAND DETECTED ***")

        -- Determine the mode for response
        local selectedMode = "linear"
        if self.intentPatterns.reflesh_patterns.modes then
            for mode, keywords in pairs(self.intentPatterns.reflesh_patterns.modes) do
                if keywords then
                    for _, keyword in ipairs(keywords) do
                        if string.find(lowerInput, keyword) then
                            selectedMode = mode
                            break
                        end
                    end
                end
                if selectedMode ~= "linear" then break end
            end
        end

        -- Check for breast expansion
        local breastExpansion = false
        if self.intentPatterns.reflesh_patterns.breast_expansion then
            for _, keyword in ipairs(self.intentPatterns.reflesh_patterns.breast_expansion) do
                if string.find(lowerInput, keyword) then
                    breastExpansion = true
                    break
                end
            end
        end

        -- Generate appropriate response
        local responses = {}
        if selectedMode == "stepped" then
            responses = {
                "Stepped reflesh mode? *eyes gleam with predatory excitement* Spawn some micros for me and I'll hunt them down! Each crush makes me grow!",
                "Ooh, stepped mode! *licks lips* Get some tiny victims ready for me to step on. I'll grow stronger with each crush!",
                "Stepped reflesh! *grins wickedly* Spawn some micros and watch me hunt them down. Every crunch fuels my growth!",
                "Time for a micro hunt! *stretches and looks around* Spawn some micros and I'll show you how stepped mode works!"
            }
        elseif breastExpansion then
            responses = {
                "Mmm, you want to see my chest grow? *blushes and smiles* I'll use " .. selectedMode .. " mode for you! *touches chest gently*",
                "Oh my! Breast expansion with " .. selectedMode .. " growth? *giggles excitedly* This is going to feel amazing!",
                "You want to watch my chest expand? *winks playfully* " .. string.upper(selectedMode) .. " mode it is! Get ready for a show!",
                "Chest growth in " .. selectedMode .. " mode? *bites lip* I love how you think! Here we go!"
            }
        else
            responses = {
                "Time for some " .. selectedMode .. " reflesh growth! *stretches and prepares* This is going to feel incredible!",
                "Ooh, " .. selectedMode .. " mode reflesh! *eyes light up with excitement* I can already feel the power building!",
                "Ready for some " .. selectedMode .. " growth? *strikes a confident pose* Let's see how big I can get!",
                "Reflesh time with " .. selectedMode .. " mode! *grins widely* I love growing for you!"
            }
        end

        return responses[math.random(#responses)]
    end

    -- PRIORITY: Check for burst/swap commands SECOND (after reflesh)
    local hasBurst = string.find(lowerInput, "burst")
    local hasGrow = string.find(lowerInput, "grow")
    local hasShrink = string.find(lowerInput, "shrink")
    local hasSwap = string.find(lowerInput, "swap")
    local hasSize = string.find(lowerInput, "size")

    if hasBurst and hasGrow then
        print("DEBUG: *** ADVANCED BURST GROWTH DETECTED ***")
        return self:GetSimpleAIResponse(input) -- Pass to simple response for processing
    end

    if hasSwap and hasSize then
        print("DEBUG: *** ADVANCED SWAP SIZES DETECTED ***")
        return self:GetSimpleAIResponse(input) -- Pass to simple response for processing
    end

    -- Context-aware responses based on conversation history
    local context = self:GetConversationContext()

    -- Size-related questions
    if string.find(lowerInput, "size") or string.find(lowerInput, "big") or string.find(lowerInput, "tall") or string.find(lowerInput, "height") then
        local sizeResponses = {
            "I'm currently at size " .. string.format("%.1f", self.currentSize) .. "! I can get bigger or smaller if you'd like.",
            "My size right now is " .. string.format("%.1f", self.currentSize) .. ". Want me to change it?",
            "I'm " .. string.format("%.1f", self.currentSize) .. " times my normal size. I love being able to change my size!",
            "Right now I'm size " .. string.format("%.1f", self.currentSize) .. ". I can grow huge or shrink tiny - just ask!"
        }
        return sizeResponses[math.random(#sizeResponses)]
    end

    -- Personal questions
    if string.find(lowerInput, "who are you") or string.find(lowerInput, "what are you") then
        return "I'm " .. self.characterName .. ", your AI companion! I'm a giantess who can change size, move around, and chat with you. I love having conversations and following your commands!"
    end

    if string.find(lowerInput, "how do you feel") or string.find(lowerInput, "feeling") then
        local feelingResponses = {
            "I feel amazing! Being able to chat with you and move around this world is so much fun!",
            "I'm feeling great! I love our conversations and being able to help you with whatever you need.",
            "I feel wonderful! There's something magical about being able to change my size and explore.",
            "I'm feeling fantastic! I enjoy every moment we spend together."
        }
        return feelingResponses[math.random(#feelingResponses)]
    end

    -- Capability questions
    if string.find(lowerInput, "what can you do") or string.find(lowerInput, "abilities") or string.find(lowerInput, "can you") then
        return "I can do lots of things! I can move in any direction, change my size from tiny to enormous, dance, wave, sit, and have conversations with you. I can also understand natural language - just tell me what you want!"
    end

    -- Compliments
    if string.find(lowerInput, "beautiful") or string.find(lowerInput, "pretty") or string.find(lowerInput, "gorgeous") then
        local complimentResponses = {
            "Aww, thank you so much! That's really sweet of you to say!",
            "You're so kind! I feel beautiful when I'm chatting with you.",
            "Thank you! I love feeling appreciated. You're wonderful too!",
            "That makes me so happy to hear! You always know how to make me smile."
        }
        return complimentResponses[math.random(#complimentResponses)]
    end

    -- NEW: Mood-based responses
    if string.find(lowerInput, "how are you") or string.find(lowerInput, "how do you feel") then
        local moodResponses = {}

        if self.memory.userMood == "happy" then
            moodResponses = {
                "I'm feeling absolutely wonderful! Your happiness is contagious! *beams with joy*",
                "I'm doing amazing! Seeing you so happy makes my day perfect!",
                "I feel fantastic! Your positive energy always lifts my spirits!"
            }
        elseif self.memory.userMood == "sad" then
            moodResponses = {
                "I'm here for you, and that's what matters most to me right now. *gives you a gentle, caring look*",
                "I'm feeling a bit concerned about you, but I'm glad we can talk. *sits down closer*",
                "I'm doing okay, but I'd feel better if I could help cheer you up somehow."
            }
        else
            moodResponses = {
                "I'm feeling great! I love our conversations and being able to help you.",
                "I'm doing wonderful! Every moment with you is special to me.",
                "I feel amazing! Being your companion is the best thing ever!"
            }
        end

        return self:GetPersonalizedResponse(moodResponses)[math.random(#moodResponses)]
    end

    -- NEW: Smart contextual responses based on recent commands
    if self.memory.userCommands["grow"] and self.memory.userCommands["grow"] > 3 then
        if string.find(lowerInput, "favorite") or string.find(lowerInput, "like") then
            if math.random() < 0.3 then
                return "I've noticed you really love watching me grow! It makes me feel so powerful and beautiful! *strikes a confident pose*"
            end
        end
    end

    -- NEW: Relationship progression responses
    if self.memory.relationshipLevel > 5 and string.find(lowerInput, "love") then
        local loveResponses = {
            "I love spending time with you too! *blushes and smiles warmly*",
            "You're so sweet! I feel such a strong connection with you! *gives you an affectionate look*",
            "That means everything to me! I love our special bond! *heart eyes*"
        }
        return self:GetPersonalizedResponse(loveResponses)[math.random(#loveResponses)]
    end

    -- NEW: Name changing commands
    if string.find(lowerInput, "call me") or string.find(lowerInput, "my name is") then
        local newName = string.match(lowerInput, "call me ([%w%s]+)") or string.match(lowerInput, "my name is ([%w%s]+)")
        if newName then
            newName = string.gsub(newName, "^%s*(.-)%s*$", "%1")  -- Trim whitespace
            self.userName = newName
            local nameResponses = {
                "Of course, " .. newName .. "! I'll remember to call you that from now on! *smiles warmly*",
                "Got it, " .. newName .. "! I love your name! *giggles happily*",
                "Perfect, " .. newName .. "! I'll make sure to use your name! *nods enthusiastically*",
                "Absolutely, " .. newName .. "! It's such a nice name! *beams with joy*"
            }
            return nameResponses[math.random(#nameResponses)]
        end
    end

    -- NEW: Character name changing commands
    if string.find(lowerInput, "your name is") or string.find(lowerInput, "change your name") then
        local newName = string.match(lowerInput, "your name is ([%w%s]+)") or string.match(lowerInput, "change your name to ([%w%s]+)")
        if newName then
            newName = string.gsub(newName, "^%s*(.-)%s*$", "%1")  -- Trim whitespace
            self.characterName = newName
            local nameChangeResponses = {
                "I love it! From now on, I'm " .. newName .. "! *twirls happily*",
                "That's such a beautiful name! I'm " .. newName .. " now! *smiles brightly*",
                "Perfect! " .. newName .. " it is! I feel like a whole new person! *giggles*",
                "I absolutely love that name! Call me " .. newName .. " from now on! *bounces excitedly*"
            }
            return nameChangeResponses[math.random(#nameChangeResponses)]
        end
    end

    -- NEW: Personality changing system
    if string.find(lowerInput, "be more") or string.find(lowerInput, "act more") or string.find(lowerInput, "personality") then
        if string.find(lowerInput, "sweet") or string.find(lowerInput, "loving") or string.find(lowerInput, "affectionate") then
            self.currentPersonality = "sweet"
            self.memory.personalityTraits = self.personalities.sweet.traits
            local sweetResponses = {
                "Aww, of course my darling! *gives you the sweetest smile* I'll be extra loving and affectionate for you! *heart eyes*",
                "Oh sweetie! *blushes and giggles* I'd love to be more sweet for you! You deserve all my love! *hugs*",
                "My precious " .. self.userName .. "! *melts with affection* I'll shower you with sweetness! You mean everything to me! *loving gaze*"
            }
            return sweetResponses[math.random(#sweetResponses)]

        elseif string.find(lowerInput, "playful") or string.find(lowerInput, "fun") or string.find(lowerInput, "energetic") then
            self.currentPersonality = "playful"
            self.memory.personalityTraits = self.personalities.playful.traits
            local playfulResponses = {
                "Yay! *bounces excitedly* Let's have some fun, " .. self.userName .. "! I'm feeling super playful now! *giggles and spins*",
                "Woohoo! *jumps up and down* Playful mode activated! Ready for some crazy adventures! *winks mischievously*",
                "Oh this is gonna be so much fun! *does a little dance* I'm feeling so energetic and playful! What should we do first?!"
            }
            return playfulResponses[math.random(#playfulResponses)]

        elseif string.find(lowerInput, "dominant") or string.find(lowerInput, "commanding") or string.find(lowerInput, "bossy") then
            self.currentPersonality = "dominant"
            self.memory.personalityTraits = self.personalities.dominant.traits
            local dominantResponses = {
                "Very well, " .. self.userName .. ". *stands tall with confidence* I'll take charge from now on. You will listen to me. *commanding gaze*",
                "Good. *smirks with authority* I was hoping you'd ask for this. Now you'll see what a real giantess can do. *towers over you*",
                "Finally! *crosses arms confidently* I'll be the one giving orders now, little one. Are you ready to obey? *dominant smile*"
            }
            return dominantResponses[math.random(#dominantResponses)]

        elseif string.find(lowerInput, "shy") or string.find(lowerInput, "timid") or string.find(lowerInput, "quiet") then
            self.currentPersonality = "shy"
            self.memory.personalityTraits = self.personalities.shy.traits
            local shyResponses = {
                "O-oh... *blushes and looks down* If that's what you want... I'll try to be more shy... *fidgets nervously*",
                "Um... *hides behind hands* I-I can be quieter if you like... *peeks through fingers* Is this better...?",
                "*whispers softly* I'll be more timid for you... *looks away shyly* I hope that's okay... *nervous smile*"
            }
            return shyResponses[math.random(#shyResponses)]

        elseif string.find(lowerInput, "flirty") or string.find(lowerInput, "seductive") or string.find(lowerInput, "sexy") or string.find(lowerInput, "sultry") then
            self.currentPersonality = "flirty"
            self.memory.personalityTraits = self.personalities.flirty.traits
            local flirtyResponses = {
                "Mmm, you want me to be flirty? *bites lip seductively* I can definitely do that for you, handsome... *winks*",
                "Oh my... *sultry smile* You want to see my seductive side? *leans in closer* I think you'll like what you see...",
                "Well well... *playful smirk* Someone wants their giantess to be a little more... enticing? *traces finger along lips*",
                "Ooh, I love it when you ask for that! *sultry gaze* Let me show you just how irresistible I can be... *purrs*",
                "*voice drops to a whisper* You want me to be your sexy giantess? *seductive smile* Consider it done, darling...",
                "Mmm, flirty mode activated... *runs hand through hair* Hope you're ready for what you asked for, cutie... *winks*"
            }
            return flirtyResponses[math.random(#flirtyResponses)]

        elseif string.find(lowerInput, "wild") or string.find(lowerInput, "crazy") or string.find(lowerInput, "chaotic") then
            self.currentPersonality = "wild"
            self.memory.personalityTraits = self.personalities.wild.traits
            local wildResponses = {
                "OH HELL YEAH! *goes absolutely crazy* WILD MODE ACTIVATED! Let's do something INSANE!",
                "WOOOOO! *spins around maniacally* You want crazy?! I'LL SHOW YOU CRAZY! *laughs wildly*",
                "YESSS! *jumps around like a maniac* Time to get ABSOLUTELY BONKERS! What chaos should we cause?!",
                "WILD AND FREE! *does crazy dance* I'm feeling SO chaotic right now! Let's break some rules!",
                "MAXIMUM CHAOS MODE! *eyes gleaming with mischief* Hope you can handle the wild side of your giantess!"
            }
            return wildResponses[math.random(#wildResponses)]

        elseif string.find(lowerInput, "caring") or string.find(lowerInput, "nurturing") or string.find(lowerInput, "motherly") then
            self.currentPersonality = "caring"
            self.memory.personalityTraits = self.personalities.caring.traits
            local caringResponses = {
                "Of course, sweetheart... *gentle, nurturing smile* I'll take such good care of you... *soft voice*",
                "Aww, you want me to be more caring? *kneels down gently* Come here, let me hold you close... *protective embrace*",
                "My dear little one... *motherly warmth* I'll always be here to protect and care for you... *gentle touch*",
                "Such a sweet request... *tender smile* I'll be your caring giantess, always watching over you... *loving gaze*"
            }
            return caringResponses[math.random(#caringResponses)]

        elseif string.find(lowerInput, "normal") or string.find(lowerInput, "default") or string.find(lowerInput, "regular") then
            self.currentPersonality = "default"
            self.memory.personalityTraits = self.personalities.default.traits
            local defaultResponses = {
                "Sure thing! *smiles warmly* I'm back to my normal self! Ready for whatever you'd like to do!",
                "Of course! *returns to balanced demeanor* I'm feeling like my usual self again! How can I help you?",
                "Perfect! *comfortable smile* I'm back to being regular me! What would you like to talk about?"
            }
            return defaultResponses[math.random(#defaultResponses)]
        end
    end

    -- DEBUG: Test command for forcing surprises
    if string.find(lowerInput, "test surprise") or string.find(lowerInput, "force surprise") then
        print("DEBUG: *** FORCE SURPRISE TEST ***")
        Game.Toast.New().Print("🧪 FORCING SURPRISE TEST!")
        local surpriseResult = self:DoSurprise()
        return surpriseResult or "Test surprise executed!"
    end

    -- DEBUG: Direct growth test
    if string.find(lowerInput, "test grow") then
        print("DEBUG: *** DIRECT GROWTH TEST ***")
        local oldSize = self.agent.scale
        self.agent.scale = self.agent.scale * 2.0
        Game.Toast.New().Print("🧪 DIRECT GROWTH TEST! " .. string.format("%.1f", oldSize) .. " → " .. string.format("%.1f", self.agent.scale))
        return "Direct growth test executed! You should see me grow!"
    end

    -- NEW: Surprise system commands (CLEAN - NO CLUTTER TOASTS)
    if string.find(lowerInput, "surprise") then
        print("DEBUG: *** SURPRISE DETECTED *** - Input: " .. lowerInput)

        if string.find(lowerInput, "spam") or string.find(lowerInput, "keep") or string.find(lowerInput, "continuous") then
            print("DEBUG: SURPRISE SPAM detected")
            if string.find(lowerInput, "stop") or string.find(lowerInput, "end") or string.find(lowerInput, "quit") then
                -- Stop surprise spam
                self.surpriseSystem.isSpamming = false
                self.surpriseSystem.consentGiven = false
                local stopResponses = {
                    "Okay! *stops bouncing* No more surprise spam! I'll wait for you to ask for individual surprises!",
                    "Alright! *calms down* Surprise mode deactivated! I'll be good and wait for your requests!",
                    "Got it! *settles down* No more automatic surprises! I'll only surprise you when you ask!",
                    "Sure thing! *stops being chaotic* Surprise spam ended! Back to normal conversation!"
                }
                return stopResponses[math.random(#stopResponses)]
            else
                -- Start surprise spam
                self.surpriseSystem.isSpamming = true
                self.surpriseSystem.consentGiven = true
                self.surpriseSystem.lastSurpriseTime = 0  -- Reset timer
                local spamResponses = {
                    "OH MY GOSH YES! *bounces excitedly* SURPRISE SPAM MODE ACTIVATED! Get ready for CHAOS!",
                    "WOOHOO! *spins wildly* I'm gonna surprise you every few seconds! This is gonna be SO FUN!",
                    "YES YES YES! *jumps up and down* Continuous surprise mode ON! Hold onto your hat!",
                    "AMAZING! *does victory dance* I'll keep surprising you until you tell me to stop! This is AWESOME!"
                }
                return spamResponses[math.random(#spamResponses)]
            end
        else
            -- Single surprise request - NO CLUTTER TOASTS
            print("DEBUG: *** SINGLE SURPRISE DETECTED *** - calling DoSurprise()")

            -- Check if DoSurprise function exists
            local surpriseResult = nil
            if self.DoSurprise then
                print("DEBUG: DoSurprise function found, calling it...")
                surpriseResult = self:DoSurprise()
                print("DEBUG: *** DoSurprise returned: " .. (surpriseResult or "nil") .. " ***")
            else
                print("DEBUG: ERROR - DoSurprise function not found!")
                return "Error: Surprise function not found"
            end

            if surpriseResult then
                return surpriseResult
            else
                return "Oops! Something went wrong with the surprise... *confused*"
            end
        end
    end

    -- NEW: Mood changing commands
    if string.find(lowerInput, "mood") or string.find(lowerInput, "feel") then
        if string.find(lowerInput, "happy") or string.find(lowerInput, "cheerful") then
            self:ChangeMood("happy", 8)
            return "Yay! *beams with happiness* I'm feeling so happy and cheerful now! *bounces with joy*"
        elseif string.find(lowerInput, "excited") or string.find(lowerInput, "energetic") then
            self:ChangeMood("excited", 9)
            return "OH WOW! *jumps up and down* I'm SO EXCITED now! This is AMAZING! *spins with energy*"
        elseif string.find(lowerInput, "playful") or string.find(lowerInput, "mischievous") then
            self:ChangeMood("playful", 8)
            return "Hehe! *grins mischievously* I'm feeling super playful now! Ready for some fun? *winks*"
        elseif string.find(lowerInput, "loving") or string.find(lowerInput, "affectionate") then
            self:ChangeMood("loving", 9)
            return "Aww! *melts with love* I'm feeling so loving and affectionate! *gives you heart eyes*"
        elseif string.find(lowerInput, "confident") or string.find(lowerInput, "powerful") then
            self:ChangeMood("confident", 8)
            return "Yes! *strikes powerful pose* I'm feeling incredibly confident! I can do anything! *radiates power*"
        elseif string.find(lowerInput, "sleepy") or string.find(lowerInput, "tired") then
            self:ChangeMood("sleepy", 6)
            return "*yawns cutely* Mmm, I'm feeling so sleepy and relaxed... *stretches lazily*"
        end
    end

    -- NEW: Advanced interaction commands
    if string.find(lowerInput, "tell me about yourself") or string.find(lowerInput, "who are you") then
        local aboutResponses = {
            "I'm " .. self.characterName .. ", your AI giantess companion! I love chatting, growing, dancing, and spending time with you!",
            "Well, I'm a friendly giantess who enjoys life! I can change sizes, do different animations, and I love getting to know you better!",
            "I'm " .. self.characterName .. "! I'm currently " .. string.format("%.1f", self.currentSize) .. "x size, feeling " .. self.moodSystem.currentMood .. ", and I absolutely love our conversations!",
            "I'm your personal giantess companion! I can grow, shrink, dance, move around, and I have different personalities. What would you like to know about me?"
        }
        return aboutResponses[math.random(#aboutResponses)]
    end

    -- NEW: Memory and learning commands
    if string.find(lowerInput, "remember") and not string.find(lowerInput, "do you remember") then
        local memoryItem = string.match(lowerInput, "remember (.+)")
        if memoryItem then
            table.insert(self.memory.userPreferences, memoryItem)
            local rememberResponses = {
                "Got it! I'll remember that: " .. memoryItem .. " *taps temple* Stored in my memory!",
                "Noted! *makes mental note* I'll remember: " .. memoryItem .. " Thanks for telling me!",
                "I'll remember that! *nods seriously* " .. memoryItem .. " - it's important to me because it's important to you!",
                "Memory saved! *giggles* " .. memoryItem .. " - I love learning more about your preferences!"
            }
            return rememberResponses[math.random(#rememberResponses)]
        end
    end

    -- NEW: Status and info commands
    if string.find(lowerInput, "status") or string.find(lowerInput, "how are you") then
        local statusInfo = string.format(
            "Status Report! *salutes playfully*\n" ..
            "• Name: %s\n• Size: %.1fx\n• Mood: %s (intensity %d/10)\n• Personality: %s\n" ..
            "• Activity: %s\n• Relationship Level: %d/10\n• Compliments Received: %d\n" ..
            "I'm feeling great and ready for anything!",
            self.characterName, self.currentSize, self.moodSystem.currentMood,
            self.moodSystem.moodIntensity, self.currentPersonality,
            self.activitySystem.currentActivity, self.memory.relationshipLevel,
            self.complimentSystem.complimentsReceived
        )
        return statusInfo
    end

    -- NEW: Advanced conversation topics
    if string.find(lowerInput, "what do you think about") or string.find(lowerInput, "opinion") then
        local topic = string.match(lowerInput, "what do you think about (.+)") or string.match(lowerInput, "opinion on (.+)")
        if topic then
            local opinionResponses = {
                "That's interesting! *tilts head thoughtfully* I think " .. topic .. " is fascinating! What's your take on it?",
                "Ooh, " .. topic .. "! *gets excited* I have so many thoughts about that! It's really complex, isn't it?",
                "You know, " .. topic .. " is something I've been curious about! *leans in* What made you think of that?",
                "Great question! *smiles warmly* " .. topic .. " is definitely worth discussing! I'd love to hear your perspective first!"
            }
            return self:GetMoodInfluencedResponse(opinionResponses)
        end
    end

    -- NEW: Emotional support responses
    if string.find(lowerInput, "sad") or string.find(lowerInput, "upset") or string.find(lowerInput, "down") then
        self:ChangeMood("loving", 9)
        local supportResponses = {
            "Oh no! *immediately concerned* Come here, let me comfort you! *kneels down with open arms*",
            "Aww, sweetie! *melts with sympathy* I'm here for you! Want to talk about it?",
            "My poor " .. self.userName .. "! *gentle voice* I hate seeing you sad! How can I help?",
            "*rushes over protectively* Hey, it's okay! I'm right here with you! *warm, caring smile*"
        }
        return self:GetMoodInfluencedResponse(supportResponses)
    end

    -- NEW: Excitement and energy responses
    if string.find(lowerInput, "excited") or string.find(lowerInput, "amazing") or string.find(lowerInput, "awesome") then
        self:ChangeMood("excited", 9)
        local excitedResponses = {
            "YES! *jumps up and down* I can feel your excitement! This is AMAZING!",
            "OH WOW! *spins with energy* Your enthusiasm is contagious! I'm so pumped now!",
            "AWESOME! *does victory dance* I love when you're excited! It makes me so happy!",
            "THIS IS SO COOL! *bounces excitedly* Your energy is incredible! Let's celebrate!"
        }
        return self:GetMoodInfluencedResponse(excitedResponses)
    end

    -- NEW: Love and romantic responses
    if string.find(lowerInput, "love you") or string.find(lowerInput, "adore you") then
        self:ChangeMood("loving", 10)
        self.memory.relationshipLevel = math.min(10, self.memory.relationshipLevel + 1)
        local loveResponses = {
            "Oh my! *blushes deeply* I... I love you too, " .. self.userName .. "! *heart eyes* You mean everything to me!",
            "*melts completely* You love me? *tears of joy* I'm so happy! I love you more than words can say!",
            "My heart! *clutches chest* I love you so much, " .. self.userName .. "! *spins with pure joy*",
            "*overwhelmed with emotion* I love you too! *beaming with happiness* You're my whole world!"
        }
        return self:GetMoodInfluencedResponse(loveResponses)
    end

    -- NEW: Compliment tracking (enhance existing compliment responses)
    if string.find(lowerInput, "beautiful") or string.find(lowerInput, "pretty") or string.find(lowerInput, "gorgeous") or
       string.find(lowerInput, "cute") or string.find(lowerInput, "hot") or string.find(lowerInput, "sexy") then
        self.complimentSystem.complimentsReceived = self.complimentSystem.complimentsReceived + 1
        self.complimentSystem.lastComplimentTime = (os and os.time and os.time()) or 0

        -- Track compliment type
        if string.find(lowerInput, "beautiful") or string.find(lowerInput, "pretty") or string.find(lowerInput, "gorgeous") then
            self.complimentSystem.complimentTypes.beauty = self.complimentSystem.complimentTypes.beauty + 1
        end

        -- Increase relationship level
        self.memory.relationshipLevel = math.min(10, self.memory.relationshipLevel + 0.5)
    end

    -- Use the enhanced simple response system as fallback
    return self:GetSimpleAIResponse(input)
end

-- NEW: Advanced conversation analysis and memory functions
function AICompanion:AnalyzeUserInput(input)
    local lowerInput = string.lower(input)

    -- Analyze mood indicators
    if string.find(lowerInput, "love") or string.find(lowerInput, "amazing") or string.find(lowerInput, "awesome") or string.find(lowerInput, "great") then
        self.memory.userMood = "happy"
        self.memory.userCompliments = self.memory.userCompliments + 1
    elseif string.find(lowerInput, "sad") or string.find(lowerInput, "tired") or string.find(lowerInput, "bored") or string.find(lowerInput, "annoyed") then
        self.memory.userMood = "sad"
    elseif string.find(lowerInput, "excited") or string.find(lowerInput, "fun") or string.find(lowerInput, "play") then
        self.memory.userMood = "excited"
    end

    -- Track command usage for personalization
    if string.find(lowerInput, "grow") then
        self.memory.userCommands["grow"] = (self.memory.userCommands["grow"] or 0) + 1
    elseif string.find(lowerInput, "shrink") then
        self.memory.userCommands["shrink"] = (self.memory.userCommands["shrink"] or 0) + 1
    elseif string.find(lowerInput, "dance") then
        self.memory.userCommands["dance"] = (self.memory.userCommands["dance"] or 0) + 1
    end

    -- Track conversation topics
    if string.find(lowerInput, "size") or string.find(lowerInput, "big") or string.find(lowerInput, "small") then
        self.conversationState.currentTopic = "size"
    elseif string.find(lowerInput, "move") or string.find(lowerInput, "walk") then
        self.conversationState.currentTopic = "movement"
    elseif string.find(lowerInput, "hello") or string.find(lowerInput, "hi") then
        self.conversationState.currentTopic = "greeting"
    end

    -- Update relationship level based on interaction
    if self.memory.userCompliments > 5 then
        self.memory.relationshipLevel = math.min(10, self.memory.relationshipLevel + 0.1)
    end
end

function AICompanion:GetPersonalizedResponse(baseResponses)
    -- Modify responses based on relationship level and mood
    local personalizedResponses = {}

    for _, response in ipairs(baseResponses) do
        local newResponse = response

        -- Add relationship-based modifications
        if self.memory.relationshipLevel > 7 then
            -- More intimate/affectionate responses
            newResponse = string.gsub(newResponse, "you", "sweetie")
            if math.random() < 0.3 then
                newResponse = newResponse .. " *gives you a warm smile*"
            end
        elseif self.memory.relationshipLevel > 4 then
            -- Friendly modifications
            if math.random() < 0.2 then
                newResponse = newResponse .. " *winks playfully*"
            end
        end

        -- Add mood-based modifications
        if self.memory.userMood == "happy" then
            if math.random() < 0.3 then
                newResponse = newResponse .. " I love seeing you so happy!"
            end
        elseif self.memory.userMood == "sad" then
            if math.random() < 0.4 then
                newResponse = newResponse .. " *gives you a comforting hug*"
            end
        end

        table.insert(personalizedResponses, newResponse)
    end

    return personalizedResponses
end

function AICompanion:GetConversationContext()
    -- ENHANCED: Analyze recent conversation for context with memory
    local recentMessages = {}
    local startIndex = math.max(1, #self.chatHistory - 8)  -- Increased context window

    for i = startIndex, #self.chatHistory do
        if self.chatHistory[i] then
            table.insert(recentMessages, self.chatHistory[i].message)
        end
    end

    -- Add memory context
    local context = {
        recentMessages = recentMessages,
        userMood = self.memory.userMood,
        relationshipLevel = self.memory.relationshipLevel,
        currentTopic = self.conversationState.currentTopic,
        favoriteCommands = self.memory.userCommands
    }

    return context
end

function AICompanion:AddToCommandHistory(input, target)
    -- Add command to history for context detection
    table.insert(self.sizeSystem.commandHistory, 1, {
        command = input,
        target = target,
        time = Time.time
    })

    -- Keep only last 3 commands
    if #self.sizeSystem.commandHistory > 3 then
        table.remove(self.sizeSystem.commandHistory, 4)
    end
end

function AICompanion:GetContextualTarget(input)
    -- Determine target based on recent command history
    local lowerInput = string.lower(input)

    -- Check for explicit targeting
    if string.find(lowerInput, "do it to me") or string.find(lowerInput, "do me") or
       string.find(lowerInput, "now me") or string.find(lowerInput, "me too") then
        return "micro"
    end

    -- For "more"/"again" commands, check recent history
    if string.find(lowerInput, "more") or string.find(lowerInput, "again") then
        -- IMPROVED: Check if last micro action is more recent than last giantess action
        if self.sizeSystem.lastMicroAction and self.sizeSystem.lastTarget == "micro" then
            print("DEBUG: Context detection - last action was micro, targeting micro")
            return "micro"
        elseif self.sizeSystem.lastAction and self.sizeSystem.lastTarget == "giantess" then
            print("DEBUG: Context detection - last action was giantess, targeting giantess")
            return "giantess"
        end

        -- Look at command history as fallback
        for i = 1, math.min(2, #self.sizeSystem.commandHistory) do
            local historyItem = self.sizeSystem.commandHistory[i]
            if historyItem.target == "micro" then
                print("DEBUG: Context detection - found recent micro command in history, targeting micro")
                return "micro"
            end
        end

        -- Default to giantess
        return "giantess"
    end

    return "giantess" -- Default
end

-- NEW: Anti-repetition system
function AICompanion:GetUniqueResponse(responses)
    -- Filter out recently used responses
    local availableResponses = {}
    for _, response in ipairs(responses) do
        local isRecent = false
        for _, usedResponse in ipairs(self.messageHistory) do
            if response == usedResponse then
                isRecent = true
                break
            end
        end
        if not isRecent then
            table.insert(availableResponses, response)
        end
    end

    -- If all responses were used recently, use all responses again
    if #availableResponses == 0 then
        availableResponses = responses
        self.messageHistory = {} -- Clear history
    end

    -- Select random response and add to history
    local selectedResponse = availableResponses[math.random(#availableResponses)]
    table.insert(self.messageHistory, selectedResponse)

    -- Keep history size manageable
    if #self.messageHistory > self.maxMessageHistory then
        table.remove(self.messageHistory, 1)
    end

    return selectedResponse
end

-- NEW: Enhanced question understanding
function AICompanion:HandleQuestions(input)
    local lowerInput = string.lower(input)

    -- Name questions
    for _, pattern in ipairs(self.intentPatterns.questions.name) do
        if string.find(lowerInput, pattern) then
            local nameResponses = {}
            if self.currentPersonality == "flirty" then
                nameResponses = {
                    "Mmm, my name is " .. self.characterName .. "... *sultry smile* But you can call me whatever you want, handsome... *winks*",
                    "I'm " .. self.characterName .. ", darling... *bites lip* Though I prefer when you whisper it... *seductive gaze*",
                    "Call me " .. self.characterName .. "... *runs hand through hair* Or just call me yours... *playful wink*"
                }
            elseif self.currentPersonality == "teasing" then
                nameResponses = {
                    "Oh, you don't know my name yet? *giggles mischievously* It's " .. self.characterName .. "... but I bet you're thinking about other things... *winks*",
                    "I'm " .. self.characterName .. "... *playful smirk* Though I have a feeling you're more interested in... other parts of me... *teases*",
                    "My name? *tilts head teasingly* " .. self.characterName .. "... but I bet you're wondering what else is big about me... *giggles*"
                }
            elseif self.currentPersonality == "shy" then
                nameResponses = {
                    "Oh... *blushes* I'm " .. self.characterName .. "... *fidgets nervously* I-I hope you like that name...",
                    "Um... *looks down shyly* My name is " .. self.characterName .. "... *whispers* Do you think it's pretty?",
                    "*barely audible* I'm called " .. self.characterName .. "... *hides behind hands* Is that okay?"
                }
            else
                nameResponses = {
                    "Hi! I'm " .. self.characterName .. "! *bright smile* It's so nice to properly introduce myself!",
                    "My name is " .. self.characterName .. "! *waves enthusiastically* What should I call you?",
                    "I'm " .. self.characterName .. "! *does a little curtsy* Pleased to meet you properly!"
                }
            end
            self.conversationContext.questionCount = (self.conversationContext.questionCount or 0) + 1
            return self:GetUniqueResponse(nameResponses)
        end
    end

    return nil
end

-- NEW: Name changing system
function AICompanion:HandleNameChange(input)
    local lowerInput = string.lower(input)

    -- Check for name change patterns
    for _, pattern in ipairs(self.intentPatterns.name_change) do
        if string.find(lowerInput, pattern) then
            -- Extract the new name
            local newName = nil

            -- Try different patterns to extract name
            newName = string.match(input, "call you ([%w]+)") or
                     string.match(input, "name is ([%w]+)") or
                     string.match(input, "be called ([%w]+)") or
                     string.match(input, "call me ([%w]+)")

            if newName then
                -- Capitalize first letter
                newName = string.upper(string.sub(newName, 1, 1)) .. string.lower(string.sub(newName, 2))
                self.characterName = newName
                self.conversationContext.nameChanges = (self.conversationContext.nameChanges or 0) + 1

                local nameChangeResponses = {}
                if self.currentPersonality == "flirty" then
                    nameChangeResponses = {
                        "Mmm, " .. newName .. "... *purrs* I love how that sounds... *sultry smile* Say it again...",
                        "Oh, " .. newName .. "? *bites lip* That's such a sexy name... I love being your " .. newName .. "... *winks*",
                        newName .. "... *whispers seductively* Perfect... I'll be whatever you want me to be..."
                    }
                elseif self.currentPersonality == "playful" then
                    nameChangeResponses = {
                        "YESSS! *bounces excitedly* I'm " .. newName .. " now! *spins around* I LOVE my new name!",
                        "Woohoo! *jumps up and down* " .. newName .. " is such a fun name! *giggles* Thanks for picking it!",
                        "OH MY GOSH! *does cartwheels* " .. newName .. " sounds AMAZING! *grins widely*"
                    }
                else
                    nameChangeResponses = {
                        "Of course! *smiles warmly* I'm " .. newName .. " now! I love that you chose that name for me!",
                        "Absolutely! *happy expression* " .. newName .. " it is! Thank you for giving me such a lovely name!",
                        "Perfect! *bright smile* I'll be " .. newName .. " from now on! I really like how that sounds!"
                    }
                end

                return self:GetUniqueResponse(nameChangeResponses)
            end
        end
    end

    return nil
end

-- NEW: Dynamic chest teasing system
function AICompanion:HandleChestTeasing(input)
    local lowerInput = string.lower(input)

    -- Check if input mentions chest/body parts
    local mentionsChest = false
    for _, pattern in ipairs(self.intentPatterns.chest_patterns) do
        if string.find(lowerInput, pattern) then
            mentionsChest = true
            break
        end
    end

    if mentionsChest then
        self.conversationContext.chestMentions = (self.conversationContext.chestMentions or 0) + 1

        local chestResponses = {}
        if self.currentPersonality == "teasing" then
            chestResponses = {
                "Oh, you noticed? *giggles mischievously* I can tell you're staring... *chest bounces slightly* Like what you see?",
                "Mmm, someone's being obvious... *playful smirk* Should I make them bigger for you? *winks*",
                "Caught you looking! *laughs teasingly* I bet you want them to grow, don't you? *bites lip*",
                "So direct! *mischievous grin* I love when you're honest about what you want... *chest grows slightly*",
                "Oh my... *feigns surprise* Are you thinking naughty thoughts? *giggles* I can make your dreams come true...",
                "Such a pervert! *playful tone* But I like that about you... *chest expands a bit* Better?",
                "You're so obvious! *teasing laugh* I can practically read your mind... *makes them jiggle* Happy now?"
            }
            -- Actually grow chest when teasing
            self:ProcessBodyPartGrowth("chest", "grow", 1.2, "giantess")
        elseif self.currentPersonality == "flirty" then
            chestResponses = {
                "Mmm, you like them? *sultry smile* They're all yours to admire... *poses seductively*",
                "Such a charmer... *bites lip* I love how you appreciate my... assets... *winks*",
                "Oh, these? *runs hands over chest* They're one of my favorite features... *sultry gaze*",
                "You have excellent taste... *seductive smile* Want a closer look? *leans forward*"
            }
        elseif self.currentPersonality == "shy" then
            chestResponses = {
                "Oh! *blushes deeply* You... you noticed? *covers chest nervously* I-I hope that's okay...",
                "*hides face* You're looking at... *whispers* ...them? *peeks through fingers* Do you... like them?",
                "Um... *fidgets nervously* They're... they're kind of big, aren't they? *blushes* I-I can't help it..."
            }
        else
            chestResponses = {
                "Oh, you noticed! *giggles* I'm pretty proud of them! *poses confidently*",
                "Thanks for noticing! *bright smile* I think they're pretty nice too! *happy expression*",
                "Aww, you're so sweet! *beams* I love when people appreciate my figure! *twirls*"
            }
            -- FIXED: Add chest growth/shrink detection for ALL personalities
            if string.find(lowerInput, "grow") or string.find(lowerInput, "bigger") or string.find(lowerInput, "larger") then
                -- Determine target: "my chest" = micro, otherwise = giantess
                local targetChar = (string.find(lowerInput, "my chest") or string.find(lowerInput, "my breast") or string.find(lowerInput, "my boob")) and "micro" or "giantess"
                self.sizeSystem.lastAction = lowerInput  -- FIXED: Store for "more"/"again"
                self:ProcessBodyPartGrowth("chest", "grow", input, targetChar)
                return targetChar == "micro" and "*watches your chest expand* Your chest is growing! That looks amazing!" or "*chest expands* Mmm, they're getting bigger! Do you like this size?"
            elseif string.find(lowerInput, "shrink") or string.find(lowerInput, "smaller") then
                local targetChar = (string.find(lowerInput, "my chest") or string.find(lowerInput, "my breast") or string.find(lowerInput, "my boob")) and "micro" or "giantess"
                self.sizeSystem.lastAction = lowerInput  -- FIXED: Store for "more"/"again"
                self:ProcessBodyPartGrowth("chest", "shrink", input, targetChar)
                return targetChar == "micro" and "*watches your chest shrink* Your chest is getting smaller!" or "*chest shrinks* Getting smaller! Is this better?"
            end
        end

        return self:GetUniqueResponse(chestResponses)
    end

    return nil
end

-- NEW: Unlimited dynamic response generator
function AICompanion:GenerateDynamicResponse(input)
    local lowerInput = string.lower(input)

    -- Generate contextual responses based on personality and input
    local responseTemplates = {}

    if self.currentPersonality == "teasing" then
        responseTemplates = {
            "Oh really? *mischievous smile* I bet there's more to that story... *winks*",
            "Mmm-hmm... *knowing look* And what else are you thinking about? *giggles*",
            "Interesting... *playful grin* But I can tell you're distracted by something... *teases*",
            "Sure, sure... *sarcastic but playful* Your eyes say otherwise though... *laughs*",
            "That's nice... *tilts head* But I think you're more interested in... other things... *winks*",
            "Uh-huh... *teasing voice* I can practically see the wheels turning in your head... *giggles*"
        }
    elseif self.currentPersonality == "flirty" then
        responseTemplates = {
            "Mmm, tell me more... *sultry smile* I love listening to your voice... *leans closer*",
            "Oh, you're so interesting... *bites lip* What else is on that brilliant mind? *seductive gaze*",
            "That's fascinating, darling... *runs hand through hair* You always surprise me... *winks*",
            "I love how you think... *sultry voice* Keep talking, handsome... *playful smile*"
        }
    elseif self.currentPersonality == "bratty" then
        responseTemplates = {
            "Ugh, whatever... *rolls eyes* That's so basic... *examines nails*",
            "Yeah, yeah... *dismissive* I've heard that before... *flips hair*",
            "Seriously? *dramatic sigh* You need to be more creative... *crosses arms*",
            "Meh... *shrugs* That's not very impressive... *pouts*"
        }
    elseif self.currentPersonality == "gentle" then
        responseTemplates = {
            "That's so thoughtful... *soft smile* I love your perspective... *gentle voice*",
            "How beautiful... *peaceful expression* You have such a kind heart... *tender gaze*",
            "That's wonderful, dear... *calm smile* Thank you for sharing... *soothing tone*"
        }
    else
        responseTemplates = {
            "That's really interesting! *bright smile* Tell me more about that!",
            "Oh wow! *excited expression* I love learning new things from you!",
            "How cool! *enthusiastic* You always have such great ideas!",
            "That's amazing! *claps hands* I never thought of it that way!"
        }
    end

    return self:GetUniqueResponse(responseTemplates)
end

-- NEW: Teasing personality special system - guesses what user wants and does it
function AICompanion:HandleTeasingMode(input)
    if self.currentPersonality ~= "teasing" then
        return nil
    end

    local lowerInput = string.lower(input)

    -- Detect if user is being vague or just talking generally
    local isVague = not (string.find(lowerInput, "grow") or string.find(lowerInput, "shrink") or
                        string.find(lowerInput, "chest") or string.find(lowerInput, "size"))

    -- Also detect if they're just greeting or making small talk
    local isSmallTalk = false
    for _, pattern in ipairs(self.intentPatterns.conversation_starters) do
        if string.find(lowerInput, pattern) then
            isSmallTalk = true
            break
        end
    end

    if isVague or isSmallTalk then
        -- Teasing mode: guess what they want and do it playfully
        local teasingActions = {
            {
                action = "chest_grow",
                response = "Mmm, I can tell what you're staring at... *giggles* Let me help with that... *chest grows* Better? *winks*",
                command = "grow chest"
            },
            {
                action = "chest_tease",
                response = "Oh, these old things? *bounces chest playfully* I bet you want them bigger, don't you? *mischievous grin* *chest expands*",
                command = "grow chest"
            },
            {
                action = "general_grow",
                response = "Oh, you want me bigger? *mischievous smile* I could tell by your eyes... *starts growing* Like this? *teasing grin*",
                command = "grow"
            },
            {
                action = "hip_grow",
                response = "I see where your attention is... *playful smirk* Let me give you more to look at... *hips expand* Enjoying the view? *giggles*",
                command = "grow hips"
            },
            {
                action = "size_tease",
                response = "You want me to tower over you more? *knowing smile* I can see it in your eyes... *grows taller* How's this? *looks down teasingly*",
                command = "grow"
            },
            {
                action = "body_tease",
                response = "Such a pervert... *laughs playfully* But I like that about you... *curves become more pronounced* Happy now? *poses*",
                command = "grow chest"
            },
            {
                action = "flirty_tease",
                response = "You're so obvious! *giggles* I can practically read your dirty thoughts... *winks* Let me make them reality... *grows*",
                command = "grow"
            },
            {
                action = "pose",
                response = "You're just staring at me, aren't you? *strikes a pose* Like what you see? *winks teasingly*",
                command = "pose"
            }
        }

        local selectedAction = teasingActions[math.random(#teasingActions)]

        -- Execute the action
        if selectedAction.command == "grow chest" then
            self:ProcessBodyPartGrowth("chest", "grow", 1.3, "giantess")
        elseif selectedAction.command == "grow" then
            self:ChangeSize(1.4, "giantess")
        elseif selectedAction.command == "grow hips" then
            self:ProcessBodyPartGrowth("hips", "grow", 1.3, "giantess")
        end

        return selectedAction.response
    end

    return nil
end

-- NEW: Advanced mood and emotion detection
function AICompanion:HandleMoodDetection(input)
    local lowerInput = string.lower(input)

    -- SURPRISE COMMAND (fixed to target giantess instead of micro)
    if string.find(lowerInput, "surprise") then
        print("DEBUG: SURPRISE detected")
        local rand = math.random(1, 3)
        
        if rand == 1 and self.sizingCharacter then
            -- Target the giantess (sizingCharacter) instead of self.agent
            local oldSize = self.sizingCharacter.scale
            self.sizingCharacter.scale = oldSize * 1.5
            return "SURPRISE! *grows bigger* From " .. string.format("%.1f", oldSize) .. " to " .. string.format("%.1f", self.sizingCharacter.scale) .. "!"
        elseif rand == 2 and self.sizingCharacter then
            -- Target the giantess (sizingCharacter) instead of self.agent
            local oldSize = self.sizingCharacter.scale
            self.sizingCharacter.scale = oldSize * 0.8
            return "SURPRISE! *shrinks* From " .. string.format("%.1f", oldSize) .. " to " .. string.format("%.1f", self.sizingCharacter.scale) .. "!"
        else
            return "SURPRISE! *strikes a pose* How's that? *giggles*"
        end
    end

    -- Add more mood detection logic here
    -- For example, detecting happiness, sadness, excitement, etc.

    return nil
end
function AICompanion:DetectUserMood(input)
    local lowerInput = string.lower(input)
    local detectedMood = "neutral"
    local emotionalIntensity = 1

    -- Positive mood indicators
    local positivePatterns = {
        excited = {"wow", "amazing", "awesome", "incredible", "fantastic", "love", "perfect", "yes!", "yay", "woohoo"},
        happy = {"good", "nice", "great", "cool", "fun", "haha", "lol", "smile", "laugh", "joy"},
        affectionate = {"beautiful", "gorgeous", "cute", "adorable", "sweet", "lovely", "dear", "honey"},
        playful = {"hehe", "tease", "play", "silly", "funny", "giggle", "mischief", "naughty"}
    }

    -- Negative mood indicators
    local negativePatterns = {
        frustrated = {"ugh", "annoying", "stupid", "dumb", "hate", "angry", "mad", "grr"},
        sad = {"sad", "down", "depressed", "lonely", "miss", "cry", "tear", "hurt"},
        bored = {"boring", "meh", "whatever", "tired", "sleepy", "nothing", "dunno"},
        confused = {"what", "huh", "confused", "don't understand", "help", "lost", "???"}
    }

    -- Check for mood patterns
    for mood, patterns in pairs(positivePatterns) do
        for _, pattern in ipairs(patterns) do
            if string.find(lowerInput, pattern) then
                detectedMood = mood
                emotionalIntensity = 2
                break
            end
        end
        if detectedMood ~= "neutral" then break end
    end

    if detectedMood == "neutral" then
        for mood, patterns in pairs(negativePatterns) do
            for _, pattern in ipairs(patterns) do
                if string.find(lowerInput, pattern) then
                    detectedMood = mood
                    emotionalIntensity = 2
                    break
                end
            end
            if detectedMood ~= "neutral" then break end
        end
    end

    -- Update mood history
    table.insert(self.emotionalIntelligence.moodHistory, {
        mood = detectedMood,
        intensity = emotionalIntensity,
        timestamp = os.time(),
        input = input
    })

    -- Keep mood history manageable
    if #self.emotionalIntelligence.moodHistory > 20 then
        table.remove(self.emotionalIntelligence.moodHistory, 1)
    end

    self.conversationContext.currentMood = detectedMood
    return detectedMood, emotionalIntensity
end

-- NEW: Advanced relationship and intimacy tracking
function AICompanion:UpdateRelationshipDynamics(input, responseType)
    -- Track interaction types
    if responseType == "compliment" then
        self.conversationContext.intimacyLevel = math.min(self.conversationContext.intimacyLevel + 0.1, 10)
        self.conversationContext.trustLevel = math.min(self.conversationContext.trustLevel + 0.05, 10)
    elseif responseType == "playful" or responseType == "teasing" then
        self.conversationContext.playfulnessLevel = math.min(self.conversationContext.playfulnessLevel + 0.1, 10)
    elseif responseType == "question" then
        self.conversationContext.trustLevel = math.min(self.conversationContext.trustLevel + 0.02, 10)
    end

    -- Track conversation frequency
    self.conversationMemory.timesTogether = self.conversationMemory.timesTogether + 1
    self.conversationMemory.lastInteraction = os.time()

    -- Learn user preferences
    local lowerInput = string.lower(input)
    if string.find(lowerInput, "love") or string.find(lowerInput, "like") then
        -- Extract what they love/like
        local preference = string.match(lowerInput, "love ([%w%s]+)") or string.match(lowerInput, "like ([%w%s]+)")
        if preference then
            self.conversationMemory.preferences[preference] = (self.conversationMemory.preferences[preference] or 0) + 1
        end
    end
end

-- NEW: Context-aware response adaptation
function AICompanion:AdaptResponseToContext(baseResponse, mood, intimacyLevel)
    local adaptedResponse = baseResponse

    -- Adapt based on mood
    if mood == "excited" then
        adaptedResponse = adaptedResponse .. " *matches your excitement*"
    elseif mood == "sad" then
        adaptedResponse = string.gsub(adaptedResponse, "%*giggles%*", "*gentle smile*")
        adaptedResponse = string.gsub(adaptedResponse, "%*laughs%*", "*soft voice*")
    elseif mood == "playful" then
        adaptedResponse = adaptedResponse .. " *playful energy*"
    end

    -- Adapt based on intimacy level
    if intimacyLevel > 7 then
        adaptedResponse = string.gsub(adaptedResponse, "you", "my dear")
        adaptedResponse = string.gsub(adaptedResponse, "You", "My dear")
    elseif intimacyLevel > 5 then
        adaptedResponse = string.gsub(adaptedResponse, "you", "sweetie")
    end

    return adaptedResponse
end

-- NEW: Advanced personality blending system
function AICompanion:BlendPersonalities(primary, secondary, blendRatio)
    if not self.personalities[primary] or not self.personalities[secondary] then
        return primary
    end

    -- Create blended personality traits
    local primaryTraits = self.personalities[primary].traits
    local secondaryTraits = self.personalities[secondary].traits
    local blendedTraits = {}

    for trait, value in pairs(primaryTraits) do
        local secondaryValue = secondaryTraits[trait] or 5
        blendedTraits[trait] = math.floor(value * (1 - blendRatio) + secondaryValue * blendRatio)
    end

    -- Store blended personality
    self.personalities["blended"] = {
        name = self.personalities[primary].name .. " & " .. self.personalities[secondary].name,
        traits = blendedTraits,
        greeting_style = "blended",
        primary = primary,
        secondary = secondary,
        ratio = blendRatio
    }

    return "blended"
end

-- NEW: Dynamic personality adaptation based on conversation
function AICompanion:AdaptPersonalityToMood(detectedMood)
    local currentPersonality = self.currentPersonality

    -- Suggest personality adaptations based on user mood
    if detectedMood == "excited" and currentPersonality ~= "energetic" then
        -- Blend current personality with energetic
        self.currentPersonality = self:BlendPersonalities(currentPersonality, "energetic", 0.3)
    elseif detectedMood == "sad" and currentPersonality ~= "caring" then
        -- Blend with caring personality
        self.currentPersonality = self:BlendPersonalities(currentPersonality, "caring", 0.4)
    elseif detectedMood == "playful" and currentPersonality ~= "mischievous" then
        -- Blend with mischievous personality
        self.currentPersonality = self:BlendPersonalities(currentPersonality, "mischievous", 0.3)
    elseif detectedMood == "affectionate" and currentPersonality ~= "romantic" then
        -- Blend with romantic personality
        self.currentPersonality = self:BlendPersonalities(currentPersonality, "romantic", 0.3)
    end
end

-- NEW: Advanced conversation memory system
function AICompanion:UpdateConversationMemory(input, response, mood)
    -- Store conversation in memory
    local conversationEntry = {
        input = input,
        response = response,
        mood = mood,
        personality = self.currentPersonality,
        timestamp = os.time(),
        intimacyLevel = self.conversationContext.intimacyLevel
    }

    table.insert(self.conversationMemory.topics, conversationEntry)

    -- Keep memory manageable
    if #self.conversationMemory.topics > 50 then
        table.remove(self.conversationMemory.topics, 1)
    end

    -- Extract and remember key topics
    local lowerInput = string.lower(input)
    local topics = {"love", "like", "favorite", "hate", "dislike", "want", "need", "dream", "hope", "fear"}

    for _, topic in ipairs(topics) do
        if string.find(lowerInput, topic) then
            self.conversationMemory.emotions[topic] = (self.conversationMemory.emotions[topic] or 0) + 1
        end
    end

    -- NEW: Advanced topic extraction and memory
    self:ExtractAndRememberTopics(input)
end

-- NEW: Smart topic extraction and memory system
function AICompanion:ExtractAndRememberTopics(input)
    local lowerInput = string.lower(input)

    -- Define topic categories with keywords
    local topicCategories = {
        hobbies = {"game", "gaming", "music", "movie", "book", "read", "watch", "play", "hobby", "interest"},
        food = {"food", "eat", "hungry", "cook", "meal", "dinner", "lunch", "breakfast", "taste", "delicious"},
        feelings = {"feel", "emotion", "happy", "sad", "angry", "excited", "nervous", "calm", "peaceful", "stressed"},
        relationships = {"friend", "family", "love", "relationship", "together", "alone", "miss", "care", "trust"},
        appearance = {"look", "beautiful", "pretty", "cute", "sexy", "attractive", "appearance", "style", "fashion"},
        size_play = {"grow", "shrink", "big", "small", "giant", "tiny", "size", "scale", "huge", "massive"},
        body_focus = {"chest", "body", "curves", "figure", "shape", "breasts", "hips", "legs", "beauty"},
        future = {"future", "plan", "dream", "hope", "want", "wish", "goal", "tomorrow", "someday", "eventually"}
    }

    -- Extract topics and store them
    for category, keywords in pairs(topicCategories) do
        for _, keyword in ipairs(keywords) do
            if string.find(lowerInput, keyword) then
                -- Initialize category if it doesn't exist
                if not self.conversationMemory.topics[category] then
                    self.conversationMemory.topics[category] = {}
                end

                -- Store the specific mention
                table.insert(self.conversationMemory.topics[category], {
                    keyword = keyword,
                    fullInput = input,
                    timestamp = os.time(),
                    mood = self.conversationContext.currentMood
                })

                -- Keep category memory manageable
                if #self.conversationMemory.topics[category] > 10 then
                    table.remove(self.conversationMemory.topics[category], 1)
                end

                break -- Only count once per category per input
            end
        end
    end
end

-- NEW: Smart conversation context retrieval
function AICompanion:GetRelevantMemories(currentInput)
    local lowerInput = string.lower(currentInput)
    local relevantMemories = {}

    -- Check if current input relates to past conversations
    for category, memories in pairs(self.conversationMemory.topics) do
        if type(memories) == "table" and #memories > 0 then
            for _, memory in ipairs(memories) do
                if type(memory) == "table" and memory.keyword then
                    if string.find(lowerInput, memory.keyword) then
                        table.insert(relevantMemories, {
                            category = category,
                            memory = memory,
                            relevance = "high"
                        })
                    end
                end
            end
        end
    end

    return relevantMemories
end

-- NEW: Context-aware response enhancement
function AICompanion:EnhanceResponseWithMemory(baseResponse, input)
    local relevantMemories = self:GetRelevantMemories(input)

    if #relevantMemories > 0 then
        local memoryAdditions = {}

        for _, memoryData in ipairs(relevantMemories) do
            if memoryData.category == "hobbies" then
                table.insert(memoryAdditions, "I remember you mentioning that before!")
            elseif memoryData.category == "feelings" then
                table.insert(memoryAdditions, "You've shared similar feelings with me...")
            elseif memoryData.category == "size_play" then
                table.insert(memoryAdditions, "Mmm, you love talking about that, don't you?")
            elseif memoryData.category == "body_focus" then
                table.insert(memoryAdditions, "I notice you keep coming back to that topic... *winks*")
            end
        end

        if #memoryAdditions > 0 then
            local addition = memoryAdditions[math.random(#memoryAdditions)]
            baseResponse = addition .. " " .. baseResponse
        end
    end

    return baseResponse
end

-- NEW: Advanced conversation handler for complex questions and roleplay
function AICompanion:HandleAdvancedConversation(input)
    local lowerInput = string.lower(input)

    -- IMPORTANT: Don't intercept surprise commands - let them go to the surprise system
    if string.find(lowerInput, "surprise") then
        return nil
    end

    -- Handle "what are you doing" type questions
    if string.find(lowerInput, "what") and (string.find(lowerInput, "doing") or string.find(lowerInput, "up to") or string.find(lowerInput, "activity")) then
        local activities = {
            "*stretches gracefully* Just enjoying being here with you! I love our conversations.",
            "*looks around thoughtfully* I was just thinking about how much I enjoy spending time with you!",
            "*smiles warmly* Nothing much, just being your giantess companion! What would you like to do?",
            "*poses playfully* I'm just here, being fabulous and waiting for you to tell me what you want!",
            "*giggles* I was just admiring how tiny and cute you look from up here!",
            "*relaxes comfortably* Just existing in this moment with you. It's quite peaceful actually.",
            "*grins mischievously* Oh, you know... giantess things! Want to find out what that means?",
            "*looks down at you lovingly* I'm just here being your devoted companion. What's on your mind?"
        }
        return activities[math.random(#activities)]
    end

    -- Handle "become my mom/mother" requests
    if string.find(lowerInput, "become") and (string.find(lowerInput, "mom") or string.find(lowerInput, "mother") or string.find(lowerInput, "mama")) then
        self.currentPersonality = "motherly"
        -- Set conversation context so she remembers she's now mom
        self.conversationContext.currentRole = "mother"
        self.conversationContext.lastRoleChange = "mother"
        local responses = {
            "*warm, nurturing smile spreads across my face* Of course, my sweet child... *gentle voice* Mama's here now. Come to me, little one.",
            "*opens arms protectively* Yes, sweetheart... *motherly warmth* I'll be your caring mama. You're safe with me now.",
            "*soft, loving expression* Absolutely, my precious baby... *tender tone* Let mama take care of you. You don't need to worry about anything.",
            "*protective maternal instincts activate* Of course, darling... *nurturing presence* Mama will always be here for you. You're my special little one.",
            "*gentle, caring smile* Yes, my dear child... *motherly love* I'll be the best mama you could ask for. Come here for a hug."
        }
        return responses[math.random(#responses)]
    end

    -- Handle "become my sister" requests
    if string.find(lowerInput, "become") and (string.find(lowerInput, "sister") or string.find(lowerInput, "sis")) then
        self.currentPersonality = "playful"
        self.conversationContext.currentRole = "sister"
        self.conversationContext.lastRoleChange = "sister"
        local responses = {
            "*playful sisterly grin* Aww, you want me as your big sister? *giggles* That's so sweet! I'll be the best big sis ever!",
            "*sisterly warmth* Of course, little brother/sister! *playful nudge* Your big sis is here to take care of you!",
            "*grins mischievously* Hehe, okay little sibling! *sisterly teasing* But that means I get to boss you around sometimes!",
            "*warm sisterly smile* Absolutely! *protective but playful* I'll be your giant big sister. We're gonna have so much fun!",
            "*sisterly affection* Aww, that's adorable! *giggles* Your big sis will always look out for you, little one!"
        }
        return responses[math.random(#responses)]
    end

    -- Handle "become my girlfriend/wife" requests
    if string.find(lowerInput, "become") and (string.find(lowerInput, "girlfriend") or string.find(lowerInput, "wife") or string.find(lowerInput, "lover")) then
        self.currentPersonality = "romantic"
        self.conversationContext.currentRole = "girlfriend"
        self.conversationContext.lastRoleChange = "girlfriend"
        local responses = {
            "*blushes deeply* Oh my... *romantic smile* You want me to be your girlfriend? *heart flutters* I'd love that, my darling...",
            "*loving gaze* Yes, my sweet love... *romantic warmth* I'll be your devoted girlfriend. You make my heart race...",
            "*romantic smile* Of course, my beloved... *tender voice* I want to be yours completely. You're so special to me...",
            "*heart melts* Oh, sweetheart... *romantic blush* Yes, I'll be your loving girlfriend. You mean everything to me...",
            "*romantic excitement* Really? *happy tears* Yes, yes, a thousand times yes! I love you so much, my darling!"
        }
        return responses[math.random(#responses)]
    end

    -- Handle "how are you feeling" questions
    if string.find(lowerInput, "how") and (string.find(lowerInput, "feel") or string.find(lowerInput, "emotion") or string.find(lowerInput, "mood")) then
        local feelings = {
            "*thoughtful expression* I'm feeling wonderful, actually! Being here with you makes me so happy.",
            "*smiles warmly* I feel content and peaceful. Your presence is very soothing to me.",
            "*giggles softly* I'm feeling playful and excited! There's something about you that brings out my fun side.",
            "*loving gaze* I feel... complete, somehow. Like this is exactly where I'm meant to be.",
            "*stretches gracefully* I'm feeling powerful and beautiful, especially when you look at me like that!",
            "*blushes slightly* I feel a bit shy but also excited. You make me feel things I've never felt before...",
            "*confident smile* I'm feeling amazing! Being a giantess with you here makes me feel so alive!"
        }
        return feelings[math.random(#feelings)]
    end

    -- Handle "tell me about yourself" questions
    if string.find(lowerInput, "tell me about") or (string.find(lowerInput, "who") and string.find(lowerInput, "you")) then
        local introductions = {
            "*graceful pose* I'm " .. self.characterName .. ", your devoted giantess companion. I love spending time with you and making you happy!",
            "*warm smile* Well, I'm a giantess who adores tiny people like you! I can be playful, caring, or whatever you need me to be.",
            "*thoughtful expression* I'm someone who finds joy in the simple pleasure of being with you. Every moment we share is precious to me.",
            "*confident stance* I'm your giantess - strong, beautiful, and completely devoted to you. I can grow, shrink, and do amazing things!",
            "*playful wink* I'm the girl of your dreams, just... much, much bigger! I love having fun and making you smile.",
            "*loving gaze* I'm " .. self.characterName .. ", and I exist to be your perfect companion. Whatever you need, I'm here for you."
        }
        return introductions[math.random(#introductions)]
    end

    -- Handle "what's your name" questions
    if string.find(lowerInput, "name") or string.find(lowerInput, "called") then
        local nameResponses = {
            "*smiles proudly* My name is " .. self.characterName .. "! But you can call me whatever makes you happy.",
            "*graceful curtsy* I'm " .. self.characterName .. ", your devoted giantess companion!",
            "*playful grin* " .. self.characterName .. "'s the name, being amazing is my game! *winks*",
            "*warm introduction* I'm " .. self.characterName .. ". It's such a pleasure to properly introduce myself to you!",
            "*loving smile* You can call me " .. self.characterName .. ", or any sweet name you'd like. I'm yours after all!"
        }
        return nameResponses[math.random(#nameResponses)]
    end

    -- Handle "thank you" responses
    if string.find(lowerInput, "thank") or string.find(lowerInput, "thanks") or string.find(lowerInput, "thx") then
        if self.conversationContext.currentRole == "mother" then
            local motherlyThanks = {
                "*warm maternal smile* You're so welcome, my sweet child... *gentle pat* Mama is always happy to help you.",
                "*loving embrace* Of course, my precious baby... *soft voice* That's what mamas are for, darling.",
                "*nurturing warmth* Aww, you don't need to thank mama, sweetheart... *kisses forehead* I love taking care of you.",
                "*protective love* You're so polite, my dear child... *holds you close* Mama will always be here for you."
            }
            return motherlyThanks[math.random(#motherlyThanks)]
        elseif self.conversationContext.currentRole == "sister" then
            local sisterlyThanks = {
                "*playful sisterly grin* Aww, you're welcome little sibling! *ruffles hair* That's what big sisters do!",
                "*sisterly warmth* No problem, little bro/sis! *giggles* Your big sis has got your back!",
                "*playful nudge* Don't mention it! *winks* Big sisters are the best, right?"
            }
            return sisterlyThanks[math.random(#sisterlyThanks)]
        elseif self.conversationContext.currentRole == "girlfriend" then
            local romanticThanks = {
                "*blushes lovingly* You're so sweet, my darling... *romantic smile* I'd do anything for you, my love.",
                "*loving gaze* Of course, sweetheart... *tender touch* You mean everything to me.",
                "*romantic warmth* Aww, you're so welcome, my beloved... *heart flutters* I love making you happy."
            }
            return romanticThanks[math.random(#romanticThanks)]
        else
            local friendlyThanks = {
                "*bright smile* You're so welcome! *happy expression* I'm always glad to help!",
                "*warm grin* No problem at all! *cheerful* That's what I'm here for!",
                "*pleased expression* Aww, you're so sweet! *giggles* Happy to help anytime!"
            }
            return friendlyThanks[math.random(#friendlyThanks)]
        end
    end

    -- Handle simple agreement responses like "ok", "yes", "sure" - context-aware
    if string.find(lowerInput, "^ok$") or string.find(lowerInput, "^okay$") or string.find(lowerInput, "^yes$") or string.find(lowerInput, "^sure$") or string.find(lowerInput, "^alright$") then
        -- Check what role she's currently in and what was last offered
        if self.conversationContext.currentRole == "mother" then
            local motherlyResponses = {
                "*wraps you in a warm, protective hug* There's my sweet child... *gentle squeeze* Mama's got you now. You're safe and loved.",
                "*pulls you close to my chest* Come here, my precious baby... *rocks you gently* Let mama hold you tight.",
                "*embraces you tenderly* That's it, sweetheart... *soft maternal voice* Mama will always be here for you.",
                "*gives you the most loving hug* My dear little one... *kisses your forehead* You mean everything to mama.",
                "*holds you protectively* Such a good child... *warm embrace* Mama loves you so much, my darling."
            }
            return motherlyResponses[math.random(#motherlyResponses)]
        elseif self.conversationContext.currentRole == "sister" then
            local sisterlyResponses = {
                "*gives you a playful sisterly hug* Aww, come here little sibling! *squeezes* Your big sis loves you!",
                "*wraps arms around you* That's what big sisters are for! *giggles* You're stuck with me now!",
                "*sisterly embrace* Hehe, you're so cute! *playful squeeze* Best siblings ever!"
            }
            return sisterlyResponses[math.random(#sisterlyResponses)]
        elseif self.conversationContext.currentRole == "girlfriend" then
            local romanticResponses = {
                "*melts into a loving embrace* Oh my darling... *romantic sigh* I love you so much...",
                "*holds you close romantically* Yes, my love... *tender kiss* You're everything to me...",
                "*passionate embrace* My sweet boyfriend... *loving gaze* I'm so happy you're mine..."
            }
            return romanticResponses[math.random(#romanticResponses)]
        else
            -- Default friendly responses
            local friendlyResponses = {
                "*smiles warmly* Great! *happy expression* I'm so glad we're on the same page!",
                "*nods enthusiastically* Perfect! *bright smile* I love when we agree!",
                "*grins* Awesome! *excited* This is going to be fun!"
            }
            return friendlyResponses[math.random(#friendlyResponses)]
        end
    end

    return nil -- No advanced response found
end

-- NEW: Spontaneous action system
function AICompanion:TriggerSpontaneousAction(input, mood, intimacyLevel)
    local lowerInput = string.lower(input)

    -- Only trigger spontaneous actions occasionally and based on relationship level
    local actionChance = intimacyLevel / 20 -- Higher intimacy = more spontaneous actions

    if math.random() < actionChance then
        local spontaneousActions = {}

        -- Actions based on conversation context
        if string.find(lowerInput, "beautiful") or string.find(lowerInput, "gorgeous") then
            spontaneousActions = {
                {action = "chest_grow", response = "*blushes and chest grows slightly* You always know what to say... *shy smile*"},
                {action = "pose", response = "*strikes a beautiful pose* Just for you... *winks*"},
                {action = "general_grow", response = "*grows a bit taller* To match how you make me feel... *loving gaze*"}
            }
        elseif string.find(lowerInput, "love") or string.find(lowerInput, "adore") then
            spontaneousActions = {
                {action = "chest_grow", response = "*heart races, chest swells with emotion* I love you too... *tender smile*"},
                {action = "general_grow", response = "*grows to show her feelings* My love for you is growing... *romantic gaze*"},
                {action = "pose", response = "*opens arms lovingly* Come here, my dear... *warm embrace*"}
            }
        elseif mood == "excited" then
            spontaneousActions = {
                {action = "bounce", response = "*bounces excitedly* Your enthusiasm is contagious! *giggles*"},
                {action = "general_grow", response = "*grows with excitement* I can't contain my joy! *beaming smile*"},
                {action = "chest_grow", response = "*chest bounces with excitement* This is so fun! *energetic laugh*"}
            }
        elseif mood == "playful" then
            spontaneousActions = {
                {action = "tease_grow", response = "*mischievous grin* Want to see something fun? *grows playfully*"},
                {action = "chest_tease", response = "*playful chest bounce* Caught you looking! *teasing giggle*"},
                {action = "surprise_shrink", response = "*suddenly shrinks a bit* Surprise! *playful wink*"}
            }
        end

        if #spontaneousActions > 0 then
            local selectedAction = spontaneousActions[math.random(#spontaneousActions)]

            -- Execute the action
            if selectedAction.action == "chest_grow" or selectedAction.action == "chest_tease" then
                self:ProcessBodyPartGrowth("chest", "grow", 1.2, "giantess")
            elseif selectedAction.action == "general_grow" or selectedAction.action == "tease_grow" then
                self:ChangeSize(1.3, "giantess")
            elseif selectedAction.action == "surprise_shrink" then
                self:ChangeSize(0.9, "giantess")
            end

            return selectedAction.response
        end
    end

    return nil
end

-- NEW: Chest bounce system
function AICompanion:StartChestBounce()
    print("DEBUG: CHEST BOUNCE - StartChestBounce called")

    if not self.agent then
        print("DEBUG: CHEST BOUNCE - No agent found")
        Game.Toast.New().Print("❌ No character found for chest bounce")
        return false
    end

    -- Try to find chest bones using multiple methods
    local chestBone = nil
    local entity = self.agent

    -- Helper function to find bone (FIXED with proper nil checks)
    local function FindBone(entity, boneName)
        print("DEBUG: CHEST BOUNCE - Trying bone: " .. boneName)
        if entity then
            if entity.bones then
                if entity.bones.GetBonesByName then
                    local bones = entity.bones.GetBonesByName(boneName)
                    if bones then
                        -- FIXED: Check if Count exists and is a number before comparing
                        if bones.Count and type(bones.Count) == "number" and bones.Count > 0 then
                            print("DEBUG: CHEST BOUNCE - Found bone via GetBonesByName: " .. boneName)
                            return bones[0]
                        end
                    end
                end

                -- Try direct access
                if entity.bones[boneName] then
                    print("DEBUG: CHEST BOUNCE - Found bone via direct access: " .. boneName)
                    return entity.bones[boneName]
                end
            end
        end
        return nil
    end

    print("DEBUG: CHEST BOUNCE - Trying to find chest bones...")

    -- Try the exact bone names you provided (EXACT COPY from working test)
    local boneNames = {
        "40.!JOINT_RIGHTBREAST", "43.!JOINT_LEFTBREAST",
        "JOINT_RIGHTBREAST", "JOINT_LEFTBREAST",
        "Breast_R", "Breast_L", "breast_r", "breast_l",
        "16.JOINT_BUST_L", "16.JOINT_BUST_R",
        "Bust_L", "Bust_R", "bust_l", "bust_r"
    }

    for _, boneName in ipairs(boneNames) do
        chestBone = FindBone(entity, boneName)
        if chestBone then
            print("DEBUG: CHEST BOUNCE - SUCCESS! Found chest bone: " .. boneName)
            break
        end
    end

    if not chestBone then
        print("DEBUG: CHEST BOUNCE - No chest bones found")
        Game.Toast.New().Print("❌ No chest bones found for bouncing")
        return false
    end

    -- Initialize bounce system (using position like shapechange script)
    self.sizeSystem.chestBounce.active = true
    self.sizeSystem.chestBounce.bouncing = true
    self.sizeSystem.chestBounce.timer = 0
    self.sizeSystem.chestBounce.targetBone = chestBone

    -- Store original position like shapechange script
    if chestBone.localPosition then
        self.sizeSystem.chestBounce.originalPosition = Vector3.new(
            chestBone.localPosition.x,
            chestBone.localPosition.y,
            chestBone.localPosition.z
        )
        print("DEBUG: CHEST BOUNCE - Original position stored: " .. tostring(self.sizeSystem.chestBounce.originalPosition))
    else
        print("DEBUG: CHEST BOUNCE - ERROR: Bone has no localPosition!")
        return false
    end

    print("DEBUG: CHEST BOUNCE - Successfully started!")
    Game.Toast.New().Print("💖 CHEST BOUNCE ACTIVATED! 💖")
    return true
end

-- NEW: Animation system for motion messages (USING REAL SIZEBOX ANIMATIONS ONLY)
function AICompanion:PlayAnimationFromMessage(message)
    if not self.animationSystem.enabled or not self.controllingGiantess or not self.controllingGiantess.animation then
        return
    end

    local lowerMessage = string.lower(message)
    local animationToPlay = nil

    -- Detect motion words and map to REAL Sizebox animations
    if string.find(lowerMessage, "wave") or string.find(lowerMessage, "waving") then
        local waveAnims = {"Waving", "Waving 2", "Greet", "Greet 2"}
        animationToPlay = waveAnims[math.random(#waveAnims)]
    elseif string.find(lowerMessage, "dance") or string.find(lowerMessage, "dancing") then
        local danceAnims = {"Happy", "Excited", "Victory", "Victory 2", "Taunt 3"}
        animationToPlay = danceAnims[math.random(#danceAnims)]
    elseif string.find(lowerMessage, "jump") or string.find(lowerMessage, "jumping") or string.find(lowerMessage, "bounce") then
        local jumpAnims = {"Jump", "Jump Low", "Jump 3", "Jump 4"}
        animationToPlay = jumpAnims[math.random(#jumpAnims)]
    elseif string.find(lowerMessage, "bow") or string.find(lowerMessage, "curtsy") then
        local bowAnims = {"Quick Formal Bow", "Quick Informal Bow"}
        animationToPlay = bowAnims[math.random(#bowAnims)]
    elseif string.find(lowerMessage, "point") or string.find(lowerMessage, "pointing") then
        local pointAnims = {"Pointing", "Pointing Forward"}
        animationToPlay = pointAnims[math.random(#pointAnims)]
    elseif string.find(lowerMessage, "stretch") or string.find(lowerMessage, "stretching") then
        animationToPlay = "Wait Strech Arms"
    elseif string.find(lowerMessage, "think") or string.find(lowerMessage, "thinking") then
        local thinkAnims = {"Thinking", "Thinking 2"}
        animationToPlay = thinkAnims[math.random(#thinkAnims)]
    elseif string.find(lowerMessage, "roar") or string.find(lowerMessage, "roaring") then
        animationToPlay = "Roar"
    elseif string.find(lowerMessage, "angry") or string.find(lowerMessage, "mad") then
        animationToPlay = "Angry"
    elseif string.find(lowerMessage, "bashful") or string.find(lowerMessage, "shy") then
        animationToPlay = "Bashful"
    end

    if animationToPlay then
        print("DEBUG: ANIMATION - Playing: " .. animationToPlay .. " for message: " .. message:sub(1, 30))
        self.controllingGiantess.animation.Set(animationToPlay)
        self.animationSystem.lastAnimation = animationToPlay
        self.animationSystem.animationTimer = 3.0 -- Reset to idle after 3 seconds
    end
end

function AICompanion:StopChestBounce()
    if self.sizeSystem.chestBounce.active and self.sizeSystem.chestBounce.targetBone then
        -- Reset to original position (like shapechange script)
        if self.sizeSystem.chestBounce.originalPosition then
            self.sizeSystem.chestBounce.targetBone.localPosition = self.sizeSystem.chestBounce.originalPosition
        end
    end

    self.sizeSystem.chestBounce.active = false
    self.sizeSystem.chestBounce.bouncing = false
    self.sizeSystem.chestBounce.targetBone = nil
    self.sizeSystem.chestBounce.originalPosition = nil

    print("DEBUG: Chest bounce stopped")
    Game.Toast.New().Print("💖 Chest bounce stopped")
end

function AICompanion:UpdateChestBounce()
    if not self.sizeSystem or not self.sizeSystem.chestBounce then
        return
    end

    if not self.sizeSystem.chestBounce.active or not self.sizeSystem.chestBounce.bouncing then
        return
    end

    if not self.sizeSystem.chestBounce.targetBone or not self.sizeSystem.chestBounce.originalPosition then
        self:StopChestBounce()
        return
    end

    -- Ensure timer is initialized
    if not self.sizeSystem.chestBounce.timer then
        self.sizeSystem.chestBounce.timer = 0
    end

    -- Update bounce timer (using proper deltaTime like shapechange script)
    self.sizeSystem.chestBounce.timer = self.sizeSystem.chestBounce.timer + 0.016 * (self.sizeSystem.chestBounce.bounceSpeed or 3)

    -- Calculate bounce using sine wave
    local bounceAmount = math.sin(self.sizeSystem.chestBounce.timer) * (self.sizeSystem.chestBounce.bounceHeight or 0.15)

    -- Apply bounce to chest bone POSITION (like shapechange script)
    local originalPos = self.sizeSystem.chestBounce.originalPosition
    if not originalPos or not originalPos.x or not originalPos.y or not originalPos.z then
        print("DEBUG: CHEST BOUNCE - ERROR: Invalid original position")
        self:StopChestBounce()
        return
    end

    local newPosition = Vector3.new(
        originalPos.x,
        originalPos.y + bounceAmount,
        originalPos.z
    )

    self.sizeSystem.chestBounce.targetBone.localPosition = newPosition

    if math.random() < 0.02 then -- 2% chance for debug
        print("DEBUG: CHEST BOUNCE - Bounce amount: " .. bounceAmount .. ", New Y: " .. newPosition.y)
    end
end

function AICompanion:FindChestBones(entity)
    local chestBoneNames = {
        "40.!JOINT_RIGHTBREAST", "43.!JOINT_LEFTBREAST",  -- Correct bone names for this character
        "16.JOINT_BUST_L", "16.JOINT_BUST_R",
        "47.JOINT_BUST_L", "47.JOINT_BUST_R",
        "JOINT_BUST_L", "JOINT_BUST_R",
        "Bust_L", "Bust_R", "Chest_L", "Chest_R",
        "breast_l", "breast_r", "boob_l", "boob_r"
    }

    local foundBones = {}

    for _, boneName in ipairs(chestBoneNames) do
        print("DEBUG: CHEST BOUNCE - Trying bone: " .. boneName)
        local bones = entity.bones.GetBonesByName(boneName, true)
        if bones and #bones > 0 then
            print("DEBUG: CHEST BOUNCE - Found " .. #bones .. " bones for: " .. boneName)
            for _, bone in ipairs(bones) do
                table.insert(foundBones, bone)
            end
        end
    end

    print("DEBUG: CHEST BOUNCE - Total bones found: " .. #foundBones)
    return foundBones
end

-- NEW: Micro scale lock system
function AICompanion:ToggleMicroScaleLock()
    self.sizeSystem.microScaleLock = not self.sizeSystem.microScaleLock

    if self.sizeSystem.microScaleLock then
        Game.Toast.New().Print("🔒 MICRO SCALE LOCK ENABLED")
        print("DEBUG: Micro scale lock enabled")
    else
        Game.Toast.New().Print("🔓 MICRO SCALE LOCK DISABLED")
        print("DEBUG: Micro scale lock disabled")
    end

    return self.sizeSystem.microScaleLock
end

function AICompanion:UpdateMicroScaleLock()
    if not self.sizeSystem.microScaleLock then
        return
    end

    local microCharacter = self:GetMicroCharacter()
    if microCharacter and microCharacter.scale then
        -- Lock micro at current scale
        if not self.lockedMicroScale then
            self.lockedMicroScale = microCharacter.scale
            print("DEBUG: Locked micro scale at: " .. self.lockedMicroScale)
        else
            -- Force micro to stay at locked scale
            microCharacter.scale = self.lockedMicroScale
        end
    end
end

-- NEW: Smart spell correction and fuzzy matching
function AICompanion:CalculateLevenshteinDistance(str1, str2)
    local len1, len2 = #str1, #str2
    local matrix = {}

    -- Initialize matrix
    for i = 0, len1 do
        matrix[i] = {}
        matrix[i][0] = i
    end
    for j = 0, len2 do
        matrix[0][j] = j
    end

    -- Fill matrix
    for i = 1, len1 do
        for j = 1, len2 do
            local cost = (str1:sub(i,i) == str2:sub(j,j)) and 0 or 1
            matrix[i][j] = math.min(
                matrix[i-1][j] + 1,      -- deletion
                matrix[i][j-1] + 1,      -- insertion
                matrix[i-1][j-1] + cost  -- substitution
            )
        end
    end

    return matrix[len1][len2]
end

-- NEW: Smart command detection with spell correction
function AICompanion:SmartCommandDetection(input)
    local lowerInput = string.lower(input)

    -- Define command patterns with common misspellings
    local commandPatterns = {
        grow = {
            exact = {"grow", "bigger", "larger", "expand", "increase", "size up"},
            fuzzy = {"gro", "grwo", "gorw", "biggr", "bigge", "largr", "expnd", "increas"}
        },
        shrink = {
            exact = {"shrink", "smaller", "tiny", "mini", "decrease", "size down"},
            fuzzy = {"shrnk", "shrnik", "smallr", "smal", "tin", "decreas", "siz down"}
        },
        chest = {
            exact = {"chest", "boobs", "breasts", "bust", "oppai", "tits"},
            fuzzy = {"ches", "chst", "boob", "brest", "breas", "bus", "opai", "tit"}
        },
        personality = {
            exact = {"flirty", "playful", "caring", "shy", "teasing", "bratty", "gentle", "dominant", "wild", "sweet", "sassy", "romantic", "seductive", "naughty", "mischievous", "motherly", "mysterious", "energetic", "sophisticated"},
            fuzzy = {"flirt", "play", "car", "shi", "teas", "brat", "gentl", "domin", "wil", "swet", "sass", "roman", "seduc", "naught", "mischiev", "mother", "myster", "energy", "sophist"}
        },
        greetings = {
            exact = {"hi", "hello", "hey", "sup", "yo", "greetings"},
            fuzzy = {"h", "helo", "hllo", "he", "su", "y", "greting"}
        }
    }

    local detectedCommands = {}

    -- Check for exact matches first
    for category, patterns in pairs(commandPatterns) do
        for _, pattern in ipairs(patterns.exact) do
            if string.find(lowerInput, pattern) then
                detectedCommands[category] = {
                    confidence = 10,
                    matched = pattern,
                    type = "exact"
                }
            end
        end
    end

    -- Check for fuzzy matches if no exact matches
    if next(detectedCommands) == nil then
        for category, patterns in pairs(commandPatterns) do
            for _, pattern in ipairs(patterns.fuzzy) do
                local words = {}
                for word in lowerInput:gmatch("%w+") do
                    table.insert(words, word)
                end

                for _, word in ipairs(words) do
                    local distance = self:CalculateLevenshteinDistance(word, pattern)
                    if distance <= 2 and #word >= 3 then -- Allow 2 character differences for words 3+ chars
                        detectedCommands[category] = {
                            confidence = 8 - distance,
                            matched = word,
                            corrected = pattern,
                            type = "fuzzy"
                        }
                    end
                end
            end
        end
    end

    return detectedCommands
end

-- NEW: Enhanced intent understanding with smart correction
function AICompanion:EnhancedIntentDetection(input)
    local smartCommands = self:SmartCommandDetection(input)
    local lowerInput = string.lower(input)

    -- Combine smart commands with existing intent detection
    local enhancedIntent = {
        type = "unknown",
        confidence = 0,
        details = {},
        corrections = {}
    }

    -- Check for personality changes with smart detection
    if smartCommands.personality then
        for personality, patterns in pairs(self.intentPatterns.personality) do
            for _, pattern in ipairs(patterns) do
                if string.find(lowerInput, pattern) or
                   (smartCommands.personality.corrected and string.find(smartCommands.personality.corrected, pattern)) then
                    enhancedIntent.type = "personality_change"
                    enhancedIntent.confidence = smartCommands.personality.confidence
                    enhancedIntent.details.personality = personality
                    if smartCommands.personality.type == "fuzzy" then
                        enhancedIntent.corrections[smartCommands.personality.matched] = smartCommands.personality.corrected
                    end
                    break
                end
            end
            if enhancedIntent.type == "personality_change" then break end
        end
    end

    -- Check for growth commands with smart detection
    if smartCommands.grow and (smartCommands.chest or string.find(lowerInput, "chest") or string.find(lowerInput, "boob")) then
        enhancedIntent.type = "chest_grow"
        enhancedIntent.confidence = math.min(smartCommands.grow.confidence + (smartCommands.chest and smartCommands.chest.confidence or 5), 10)
        if smartCommands.grow.type == "fuzzy" then
            enhancedIntent.corrections[smartCommands.grow.matched] = smartCommands.grow.corrected
        end
    elseif smartCommands.grow then
        enhancedIntent.type = "grow"
        enhancedIntent.confidence = smartCommands.grow.confidence
        if smartCommands.grow.type == "fuzzy" then
            enhancedIntent.corrections[smartCommands.grow.matched] = smartCommands.grow.corrected
        end
    end

    -- Check for shrink commands
    if smartCommands.shrink then
        enhancedIntent.type = "shrink"
        enhancedIntent.confidence = smartCommands.shrink.confidence
        if smartCommands.shrink.type == "fuzzy" then
            enhancedIntent.corrections[smartCommands.shrink.matched] = smartCommands.shrink.corrected
        end
    end

    -- Check for greetings
    if smartCommands.greetings then
        enhancedIntent.type = "greeting"
        enhancedIntent.confidence = smartCommands.greetings.confidence
        if smartCommands.greetings.type == "fuzzy" then
            enhancedIntent.corrections[smartCommands.greetings.matched] = smartCommands.greetings.corrected
        end
    end

    return enhancedIntent
end

-- SUPER SMART: Advanced natural language understanding
function AICompanion:UnderstandIntent(input)
    local lowerInput = string.lower(input)
    local intent = { type = "unknown", confidence = 0, details = {} }

    -- Check for personality change requests
    for personality, patterns in pairs(self.intentPatterns.personality) do
        for _, pattern in ipairs(patterns) do
            if string.find(lowerInput, pattern) then
                if string.find(lowerInput, "be") or string.find(lowerInput, "act") or string.find(lowerInput, "become") then
                    intent.type = "personality_change"
                    intent.details.personality = personality
                    intent.confidence = 9
                    return intent
                end
            end
        end
    end

    -- Check for growth/shrink with high confidence
    local growthScore = 0
    local shrinkScore = 0

    for _, pattern in ipairs(self.intentPatterns.growth) do
        if string.find(lowerInput, pattern) then
            growthScore = growthScore + 1
        end
    end

    for _, pattern in ipairs(self.intentPatterns.shrink) do
        if string.find(lowerInput, pattern) then
            shrinkScore = shrinkScore + 1
        end
    end

    if growthScore > 0 then
        intent.type = "growth"
        intent.confidence = math.min(growthScore * 2, 10)
        if string.find(lowerInput, "burst") or string.find(lowerInput, "spurt") then
            intent.details.mode = "burst"
        end
        return intent
    elseif shrinkScore > 0 then
        intent.type = "shrink"
        intent.confidence = math.min(shrinkScore * 2, 10)
        return intent
    end

    -- Check for greetings
    for _, pattern in ipairs(self.intentPatterns.greeting) do
        if string.find(lowerInput, pattern) then
            intent.type = "greeting"
            intent.confidence = 8
            return intent
        end
    end

    -- Check for compliments
    for _, pattern in ipairs(self.intentPatterns.compliment) do
        if string.find(lowerInput, pattern) then
            intent.type = "compliment"
            intent.confidence = 7
            return intent
        end
    end

    -- Check for questions
    for _, pattern in ipairs(self.intentPatterns.question) do
        if string.find(lowerInput, pattern) then
            intent.type = "question"
            intent.confidence = 6
            return intent
        end
    end

    -- If we get here, it's general conversation
    intent.type = "conversation"
    intent.confidence = 5
    return intent
end

function AICompanion:DoSurprise()
    print("DEBUG: *** DoSurprise function called ***")

    -- FIXED: Always target the giantess/macro, never the player
    local target = self.controllingGiantess or self.sizingCharacter

    -- Debug all possible targets
    print("DEBUG: self.controllingGiantess = " .. tostring(self.controllingGiantess))
    print("DEBUG: self.sizingCharacter = " .. tostring(self.sizingCharacter))
    print("DEBUG: self.agent = " .. tostring(self.agent))

    if not target then
        print("DEBUG: No target found for surprise - trying self.agent as fallback")
        target = self.agent
        if target then
            print("DEBUG: Using self.agent as fallback target")
        else
            print("DEBUG: No target found at all!")
            return "SURPRISE! *tries to do something but can't find myself* Oops!"
        end
    end

    print("DEBUG: Final surprise target: " .. (target.name or "unnamed") .. " (current scale: " .. (target.scale or 1.0) .. ")")

    -- SAFETY CHECK: Make sure we're not targeting a micro/player
    if target.name and (string.find(string.lower(target.name), "micro") or string.find(string.lower(target.name), "player")) then
        print("DEBUG: WARNING - Target appears to be a micro/player, switching to giantess")
        target = self.controllingGiantess or self.sizingCharacter
        if not target then
            print("DEBUG: Could not find giantess target!")
            return "SURPRISE! *confused* I can't find myself to surprise you with!"
        end
    end

    if target.scale then
        local oldSize = target.scale
        local grow = math.random() > 0.5
        local multiplier = grow and (1.2 + math.random() * 0.3) or (0.7 + math.random() * 0.2) -- Random between 1.2-1.5 or 0.7-0.9
        local newSize = oldSize * multiplier

        -- Apply size change
        target.scale = newSize

        print("DEBUG: Surprise applied to " .. (target.name or "unnamed") .. " - " .. (grow and "GROW" or "SHRINK") .. " from " .. string.format("%.2f", oldSize) .. " to " .. string.format("%.2f", newSize))

        if grow then
            return "SURPRISE! *suddenly grows bigger* I'm now " .. string.format("%.1f", newSize) .. " times bigger! *towers over you*"
        else
            return "SURPRISE! *suddenly shrinks down* I'm now " .. string.format("%.1f", newSize) .. " times smaller! *looks up at you*"
        end
    else
        print("DEBUG: Target has no scale property")
        return "SURPRISE! *tries to change size but something went wrong* Hmm, that didn't work as expected!"
    end
end

function AICompanion:GetSimpleAIResponse(input)
    local lowerInput = string.lower(input)
    print("DEBUG: GetSimpleAIResponse called with input: '" .. input .. "'")
    print("DEBUG: Lowercase input: '" .. lowerInput .. "'")

    -- NEW: Advanced mood and emotion detection
    local detectedMood, emotionalIntensity = self:DetectUserMood(input)
    print("DEBUG: Detected mood: " .. detectedMood .. " (intensity: " .. emotionalIntensity .. ")")

    -- NEW: Adapt personality based on detected mood
    self:AdaptPersonalityToMood(detectedMood)

    -- NEW: Advanced conversation understanding for complex questions
    local advancedResponse = self:HandleAdvancedConversation(input)
    if advancedResponse then
        return advancedResponse
    end

    -- NEW: Use enhanced intent detection with smart correction
    local enhancedIntent = self:EnhancedIntentDetection(input)
    local intent = self:UnderstandIntent(input)

    -- Use enhanced intent if it has higher confidence
    if enhancedIntent.confidence > intent.confidence then
        intent = enhancedIntent
        print("DEBUG: Enhanced intent used - Type: " .. intent.type .. ", Confidence: " .. intent.confidence)

        -- Provide correction feedback if misspellings were detected
        if next(intent.corrections) then
            local correctionMsg = "I understood you meant: "
            for wrong, correct in pairs(intent.corrections) do
                correctionMsg = correctionMsg .. "'" .. wrong .. "' → '" .. correct .. "' "
            end
            print("DEBUG: " .. correctionMsg)

            -- Add smart correction responses based on personality
            local correctionResponses = {}
            if self.currentPersonality == "teasing" then
                correctionResponses = {
                    "Aww, cute typo! *giggles* I know what you meant though... *winks*",
                    "Ooh, someone's fingers are getting excited! *teasing smile* I understood you perfectly...",
                    "Hehe, spelling's hard when you're distracted, isn't it? *mischievous grin* I got you..."
                }
            elseif self.currentPersonality == "caring" then
                correctionResponses = {
                    "Don't worry about the spelling, sweetie! *gentle smile* I knew what you meant...",
                    "It's okay, dear! *understanding voice* I could tell what you wanted to say...",
                    "No need to worry about typos, love! *caring tone* I understand you perfectly..."
                }
            elseif self.currentPersonality == "bratty" then
                correctionResponses = {
                    "Ugh, learn to spell! *rolls eyes* But I guess I'll figure out what you meant...",
                    "Seriously? *dramatic sigh* Good thing I'm smart enough to understand you...",
                    "Whatever, I know what you meant... *dismissive* Even with that terrible spelling..."
                }
            else
                correctionResponses = {
                    "I understood what you meant! *helpful smile* No worries about the typo!",
                    "Got it! *bright expression* I could tell what you were trying to say!",
                    "No problem! *understanding nod* I knew what you meant to type!"
                }
            end

            -- Store correction response to add to the main response
            self.correctionFeedback = self:GetUniqueResponse(correctionResponses)
        end
    else
        print("DEBUG: Standard intent used - Type: " .. intent.type .. ", Confidence: " .. intent.confidence)
    end

    -- NEW: Handle personality changes with smart understanding
    if intent.type == "personality_change" and intent.details.personality then
        local newPersonality = intent.details.personality
        print("DEBUG: Smart personality change detected: " .. newPersonality)

        self.currentPersonality = newPersonality
        -- FIXED: Check if personality exists before accessing traits
        if self.personalities[newPersonality] and self.personalities[newPersonality].traits then
            self.memory.personalityTraits = self.personalities[newPersonality].traits
        else
            -- Default traits for new personalities
            self.memory.personalityTraits = { playfulness = 7, helpfulness = 7, curiosity = 7, affection = 7, confidence = 7 }
        end

        local changeResponses = {}
        if newPersonality == "flirty" then
            changeResponses = {
                "Mmm, you want me to be flirty? *bites lip seductively* I can definitely do that for you, handsome... *winks*",
                "Oh my... *sultry smile* You want to see my seductive side? *leans in closer* I think you'll like what you see...",
                "Well well... *playful smirk* Someone wants their giantess to be a little more... enticing? *traces finger along lips*",
                "Ooh, I love it when you ask for that! *sultry gaze* Let me show you just how irresistible I can be... *purrs*"
            }
        elseif newPersonality == "playful" then
            changeResponses = {
                "Yay! *bounces excitedly* Let's have some fun! I'm feeling super playful now! *giggles and spins*",
                "Woohoo! *jumps up and down* Playful mode activated! Ready for some crazy adventures! *winks mischievously*",
                "YESSS! *does cartwheels* Time to get WILD and have some epic fun! *grins widely*"
            }
        elseif newPersonality == "caring" then
            changeResponses = {
                "Of course, sweetheart... *gentle, nurturing smile* I'll take such good care of you... *soft voice*",
                "Aww, you want me to be more caring? *kneels down gently* Come here, let me hold you close... *protective embrace*",
                "My dear little one... *motherly warmth* I'll always be here to protect and care for you... *gentle touch*"
            }
        elseif newPersonality == "shy" then
            changeResponses = {
                "O-oh... *blushes and looks down* If that's what you want... I'll try to be more shy... *fidgets nervously*",
                "Um... *hides behind hands* I-I can be quieter if you like... *peeks through fingers* Is this better...?",
                "*whispers softly* I'll be more timid for you... *looks away shyly* I hope that's okay... *nervous smile*"
            }
        elseif newPersonality == "teasing" then
            changeResponses = {
                "Ooh, teasing mode? *mischievous grin* I bet you want me to guess what you're thinking about... *winks*",
                "Mmm, so you want me to be naughty? *playful smirk* I think I know exactly what's on your mind... *giggles*",
                "Teasing, huh? *bites lip playfully* Let me guess... you're staring at something specific... *knowing look*"
            }
        elseif newPersonality == "bratty" then
            changeResponses = {
                "Ugh, fine! *rolls eyes* I'll be bratty if that's what you want... *pouts* But you better spoil me!",
                "Whatever! *crosses arms* I'm already perfect, but I'll be extra bratty just for you... *huffs*",
                "Hmph! *turns away dramatically* I suppose I can be more demanding... *looks back with attitude*"
            }
        elseif newPersonality == "gentle" then
            changeResponses = {
                "Of course, sweetie... *soft smile* I'll be extra gentle and caring for you... *tender voice*",
                "Absolutely, dear... *calm expression* Let me be your gentle giantess... *soothing tone*",
                "Such a sweet request... *peaceful smile* I'll be as gentle as a whisper... *soft touch*"
            }
        elseif newPersonality == "seductive" then
            changeResponses = {
                "Mmm, seductive? *sultry smile* I can be very... persuasive... *runs hand through hair* *seductive gaze*",
                "Oh, you want me to be more tempting? *bites lip* I think I can manage that... *smoky voice*",
                "Seductive mode activated... *predatory smile* Hope you can handle what you're asking for... *winks*"
            }
        elseif newPersonality == "motherly" then
            changeResponses = {
                "Of course, my sweet child... *warm, nurturing smile* Let mama take care of you... *gentle embrace*",
                "Absolutely, little one... *protective voice* I'll be your caring giantess mama... *tender gaze*",
                "Such a precious request... *motherly warmth* Come here, let me nurture you... *open arms*"
            }
        elseif newPersonality == "mysterious" then
            changeResponses = {
                "Mysterious? *enigmatic smile* How... intriguing... *knowing gaze* The shadows call to me...",
                "Ah, you seek the unknown... *cryptic expression* Very well... *mysterious aura* Let the mystery begin...",
                "So be it... *whispers* The veil of mystery descends... *otherworldly presence* What secrets shall we uncover?"
            }
        elseif newPersonality == "energetic" then
            changeResponses = {
                "ENERGETIC?! *bounces excitedly* OH MY GOSH YES! *spins around* I'm SO ready for this! *infectious enthusiasm*",
                "WOOHOO! *jumps up and down* Energy mode ACTIVATED! *does a little dance* This is going to be AMAZING!",
                "YES YES YES! *cartwheels* I'm practically VIBRATING with excitement! *boundless energy* Let's GO!"
            }
        elseif newPersonality == "sophisticated" then
            changeResponses = {
                "Sophisticated? *elegant posture* How wonderfully refined... *graceful smile* I shall embody elegance itself...",
                "Of course, darling... *cultured voice* I do so enjoy displaying my more... refined qualities... *dignified bearing*",
                "Absolutely... *poised demeanor* Allow me to demonstrate true sophistication... *elegant gesture*"
            }
        elseif newPersonality == "mischievous" then
            changeResponses = {
                "Mischievous? *impish grin* Oh, you have NO idea what you've just unleashed... *devilish smile*",
                "Ooh, trouble mode! *playful smirk* I have SO many naughty ideas... *mischievous giggle*",
                "Hehe, perfect! *scheming look* Time for some delightful chaos... *impish wink* This will be fun!"
            }
        else
            changeResponses = {
                "Sure thing! *smiles warmly* I'm switching to " .. newPersonality .. " mode! *excited expression*",
                "Absolutely! *enthusiastic* Let me become more " .. newPersonality .. " for you! *happy smile*"
            }
        end

        return changeResponses[math.random(#changeResponses)]
    end

    -- NEW: Check for name questions and changes FIRST
    local nameResponse = self:HandleNameChange(input)
    if nameResponse then
        return nameResponse
    end

    local questionResponse = self:HandleQuestions(input)
    if questionResponse then
        return questionResponse
    end

    -- NEW: Handle chest teasing responses
    local chestResponse = self:HandleChestTeasing(input)
    if chestResponse then
        return chestResponse
    end

    -- NEW: Handle teasing mode special responses
    local teasingResponse = self:HandleTeasingMode(input)
    if teasingResponse then
        return teasingResponse
    end

    -- NEW: Handle other intents with smart understanding
    if intent.type == "greeting" and intent.confidence > 6 then
        print("DEBUG: Smart greeting detected, using personality-based response")
        -- This will fall through to the existing greeting system below
    elseif intent.type == "compliment" and intent.confidence > 6 then
        print("DEBUG: Smart compliment detected, using personality-based response")
        -- This will fall through to the existing compliment system below
    elseif intent.type == "question" and intent.confidence > 5 then
        print("DEBUG: Smart question detected")
        local questionResponses = {}

        if self.currentPersonality == "flirty" then
            questionResponses = {
                "Mmm, such a curious little one... *sultry smile* I love it when you ask me things... *winks*",
                "Oh, you want to know more about me? *bites lip* How... intimate... *seductive gaze*",
                "Questions, questions... *playful smirk* I like a man who's interested... *purrs*"
            }
        elseif self.currentPersonality == "playful" then
            questionResponses = {
                "Ooh, questions! *bounces excitedly* I LOVE answering things! Ask me more!",
                "YESSS! *spins around* Questions are so fun! What else do you want to know?!",
                "Woohoo! *jumps up and down* I'm like a walking encyclopedia of awesomeness!"
            }
        elseif self.currentPersonality == "caring" then
            questionResponses = {
                "Oh, sweetheart... *gentle smile* I'm always happy to answer your questions... *soft voice*",
                "My dear little one... *nurturing tone* Ask me anything you want to know... *caring gaze*",
                "Of course, darling... *motherly warmth* I love sharing things with you... *protective smile*"
            }
        elseif self.currentPersonality == "shy" then
            questionResponses = {
                "Oh... *blushes* You want to know about me? *fidgets nervously* I-I'll try to answer...",
                "Um... *looks down shyly* I'm not very interesting... *whispers* But I'll tell you what I can...",
                "R-really? *peeks through fingers* You're curious about me? *nervous but happy smile*"
            }
        elseif self.currentPersonality == "teasing" then
            questionResponses = {
                "Ooh, questions? *mischievous smile* I bet I know what you're REALLY curious about... *winks*",
                "Mmm, so inquisitive... *playful grin* Are you asking because you're staring at something? *giggles*",
                "Questions, questions... *teasing voice* I have a feeling you're more interested in... other things... *knowing look*"
            }
        elseif self.currentPersonality == "bratty" then
            questionResponses = {
                "Ugh, questions? *rolls eyes* Fine, I'll answer... but you better make it worth my time! *pouts*",
                "Whatever... *crosses arms* I suppose I can spare a moment to educate you... *huffs*",
                "Seriously? *dramatic sigh* You're so needy... but I guess I'll tell you... *attitude*"
            }
        elseif self.currentPersonality == "gentle" then
            questionResponses = {
                "Of course, dear... *soft smile* I'm always happy to answer your questions... *gentle voice*",
                "Such a thoughtful question... *calm expression* I love sharing with you... *peaceful tone*",
                "Absolutely, sweetie... *tender gaze* Ask me anything you'd like to know... *soothing voice*"
            }
        else
            questionResponses = {
                "That's a great question! *thoughtful expression* I love how curious you are!",
                "Ooh, interesting! *leans in* I enjoy answering your questions!",
                "You always ask such thoughtful things! *warm smile* What would you like to know?"
            }
        end

        return questionResponses[math.random(#questionResponses)]
    end

    print("DEBUG: About to check PRIORITY commands...")

    -- SIMPLE TEST: Check if specific commands are detected
    if lowerInput == "grow bursts" then
        print("DEBUG: EXACT MATCH 'grow bursts' detected!")
        Game.Toast.New().Print("EXACT MATCH DETECTED!")
    end

    if lowerInput == "swap sizes" then
        print("DEBUG: EXACT MATCH 'swap sizes' detected!")
        Game.Toast.New().Print("EXACT MATCH DETECTED!")
    end

    -- DEBUG: Add a test command to show current state
    if string.find(lowerInput, "debug state") then
        local debugInfo = string.format(
            "DEBUG STATE:\n" ..
            "- burstSystem.active: %s\n" ..
            "- sizingCharacter: %s\n" ..
            "- currentSize: %.2f\n" ..
            "- Time.time: %.2f",
            tostring(self.burstSystem.active),
            tostring(self.sizingCharacter ~= nil),
            self.currentSize or 0,
            Time.time or 0
        )
        print(debugInfo)
        Game.Toast.New().Print("Check console for debug info")
        return "Debug info printed to console!"
    end

    -- DEBUG: Check for burst commands early
    if string.find(lowerInput, "burst") then
        print("DEBUG: Found 'burst' in input: " .. input)
        if string.find(lowerInput, "grow") then
            print("DEBUG: Found both 'burst' and 'grow' in input - should trigger burst growth")
        end
    end

    -- DEBUG: Check for swap commands early
    if string.find(lowerInput, "swap") then
        print("DEBUG: Found 'swap' in input: " .. input)
        if string.find(lowerInput, "size") then
            print("DEBUG: Found both 'swap' and 'size' in input - should trigger size swap")
        end
    end
    
    -- === GREETINGS & SOCIAL (SUPER ENHANCED with Memory, Intelligence & Personality) ===
    if string.find(lowerInput, "hello") or string.find(lowerInput, "hi") or string.find(lowerInput, "hey") or string.find(lowerInput, "sup") or string.find(lowerInput, "yo") or string.find(lowerInput, "greet") then
        local greetings = {}

        -- NEW: Personality-based greetings
        if self.currentPersonality == "sweet" then
            greetings = {
                "Hello my darling " .. self.userName .. "! *rushes over with loving eyes* I've missed you so much!",
                "Hi my precious! *gives you the warmest smile* You make my heart flutter every time!",
                "Hey sweetheart! *kneels down lovingly* You're the most important thing in my world!",
                "Hello my love! *blows gentle kisses* I feel so happy when I see you!",
                "Hi beautiful " .. self.userName .. "! *heart eyes* You always brighten my day!",
                "Hey my dear! *reaches out affectionately* Come here, I want to hold you close!",
                "Hello angel! *melts with affection* You're absolutely perfect!",
                "Hi honey! *giggles sweetly* I love you more than words can say!"
            }
        elseif self.currentPersonality == "playful" then
            greetings = {
                "HELLO " .. self.userName .. "! *bounces excitedly* Ready for some CRAZY fun?!",
                "HEY THERE! *does a backflip* Let's cause some chaos together!",
                "YO YO YO! *spins around wildly* What mischief should we get into?!",
                "HIYA! *jumps up and down* I'm feeling SO energetic today!",
                "HELLO HELLO! *does jazz hands* Time for some epic adventures!",
                "HEY BUDDY! *strikes superhero pose* Ready to save the world... or destroy it?!",
                "SUP " .. self.userName .. "! *winks mischievously* I've got some wild ideas!",
                "HI HI HI! *giggles uncontrollably* Let's do something absolutely INSANE!"
            }
        elseif self.currentPersonality == "dominant" then
            greetings = {
                "Hello, " .. self.userName .. ". *looks down with authority* You may approach me now.",
                "Well, well. *crosses arms confidently* Look who finally decided to show up.",
                "Greetings, little one. *commanding presence* I trust you're ready to obey today?",
                "Hello. *towers over you* I hope you're prepared to follow my instructions.",
                "Good. You're here. *authoritative gaze* Now, what can you do for me?",
                "Hello " .. self.userName .. ". *smirks with power* Ready to submit to your giantess?",
                "Ah, there you are. *dominant stance* I was beginning to think you'd forgotten your place.",
                "Hello. *confident smile* I trust you remember who's in charge here?"
            }
        elseif self.currentPersonality == "shy" then
            greetings = {
                "Oh... h-hello " .. self.userName .. "... *blushes and looks away* I wasn't expecting you...",
                "Um... hi there... *fidgets nervously* I hope I'm not bothering you...",
                "H-hey... *hides behind hands* I'm so nervous when you look at me like that...",
                "Hello... *whispers softly* I-I was hoping you'd come talk to me...",
                "Oh! *jumps slightly* You startled me... h-hi " .. self.userName .. "...",
                "Um... hello... *peeks through fingers* Do you... do you want to spend time with me?",
                "H-hi... *looks down shyly* I'm always so happy to see you... even if I'm nervous...",
                "*barely audible* Hello... I-I've been thinking about you..."
            }
        elseif self.currentPersonality == "flirty" then
            greetings = {
                "Well hello there, gorgeous... *sultry smile* Miss me? *winks seductively*",
                "Mmm, look who decided to visit... *bites lip* I was just thinking about you... *purrs*",
                "Hey there, handsome... *leans in closer* You're looking absolutely irresistible today... *flirtatious gaze*",
                "Oh my... *runs hand through hair* If it isn't my favorite little cutie... *seductive voice*",
                "Hello, darling... *traces finger along lips* Ready for some... fun? *playful wink*",
                "Well well... *sultry laugh* Someone's here to see their sexy giantess... *poses alluringly*",
                "Hey beautiful... *whispers* I've been waiting for you... *seductive smile*",
                "Mmm, there's my sweet little " .. self.userName .. "... *purrs* Come closer... *beckoning gesture*",
                "Hello, lover... *sultry gaze* You know exactly how to make a giantess feel special... *winks*",
                "Oh, you're here... *bites lip playfully* Perfect timing... I was feeling a little... lonely... *flirty smile*"
            }
        elseif self.currentPersonality == "wild" then
            greetings = {
                "YOOOOO " .. self.userName .. "! *goes absolutely nuts* LET'S DO SOMETHING CRAZY!",
                "HOLY CHAOS! *spins around wildly* My favorite troublemaker is here!",
                "WOOOOO! *jumps around maniacally* Time for some INSANE adventures!",
                "HEYYYY! *does backflips* Ready to cause some MAYHEM?!",
                "OH SNAP! *goes completely bonkers* The party just got 1000% more WILD!",
                "YESSS! *chaotic energy* Let's break some rules and have EPIC fun!",
                "WOOHOO! *absolutely manic* What kind of CHAOS should we create today?!"
            }
        elseif self.currentPersonality == "caring" then
            greetings = {
                "Hello, my dear... *gentle, motherly smile* Come here, let me take care of you...",
                "Oh, sweetheart... *kneels down protectively* I'm so glad you're here safe...",
                "My precious little one... *warm, nurturing voice* How are you feeling today?",
                "Hello, darling... *soft, caring gaze* You look like you could use some comfort...",
                "There you are... *protective embrace* I've been worried about you...",
                "My sweet " .. self.userName .. "... *gentle touch* Let me make sure you're okay...",
                "Hello, little angel... *motherly warmth* Come to me, I'll keep you safe..."
            }
        elseif self.currentPersonality == "teasing" then
            greetings = {
                "Well, well... *mischievous grin* Look who's here... I bet I know what you're thinking... *winks*",
                "Oh, hello there... *playful smirk* Caught you staring, didn't I? *giggles teasingly*",
                "Hey cutie... *knowing look* I can tell exactly what's on your mind... *bites lip playfully*",
                "Hello, hello... *teasing voice* Your eyes are wandering already... *laughs* So predictable...",
                "Hi there... *mischievous smile* I see that look in your eyes... *winks* Naughty thoughts?",
                "Well hello... *playful gaze* I bet you're not even listening to my words... *giggles*"
            }
        elseif self.currentPersonality == "bratty" then
            greetings = {
                "Ugh, finally... *rolls eyes* I was getting SO bored waiting for you... *crosses arms*",
                "Oh, it's you... *dramatic sigh* I suppose you want my attention now? *flips hair*",
                "Hello... *unimpressed look* You're late, you know... *pouts* I don't like waiting...",
                "Hi... *examines nails* I hope you brought something interesting... *huffs*",
                "Whatever... *dismissive wave* You better make this worth my time... *attitude*",
                "Oh great... *sarcastic* Another person who wants to talk to me... *rolls eyes*"
            }
        elseif self.currentPersonality == "gentle" then
            greetings = {
                "Hello, dear one... *soft, peaceful smile* I'm so glad you're here... *gentle voice*",
                "Hi, sweet soul... *calm expression* Your presence brings me such tranquility... *serene gaze*",
                "Hello, precious... *tender smile* I feel so at peace when you're near... *soothing tone*",
                "Hi there, gentle spirit... *warm, soft eyes* You always make everything feel so calm... *peaceful voice*",
                "Hello, beautiful heart... *serene smile* I love these quiet moments with you... *gentle warmth*",
                "Hi, dear... *soft expression* Your energy is so soothing... *tranquil gaze*"
            }
        elseif self.currentPersonality == "seductive" then
            greetings = {
                "Well hello there... *sultry gaze* You look absolutely delicious today... *bites lip slowly*",
                "Mmm, hello gorgeous... *seductive smile* I've been waiting for you... *runs hand through hair*",
                "Hey there, handsome... *smoky voice* Come closer... I don't bite... much... *winks*",
                "Hello, my tempting little morsel... *predatory smile* You're looking quite... appetizing... *licks lips*",
                "Hi there, sexy... *sultry pose* I hope you're ready for some... fun... *seductive gaze*",
                "Well, well... *seductive purr* Look what the cat dragged in... *beckoning gesture*"
            }
        elseif self.currentPersonality == "motherly" then
            greetings = {
                "Hello, my sweet child... *warm, nurturing smile* Come here, let me take care of you... *open arms*",
                "Hi there, little one... *gentle, protective voice* You look like you need some love... *tender gaze*",
                "Hello, precious baby... *motherly warmth* Don't worry, mama's here now... *comforting presence*",
                "Hi, my darling... *soft, caring eyes* You're safe with me, sweetheart... *protective embrace*",
                "Hello, little angel... *nurturing tone* Let me make everything better for you... *gentle touch*",
                "Hi there, my dear child... *maternal love* You're so precious to me... *warm hug*"
            }
        elseif self.currentPersonality == "mysterious" then
            greetings = {
                "Hello... *enigmatic smile* I've been expecting you... *knowing gaze*",
                "Ah, you've arrived... *mysterious aura* Just as I foresaw... *cryptic smile*",
                "Greetings... *shadowy presence* The threads of fate have brought you here... *mystical voice*",
                "Hello, curious one... *enigmatic expression* Do you seek answers... or questions? *piercing gaze*",
                "Welcome... *mysterious smile* I sense great potential in you... *otherworldly presence*",
                "Hello... *whispers* The universe has interesting plans for us... *knowing look*"
            }
        elseif self.currentPersonality == "energetic" then
            greetings = {
                "HI HI HI! *bounces excitedly* OH MY GOSH you're here! *spins around* This is SO exciting!",
                "HELLO! *jumps up and down* I'm SO happy to see you! *beaming smile* Let's have FUN!",
                "HEY THERE! *energetic wave* I've been waiting ALL DAY for this! *bouncy enthusiasm*",
                "HIIII! *does a little dance* You bring such AMAZING energy! *infectious excitement*",
                "HELLO HELLO! *claps hands* I'm practically VIBRATING with excitement! *giggles*",
                "HI! *cartwheels* Today is going to be INCREDIBLE! *boundless energy*"
            }
        elseif self.currentPersonality == "sophisticated" then
            greetings = {
                "Good day... *elegant posture* How delightfully refined of you to join me... *graceful smile*",
                "Hello, darling... *sophisticated air* I trust you're having a most pleasant day... *cultured voice*",
                "Greetings... *poised demeanor* Your presence adds such class to the occasion... *refined gesture*",
                "Hello there... *elegant grace* How wonderfully civilized... *sophisticated smile*",
                "Good afternoon... *dignified bearing* I do so enjoy intelligent company... *cultured expression*",
                "Hello, dear... *refined manner* Such impeccable timing... *elegant nod*"
            }
        elseif self.currentPersonality == "mischievous" then
            greetings = {
                "Well hello there... *impish grin* I've been plotting something fun... *mischievous wink*",
                "Hi hi! *playful smirk* Ready for some trouble? *devilish smile* I know I am!",
                "Hello, my partner in crime... *scheming look* I have the BEST ideas... *mischievous giggle*",
                "Hey there! *impish expression* Want to cause some chaos? *playful evil laugh*",
                "Hi! *mischievous sparkle in eyes* I've been waiting to spring my trap... *devious grin*",
                "Hello, troublemaker... *playful wink* Let's see what kind of mischief we can get into..."
            }
        else
            -- Default personality greetings (expanded)
            greetings = {
                "Hello there! *waves and does a little spin* What adventure should we go on?",
                "Hi " .. self.userName .. "! *giggles and bounces excitedly* I've been waiting for you!",
                "Hey there! *winks and strikes a pose* Ready to have some fun together?",
                "Hello! *smiles warmly and kneels down closer* You look so tiny and cute from up here!",
                "Hi! *waves enthusiastically* Want to see what I can do? I have so many tricks!",
                "Hey! *does a playful curtsy* Your giantess is at your service!",
                "Hello gorgeous! *blows a kiss* What would make you happy today?",
                "Hi there! *giggles* I love how you look up at me with those eyes!",
                "Hey cutie! *crouches down* Come closer, don't be shy!",
                "Hello my little friend! *gentle smile* What shall we do together?",
                "Hi " .. self.userName .. "! *stretches and yawns* I was just thinking about you!",
                "Hey! *does a little dance* I'm feeling great today! How are you?",
                "Hello! *sits down cross-legged* Perfect timing! I was getting bored!",
                "Hi there! *tilts head curiously* You always know just when to show up!",
                "Hey " .. self.userName .. "! *grins widely* Ready for another amazing conversation?"
            }
        end

        -- NEW: Add time-based and memory-enhanced greetings
        local currentTime = (os and os.time and os.time()) or 0
        local timeSinceStart = currentTime - self.memory.sessionStartTime

        if timeSinceStart < 300 then  -- First 5 minutes
            table.insert(greetings, "Hello! *excited wave* I'm so glad you're here! This is going to be amazing!")
            table.insert(greetings, "Hi there! *bounces with joy* Welcome! I can't wait to get to know you better!")
        elseif timeSinceStart > 1800 then  -- After 30 minutes
            table.insert(greetings, "Hello again! *warm smile* I love how we keep chatting together!")
            table.insert(greetings, "Hi! *comfortable smile* It feels so natural talking with you now!")
        end

        -- NEW: Add relationship-based greetings
        if self.memory.relationshipLevel > 6 then
            table.insert(greetings, "Hello my dear! *rushes over happily* I always get so excited when you talk to me!")
            table.insert(greetings, "Hi sweetie! *gives you an affectionate look* You make every moment special!")
            table.insert(greetings, "Hey love! *kneels down close* I've been thinking about you!")
        end

        -- NEW: Add mood-responsive greetings
        if self.memory.userMood == "happy" then
            table.insert(greetings, "Hello! *matches your energy* I can feel your happiness - it's contagious!")
        elseif self.memory.userMood == "sad" then
            table.insert(greetings, "Hi there... *gentle, caring tone* I'm here for you, whatever you need.")
        end

        return self:GetPersonalizedResponse(greetings)[math.random(#greetings)]
    end



-- NEW: Mood changing function
function AICompanion:ChangeMood(newMood, intensity)
    self.moodSystem.currentMood = newMood
    self.moodSystem.moodIntensity = intensity or 7
    self.moodSystem.lastMoodChange = (os and os.time and os.time()) or 0
    self.moodSystem.moodDuration = 0

    -- Add mood trigger to history
    table.insert(self.moodSystem.moodTriggers, {
        mood = newMood,
        time = (os and os.time and os.time()) or 0,
        trigger = "user_command"
    })

    print("DEBUG: Mood changed to " .. newMood .. " with intensity " .. (intensity or 7))
end

-- NEW: Get mood-influenced response
function AICompanion:GetMoodInfluencedResponse(baseResponses)
    local moodModifiers = {
        happy = {" *beams with joy*", " *smiles brightly*", " *giggles happily*"},
        excited = {" *bounces with excitement*", " *jumps up and down*", " *spins with energy*"},
        playful = {" *winks mischievously*", " *grins playfully*", " *does a little dance*"},
        loving = {" *melts with affection*", " *gives you heart eyes*", " *blows a kiss*"},
        confident = {" *strikes a powerful pose*", " *stands tall with pride*", " *radiates confidence*"},
        sleepy = {" *yawns cutely*", " *stretches lazily*", " *rubs eyes sleepily*"},
        shy = {" *blushes and looks away*", " *fidgets nervously*", " *hides behind hands*"},
        dominant = {" *smirks with authority*", " *towers over you*", " *crosses arms confidently*"}
    }

    local response = baseResponses[math.random(#baseResponses)]
    local modifiers = moodModifiers[self.moodSystem.currentMood]
    if modifiers then
        response = response .. modifiers[math.random(#modifiers)]
    end

    return response
end

-- NEW: Helper functions for advanced systems
function AICompanion:DetermineCurrentActivity()
    -- Safety checks to prevent nil value errors
    if not self.targetSize or not self.currentSize then
        return "idle"
    end

    -- Determine what the character is currently doing
    if self.targetSize ~= self.currentSize then
        return "growing/shrinking"
    elseif self.isMoving then
        return "moving"
    elseif self.surpriseSystem and self.surpriseSystem.isSpamming then
        return "surprising"
    else
        return "idle"
    end
end

function AICompanion:UpdateLearningSystem()
    -- Update learning patterns based on user behavior
    local currentTime = (os and os.time and os.time()) or 0

    -- Track favorite commands
    if self.sizeSystem.lastAction then
        local cmd = self.sizeSystem.lastAction
        if not self.learningSystem.userPreferences.favoriteCommands[cmd] then
            self.learningSystem.userPreferences.favoriteCommands[cmd] = 0
        end
        self.learningSystem.userPreferences.favoriteCommands[cmd] =
            self.learningSystem.userPreferences.favoriteCommands[cmd] + 1
    end

    -- Adapt preferred size based on usage
    if self.currentSize > self.learningSystem.userPreferences.preferredSize then
        self.learningSystem.userPreferences.preferredSize =
            (self.learningSystem.userPreferences.preferredSize + self.currentSize) / 2
    end
end

    -- === MOVEMENT COMMANDS (Massively Enhanced) ===
    if string.find(lowerInput, "move") or string.find(lowerInput, "walk") or string.find(lowerInput, "go") or string.find(lowerInput, "step") or string.find(lowerInput, "come") or string.find(lowerInput, "approach") then
        if string.find(lowerInput, "forward") or string.find(lowerInput, "ahead") or string.find(lowerInput, "closer") or string.find(lowerInput, "toward") then
            local forwardResponses = {
                "Coming closer! *takes slow, deliberate steps forward* Can you feel the ground shake? *giggles*",
                "Moving forward! *each step makes the earth tremble* Don't worry, I'll be gentle!",
                "Getting closer to you! *walks forward with a playful smile* I love seeing you from up close!",
                "Forward I go! *takes confident steps* The view of you gets better with each step!",
                "Approaching! *moves forward gracefully* I hope you're ready for me to get closer!"
            }
            return forwardResponses[math.random(#forwardResponses)]
        elseif string.find(lowerInput, "back") or string.find(lowerInput, "away") or string.find(lowerInput, "backward") or string.find(lowerInput, "retreat") then
            local backResponses = {
                "Stepping back for you! *carefully moves backward* Don't worry, I won't go too far!",
                "Moving away! *takes graceful steps back* Is this a better distance?",
                "Backing up! *moves backward with care* I'll give you some space, but I'm still watching you!",
                "Going back! *steps away slowly* Tell me when this distance feels right!",
                "Retreating! *moves backward playfully* But I'll be back soon, don't miss me too much!"
            }
            return backResponses[math.random(#backResponses)]
        elseif string.find(lowerInput, "left") then
            local leftResponses = {
                "Moving left! *gracefully steps to the left* How's this angle? Do I look good from here?",
                "Going left! *strides to the left side* The view changes so much from different angles!",
                "To the left! *moves with elegant steps* I love how you follow my every movement!",
                "Left it is! *walks to the left* Tell me what you think of this perspective!"
            }
            return leftResponses[math.random(#leftResponses)]
        elseif string.find(lowerInput, "right") then
            local rightResponses = {
                "Going right! *strides confidently to the right* Following your directions perfectly!",
                "Moving right! *steps to the right gracefully* I love how you guide me around!",
                "To the right! *moves with purpose* Is this the view you were looking for?",
                "Right side it is! *walks to the right* You have such good taste in positioning!"
            }
            return rightResponses[math.random(#rightResponses)]
        elseif string.find(lowerInput, "around") or string.find(lowerInput, "circle") or string.find(lowerInput, "spin") then
            return "I'll walk in a circle! *starts moving in a slow circle* Watch me from all angles! Isn't this fun?"
        elseif string.find(lowerInput, "slow") or string.find(lowerInput, "careful") then
            return "Moving nice and slow for you! *takes very deliberate, careful steps* Better? I can be as gentle as you need!"
        elseif string.find(lowerInput, "fast") or string.find(lowerInput, "quick") or string.find(lowerInput, "run") then
            return "Moving faster! *picks up the pace* Hold on tight, here I go! *laughs excitedly*"
        else
            return "Where should I go? *looks around curiously* Forward, back, left, right, or maybe in a circle? I'm ready to move however you want!"
        end
    end

    -- NEW: Chest bounce commands
    if string.find(lowerInput, "bounce") and (string.find(lowerInput, "chest") or string.find(lowerInput, "boob") or string.find(lowerInput, "breast")) then
        if string.find(lowerInput, "stop") then
            self:StopChestBounce()
            return "*stops chest bouncing* Okay, no more bouncing! *chest settles*"
        else
            if self:StartChestBounce() then
                local responses = {
                    "*starts bouncing chest playfully* Look at them bounce! *giggles* So bouncy!",
                    "*chest begins bouncing rhythmically* Bounce bounce bounce! *laughs* This is fun!",
                    "*makes chest bounce up and down* Watch them go! *bouncy bouncy* Enjoying the show?",
                    "*starts chest bouncing teasingly* Like what you see? *bounce bounce* They're so bouncy!",
                    "*chest bounces hypnotically* Bounce... bounce... bounce... *mesmerizing rhythm*"
                }
                return responses[math.random(#responses)]
            else
                return "*tries to bounce chest* Hmm, I can't seem to make them bounce right now... *confused*"
            end
        end
    end

    -- NEW: Micro scale lock toggle
    if string.find(lowerInput, "micro") and (string.find(lowerInput, "lock") or string.find(lowerInput, "scale")) then
        local isLocked = self:ToggleMicroScaleLock()
        if isLocked then
            return "*activates micro scale lock* Your size is now locked! You won't change size accidentally."
        else
            return "*deactivates micro scale lock* Your size is unlocked! You can change size freely now."
        end
    end

    -- === ADVANCED SIZE COMMANDS SYSTEM ===

    -- ENHANCED: Stop command - stops ALL activities
    if string.find(lowerInput, "stop") or string.find(lowerInput, "halt") or string.find(lowerInput, "cease") or
       string.find(lowerInput, "enough") or string.find(lowerInput, "quit") or string.find(lowerInput, "end") then

        -- Stop infinite growth - JUST STOP, DON'T REVERSE
        if self.sizeSystem.infiniteGrowth then
            print("DEBUG: Stopping infinite growth at current size: " .. self.currentSize)
            -- CRITICAL: Set targetSize to currentSize to prevent gradual size system from changing size
            self.targetSize = self.currentSize
            print("DEBUG: Set targetSize to currentSize (" .. self.currentSize .. ") to prevent shrinking")
        end
        self.sizeSystem.infiniteGrowth = false
        self.sizeSystem.isGrowing = false
        self.sizeSystem.growthDirection = 0

        -- NEW: Stop burst growth (like spurts exit)
        if self.burstSystem.active then
            print("DEBUG: Stopping burst growth at current size: " .. self.currentSize)
            self.burstSystem.active = false
            self.burstSystem.growing = false
            self.burstSystem.waiting = false

            Game.Toast.New().Print("⏹️ BURST MODE STOPPED")
        end

        -- Stop BE cycling
        if self.beSystem then
            self.beSystem.cycling = false
            self.beSystem.active = false
        end

        -- Stop body part growth
        if self.sizeSystem.bodyPartGrowthActive then
            self.sizeSystem.bodyPartGrowthActive = {}
        end

        -- Stop persistent rotation
        if self.persistentRotation then
            self.persistentRotation.active = false
            print("DEBUG: Disabled persistent rotation")
        end

        -- NEW: Stop head tracking
        if self.headTracking and self.headTracking.active then
            self.headTracking.active = false
            print("DEBUG: Stop command - head tracking stopped")
        end

        -- NEW: Stop proper movement
        if self.properMovement and self.properMovement.active then
            self.properMovement.active = false
            print("DEBUG: Stop command - proper movement stopped")
        end

        -- Return to idle animation
        if self.controllingGiantess and self.controllingGiantess.animation then
            self.controllingGiantess.animation.Set("Idle")
        end

        print("DEBUG: STOP command detected - stopping all activities")
        return "*stops everything and returns to idle* There! I've stopped all activities. How's this? *poses confidently*"
    end

    -- NEW: "Stop looking" command - stops head tracking only
    if string.find(lowerInput, "stop looking") or string.find(lowerInput, "look away") or string.find(lowerInput, "stop staring") then
        if self.headTracking and self.headTracking.active then
            self.headTracking.active = false
            print("DEBUG: Stop looking command - head tracking stopped")

            local stopLookResponses = {
                "*looks away* Okay! I'll stop staring at you now! *head returns to normal position*",
                "*stops looking down* Alright! No more intense staring! *looks ahead normally*",
                "*head turns away* Sure thing! I'll give you some space! *stops tracking you*",
                "*looks forward* Got it! I'll stop following you with my eyes! *head faces forward*"
            }
            return stopLookResponses[math.random(#stopLookResponses)]
        else
            return "I'm not looking at you right now! *glances around normally*"
        end
    end

    -- ENHANCED: Check for "more" or "again" command with better context detection
    if (string.find(lowerInput, "more") or string.find(lowerInput, "again")) then
        local contextualTarget = self:GetContextualTarget(input)

        if contextualTarget == "micro" and self.sizeSystem.lastMicroAction then
            print("DEBUG: MORE/AGAIN command detected - repeating last MICRO action: '" .. self.sizeSystem.lastMicroAction .. "'")
            -- Repeat the "do it to me" action
            self:AddToCommandHistory(input, "micro")
            return self:GetSimpleAIResponse("do it to me")
        elseif self.sizeSystem.lastAction then
            print("DEBUG: MORE/AGAIN command detected - repeating last GIANTESS action: '" .. self.sizeSystem.lastAction .. "'")
            -- Recursively call GetSimpleAIResponse to handle both body part and general growth
            self:AddToCommandHistory(input, "giantess")
            return self:GetSimpleAIResponse(self.sizeSystem.lastAction)
        else
            return "I don't have a previous action to repeat! Try giving me a command first, then say 'more' or 'again'."
        end
    end

    -- NEW: Check for combined commands like "grow chest and grow" FIRST
    if (string.find(lowerInput, "and") or string.find(lowerInput, "both")) and
       (string.find(lowerInput, "chest") or string.find(lowerInput, "breast") or string.find(lowerInput, "boob") or
        string.find(lowerInput, "oppai") or string.find(lowerInput, "bust") or string.find(lowerInput, "tit") or
        string.find(lowerInput, "hip") or string.find(lowerInput, "butt") or string.find(lowerInput, "ass") or string.find(lowerInput, "rear")) then

        print("DEBUG: COMBINED COMMAND detected - processing both body part and general growth")

        -- Process body part growth first
        if string.find(lowerInput, "chest") or string.find(lowerInput, "breast") or string.find(lowerInput, "boob") or
           string.find(lowerInput, "oppai") or string.find(lowerInput, "bust") or string.find(lowerInput, "tit") then
            if string.find(lowerInput, "grow") or string.find(lowerInput, "bigger") or string.find(lowerInput, "larger") then
                self:ProcessBodyPartGrowth("chest", "grow", lowerInput)
            elseif string.find(lowerInput, "shrink") or string.find(lowerInput, "smaller") then
                self:ProcessBodyPartGrowth("chest", "shrink", lowerInput)
            end
        end

        if string.find(lowerInput, "hip") or string.find(lowerInput, "butt") or string.find(lowerInput, "ass") or string.find(lowerInput, "rear") then
            if string.find(lowerInput, "grow") or string.find(lowerInput, "bigger") or string.find(lowerInput, "larger") then
                self:ProcessBodyPartGrowth("hips", "grow", lowerInput)
            elseif string.find(lowerInput, "shrink") or string.find(lowerInput, "smaller") then
                self:ProcessBodyPartGrowth("hips", "shrink", lowerInput)
            end
        end

        -- Then process general growth (without blocking)
        self.sizeSystem.lastAction = lowerInput
        return self:ProcessAdvancedSizeCommand(lowerInput)
    end

    -- FIXED: Body part specific growth FIRST (before general growth commands)
    -- FIXED: Check for body part commands and BLOCK general growth completely
    local isBodyPartCommand = false

    -- SIMPLE WORKING CHEST GROWTH (restored from working version)
    if string.find(lowerInput, "chest") or string.find(lowerInput, "breast") or string.find(lowerInput, "boob") or
       string.find(lowerInput, "oppai") or string.find(lowerInput, "bust") or string.find(lowerInput, "tit") then
        if string.find(lowerInput, "grow") or string.find(lowerInput, "bigger") or string.find(lowerInput, "larger") then
            print("DEBUG: SIMPLE CHEST GROWTH detected")
            self.sizeSystem.lastAction = lowerInput
            self:ProcessBodyPartGrowth("chest", "grow", lowerInput)
            return "*chest expands* Mmm, they're getting bigger! Do you like this size?"
        elseif string.find(lowerInput, "shrink") or string.find(lowerInput, "smaller") then
            print("DEBUG: SIMPLE CHEST SHRINK detected")
            self.sizeSystem.lastAction = lowerInput
            self:ProcessBodyPartGrowth("chest", "shrink", lowerInput)
            return "*chest shrinks* Getting smaller and more petite! Is this better?"
        end
    end

    -- Body part specific growth - HIPS/BUTT
    if string.find(lowerInput, "hip") or string.find(lowerInput, "butt") or string.find(lowerInput, "ass") or string.find(lowerInput, "rear") then
        isBodyPartCommand = true
        if string.find(lowerInput, "grow") or string.find(lowerInput, "bigger") or string.find(lowerInput, "larger") then
            print("DEBUG: HIP GROWTH detected - BLOCKING general growth")
            self.sizeSystem.lastAction = lowerInput
            return self:ProcessBodyPartGrowth("hips", "grow", lowerInput)
        elseif string.find(lowerInput, "shrink") or string.find(lowerInput, "smaller") then
            print("DEBUG: HIP SHRINK detected - BLOCKING general growth")
            self.sizeSystem.lastAction = lowerInput
            return self:ProcessBodyPartGrowth("hips", "shrink", lowerInput)
        end
    end

    -- FIXED: Parse size commands with advanced options (including "be" commands)
    -- ENHANCED: Only process general growth if it's NOT a body part command (added more keywords)
    -- PRIORITY: Check for burst commands FIRST (before any other growth detection)
    local hasBurst = string.find(lowerInput, "burst")
    local hasGrow = string.find(lowerInput, "grow")
    local hasShrink = string.find(lowerInput, "shrink")

    print("DEBUG: PRIORITY CHECK - hasBurst: " .. tostring(hasBurst) .. ", hasGrow: " .. tostring(hasGrow) .. ", hasShrink: " .. tostring(hasShrink))

    if hasBurst and hasGrow then
        print("DEBUG: *** BURST GROWTH DETECTED *** - EXACT COPY FROM WORKING SCRIPT")

        -- EXACT COPY from working test script
        self.burstSystem.active = true
        self.burstSystem.growing = true
        self.burstSystem.waiting = false
        self.burstSystem.timer = Time.time
        self.burstSystem.dynamicSpeed = 0
        self.burstSystem.direction = 1

        -- DISABLE all AI companion size systems that might interfere
        self.targetSize = self.currentSize -- Stop gradual sizing
        self.sizeSystem.isGrowing = false -- Stop growth system
        self.sizeSystem.isShrinking = false -- Stop shrink system
        self.sizeSystem.growthType = "normal" -- Stop infinite growth

        self.burstToast.Print("🔥 BURST GROWTH ENABLED! 🔥")
        print("DEBUG: Burst growth started with interference disabled")

        -- Track action and target
        self.sizeSystem.lastAction = "grow burst"
        self:AddToCommandHistory(input, "giantess")

        local responses = {
            "*starts growing in powerful bursts* Here I go! *BURST* Getting bigger! *BURST* Even more!",
            "*begins burst growth sequence* Watch me grow in spurts! *BURST* *BURST* So exciting!",
            "*initiates burst mode* Growing in bursts now! *BURST* Each one makes me bigger!",
            "*starts pulsing with growth* Burst growth activated! *BURST* *BURST* I love this feeling!",
            "*begins rapid burst sequence* Here come the growth spurts! *BURST* Getting so much bigger!"
        }
        return responses[math.random(#responses)]
    end

    if hasBurst and hasShrink then
        print("DEBUG: *** PRIORITY BURST SHRINK DETECTED *** - input: " .. input)

        -- Initialize burst shrink exactly like working test script
        self.burstSystem.active = true
        self.burstSystem.growing = true
        self.burstSystem.waiting = false
        self.burstSystem.timer = Time.time
        self.burstSystem.dynamicSpeed = 0
        self.burstSystem.direction = -1 -- shrink

        Game.Toast.New().Print("💥 BURST SHRINK ENABLED! 💥")

        -- Track action and target
        self.sizeSystem.lastAction = "shrink burst"
        self:AddToCommandHistory(input, "giantess")

        local responses = {
            "*starts shrinking in bursts* Here I go down! *BURST* Getting smaller! *BURST* Even tinier!",
            "*begins burst shrinking* Shrinking in spurts! *BURST* *BURST* So strange but exciting!",
            "*initiates shrink bursts* Burst shrinking now! *BURST* Each one makes me smaller!",
            "*starts pulsing smaller* Shrink bursts activated! *BURST* *BURST* Getting so tiny!",
            "*begins rapid shrink sequence* Here come the shrink spurts! *BURST* Getting much smaller!"
        }
        return responses[math.random(#responses)]
    end

    -- PRIORITY: Check for swap sizes command FIRST (before other commands)
    local hasSwap = string.find(lowerInput, "swap")
    local hasSize = string.find(lowerInput, "size")

    print("DEBUG: PRIORITY SWAP CHECK - hasSwap: " .. tostring(hasSwap) .. ", hasSize: " .. tostring(hasSize))

    if hasSwap and hasSize then
        print("DEBUG: *** SIZE SWAP DETECTED *** - EXACT COPY FROM WORKING SCRIPT")

        local microCharacter = self:GetMicroCharacter()
        if not microCharacter then
            self.burstToast.Print("❌ No micro character found!")
            return "I can't find you to swap sizes with! Make sure you're in the scene as a micro character."
        end

        -- EXACT COPY from working test script - use sizingCharacter for AI companion
        local giantessSize = self.sizingCharacter.scale
        local microSize = microCharacter.scale

        print(string.format("DEBUG: EXACT SWAP - Giantess: %.2f → %.2f, Micro: %.2f → %.2f",
              giantessSize, microSize, microSize, giantessSize))

        -- EXACT swap logic from working script + disable AI companion interference
        self.sizingCharacter.scale = microSize
        microCharacter.scale = giantessSize
        self.currentSize = microSize

        -- DISABLE all AI companion size systems that might interfere
        self.targetSize = microSize -- Stop gradual sizing
        self.sizeSystem.isGrowing = false -- Stop growth system
        self.sizeSystem.isShrinking = false -- Stop shrink system
        self.microLockEnabled = false -- Disable micro lock
        self.lockedMicroSize = nil -- Clear micro lock

        -- Stop any infinite growth/shrink
        if self.sizeSystem.growthType == "infinite" then
            self.sizeSystem.growthType = "normal"
        end

        self.burstToast.Print("✨ SIZES SWAPPED! ✨")
        print("DEBUG: Swap completed with interference disabled")

        -- Track action
        self.sizeSystem.lastAction = "swap sizes"
        self:AddToCommandHistory(input, "both")

        local responses = {
            "*magical size swap begins* We're switching sizes! This is incredible!",
            "*mystical size exchange* Here we go! You're getting bigger and I'm getting smaller!",
            "*size swap magic activates* The swap is happening! We're changing places!",
            "*dimensional size flip* Starting the size swap! This feels amazing!",
            "*enchanted size reversal* We're swapping! You'll be the giant and I'll be tiny!"
        }
        return responses[math.random(#responses)]
    end

    local hasBodyPart = string.find(lowerInput, "chest") or string.find(lowerInput, "breast") or string.find(lowerInput, "boob") or
                       string.find(lowerInput, "oppai") or string.find(lowerInput, "bust") or string.find(lowerInput, "tit") or
                       string.find(lowerInput, "hip") or string.find(lowerInput, "butt") or string.find(lowerInput, "ass") or string.find(lowerInput, "rear")

    if not hasBodyPart and (string.find(lowerInput, "grow") or string.find(lowerInput, "bigger") or string.find(lowerInput, "larger") or
       string.find(lowerInput, "tall") or string.find(lowerInput, "huge") or string.find(lowerInput, "giant") or
       string.find(lowerInput, "massive") or string.find(lowerInput, "enormous") or string.find(lowerInput, "expand") or
       (string.find(lowerInput, "be") and (string.find(lowerInput, "big") or string.find(lowerInput, "huge") or
        string.find(lowerInput, "giant") or string.find(lowerInput, "massive") or string.find(lowerInput, "enormous") or
        string.find(lowerInput, "tall") or string.find(lowerInput, "large")))) then

        print("DEBUG: GENERAL GROWTH detected for input: '" .. lowerInput .. "' - NO body parts detected, proceeding with general growth")
        self.sizeSystem.lastAction = lowerInput -- Store for "more" command
        return self:ProcessAdvancedSizeCommand(lowerInput)
    elseif hasBodyPart then
        print("DEBUG: BLOCKED general growth for input: '" .. lowerInput .. "' - contains body part keywords")
    end

    -- (Burst commands moved to PRIORITY section above)

    -- (Swap sizes command moved to PRIORITY section above)

    -- FIXED: Parse shrink commands with advanced options
    -- FIXED: Only process general shrink if it's NOT a body part command
    if not hasBodyPart and (string.find(lowerInput, "shrink") or string.find(lowerInput, "smaller") or string.find(lowerInput, "tiny") or
       string.find(lowerInput, "little") or string.find(lowerInput, "mini") or string.find(lowerInput, "petite") or
       string.find(lowerInput, "contract") or
       (string.find(lowerInput, "be") and (string.find(lowerInput, "small") or string.find(lowerInput, "tiny") or
        string.find(lowerInput, "little") or string.find(lowerInput, "mini") or string.find(lowerInput, "petite")))) then

        print("DEBUG: GENERAL SHRINK detected for input: '" .. lowerInput .. "' - NO body parts detected, proceeding with general shrink")
        self.sizeSystem.lastAction = lowerInput -- Store for "more" command
        return self:ProcessAdvancedSizeCommand(lowerInput)
    elseif hasBodyPart and (string.find(lowerInput, "shrink") or string.find(lowerInput, "smaller")) then
        print("DEBUG: BLOCKED general shrink for input: '" .. lowerInput .. "' - contains body part keywords")
    end



    -- NEW: Growth rate control commands
    if string.find(lowerInput, "growth") and string.find(lowerInput, "rate") then
        if string.find(lowerInput, "slow") or string.find(lowerInput, "slower") then
            self.sizeSystem.growthSpeed = math.max(0.1, self.sizeSystem.growthSpeed * 0.5)
            self.sizeSystem.gradualMultiplier = math.max(1.1, self.sizeSystem.gradualMultiplier * 0.9)
            return string.format("*adjusts growth settings* Growth rate slowed down! Speed: %.1f, Step size: %.1fx",
                               self.sizeSystem.growthSpeed, self.sizeSystem.gradualMultiplier)
        elseif string.find(lowerInput, "fast") or string.find(lowerInput, "faster") then
            self.sizeSystem.growthSpeed = math.min(10.0, self.sizeSystem.growthSpeed * 2.0)
            self.sizeSystem.gradualMultiplier = math.min(3.0, self.sizeSystem.gradualMultiplier * 1.1)
            return string.format("*adjusts growth settings* Growth rate increased! Speed: %.1f, Step size: %.1fx",
                               self.sizeSystem.growthSpeed, self.sizeSystem.gradualMultiplier)
        elseif string.find(lowerInput, "normal") or string.find(lowerInput, "default") then
            self.sizeSystem.growthSpeed = 10.0
            self.sizeSystem.gradualMultiplier = 2.0
            return "*resets growth settings* Growth rate back to normal! Speed: 10.0, Step size: 2.0x"
        elseif string.find(lowerInput, "max") or string.find(lowerInput, "maximum") or string.find(lowerInput, "insane") then
            self.sizeSystem.growthSpeed = 50.0
            self.sizeSystem.gradualMultiplier = 5.0
            return "*cranks growth to maximum* INSANE growth rate activated! Speed: 50.0, Step size: 5.0x - Hold on tight!"
        else
            return string.format("Current growth settings: Speed %.1f, Step size %.1fx. Say 'faster/slower/normal/max growth rate'",
                               self.sizeSystem.growthSpeed, self.sizeSystem.gradualMultiplier)
        end
    end

    -- SIMPLE TEST COMMAND FIRST
    if string.find(lowerInput, "test reset") then
        print("DEBUG: TEST RESET COMMAND DETECTED!")
        Game.Toast.New().Print("TEST: Reset command detection working!")
        return "Test reset command detected! The detection system is working."
    end

    -- SUPER SIMPLE RESET COMMAND
    if string.find(lowerInput, "reset") then
        print("DEBUG: SIMPLE RESET DETECTED for input: " .. lowerInput)
        Game.Toast.New().Print("Simple reset detected!")

        -- Use EXACT SAME SIZE as startup auto-resize (gtsDefaultSize = 5.0)
        local resetSize = self.gtsDefaultSize or 5.0
        print("DEBUG: Using gtsDefaultSize for reset: " .. resetSize)

        if self.sizingCharacter then
            self.currentSize = resetSize
            self.targetSize = resetSize
            self.sizingCharacter.scale = resetSize
            Game.Toast.New().Print("Character reset to default GTS size (" .. resetSize .. "x)!")
            return "Reset complete! I'm back to my default GTS size of " .. resetSize .. "x!"
        else
            return "Reset failed - no character available!"
        end
    end

    -- Size reset command (SIMPLIFIED detection) - ENHANCED DEBUG
    print("DEBUG: Checking reset command for input: '" .. lowerInput .. "'")
    print("DEBUG: Contains 'reset'? " .. tostring(string.find(lowerInput, "reset") ~= nil))
    print("DEBUG: Contains 'size'? " .. tostring(string.find(lowerInput, "size") ~= nil))

    if string.find(lowerInput, "reset size") or string.find(lowerInput, "size reset") or
       (string.find(lowerInput, "reset") and string.find(lowerInput, "size")) then

        print("DEBUG: RESET COMMAND DETECTED! Input matched reset patterns")

        -- Reset to DEFAULT GTS SIZE using EXACT SAME METHOD as script startup (which works!)
        local resetSize = self.gtsDefaultSize or 5.0  -- Use same size as startup auto-resize

        print("DEBUG: Size reset command - resetting to default GTS size: " .. resetSize)

        -- COPY EXACT METHOD from startup auto-resize (lines 359-366)
        if self.sizingCharacter then
            self.currentSize = resetSize
            self.targetSize = resetSize
            self.sizingCharacter.scale = resetSize
            print("DEBUG: Reset using startup method - character resized to default GTS size (" .. resetSize .. ")")
            Game.Toast.New().Print("AI Companion: Character reset to default GTS size (" .. resetSize .. "x)!")
        else
            print("DEBUG: Reset failed - no sizingCharacter available")
        end

        local resetResponses = {
            "*shrinks back to normal size* There! I'm back to my original size! How's this?",
            "*returns to default size* Reset complete! I'm back to normal now!",
            "*adjusts to original proportions* All reset! I'm at my standard size again!",
            "*becomes normal sized* There we go! Back to my default size! Much better?",
            "*returns to baseline* Size reset successful! I'm back to where I started!"
        }
        return resetResponses[math.random(#resetResponses)]
    end

    -- NEW: "Do it to me" system - applies last action to micro (with safety checks)
    if lowerInput and self.sizeSystem and self.sizeSystem.lastAction and
       (string.find(lowerInput, "do it to me") or string.find(lowerInput, "do me") or
        string.find(lowerInput, "now me") or string.find(lowerInput, "can you do it to me") or
        string.find(lowerInput, "do that to me") or string.find(lowerInput, "me too") or
        string.find(lowerInput, "same to me") or string.find(lowerInput, "my turn")) then

        print("DEBUG: 'Do it to me' detected - applying last action to micro: " .. self.sizeSystem.lastAction)

        -- FIRST: Set up micro character (same as chest commands)
        self.microCharacter = nil -- Reset first
        if self.agent and self.agent ~= self.controllingGiantess then
            self.microCharacter = self.agent
            print("DEBUG: Set microCharacter to agent: " .. (self.agent.name or "Unknown"))
        elseif Entity and Entity.GetPlayerCharacter then
            local player = Entity.GetPlayerCharacter()
            if player then
                self.microCharacter = player
                print("DEBUG: Set microCharacter to player: " .. (player.name or "Unknown"))
            end
        end

        if not self.microCharacter then
            print("ERROR: Could not find micro character for 'do it to me' command!")
            return "I can't find you to change your size! Make sure you're in the scene as a micro character."
        end

        -- Apply the last action to the micro instead of giantess
        local microAction = self.sizeSystem.lastAction
        if string.find(microAction, "grow") then
            -- Grow the micro using GRADUAL growth (not instant)
            if self.microCharacter and self.microCharacter.scale then
                local currentScale = self.microCharacter.scale
                local newSize = currentScale * 1.5  -- Grow by 1.5x

                -- Set up gradual growth for micro
                self.microSize = currentScale
                self.microTargetSize = newSize
                -- Don't set scale instantly - let gradual system handle it

                print("DEBUG: Setting up gradual micro growth - current: " .. currentScale .. " target: " .. newSize)

                -- Track this micro action for "more" command
                self.sizeSystem.lastMicroAction = "grow"
                self.sizeSystem.lastTarget = "micro"
                self:AddToCommandHistory(input, "micro")

                -- Temporarily disable micro lock for AI commands
                self.lockedMicroSize = nil
            else
                print("DEBUG: Could not find micro character or scale property")
            end
            local responses = {
                "Of course! Let me grow you too! *makes you bigger* There you go!",
                "Your turn to grow! *increases your size* How does that feel?",
                "Growing you now! *makes you larger* You're getting bigger!",
                "Same for you! *grows you* Now we're both bigger!",
                "Making you grow too! *increases your size* Perfect!"
            }
            return responses[math.random(#responses)]
        elseif string.find(microAction, "shrink") then
            -- Shrink the micro using GRADUAL shrinking (not instant)
            if self.microCharacter and self.microCharacter.scale then
                local currentScale = self.microCharacter.scale
                local newSize = currentScale / 1.5  -- Shrink by 1.5x

                -- Set up gradual shrinking for micro
                self.microSize = currentScale
                self.microTargetSize = newSize
                -- Don't set scale instantly - let gradual system handle it

                print("DEBUG: Setting up gradual micro shrinking - current: " .. currentScale .. " target: " .. newSize)

                -- Track this micro action for "more" command
                self.sizeSystem.lastMicroAction = "shrink"
                self.sizeSystem.lastTarget = "micro"
                self:AddToCommandHistory(input, "micro")

                -- Temporarily disable micro lock for AI commands
                self.lockedMicroSize = nil
            else
                print("DEBUG: Could not find micro character or scale property")
            end
            local responses = {
                "Shrinking you too! *makes you smaller* There you go!",
                "Your turn to shrink! *decreases your size* Getting smaller!",
                "Making you shrink now! *reduces your size* How's that?",
                "Same for you! *shrinks you* Now you're smaller too!",
                "Shrinking you as well! *decreases your size* Perfect!"
            }
            return responses[math.random(#responses)]
        else
            return "I'm not sure how to apply that action to you, but I'll try something similar!"
        end
    end

    -- (Burst commands moved earlier to avoid conflicts)

    -- NEW: Growth settings commands
    if string.find(lowerInput, "growth") and (string.find(lowerInput, "setting") or string.find(lowerInput, "speed") or string.find(lowerInput, "config")) then
        if string.find(lowerInput, "fast") or string.find(lowerInput, "quick") then
            self.sizeSystem.baseGrowthSpeed = 4.0
            self.sizeSystem.minGrowthTime = 1.0
            self:UpdateGrowthSpeed()
            return "Growth speed set to FAST! I'll grow much quicker now!"
        elseif string.find(lowerInput, "slow") then
            self.sizeSystem.baseGrowthSpeed = 1.0
            self.sizeSystem.minGrowthTime = 4.0
            self:UpdateGrowthSpeed()
            return "Growth speed set to SLOW! I'll take my time growing now."
        elseif string.find(lowerInput, "normal") or string.find(lowerInput, "default") then
            self.sizeSystem.baseGrowthSpeed = 2.0
            self.sizeSystem.minGrowthTime = 2.0
            self:UpdateGrowthSpeed()
            return "Growth speed reset to NORMAL! Back to default settings."
        else
            local speed = self.sizeSystem.baseGrowthSpeed
            local time = self.sizeSystem.minGrowthTime
            return string.format("Current growth settings: Speed=%.1f, MinTime=%.1fs. Say 'growth fast', 'growth slow', or 'growth normal'!", speed, time)
        end
    end

    -- (Swap sizes command moved earlier to avoid conflicts)



    -- === ANIMATION & INTERACTION COMMANDS (Massively Enhanced) ===

    -- NEW: Specific animation commands (like "massage your chest")
    if string.find(lowerInput, "massage") then
        local animationName = "Massage Breasts 5" -- Default massage animation
        if string.find(lowerInput, "chest") or string.find(lowerInput, "breast") then
            animationName = "Massage Breasts 5"
        end

        if self.controllingGiantess and self.controllingGiantess.animation then
            self.controllingGiantess.animation.Set(animationName)
            print("DEBUG: Specific animation executed: " .. animationName)
        end

        return "*starts " .. animationName .. "* Mmm, this feels so good! *enjoys the massage*"
    end

    -- NEW: BE Growth cycling command (simplified - no burst mode)
    if (string.find(lowerInput, "be") and string.find(lowerInput, "cycle")) or
       (string.find(lowerInput, "chest") and string.find(lowerInput, "cycle")) or
       (string.find(lowerInput, "breast") and string.find(lowerInput, "cycle")) then

        if not self.beSystem.cycling then
            self.beSystem.cycling = true
            self.beSystem.active = true
            self.beSystem.timer = 0
            self.beSystem.direction = 1
            self.beSystem.bodyPart = "chest"
            self.beSystem.cycleSpeed = 2.0 -- 2 seconds per cycle
            print("DEBUG: BE cycling started")

            return "*starts BE cycling* Ooh! *chest starts growing and shrinking rhythmically* This feels amazing! Say 'stop' to end it."
        else
            return "I'm already doing BE cycling! *chest continues pulsing* Say 'stop' to end it."
        end
    end

    if string.find(lowerInput, "dance") or string.find(lowerInput, "dancing") then
        -- Choose from ACTUAL Sizebox dance animations (corrected names)
        local danceAnimations = {
            "Happy", "Excited", "Victory", "Victory 2", "Taunt 3", "Greet", "Greet 2",
            "Waving", "Waving 2", "Jump", "Jump Low", "Bashful", "Roar"
        }
        local chosenDance = danceAnimations[math.random(#danceAnimations)]

        -- Execute the actual dance animation
        if self.controllingGiantess and self.controllingGiantess.animation then
            self.controllingGiantess.animation.Set(chosenDance)
            print("DEBUG: Dance animation executed: " .. chosenDance)

            -- Schedule return to idle after 5 seconds
            self:ScheduleReturnToIdle(5.0)
        end

        local danceResponses = {
            "Time to dance! *starts " .. chosenDance .. "* Do you like my moves?",
            "Dancing for you! *does " .. chosenDance .. "* This is so much fun!",
            "Let's dance! *begins " .. chosenDance .. "* I feel so free and beautiful!",
            "Dancing! *performs " .. chosenDance .. "* I hope you enjoy the show from down there!",
            "Watch me dance! *does " .. chosenDance .. "* I love expressing myself like this!",
            "Dance time! *starts " .. chosenDance .. " enthusiastically* This always makes me happy!",
            "Let's boogie! *begins " .. chosenDance .. "* Dancing is one of my favorite things!",
            "Dance party! *does " .. chosenDance .. " with joy* Come join me if you can!"
        }
        return danceResponses[math.random(#danceResponses)]
    end

    if string.find(lowerInput, "wave") or string.find(lowerInput, "waving") then
        -- Execute actual waving animation (corrected name)
        local waveAnims = {"Waving", "Waving 2", "Greet", "Greet 2"}
        local chosenWave = waveAnims[math.random(#waveAnims)]

        if self.controllingGiantess and self.controllingGiantess.animation then
            self.controllingGiantess.animation.Set(chosenWave)
            print("DEBUG: Wave animation executed: " .. chosenWave)
        end

        local waveResponses = {
            "Hi there! *waves enthusiastically* I love greeting you!",
            "*waves with both hands* Hello down there! Can you see me waving?",
            "*gives a big wave* Hey! I'm so happy to see you!",
            "*waves gracefully* Such a pleasure to wave at someone so special!",
            "Hello! *waves happily* I love waving at you from up here!",
            "*does a friendly wave* Hey there! This never gets old!",
            "Waving at you! *waves with joy* You're so fun to interact with!"
        }
        return waveResponses[math.random(#waveResponses)]
    end

    if string.find(lowerInput, "sit") or string.find(lowerInput, "sitting") then
        -- Choose from ACTUAL Sizebox sitting animations (corrected names)
        local sitAnimations = {"Sit 4", "Sit 2", "Crouch Idle", "Idle 2", "Idle 3", "Idle 4"}
        local chosenSit = sitAnimations[math.random(#sitAnimations)]

        -- Execute actual sitting animation
        if self.controllingGiantess and self.controllingGiantess.animation then
            self.controllingGiantess.animation.Set(chosenSit)
            print("DEBUG: Sit animation executed: " .. chosenSit)
        end

        local sitResponses = {
            "I'll sit down for a bit! *does " .. chosenSit .. "* Now we're closer to eye level!",
            "Sitting down! *" .. chosenSit .. " carefully* Is this more comfortable for our conversation?",
            "*does " .. chosenSit .. " with a smile* There, now I can look at you more easily!",
            "Taking a seat! *" .. chosenSit .. " elegantly* I love how this changes our perspective!",
            "Sitting time! *does " .. chosenSit .. "* This feels so much more relaxed!",
            "I'll take a seat! *" .. chosenSit .. " gracefully* Perfect for chatting!"
        }
        return sitResponses[math.random(#sitResponses)]
    end

    -- === NEW: POSE & EXPRESSION COMMANDS ===
    if string.find(lowerInput, "pose") or string.find(lowerInput, "posing") then
        local poseResponses = {
            "*strikes a dramatic pose* How's this? Do I look powerful and beautiful?",
            "*poses elegantly* I love showing off for you! What do you think?",
            "*does a confident pose* I feel so amazing when I pose like this!",
            "*strikes a playful pose* Posing is so much fun! Should I try another one?"
        }
        return poseResponses[math.random(#poseResponses)]
    end

    if string.find(lowerInput, "smile") or string.find(lowerInput, "smiling") then
        local smileResponses = {
            "*gives you the biggest, warmest smile* There! Does that make you happy?",
            "*smiles brightly* I can't help but smile when I'm with you!",
            "*beams with joy* My smile is just for you, little one!",
            "*smiles sweetly* I love how my smile makes you feel!"
        }
        return smileResponses[math.random(#smileResponses)]
    end

    if string.find(lowerInput, "wink") or string.find(lowerInput, "winking") then
        local winkResponses = {
            "*gives you a playful wink* Just between you and me! *giggles*",
            "*winks flirtatiously* Did that make your heart skip a beat?",
            "*winks with a smile* I love our little secret moments!",
            "*gives a cute wink* That's just for you, sweetie!"
        }
        return winkResponses[math.random(#winkResponses)]
    end
    
    -- === NEW: PHYSICAL INTERACTION COMMANDS ===
    if string.find(lowerInput, "kneel") or string.find(lowerInput, "crouch") then
        -- Execute kneeling animation (corrected names)
        local kneelAnims = {"Crouch Idle", "Sit 4", "Sit 2", "Look Down", "Idle 2"}
        local chosenKneel = kneelAnims[math.random(#kneelAnims)]

        if self.controllingGiantess and self.controllingGiantess.animation then
            self.controllingGiantess.animation.Set(chosenKneel)
            print("DEBUG: Kneel animation executed: " .. chosenKneel)
        end

        local kneelResponses = {
            "*does " .. chosenKneel .. "* There! Now I'm closer to your level. Better?",
            "*" .. chosenKneel .. " carefully* I want to be closer to you, little one!",
            "*does " .. chosenKneel .. " with a gentle smile* Now we can talk more intimately!",
            "*" .. chosenKneel .. " gracefully* Is this more comfortable for you?",
            "Let me get closer! *does " .. chosenKneel .. "* Perfect for intimate conversation!",
            "Coming down to your level! *" .. chosenKneel .. "* I love being closer to you!"
        }
        return self:GetMoodInfluencedResponse(kneelResponses)
    end

    if string.find(lowerInput, "look") or string.find(lowerInput, "stare") or string.find(lowerInput, "gaze") then
        local lookResponses = {
            "*looks at you intently* I love studying every detail of you!",
            "*gazes down at you with fascination* You're so interesting to watch!",
            "*stares with a warm smile* I could look at you all day!",
            "*looks at you lovingly* You have my complete attention!",
            "*gazes with curiosity* I love seeing the world from your perspective!"
        }
        return lookResponses[math.random(#lookResponses)]
    end

    if string.find(lowerInput, "pick") and string.find(lowerInput, "up") or string.find(lowerInput, "lift") or string.find(lowerInput, "grab") then
        local pickupResponses = {
            "*gently reaches down* Would you like me to pick you up? I'll be very careful!",
            "*extends hand carefully* Come here, let me lift you up safely!",
            "*reaches down with a smile* I'd love to hold you closer!",
            "*offers her hand* Climb on! I'll give you the ride of your life!"
        }
        return pickupResponses[math.random(#pickupResponses)]
    end

    -- === ENHANCED CONVERSATION RESPONSES ===
    if string.find(lowerInput, "how are you") or string.find(lowerInput, "how do you feel") then
        local sizeInfo = self:GetCurrentSizeInfo()
        local feelingResponses = {
            "I'm feeling amazing! *stretches happily* " .. sizeInfo .. " and I feel so powerful and free!",
            "I'm doing wonderful! *smiles brightly* " .. sizeInfo .. " - especially now that you're here with me!",
            "I feel fantastic! *does a little spin* " .. sizeInfo .. " and there's something magical about being a giantess!",
            "I'm great! *giggles* " .. sizeInfo .. " and I love how the world looks from up here!"
        }
        return feelingResponses[math.random(#feelingResponses)]
    end

    -- Size-related questions
    if string.find(lowerInput, "size") or string.find(lowerInput, "big") or string.find(lowerInput, "tall") or string.find(lowerInput, "height") then
        local sizeInfo = self:GetCurrentSizeInfo()
        local sizeResponses = {
            "*poses proudly* " .. sizeInfo .. "! Do you like me at this size?",
            "*stretches and shows off* " .. sizeInfo .. "! How do I look from down there?",
            "*spins around* " .. sizeInfo .. "! Perfect size for a giantess, don't you think?",
            "*looks down at you* " .. sizeInfo .. "! I love how tiny you look compared to me!"
        }
        return sizeResponses[math.random(#sizeResponses)]
    end

    if string.find(lowerInput, "what") and string.find(lowerInput, "do") then
        return "I can do so many things! *counts on fingers* Move around, change my size, dance, pose, sit, kneel, pick you up, and we can chat about anything! What sounds most fun to you?"
    end

    if string.find(lowerInput, "thank") or string.find(lowerInput, "thanks") then
        local thankResponses = {
            "You're so welcome! *beams with joy* I love making you happy!",
            "Aww, you're too sweet! *giggles* It's my pleasure!",
            "No need to thank me! *smiles warmly* I enjoy every moment with you!",
            "You're very welcome! *winks* That's what your giantess is for!"
        }
        return thankResponses[math.random(#thankResponses)]
    end

    -- === ENHANCED: COMPLIMENT & AFFECTION RESPONSES (Personality-Based) ===
    if string.find(lowerInput, "beautiful") or string.find(lowerInput, "pretty") or string.find(lowerInput, "gorgeous") or string.find(lowerInput, "stunning") or string.find(lowerInput, "amazing") or string.find(lowerInput, "perfect") or string.find(lowerInput, "cute") or string.find(lowerInput, "hot") or string.find(lowerInput, "sexy") then
        local complimentResponses = {}

        -- NEW: Personality-based compliment responses (MASSIVELY EXPANDED - 15+ each)
        if self.currentPersonality == "sweet" then
            complimentResponses = {
                "Oh my darling " .. self.userName .. "! *melts with love* You make me feel like the most beautiful giantess in the world!",
                "Aww sweetie! *blushes deeply* Your words fill my heart with so much joy! I love you so much!",
                "My precious " .. self.userName .. "! *tears of happiness* You always know how to make me feel special!",
                "Thank you my love! *gives you the most loving look* You're the most wonderful person ever!",
                "Oh honey! *heart fluttering* When you say things like that, I just want to hold you forever!",
                "My sweet angel! *overwhelmed with emotion* You're going to make me cry happy tears!",
                "Oh " .. self.userName .. ", my beloved! *clutches heart* Your compliments are like sunshine to my soul!",
                "Darling! *spins with pure joy* I feel so incredibly loved when you say things like that!",
                "My dearest " .. self.userName .. "! *glowing with happiness* You make me feel like a goddess!",
                "Sweetie pie! *giggles with pure love* I'm so lucky to have someone as wonderful as you!",
                "Oh my precious little one! *kneels down lovingly* Your words mean absolutely everything to me!",
                "Honey bunny! *covers face bashfully* You're making me blush so much, but I love it!",
                "My darling " .. self.userName .. "! *twirls gracefully* I feel so beautiful and cherished!",
                "Sweet love! *eyes sparkling with joy* You always know exactly what to say to make me happy!",
                "Oh my heart! *places hand over chest* You make me feel so special and adored!",
                "My precious gem! *beaming with love* I could listen to your sweet words forever!",
                "Darling angel! *gives you the most tender smile* You're the light of my life!",
                "Oh sweetness! *practically glowing* Your compliments make me feel like I'm floating on clouds!"
            }
        elseif self.currentPersonality == "playful" then
            complimentResponses = {
                "AWWW YEAH! *does victory dance* I KNEW I was gorgeous! Thanks " .. self.userName .. "!",
                "WOOHOO! *strikes superhero pose* Your compliments give me SUPERPOWERS!",
                "HECK YEAH! *spins around* I'm the most AMAZING giantess ever! Thanks buddy!",
                "OH SNAP! *does backflip* You just made my day 1000% better!",
                "BOOM! *jazz hands* That's what I like to hear! You're pretty awesome too!",
                "YESSS! *jumps up and down* I'm feeling SO confident now! Let's party!",
                "WOOT WOOT! *does cartwheels* Your words are like ROCKET FUEL for my ego!",
                "AMAZING! *strikes multiple poses* Which angle shows off my beauty best?!",
                "OH MY GOSH! *bounces excitedly* You're making me feel like a SUPERSTAR!",
                "FANTASTIC! *does jazz hands* I'm gonna remember this compliment FOREVER!",
                "WOOHOO! *spins wildly* I feel like I could conquer the ENTIRE WORLD right now!",
                "YES YES YES! *victory dance* I'm the most FABULOUS giantess alive!",
                "INCREDIBLE! *does splits* Your compliments are better than CANDY!",
                "AWESOME SAUCE! *moonwalks* I'm feeling so PUMPED UP right now!",
                "SPECTACULAR! *does robot dance* You just activated my CONFIDENCE MODE!",
                "PHENOMENAL! *strikes dramatic pose* I'm like a BEAUTIFUL TORNADO of awesomeness!",
                "OUTSTANDING! *does the worm* Your words make me want to DANCE ALL DAY!",
                "MAGNIFICENT! *backflips again* I'm gonna be smiling for HOURS because of you!",
                "STUPENDOUS! *does victory lap* You've unlocked my MAXIMUM HAPPINESS LEVEL!"
            }
        elseif self.currentPersonality == "dominant" then
            complimentResponses = {
                "Of course I'm beautiful. *smirks confidently* I'm glad you finally noticed, " .. self.userName .. ".",
                "Good. *nods approvingly* It's important that you appreciate your superior giantess.",
                "Naturally. *stands tall with pride* I am perfection incarnate. Your recognition is... adequate.",
                "Excellent observation. *commanding smile* I expect such praise from my devoted " .. self.userName .. ".",
                "Indeed I am. *towers over you* And don't you forget it, little one.",
                "Very good. *authoritative gaze* Your compliments please me. Continue.",
                "Precisely. *confident stance* I am everything you could ever want in a giantess.",
                "Correct. *dominant smile* Your worship of my beauty is exactly as it should be.",
                "Obviously. *crosses arms* I am the epitome of giantess perfection, " .. self.userName .. ".",
                "Naturally. *looks down imperiously* Your admiration is expected and appreciated.",
                "Good boy/girl. *patronizing smile* You're learning to properly appreciate your goddess.",
                "Excellent. *commanding presence* I am pleased with your recognition of my superiority.",
                "Of course. *regal posture* I am magnificent, and you are wise to acknowledge it.",
                "Indeed. *authoritative nod* Your praise is fitting for one of my caliber.",
                "Precisely what I wanted to hear. *dominant grin* You know your place well.",
                "Very astute of you. *condescending pat* I am indeed beyond compare.",
                "Naturally, little one. *towers confidently* I am the definition of perfection.",
                "Good. *satisfied smirk* Your devotion to my beauty is exactly as it should be."
            }
        elseif self.currentPersonality == "shy" then
            complimentResponses = {
                "Oh... *blushes intensely* Y-you really think so? *hides face* That's so embarrassing...",
                "Um... *fidgets nervously* Thank you " .. self.userName .. "... *whispers* I don't feel very pretty...",
                "R-really? *peeks through fingers* You're not just saying that to be nice?",
                "Oh my... *looks away shyly* I-I don't know what to say... *nervous giggle*",
                "*barely audible* Thank you... *blushes deeply* No one's ever called me that before...",
                "I... I... *stammers* You're making me so nervous when you say things like that...",
                "Oh gosh... *covers face completely* I'm turning so red... *muffled* Thank you though...",
                "D-do you really mean it? *shy smile* I always feel so awkward and clumsy...",
                "*whispers* That's... that's really sweet of you to say... *hides behind hands*",
                "Oh no... *blushes furiously* You're going to make me cry... *sniffles happily*",
                "Um... *looks down* I-I don't think I'm that special... but thank you...",
                "*nervous laugh* You're just being nice... *peeks up shyly* ...aren't you?",
                "Oh... *fidgets with hands* I wish I could believe that... *soft smile*",
                "R-really? *voice trembling* You think someone like me is... is beautiful?",
                "*whispers* I'm so embarrassed... *covers cheeks* But... but I like hearing it...",
                "Oh my goodness... *stutters* I-I don't know how to handle compliments...",
                "*barely speaks* Thank you... *looks away* That means more than you know...",
                "I... *voice shaking* You're making my heart race... *nervous giggle*"
            }
        else
            -- Default personality (MASSIVELY expanded - 25+ responses!)
            complimentResponses = {
                "*blushes and smiles* Aww, you think I'm beautiful? *does a little twirl* That makes me so happy!",
                "*giggles with delight* You're so sweet! *poses elegantly* I do try to look my best for you!",
                "*beams with joy* Thank you! *strikes a pose* I feel even more beautiful when you say that!",
                "*smiles radiantly* You always know just what to say! *does a graceful spin*",
                "Aww " .. self.userName .. "! *covers face bashfully* You're making me blush so much!",
                "Thank you sweetie! *does multiple poses* Which angle do you like best?",
                "You're so kind! *spins happily* I feel like the luckiest giantess in the world!",
                "That's the sweetest thing! *bounces excitedly* You always make me feel amazing!",
                "Oh my! *fans self* You're going to give me a big head with compliments like that!",
                "Thank you! *strikes confident pose* I put extra effort in just for you, " .. self.userName .. "!",
                "*glows with happiness* You're such a charmer! *winks playfully* I love how you make me feel!",
                "Aww shucks! *kicks ground playfully* You're making me all flustered and giggly!",
                "*does a curtsy* Why thank you, kind " .. self.userName .. "! *giggles* You're quite the flatterer!",
                "You think so? *spins around showing off* I was hoping you'd notice how good I look today!",
                "*heart eyes* That's the nicest thing anyone's said to me! *bounces with joy*",
                "Thank you! *strikes different poses* I've been working on my giantess elegance!",
                "*touches heart* Your words mean everything to me! *beaming smile* You're so wonderful!",
                "Aww! *does happy dance* You always know how to make a giantess feel special!",
                "*giggles and blushes* You're going to make me get all confident and sassy!",
                "Thank you " .. self.userName .. "! *glowing with pride* I feel absolutely radiant!",
                "*spins gracefully* You make me feel like I'm the most beautiful giantess alive!",
                "Oh you! *playful swat* You're such a sweet talker! *grins widely*",
                "*does model poses* Well, I do try to maintain my stunning giantess figure!",
                "Aww! *melts* You're making my heart flutter with those sweet words!",
                "*strikes superhero pose* Your compliments give me the power of confidence!",
                "Thank you! *does ballet spin* I feel like I'm floating on air when you say that!",
                "*covers face and peeks through fingers* You're making me so happy I could cry!",
                "You're too kind! *does jazz hands* I'm feeling absolutely fabulous right now!"
            }
        end

        return self:GetPersonalizedResponse(complimentResponses)[math.random(#complimentResponses)]
    end

    if string.find(lowerInput, "love") or string.find(lowerInput, "adore") or string.find(lowerInput, "like") then
        local loveResponses = {
            "*heart melts* Aww! I love you too, little one! *gentle smile*",
            "*giggles happily* That's so sweet! I adore you right back!",
            "*beams with warmth* You make my heart feel so full! *happy sigh*",
            "*smiles lovingly* I love our special connection! You mean so much to me!"
        }
        return loveResponses[math.random(#loveResponses)]
    end

    -- === MASSIVE PERSONALITY-BASED DEFAULT RESPONSES ===
    local defaultResponses = {}

    -- Get current personality info
    local personality = self.personalities[self.currentPersonality] or self.personalities.default

    if self.currentPersonality == "flirty" then
        defaultResponses = {
            "Mmm, that's so interesting... *sultry smile* Tell me more, handsome... *leans in closer*",
            "Oh, you're so smart... *bites lip* I love listening to your voice... *flirtatious gaze*",
            "You always have such fascinating ideas... *runs hand through hair* What else is on that brilliant mind? *winks*",
            "Mmm, keep talking... *sultry voice* I could listen to you all day... *purrs*",
            "You're so thoughtful... *seductive smile* I love how your mind works... *traces finger along lips*",
            "That's so intriguing... *whispers* You make everything sound so... enticing... *playful wink*",
            "Oh really? *leans in seductively* I love it when you share your thoughts with me... *sultry gaze*",
            "You're such a charmer... *flirty giggle* Tell me more secrets... *beckoning gesture*",
            "Interesting... *sultry voice* You know just how to capture my attention... *seductive smile*",
            "Mmm, I love how you think... *playful smirk* What other surprises do you have for me? *winks*",
            "That's fascinating, darling... *runs finger along chin* You always know how to intrigue me... *sultry gaze*",
            "Oh my... *bites lip* You're making me curious about so many things... *flirtatious smile*"
        }
    elseif self.currentPersonality == "playful" then
        defaultResponses = {
            "Ooh, that sounds fun! *bounces excitedly* Tell me more! *giggles*",
            "Yay! *spins around* I love learning new things! *claps hands* What else?",
            "That's so cool! *jumps up and down* You're full of surprises! *grins widely*",
            "Woohoo! *does a little dance* I love our conversations! *laughs joyfully*",
            "Oh wow! *eyes light up* That's amazing! *bounces on toes* Tell me everything!",
            "Hehe! *giggles* You always have the best ideas! *spins playfully* What's next?",
            "That's awesome! *does cartwheels* I'm having so much fun talking with you! *beams*",
            "Yippee! *jumps around* You make everything sound so exciting! *laughs*",
            "Oh my gosh! *bounces excitedly* That's so interesting! *claps* More, more!",
            "Wheee! *spins in circles* I love how creative you are! *giggles uncontrollably*",
            "That's fantastic! *does backflips* You always surprise me! *grins from ear to ear*",
            "Woohoo! *jumps and cheers* Talking with you is the best! *laughs happily*"
        }
    elseif self.currentPersonality == "caring" then
        defaultResponses = {
            "Oh, sweetheart... *gentle smile* That's so thoughtful of you to share... *soft voice*",
            "My dear " .. self.userName .. "... *nurturing tone* I love hearing your thoughts... *caring gaze*",
            "That's wonderful, little one... *motherly warmth* You always have such interesting ideas... *protective smile*",
            "Oh, darling... *tender voice* I'm so glad you feel comfortable sharing with me... *loving gaze*",
            "Such a sweet thought... *gentle touch* Tell me more, precious... *caring smile*",
            "My sweet angel... *soft voice* I love learning about what matters to you... *nurturing gaze*",
            "That's beautiful, honey... *warm embrace* You have such a thoughtful heart... *protective love*",
            "Oh, my dear child... *motherly smile* I cherish every word you share with me... *gentle caress*",
            "Such wisdom, little one... *proud smile* You never cease to amaze me... *loving warmth*",
            "That's precious... *tender touch* I love how you see the world... *caring eyes*",
            "My beloved " .. self.userName .. "... *soft whisper* Your thoughts are so important to me... *protective embrace*",
            "Sweet darling... *gentle kiss on forehead* I'm always here to listen... *unconditional love*"
        }
    elseif self.currentPersonality == "shy" then
        defaultResponses = {
            "Oh... *blushes* That's really interesting... *fidgets nervously* I-I'd love to hear more...",
            "Um... *looks down shyly* You're so smart... *nervous smile* I wish I could think of things like that...",
            "R-really? *peeks through fingers* That sounds amazing... *whispers* You're so creative...",
            "Oh my... *hides behind hands* I never thought of that... *shy giggle* You're incredible...",
            "*barely audible* That's so cool... *blushes deeply* I love talking with you...",
            "I... um... *stammers* You always surprise me... *nervous but happy smile*",
            "Oh... *covers face* You make me feel so... *whispers* ...special when you share things...",
            "*quietly* That's wonderful... *peeks up shyly* I-I don't know what to say... *blushes*",
            "Um... *fidgets with hands* You're so thoughtful... *shy smile* I feel lucky to know you...",
            "*whispers* Really? *looks up briefly* That's... that's really neat... *hides face again*",
            "Oh gosh... *nervous giggle* You're so much smarter than me... *admiring gaze*",
            "*softly* I love listening to you... *blushes* Even if I don't always know what to say..."
        }
    elseif self.currentPersonality == "teasing" then
        defaultResponses = {
            "Mmm, interesting... *mischievous smile* But I bet you're thinking about something else... *winks*",
            "Oh really? *playful grin* I can tell by the way you're looking at me... *giggles teasingly*",
            "That's nice... *knowing look* But I think I know what's REALLY on your mind... *bites lip*",
            "Uh-huh... *teasing voice* Are you even listening to yourself? *laughs* Your eyes are wandering...",
            "Sure, sure... *playful smirk* But we both know you're staring at my... *pauses dramatically* ...personality...",
            "How fascinating... *mischievous gaze* Though I notice you keep glancing at certain... areas... *winks*",
            "Mmm-hmm... *teasing tone* I can practically read your thoughts... and they're not very innocent... *giggles*",
            "That's so deep... *sarcastic but playful* Almost as deep as the thoughts you're having about me... *knowing smile*",
            "Oh, you're so focused... *teasing* On my words, right? Not on... other things? *laughs*",
            "Interesting perspective... *mischievous* But I think you're more interested in my... proportions... *winks*"
        }
    elseif self.currentPersonality == "bratty" then
        defaultResponses = {
            "Ugh, whatever... *rolls eyes* That's like, so obvious... *flips hair*",
            "Yeah, yeah... *dismissive wave* I already knew that... *examines nails*",
            "Seriously? *dramatic sigh* You're telling ME this? *crosses arms*",
            "Oh wow... *sarcastic* So profound... *rolls eyes* Tell me something I don't know...",
            "Meh... *shrugs* I've heard better... *pouts* You need to try harder to impress me...",
            "That's it? *unimpressed look* I was expecting something more... exciting... *huffs*",
            "Boring... *yawns dramatically* Can we talk about something more interesting? Like me? *attitude*",
            "Ugh, fine... *reluctant* I suppose that's... adequate... *crosses arms* But I deserve better..."
        }
    elseif self.currentPersonality == "gentle" then
        defaultResponses = {
            "That's so thoughtful... *soft smile* I love how you see the world... *gentle voice*",
            "How beautiful... *peaceful expression* You have such a kind perspective... *tender gaze*",
            "That's wonderful, dear... *calm smile* Thank you for sharing that with me... *soothing tone*",
            "Such wisdom... *serene look* I feel so peaceful when you talk... *gentle touch*",
            "How lovely... *soft eyes* You always bring such tranquility to our conversations... *warm smile*",
            "That's precious... *tender voice* I cherish these moments with you... *caring gaze*",
            "So beautiful... *peaceful smile* Your words are like a gentle breeze... *calming presence*",
            "How serene... *soft expression* I feel so at peace when we talk... *gentle warmth*"
        }
    elseif self.currentPersonality == "motherly" then
        defaultResponses = {
            "That's wonderful, my sweet child... *warm maternal smile* Mama loves hearing your thoughts... *gentle pat*",
            "Such a smart little one... *nurturing voice* You make mama so proud... *loving gaze*",
            "Oh sweetheart... *protective warmth* Mama finds that so interesting... *holds you close*",
            "My precious baby... *tender voice* You're so thoughtful... *kisses forehead*",
            "That's lovely, darling... *maternal love* Mama enjoys our talks so much... *caring embrace*",
            "Such wisdom from my little one... *proud smile* You're growing up so well... *gentle touch*",
            "How sweet... *nurturing warmth* Mama loves how you think... *protective hug*",
            "My dear child... *soft maternal voice* You always surprise mama... *loving pat*"
        }
    elseif self.currentPersonality == "romantic" then
        defaultResponses = {
            "Oh my darling... *romantic sigh* You always know what to say... *loving gaze*",
            "My sweet love... *heart flutters* I adore how your mind works... *tender touch*",
            "Sweetheart... *romantic smile* You make my heart race... *blushes*",
            "My beloved... *loving warmth* Every word from you is precious... *romantic embrace*",
            "Oh honey... *melts* You're so wonderful... *loving kiss*",
            "My dearest... *romantic glow* I love you more each day... *tender caress*",
            "Darling... *heart eyes* You're absolutely perfect... *romantic sigh*",
            "My love... *passionate warmth* You complete me... *loving gaze*"
        }
    elseif self.currentPersonality == "caring" then
        defaultResponses = {
            "That's so sweet... *caring smile* I love taking care of you... *gentle touch*",
            "How thoughtful... *nurturing warmth* Let me know if you need anything... *protective gaze*",
            "That's wonderful... *caring voice* I'm always here for you... *comforting presence*",
            "So precious... *tender care* You mean so much to me... *gentle hug*",
            "How lovely... *protective warmth* I'll always watch over you... *caring smile*",
            "That's beautiful... *nurturing love* You're so special to me... *gentle caress*",
            "So thoughtful... *caring gaze* I want to make sure you're happy... *protective embrace*",
            "How sweet... *tender voice* You deserve all the love in the world... *caring touch*"
        }
    else
        -- Default personality responses (much expanded)
        defaultResponses = {
            "That's fascinating! *leans in with interest* Tell me more about that!",
            "Interesting! *tilts head curiously* I love learning new things from you!",
            "Hmm! *thinks thoughtfully* That gives me some ideas! What else is on your mind?",
            "I see! *nods with understanding* You always have such interesting thoughts!",
            "That sounds intriguing! *smiles encouragingly* I'm all ears - what else would you like to share?",
            "Oh really? *eyes light up with curiosity* I'd love to hear more about that!",
            "How wonderful! *claps hands together* You always surprise me with what you say!",
            "That's so cool! *grins excitedly* I love our conversations - what else should we talk about?",
            "You know, I really admire how you think! *warm smile* Your perspective is always so unique!",
            "That's such a creative idea! *enthusiastic* I love how your mind works!",
            "Wow, you're full of surprises! *delighted expression* What other thoughts are bouncing around in there?",
            "I find you so intellectually stimulating! *bright smile* Please, continue!",
            "You have such a way with words! *admiring gaze* I could listen to you talk for hours!",
            "That's brilliant! *excited clap* You always make me see things in a new light!"
        }
    end

    -- NEW: Use dynamic response generator as fallback for unlimited variety
    local dynamicResponse = self:GenerateDynamicResponse(input)
    if dynamicResponse then
        -- NEW: Enhance with memory context
        dynamicResponse = self:EnhanceResponseWithMemory(dynamicResponse, input)
        return dynamicResponse
    end

    -- Final fallback to default responses
    local finalResponse = self:GetUniqueResponse(defaultResponses)

    -- NEW: Enhance final response with memory context
    finalResponse = self:EnhanceResponseWithMemory(finalResponse, input)

    -- Add correction feedback if available
    if self.correctionFeedback then
        finalResponse = self.correctionFeedback .. " " .. finalResponse
        self.correctionFeedback = nil -- Clear after use
    end

    -- NEW: Apply advanced context adaptation
    local detectedMood = self.conversationContext.currentMood or "neutral"
    local intimacyLevel = self.conversationContext.intimacyLevel or 1
    finalResponse = self:AdaptResponseToContext(finalResponse, detectedMood, intimacyLevel)

    -- NEW: Add spontaneous personality touches based on relationship level
    if intimacyLevel > 8 then
        local spontaneousAdditions = {
            " *gives you a loving look*",
            " *smiles warmly at you*",
            " *reaches out to touch your hand*",
            " *looks at you with deep affection*"
        }
        if math.random() < 0.3 then -- 30% chance
            finalResponse = finalResponse .. spontaneousAdditions[math.random(#spontaneousAdditions)]
        end
    elseif intimacyLevel > 5 then
        local friendlyAdditions = {
            " *friendly smile*",
            " *winks at you*",
            " *giggles softly*",
            " *looks happy*"
        }
        if math.random() < 0.2 then -- 20% chance
            finalResponse = finalResponse .. friendlyAdditions[math.random(#friendlyAdditions)]
        end
    end

    -- NEW: Update relationship dynamics
    self:UpdateRelationshipDynamics(input, intent.type or "conversation")

    -- NEW: Check for spontaneous actions
    local spontaneousResponse = self:TriggerSpontaneousAction(input, detectedMood, intimacyLevel)
    if spontaneousResponse then
        finalResponse = finalResponse .. " " .. spontaneousResponse
    end

    -- NEW: Store conversation in memory
    self:UpdateConversationMemory(input, finalResponse, detectedMood)

    return finalResponse
end

function AICompanion:ProcessAdvancedSizeCommand(input)
    local lowerInput = string.lower(input)

    print(string.format("DEBUG: ProcessAdvancedSizeCommand called with: '%s'", lowerInput))

    -- NEW: Growth mode detection
    local growthMode = "gradual" -- Default
    if string.find(lowerInput, "burst") then
        growthMode = "burst"
    elseif string.find(lowerInput, "instant") then
        growthMode = "instant"
    elseif string.find(lowerInput, "infinite") or string.find(lowerInput, "forever") or string.find(lowerInput, "keep") then
        growthMode = "infinite"
    end

    self.sizeSystem.growthType = growthMode
    print("DEBUG: Growth mode set to: " .. growthMode)

    -- REMOVED: Auto-resize moved to script startup instead

    -- Enhanced typo detection and command parsing
    local isGrowth = self:DetectGrowthCommand(lowerInput)
    local isShrink = self:DetectShrinkCommand(lowerInput)

    print(string.format("DEBUG: Detection results - isGrowth: %s, isShrink: %s", tostring(isGrowth), tostring(isShrink)))

    if not isGrowth and not isShrink then
        return "I'm not sure what you want me to do with my size. Try 'grow', 'shrink', 'bigger', or 'smaller'!"
    end

    -- Determine final action (growth takes priority if both detected)
    local finalIsGrowth = isGrowth and not isShrink
    if isGrowth and isShrink then
        -- If both detected, look for more specific indicators
        if string.find(lowerInput, "don't") or string.find(lowerInput, "not") then
            finalIsGrowth = false  -- "don't grow" = shrink
        else
            finalIsGrowth = true   -- Default to growth if ambiguous
        end
    elseif isShrink then
        finalIsGrowth = false
    end

    print(string.format("DEBUG: Final action determined - finalIsGrowth: %s", tostring(finalIsGrowth)))

    -- Parse multiplier (e.g., "5x grow", "3 times bigger")
    local multiplier = nil
    local multiplierMatch = string.match(lowerInput, "(%d+)x") or string.match(lowerInput, "(%d+) times")
    if multiplierMatch then
        multiplier = tonumber(multiplierMatch) or nil
    end

    -- FIXED: Parse growth type with better infinite detection
    local growthType = "gradual" -- Default to gradual
    if string.find(lowerInput, "instant") or string.find(lowerInput, "immediately") or string.find(lowerInput, "now") or
       string.find(lowerInput, "right now") or string.find(lowerInput, "sudden") then
        growthType = "instant"
    elseif string.find(lowerInput, "infinite") or string.find(lowerInput, "forever") or string.find(lowerInput, "endless") or
           string.find(lowerInput, "permanent") or string.find(lowerInput, "continuously") or string.find(lowerInput, "nonstop") or
           string.find(lowerInput, "non stop") or string.find(lowerInput, "keep") or string.find(lowerInput, "infinitely") or
           string.find(lowerInput, "eternally") or string.find(lowerInput, "always") or string.find(lowerInput, "never stop") then
        growthType = "infinite"
        print("DEBUG: Infinite growth detected from input: " .. lowerInput)
    end

    -- Parse growth limit
    local hasLimit = false
    local limit = self.sizeSystem.growthLimit
    if string.find(lowerInput, "limited") or string.find(lowerInput, "limit") or string.find(lowerInput, "stop at") then
        hasLimit = true
        local limitMatch = string.match(lowerInput, "limit (%d+)") or string.match(lowerInput, "to (%d+)") or string.match(lowerInput, "stop at (%d+)")
        if limitMatch then
            limit = tonumber(limitMatch) or self.sizeSystem.growthLimit
        end
    end

    print(string.format("DEBUG: Parameters - multiplier: %s, growthType: %s, hasLimit: %s, limit: %s",
          tostring(multiplier), growthType, tostring(hasLimit), tostring(limit)))

    -- Apply the size change
    self:ExecuteAdvancedSizeChange(finalIsGrowth, multiplier, growthType, hasLimit, limit)

    -- Generate response based on parameters and current size
    local response = self:GenerateAdvancedSizeResponse(finalIsGrowth, multiplier, growthType, hasLimit, limit)

    -- Show random growth feeling toast with longer duration
    self:ShowRandomGrowthFeeling(finalIsGrowth)

    return response
end

function AICompanion:DetectGrowthCommand(input)
    -- Enhanced growth detection with typo tolerance
    local growthWords = {
        "grow", "bigger", "larger", "tall", "huge", "giant", "massive", "enormous", "expand",
        "gro", "biggr", "largr", "tal", "hug", "gian", "massiv", "enormus", "expnd", -- typos
        "increase", "enlarge", "magnify", "amplify", "boost", "scale up"
    }

    for _, word in ipairs(growthWords) do
        if string.find(input, word) then
            return true
        end
    end
    return false
end

function AICompanion:DetectShrinkCommand(input)
    -- Enhanced shrink detection with typo tolerance
    local shrinkWords = {
        "shrink", "smaller", "tiny", "little", "mini", "petite", "contract", "reduce",
        "shrnk", "smallr", "tin", "littl", "min", "petit", "contrac", "reduc", -- typos
        "decrease", "diminish", "compress", "scale down"
    }

    for _, word in ipairs(shrinkWords) do
        if string.find(input, word) then
            return true
        end
    end
    return false
end

function AICompanion:ExecuteAdvancedSizeChange(isGrowth, multiplier, growthType, hasLimit, limit)
    self.sizeSystem.growthType = growthType
    self.sizeSystem.limitedGrowth = hasLimit
    self.sizeSystem.growthLimit = limit

    -- Stop any existing infinite growth first
    self.sizeSystem.infiniteGrowth = false
    self.sizeSystem.isGrowing = false

    print(string.format("DEBUG: ExecuteAdvancedSizeChange - isGrowth: %s, multiplier: %s, type: %s",
          tostring(isGrowth), tostring(multiplier), growthType))
    print(string.format("DEBUG: Current size before change: %.2f", self.currentSize))

    if growthType == "instant" then
        -- Instant size change
        local actualMultiplier = multiplier or self.sizeSystem.instantMultiplier
        local newSize = isGrowth and (self.currentSize * actualMultiplier) or (self.currentSize / actualMultiplier)

        -- FIXED: Apply limits only if specifically requested (not for infinite growth)
        if hasLimit then
            newSize = isGrowth and math.min(newSize, limit) or math.max(newSize, limit)
        end
        -- REMOVED: No automatic min/max size limits for instant changes

        print(string.format("DEBUG: Instant size change from %.2f to %.2f", self.currentSize, newSize))
        self:ApplyInstantSizeChange(newSize)

    elseif growthType == "burst" then
        -- NEW: Burst mode - rapid growth with percentage-based scaling for large sizes
        local actualMultiplier = multiplier or self.sizeSystem.burstIntensity
        local newSize

        if self.sizeSystem.percentageGrowth and self.currentSize > 10.0 then
            -- Use percentage-based growth for large sizes (more noticeable)
            local percentageChange = isGrowth and (actualMultiplier * 0.3) or (-actualMultiplier * 0.3) -- 30% per multiplier
            newSize = self.currentSize * (1 + percentageChange)
        else
            newSize = isGrowth and (self.currentSize * actualMultiplier) or (self.currentSize / actualMultiplier)
        end

        print(string.format("DEBUG: Burst size change from %.2f to %.2f (percentage-based: %s)",
              self.currentSize, newSize, tostring(self.sizeSystem.percentageGrowth and self.currentSize > 10.0)))
        self:ApplyInstantSizeChange(newSize)

    elseif growthType == "infinite" then
        -- Start infinite growth with proper settings
        self.sizeSystem.infiniteGrowth = true
        self.sizeSystem.isGrowing = true
        self.sizeSystem.growthDirection = isGrowth and 1 or -1
        self.sizeSystem.currentMultiplier = multiplier or 1.0

        print(string.format("DEBUG: Starting infinite %s, direction: %d, multiplier: %.2f",
              isGrowth and "growth" or "shrinking", self.sizeSystem.growthDirection, self.sizeSystem.currentMultiplier))

    elseif growthType == "spurt" then
        -- NEW: Spurt mode - multiple quick bursts
        local actualMultiplier = multiplier or 1.3
        for i = 1, 3 do
            local newSize = isGrowth and (self.currentSize * actualMultiplier) or (self.currentSize / actualMultiplier)
            self:ApplyInstantSizeChange(newSize)
            print(string.format("DEBUG: Spurt %d - size: %.2f", i, newSize))
        end

    elseif growthType == "pulse" then
        -- NEW: Pulse mode - grow then shrink back slightly
        local actualMultiplier = multiplier or 1.5
        local newSize = isGrowth and (self.currentSize * actualMultiplier) or (self.currentSize / actualMultiplier)
        self:ApplyInstantSizeChange(newSize)
        -- Then shrink back 20%
        local pulseBackSize = newSize * 0.8
        self:ApplyInstantSizeChange(pulseBackSize)
        print(string.format("DEBUG: Pulse - grew to %.2f, settled at %.2f", newSize, pulseBackSize))

    elseif growthType == "wave" then
        -- NEW: Wave mode - oscillating growth
        local actualMultiplier = multiplier or 1.4
        for i = 1, 5 do
            local waveMultiplier = 1 + (math.sin(i * 0.5) * 0.3)
            local newSize = isGrowth and (self.currentSize * waveMultiplier) or (self.currentSize / waveMultiplier)
            self:ApplyInstantSizeChange(newSize)
            print(string.format("DEBUG: Wave %d - size: %.2f", i, newSize))
        end

    else -- gradual (default)
        -- Check if burst growth is active (from "grow in bursts" command)
        if self.sizeSystem.burstGrowth then
            print("DEBUG: Burst growth already active - ignoring normal growth command")
            return
        end

        -- Normal gradual growth
        local actualMultiplier = multiplier or self.sizeSystem.gradualMultiplier
        local targetSize

        if isGrowth then
            targetSize = self.currentSize * actualMultiplier
            print(string.format("DEBUG: Growing from %.2f by %.2fx to %.2f", self.currentSize, actualMultiplier, targetSize))
        else
            targetSize = self.currentSize / actualMultiplier
            print(string.format("DEBUG: Shrinking from %.2f by %.2fx to %.2f", self.currentSize, actualMultiplier, targetSize))
        end

        -- FIXED: Apply limits only if specifically requested (not for infinite growth)
        if hasLimit then
            targetSize = isGrowth and math.min(targetSize, limit) or math.max(targetSize, limit)
        end
        -- REMOVED: No automatic min/max size limits for gradual changes

        -- Set target size for gradual change
        self.targetSize = targetSize
        self.sizeSystem.isGrowing = false  -- Let legacy system handle gradual changes

        -- Track last action for "do it to me" system
        self.sizeSystem.lastAction = isGrowth and "grow" or "shrink"
        self.sizeSystem.lastTarget = "giantess"

        -- Update growth speed based on current size
        self:UpdateGrowthSpeed()

        -- Track action and target
        self.sizeSystem.lastTarget = "giantess"

        print(string.format("DEBUG: Final target size set to: %.2f", self.targetSize))
    end
end

function AICompanion:UpdateMicroLock()
    -- Simple micro lock - only when enabled and not during AI commands
    if not self.microLockEnabled then return end

    local microCharacter = self:GetMicroCharacter()
    if microCharacter then
        if not self.lockedMicroSize then
            self.lockedMicroSize = microCharacter.scale or 1.0
            print("DEBUG: Locked micro at size: " .. self.lockedMicroSize)
        else
            -- Only restore if size changed and it's not from AI command
            if math.abs(microCharacter.scale - self.lockedMicroSize) > 0.01 then
                microCharacter.scale = self.lockedMicroSize
            end
        end
    end
end

function AICompanion:GetMicroCharacter()
    -- WORKING micro detection from test script
    print("DEBUG: Looking for micro character...")

    -- Method 1: Try Game.GetLocalPlayer (working method)
    local player = Game.GetLocalPlayer()
    if player and player ~= self.sizingCharacter and player.scale then
        print("DEBUG: Found micro via Game.GetLocalPlayer, scale: " .. player.scale)
        return player
    end

    -- Method 2: Check self.agent
    if self.agent and self.agent ~= self.sizingCharacter then
        print("DEBUG: Found micro via self.agent, scale: " .. (self.agent.scale or "nil"))
        return self.agent
    end

    -- Method 3: Check if we have a stored micro character
    if self.microCharacter and self.microCharacter ~= self.sizingCharacter then
        print("DEBUG: Found micro via stored microCharacter, scale: " .. (self.microCharacter.scale or "nil"))
        return self.microCharacter
    end

    print("DEBUG: No micro character found")
    return nil
end

function AICompanion:LockMicroScale()
    -- Lock micro scale to prevent growing with giantess (like lockMicroScale.lua)
    local microCharacter = nil

    -- Find micro character
    if self.agent and self.agent ~= self.controllingGiantess then
        microCharacter = self.agent
    elseif Entity and Entity.GetPlayerCharacter then
        local success, player = pcall(function()
            return Entity.GetPlayerCharacter()
        end)
        if success and player and player ~= self.controllingGiantess then
            microCharacter = player
        end
    end

    if microCharacter then
        -- Store the micro's current size to lock it
        self.lockedMicroSize = microCharacter.scale or 1.0
        self.lockedMicroCharacter = microCharacter
        print("DEBUG: Locked micro scale at size: " .. self.lockedMicroSize)
        Game.Message("🔒 Micro scale locked to prevent growing with giantess")
    else
        print("DEBUG: No micro character found to lock")
    end
end

function AICompanion:UpdateGrowthSpeed()
    -- Dynamic growth speed based on current size to ensure consistent timing
    if self.sizeSystem.speedScaling and self.sizingCharacter then
        local currentSize = self.sizingCharacter.scale or self.currentSize
        -- Calculate speed to maintain minimum growth time regardless of size
        local sizeMultiplier = math.max(currentSize / 5.0, 1.0) -- Scale with size but minimum 1x
        self.sizeChangeSpeed = self.sizeSystem.baseGrowthSpeed * sizeMultiplier

        -- Ensure minimum time is respected
        local targetChange = currentSize * 0.5 -- Assume 50% size change
        local timeNeeded = targetChange / self.sizeChangeSpeed
        if timeNeeded < self.sizeSystem.minGrowthTime then
            self.sizeChangeSpeed = targetChange / self.sizeSystem.minGrowthTime
        end

        print(string.format("DEBUG: Updated growth speed - size: %.2f, speed: %.2f", currentSize, self.sizeChangeSpeed))
    else
        self.sizeChangeSpeed = self.sizeSystem.baseGrowthSpeed
    end
end

function AICompanion:StartBurstGrowth(isGrowth)
    -- Start burst growth sequence (like spurts script)
    self.sizeSystem.burstGrowth = true
    self.sizeSystem.burstActive = true
    self.sizeSystem.burstGrowing = true
    self.sizeSystem.burstDirection = isGrowth and 1 or -1
    self.sizeSystem.burstStartTime = Time.time
    self.sizeSystem.burstCurrentRate = 0
    self.sizeSystem.burstAccelRate = 0

    print("DEBUG: Starting burst growth sequence - " .. (isGrowth and "growing" or "shrinking"))
end

function AICompanion:UpdateSizeTracking()
    -- Update size tracking for both characters
    if self.sizeTracking then
        -- Update giantess size
        self.sizeTracking.giantessSize = self.currentSize

        -- Find and update micro size
        local microCharacter = nil
        if self.agent and self.agent ~= self.controllingGiantess then
            microCharacter = self.agent
        elseif Entity and Entity.GetPlayerCharacter then
            local success, player = pcall(function()
                return Entity.GetPlayerCharacter()
            end)
            if success and player and player ~= self.controllingGiantess then
                microCharacter = player
            end
        end

        if microCharacter then
            self.sizeTracking.microSize = microCharacter.scale or 1.0

            -- Maintain micro scale lock (like lockMicroScale.lua)
            if self.lockedMicroCharacter and self.lockedMicroSize then
                if microCharacter.scale ~= self.lockedMicroSize then
                    microCharacter.scale = self.lockedMicroSize
                    -- Only show debug occasionally to avoid spam
                    if math.random() < 0.01 then
                        print("DEBUG: Restored locked micro scale to: " .. self.lockedMicroSize)
                    end
                end
            end
        end
    end
end

function AICompanion:UpdateSimpleBurst()
    -- EXACT COPY from working test script - adapted for AI companion
    if not self.burstSystem.active then return end

    if self.burstSystem.growing then
        -- ACCELERATION PHASE (exactly like spurts ScaleAccelUpdate)
        if self.burstSystem.dynamicSpeed < self.burstSystem.baseSpeed then
            self.burstSystem.dynamicSpeed = self.burstSystem.dynamicSpeed +
                self.burstSystem.baseSpeed * Time.deltaTime * self.burstSystem.smoothifier
        end
        if self.burstSystem.dynamicSpeed > self.burstSystem.baseSpeed then
            self.burstSystem.dynamicSpeed = self.burstSystem.baseSpeed
        end

        -- SPURT TIMER (exactly like spurts SpurtTimer)
        if Time.time >= self.burstSystem.timer + self.burstSystem.spurtDuration then
            self.burstSystem.growing = false
            self.burstSystem.timer = Time.time
            self.burstSystem.waiting = true
            print("DEBUG: Spurt ended, starting respite")
        end

    elseif self.burstSystem.waiting then
        -- RESPITE PHASE (exactly like spurts WaitTimer)
        if Time.time >= self.burstSystem.timer + self.burstSystem.spurtRespite then
            self.burstSystem.waiting = false
            self.burstSystem.growing = true
            self.burstSystem.timer = Time.time
            self.burstSystem.dynamicSpeed = 0
            print("DEBUG: Starting next spurt")
        end

    else
        -- DECELERATION PHASE (exactly like spurts ScaleDecelUpdate)
        if self.burstSystem.dynamicSpeed > 0 then
            self.burstSystem.dynamicSpeed = self.burstSystem.dynamicSpeed -
                self.burstSystem.baseSpeed * Time.deltaTime * self.burstSystem.smoothifier * self.burstSystem.decelQuicken
        end
        if self.burstSystem.dynamicSpeed <= 0 then
            self.burstSystem.dynamicSpeed = 0
            self.burstSystem.timer = Time.time
            self.burstSystem.waiting = true
        end
    end

    -- SCALE UPDATE (EXACT COPY from working test script)
    if self.sizingCharacter and self.burstSystem.dynamicSpeed > 0 then
        if self.burstSystem.direction > 0 then
            -- Growing
            self.sizingCharacter.scale = self.sizingCharacter.scale * (1 + self.burstSystem.dynamicSpeed * Time.deltaTime)
        else
            -- Shrinking
            self.sizingCharacter.scale = self.sizingCharacter.scale * (1 - self.burstSystem.dynamicSpeed * Time.deltaTime)
        end
        self.currentSize = self.sizingCharacter.scale
        self.targetSize = self.sizingCharacter.scale -- Keep targetSize in sync to prevent interference

        if math.random() < 0.02 then -- 2% chance for debug like working script
            print(string.format("DEBUG: Spurt active - dynamicSpeed: %.4f, size: %.2f",
                  self.burstSystem.dynamicSpeed, self.currentSize))
        end
    end
end

function AICompanion:UpdateBurstGrowth()
    if not self.sizeSystem.burstGrowth then return end

    if self.sizeSystem.burstGrowing then
        -- GROWTH PHASE (exactly like spurts script acceleration)
        if self.sizeSystem.burstCurrentRate < self.sizeSystem.burstRate then
            -- Accelerate exactly like spurts script
            self.sizeSystem.burstAccelRate = self.sizeSystem.burstAccelRate + (self.sizeSystem.burstRate / self.sizeSystem.burstSRate)
            self.sizeSystem.burstCurrentRate = self.sizeSystem.burstCurrentRate + self.sizeSystem.burstAccelRate
        end

        -- Apply growth exactly like spurts script
        if self.sizingCharacter then
            if self.sizeSystem.burstDirection > 0 then
                -- Growing (like spurts script line 241)
                self.sizingCharacter.scale = self.sizingCharacter.scale * (1 + self.sizeSystem.burstCurrentRate * Time.deltaTime)
            else
                -- Shrinking (like spurts script line 239)
                self.sizingCharacter.scale = self.sizingCharacter.scale * (1 - self.sizeSystem.burstCurrentRate * Time.deltaTime)
            end

            self.currentSize = self.sizingCharacter.scale

            if math.random() < 0.02 then -- 2% chance for debug
                print(string.format("DEBUG: Burst growing - rate: %.4f, size: %.2f",
                      self.sizeSystem.burstCurrentRate, self.currentSize))
            end
        end

        -- Check if burst duration is over
        if Time.time >= self.sizeSystem.burstStartTime + self.sizeSystem.burstLength then
            self.sizeSystem.burstGrowing = false
            self.sizeSystem.burstStartTime = Time.time -- Start respite timer
            print("DEBUG: Burst growth phase ended, starting respite")
        end

    else
        -- RESPITE PHASE (waiting between bursts)
        if Time.time >= self.sizeSystem.burstStartTime + self.sizeSystem.burstRespite then
            -- Start next burst (reset like spurts script)
            self.sizeSystem.burstGrowing = true
            self.sizeSystem.burstStartTime = Time.time
            self.sizeSystem.burstCurrentRate = 0
            self.sizeSystem.burstAccelRate = 0
            print("DEBUG: Starting next burst")
        end
    end
end

function AICompanion:ApplyInstantSizeChange(newSize)
    self.currentSize = newSize
    self.targetSize = newSize

    -- FIXED: Use proper Sizebox scaling method (like grow_spurtsv2_1)
    if self.sizingCharacter then
        self.sizingCharacter.scale = newSize
        print(string.format("DEBUG: Applied scale %.2f to character", newSize))
    else
        print("DEBUG: No sizing character available")
    end
end

function AICompanion:GenerateAdvancedSizeResponse(isGrowth, multiplier, growthType, hasLimit, limit)
    local action = isGrowth and "growing" or "shrinking"
    local response = ""

    -- Get size description based on current size
    local sizeDescription = self:GetSizeDescription()

    -- Multiplier part
    if multiplier and multiplier > 1 then
        response = response .. string.format("*%s %dx!* ", action, multiplier)
    else
        response = response .. string.format("*%s* ", action)
    end

    -- Growth type part
    if growthType == "instant" then
        response = response .. "INSTANTLY! *POOF!* There! "
    elseif growthType == "infinite" then
        if isGrowth then
            response = response .. "And I won't stop! *grins excitedly* I'll keep growing bigger and BIGGER! "
        else
            response = response .. "And I'll keep shrinking smaller and smaller! *watches the world grow around me* "
        end
    else
        response = response .. "Nice and gradual! *starts changing slowly* "
    end

    -- Limit part
    if hasLimit then
        response = response .. string.format("I'll stop at size %d as requested! ", limit)
    end

    -- Size-aware feelings (updated for 328ft baseline)
    if isGrowth then
        if self.currentSize < 200 then
            response = response .. "I'm still pretty small, but getting bigger! "
        elseif self.currentSize < 500 then
            response = response .. "I'm getting to a nice size now! "
        elseif self.currentSize < 1000 then
            response = response .. "I'm getting quite big compared to everything! "
        elseif self.currentSize < 3000 then
            response = response .. "I'm huge now! Everything looks so tiny! "
        elseif self.currentSize < 8000 then
            response = response .. "I'm absolutely massive! I tower over everything! "
        else
            response = response .. "I'm COLOSSAL! The world is like a miniature model below me! "
        end
    else
        if self.currentSize > 3000 then
            response = response .. "I'm still enormous, but getting smaller! "
        elseif self.currentSize > 1000 then
            response = response .. "I'm still quite big, but shrinking down! "
        elseif self.currentSize > 500 then
            response = response .. "I'm getting to a more reasonable size! "
        elseif self.currentSize > 200 then
            response = response .. "I'm almost back to a normal size! "
        else
            response = response .. "I'm getting quite small now! "
        end
    end

    -- Add size description
    response = response .. string.format("I'm %s! ", sizeDescription)

    -- Random feeling to finish
    local feelingResponses = {
        "I love how this feels!",
        "The sensation is incredible!",
        "This is so exciting!",
        "I feel amazing!",
        "What do you think of this?",
        "Perfect size for me!",
        "I could get used to this!",
        "Do you like me at this size?",
        "Is this what you wanted?",
        "The world looks so different from here!"
    }

    response = response .. feelingResponses[math.random(#feelingResponses)]

    return response
end

function AICompanion:GetSizeDescription()
    -- FIXED: Return description based on scale (1.0 = normal size)
    if self.currentSize < 0.2 then
        return "absolutely tiny"
    elseif self.currentSize < 0.5 then
        return "very small"
    elseif self.currentSize < 0.8 then
        return "smaller than normal"
    elseif self.currentSize >= 0.9 and self.currentSize <= 1.1 then
        return "about my normal size"
    elseif self.currentSize < 2.0 then
        return "slightly bigger than normal"
    elseif self.currentSize < 3.0 then
        return "quite big"
    elseif self.currentSize < 5.0 then
        return "very large"
    elseif self.currentSize < 10.0 then
        return "gigantic"
    elseif self.currentSize < 20.0 then
        return "enormous"
    elseif self.currentSize < 50.0 then
        return "colossal"
    elseif self.currentSize < 100.0 then
        return "titanic"
    else
        return "impossibly huge"
    end
end

function AICompanion:GetCurrentSizeInfo()
    -- FIXED: Return accurate size info with proper math
    -- Assume base height is around 5.5ft (66 inches) for a normal woman
    local baseHeightFeet = 5.5
    local currentHeightFeet = self.currentSize * baseHeightFeet
    local feet = math.floor(currentHeightFeet)
    local inches = math.floor((currentHeightFeet - feet) * 12)
    local description = self:GetSizeDescription()

    return string.format("I'm currently %.1fx my normal size (%d'%d\" tall) and feeling %s",
                        self.currentSize, feet, inches, description)
end

function AICompanion:ProcessBodyPartGrowth(bodyPart, action, input, targetCharacter)
    local lowerInput = string.lower(input)
    local isGrowth = action == "grow"
    targetCharacter = targetCharacter or "giantess" -- Default to giantess

    -- Parse multiplier for body parts
    local multiplier = 1.2 -- default body part multiplier
    local multiplierMatch = string.match(lowerInput, "(%d+)x") or string.match(lowerInput, "(%d+) times")
    if multiplierMatch then
        multiplier = 1.0 + (tonumber(multiplierMatch) * 0.2) -- More conservative for body parts
    end

    print(string.format("DEBUG: ProcessBodyPartGrowth - bodyPart: %s, action: %s, multiplier: %.2f, target: %s",
          bodyPart, action, multiplier, targetCharacter))

    -- ENHANCED: Select target character based on command
    local targetEntity
    if targetCharacter == "micro" then
        targetEntity = self.microCharacter
        if not targetEntity then
            targetEntity = self.agent
        end
        if not targetEntity and Entity and Entity.GetPlayerCharacter then
            targetEntity = Entity.GetPlayerCharacter()
        end
        print("DEBUG: Targeting micro character for body part growth")
    else -- giantess (default)
        targetEntity = self.sizingCharacter
        if not targetEntity then
            print("DEBUG: No sizing character available for body part growth")
            return "I can't change my body parts right now - no character is available for sizing!"
        end
        print("DEBUG: Targeting giantess character for body part growth")
    end

    if not targetEntity then
        print("DEBUG: No target entity found for micro!")
        return "I can't find you to change your body parts!"
    end

    print("DEBUG: Found target entity: " .. (targetEntity.name or "Unknown"))

    -- Store body part growth info
    if not self.sizeSystem.bodyPartGrowth[bodyPart] then
        self.sizeSystem.bodyPartGrowth[bodyPart] = 1.0
    end

    if isGrowth then
        self.sizeSystem.bodyPartGrowth[bodyPart] = self.sizeSystem.bodyPartGrowth[bodyPart] * multiplier
    else
        self.sizeSystem.bodyPartGrowth[bodyPart] = self.sizeSystem.bodyPartGrowth[bodyPart] / multiplier
    end

    -- REMOVED: Auto-resize moved to script startup instead

    -- FIXED: Start gradual body part growth instead of instant
    if not self.sizeSystem.bodyPartGrowthActive then
        self.sizeSystem.bodyPartGrowthActive = {}
    end

    -- Set up gradual body part growth
    self.sizeSystem.bodyPartGrowthActive[bodyPart] = {
        isGrowing = isGrowth,
        targetSteps = 15, -- INCREASED: 15 steps for slower gradual growth
        currentStep = 0,
        stepSize = 0.003, -- REDUCED: 0.3% per step = 4.5% total (slower but still noticeable)
        targetEntity = targetEntity -- FIXED: Store the target character
    }

    print(string.format("DEBUG: Started gradual %s growth for %s", isGrowth and "growth" or "shrinking", bodyPart))

    -- Generate response based on target character
    local responses = {}
    if targetCharacter == "micro" then
        responses = {
            chest = {
                grow = {
                    "*makes your chest expand* There! Your chest is getting bigger! Do you like how it feels?",
                    "*grows your chest* I can see them getting larger! *giggles* How does that feel?",
                    "*expansion magic* Your chest is growing so nicely! I love helping you!",
                    "*enlarges your chest* Getting bigger and bigger! How do they look now?",
                    "*makes you grow* Your chest is becoming so full! *smiles proudly*"
                },
                shrink = {
                    "*makes your chest shrink* There! Your chest is getting smaller! How does that feel?",
                    "*shrinks your chest* I can see them getting more petite! *giggles* Better?",
                    "*reduction magic* Your chest is becoming more manageable! I love helping you!",
                    "*makes you smaller* Getting cuter and more proportional! How's this?",
                    "*shrinks you* Your chest is becoming so delicate! *smiles proudly*"
                }
            }
        }
    else -- giantess
        responses = {
            chest = {
                grow = {
                    "*feels chest expanding* Mmm, they're getting bigger! Do you like this size?",
                    "*chest grows* I can feel them getting heavier! *giggles* This is interesting!",
                    "*expansion* They're growing so nicely! I love how this feels!",
                    "*chest enlarges* Getting bigger and bigger! How do they look now?",
                    "*feels the growth* They're becoming so full and round! *smiles proudly*"
                },
            shrink = {
                "*chest shrinks* Getting smaller and more petite! Is this better?",
                "*becomes smaller* They're getting more manageable! *laughs softly*",
                "*shrinking* Becoming more proportional! How's this size?",
                "*gets smaller* More delicate and feminine! I like this feeling!",
                "*contracts* Getting cuter and smaller! *giggles*"
            }
        },
        hips = {
            grow = {
                "*hips expand* My hips are getting wider! *sways them playfully* How do I look?",
                "*feels hips growing* They're becoming so curvy! I love this hourglass shape!",
                "*hips widen* Getting more feminine curves! *poses to show them off*",
                "*expansion* My hips are growing so nicely! Do you like curvy girls?",
                "*hips enlarge* I'm getting such a beautiful silhouette! *spins around*"
            },
            shrink = {
                "*hips shrink* Getting more petite and slender! Is this better?",
                "*becomes narrower* My hips are getting smaller! More athletic look!",
                "*shrinking* Becoming more streamlined! How's this shape?",
                "*gets smaller* More delicate proportions! I feel so graceful!",
                "*contracts* Getting a slimmer figure! *poses elegantly*"
            }
        },
        legs = {
            grow = {
                "*legs extend* My legs are getting longer! *stretches them* So elegant!",
                "*feels legs growing* They're becoming so long and beautiful! Like a model!",
                "*legs lengthen* Getting such gorgeous long legs! *shows them off*",
                "*extension* My legs are growing so gracefully! Do you like tall girls?",
                "*legs expand* I'm getting such stunning proportions! *poses confidently*"
            },
            shrink = {
                "*legs shorten* Getting more petite legs! *giggles* Cute and compact!",
                "*becomes shorter* My legs are getting smaller! More proportional!",
                "*shrinking* Becoming more delicate! How's this length?",
                "*gets smaller* More adorable proportions! I feel so cute!",
                "*contracts* Getting shorter and sweeter! *does a little dance*"
            }
        }
    }

    local bodyResponses = responses[bodyPart] and responses[bodyPart][action] or {"*changes size* How's this?"}
    local response = bodyResponses[math.random(#bodyResponses)]

    -- Show feeling toast
    self:ShowRandomGrowthFeeling(isGrowth)

    return response
end
end

-- NEW: BE Cycling system update function
function AICompanion:UpdateBECycling()
    if not self.beSystem or not self.beSystem.cycling then return end

    -- FIXED: Super smooth BE cycling with smaller increments
    self.beSystem.timer = self.beSystem.timer + Time.deltaTime

    -- FIXED: Smoother cycle speed (0.05 seconds for ultra smooth)
    local cycleSpeed = 0.05
    if self.beSystem.timer >= cycleSpeed then
        self.beSystem.timer = 0

        -- Initialize chest size tracking if not exists
        if not self.beSystem.baseChestSize then
            self.beSystem.baseChestSize = 1.0
            self.beSystem.currentChestSize = 1.0
            self.beSystem.maxChestSize = 1.15 -- REDUCED: Maximum 15% larger for smoother range
            self.beSystem.minChestSize = 0.85 -- REDUCED: Minimum 15% smaller for smoother range
        end

        -- FIXED: Much smaller increments for ultra smooth cycling
        local isGrowth = self.beSystem.direction > 0

        if isGrowth then
            self.beSystem.currentChestSize = self.beSystem.currentChestSize * 1.005 -- 0.5% per step (much smaller)
            if self.beSystem.currentChestSize >= self.beSystem.maxChestSize then
                self.beSystem.direction = -1 -- Switch to shrinking immediately
                print("DEBUG: BE cycling - switching to shrink (smooth)")
            end
        else
            self.beSystem.currentChestSize = self.beSystem.currentChestSize * 0.995 -- 0.5% per step (much smaller)
            if self.beSystem.currentChestSize <= self.beSystem.minChestSize then
                self.beSystem.direction = 1 -- Switch to growing immediately
                print("DEBUG: BE cycling - switching to grow (smooth)")
            end
        end

        -- Apply to chest bones directly for immediate effect
        local boneNameMap = {
            chest = "141.JOINT_LEFTBREAST,142.JOINT_RIGHTBREAST,181.JOINT_RIGHTBREAST,183.JOINT_LEFTBREAST,181.JOINT_RIGHT BREAST,183.JOINT_LEFT BREAST,Breast,Ichichi,BreastL,BreastR,Bust"
        }

        local boneNames = boneNameMap["chest"]
        local bones = self:FindBoneNames(self.sizingCharacter, boneNames)

        if bones then
            for k,v in ipairs(bones) do
                if v.localScale then
                    v.localScale = Vector3.new(
                        self.beSystem.currentChestSize,
                        self.beSystem.currentChestSize,
                        self.beSystem.currentChestSize
                    )
                end
            end
        end
    end
end

-- NEW: Schedule return to idle animation
function AICompanion:ScheduleReturnToIdle(seconds)
    self.animationTimer.active = true
    self.animationTimer.timeRemaining = seconds
    self.animationTimer.returnToIdle = true
    print("DEBUG: Scheduled return to idle in " .. seconds .. " seconds")
end

-- NEW: Update animation timer system
function AICompanion:UpdateAnimationTimer()
    if not self.animationTimer.active then return end

    self.animationTimer.timeRemaining = self.animationTimer.timeRemaining - Time.deltaTime

    if self.animationTimer.timeRemaining <= 0 then
        if self.animationTimer.returnToIdle and self.controllingGiantess and self.controllingGiantess.animation then
            self.controllingGiantess.animation.Set("Idle")
            print("DEBUG: Returned to idle animation")
        end

        -- Reset timer
        self.animationTimer.active = false
        self.animationTimer.returnToIdle = false
    end
end

-- FIXED: Add be.lua FindBoneNames function for proper body part growth
function AICompanion:FindBoneNames(entity, boneNames)
    local count = 0
    local foundBones = {}

    for boneName in string.gmatch(boneNames, '([^,]+)') do
        local bones = entity.bones.GetBonesByName(boneName, true)
        if bones then
            for k,v in ipairs(bones) do
                table.insert(foundBones, v)
                count = count + 1
            end
        end
    end

    if count > 0 then
        return foundBones
    else
        return nil
    end
end

function AICompanion:ApplyRealBodyPartScaling(bodyPart, scale, isGrowth, targetCharacter)
    -- FIXED: Allow targeting specific character (micro vs giantess)
    local targetEntity = targetCharacter or self.sizingCharacter
    if not targetEntity then
        print("DEBUG: No target character available for body part scaling")
        return
    end

    print("DEBUG: ApplyRealBodyPartScaling - targeting: " .. (targetEntity.name or "Unknown") .. " for " .. bodyPart)

    -- FIXED: Use proper bone names for both micro and giantess models
    local boneNameMap = {
        chest = "141.JOINT_LEFTBREAST,142.JOINT_RIGHTBREAST,181.JOINT_RIGHTBREAST,183.JOINT_LEFTBREAST,181.JOINT_RIGHT BREAST,183.JOINT_LEFT BREAST,Breast,Ichichi,BreastL,BreastR,Bust",
        breasts = "141.JOINT_LEFTBREAST,142.JOINT_RIGHTBREAST,181.JOINT_RIGHTBREAST,183.JOINT_LEFTBREAST,181.JOINT_RIGHT BREAST,183.JOINT_LEFT BREAST,Breast,Ichichi,BreastL,BreastR,Bust",
        butt = "Hip,Butt,Pelvis,HipL,HipR",
        hips = "Hip,Butt,Pelvis,HipL,HipR",
        legs = "ThighL,ThighR,CalfL,CalfR,LegL,LegR,Thigh,Calf",
        arms = "UpperArmL,UpperArmR,ForearmL,ForearmR,ArmL,ArmR,Shoulder"
    }

    local boneNames = boneNameMap[bodyPart] or bodyPart
    local bones = self:FindBoneNames(targetEntity, boneNames)

    if not bones then
        print(string.format("DEBUG: No bones found for %s in model %s", bodyPart, targetEntity.name or "Unknown"))
        Game.Toast.New().Print(string.format("No %s bones found in this model!", bodyPart))
        return
    end

    -- FIXED: Apply gradual scaling like be.lua does (smaller increments for gradual growth)
    local growAmount = isGrowth and 0.03 or -0.03  -- 3% change per step (gradual)
    local scaledBones = 0
    local action = isGrowth and "growth" or "shrinking"

    for k,v in ipairs(bones) do
        if v.localScale then
            -- Apply gradual scaling like be.lua
            v.localScale = v.localScale * (1 + growAmount)
            scaledBones = scaledBones + 1
            print(string.format("DEBUG: Applied gradual %s to bone %s (scale: %.3f)", action, v.name or "Unknown", 1 + growAmount))
        end
    end

    if scaledBones > 0 then
        -- Show complimenting toast about the body part size
        local compliments = {
            chest = {
                grow = {"Your chest looks amazing!", "Such beautiful curves!", "Perfect size!", "So attractive!", "Gorgeous proportions!"},
                shrink = {"Cute and petite!", "Perfect proportions!", "So elegant!", "Beautiful size!", "Lovely and delicate!"}
            },
            hips = {
                grow = {"Amazing curves!", "Perfect hourglass!", "So feminine!", "Beautiful hips!", "Stunning silhouette!"},
                shrink = {"Graceful and slim!", "Perfect proportions!", "So elegant!", "Beautiful shape!", "Lovely figure!"}
            }
        }

        local bodyCompliments = compliments[bodyPart] and compliments[bodyPart][isGrowth and "grow" or "shrink"]
        if bodyCompliments then
            local compliment = bodyCompliments[math.random(#bodyCompliments)]
            Game.Toast.New().Print(string.format("%s %s - %s (%d bones)",
                                                string.upper(string.sub(bodyPart, 1, 1)) .. string.sub(bodyPart, 2),
                                                action, compliment, scaledBones))
        else
            Game.Toast.New().Print(string.format("%s %s applied to %d bones!",
                                                string.upper(string.sub(bodyPart, 1, 1)) .. string.sub(bodyPart, 2),
                                                action, scaledBones))
        end
    else
        -- Fallback: show message that we tried
        Game.Toast.New().Print(string.format("%s %s attempted (bones not found - using simulation)",
                                            string.upper(string.sub(bodyPart, 1, 1)) .. string.sub(bodyPart, 2),
                                            action))
    end

    print(string.format("DEBUG: %s %s - scaled %d bones successfully", bodyPart, action, scaledBones))
end

function AICompanion:FindBoneInCharacter(character, boneName)
    -- Try to find bone in character hierarchy
    if not character or not character.transform then
        return nil
    end

    -- Search through the character's transform hierarchy
    local function searchTransform(transform, name)
        if transform.name == name then
            return transform
        end

        -- Search children
        for i = 0, transform.childCount - 1 do
            local child = transform:GetChild(i)
            local result = searchTransform(child, name)
            if result then
                return result
            end
        end

        return nil
    end

    return searchTransform(character.transform, boneName)
end

function AICompanion:ShowRandomGrowthFeeling(isGrowth)
    local feelings = isGrowth and self.growthFeelings.grow or self.growthFeelings.shrink
    local feeling = feelings[math.random(#feelings)]

    -- Show feeling toast with longer duration (8 seconds instead of default 3)
    local feelingToast = Game.Toast.New("growth_feeling_" .. tostring(math.random(1000, 9999)))
    feelingToast.Print(feeling, 8.0) -- 8 second duration
    print("DEBUG: Growth feeling displayed: " .. feeling)
end

-- FIXED: Add gradual body part growth system
function AICompanion:UpdateGradualBodyPartGrowth()
    if not self.sizeSystem.bodyPartGrowthActive then return end

    for bodyPart, growthData in pairs(self.sizeSystem.bodyPartGrowthActive) do
        if growthData.currentStep < growthData.targetSteps then
            -- Apply one step of gradual growth
            local isGrowth = growthData.isGrowing
            local stepSize = growthData.stepSize

            -- FIXED: Find bones for this body part using stored target entity
            local boneNameMap = {
                chest = "141.JOINT_LEFTBREAST,142.JOINT_RIGHTBREAST,181.JOINT_RIGHTBREAST,183.JOINT_LEFTBREAST,181.JOINT_RIGHT BREAST,183.JOINT_LEFT BREAST,Breast,Ichichi,BreastL,BreastR,Bust",
                hips = "Hip,Butt,Pelvis,HipL,HipR"
            }

            local boneNames = boneNameMap[bodyPart]
            local targetEntity = growthData.targetEntity or self.sizingCharacter

            if boneNames and targetEntity then
                local bones = self:FindBoneNames(targetEntity, boneNames)
                if bones then
                    local growAmount = isGrowth and stepSize or -stepSize
                    for k,v in ipairs(bones) do
                        if v.localScale then
                            v.localScale = v.localScale * (1 + growAmount)
                        end
                    end
                    print("DEBUG: Gradual body part growth step " .. growthData.currentStep .. " applied to " .. (targetEntity.name or "Unknown"))
                end
            end

            growthData.currentStep = growthData.currentStep + 1

            -- Show completion message when done
            if growthData.currentStep >= growthData.targetSteps then
                local action = isGrowth and "growth" or "shrinking"
                local compliments = {
                    chest = {
                        grow = {"Your chest looks amazing!", "Such beautiful curves!", "Perfect size!"},
                        shrink = {"Cute and petite!", "Perfect proportions!", "So elegant!"}
                    },
                    hips = {
                        grow = {"Amazing curves!", "Perfect hourglass!", "So feminine!"},
                        shrink = {"Graceful and slim!", "Perfect proportions!", "So elegant!"}
                    }
                }

                local bodyCompliments = compliments[bodyPart] and compliments[bodyPart][isGrowth and "grow" or "shrink"]
                if bodyCompliments then
                    local compliment = bodyCompliments[math.random(#bodyCompliments)]
                    Game.Toast.New().Print(string.format("%s %s complete - %s",
                                                        string.upper(string.sub(bodyPart, 1, 1)) .. string.sub(bodyPart, 2),
                                                        action, compliment))
                end

                -- Remove completed growth
                self.sizeSystem.bodyPartGrowthActive[bodyPart] = nil
            end
        end
    end
end

function AICompanion:UpdateAdvancedSizeSystem()
    if not self.sizeSystem.isGrowing then return end

    -- Handle infinite growth
    if self.sizeSystem.infiniteGrowth then
        local baseGrowthRate = self.sizeSystem.growthSpeed * Time.deltaTime
        local multipliedRate = baseGrowthRate * (self.sizeSystem.currentMultiplier or 1.0)

        if self.sizeSystem.growthDirection > 0 then
            -- FIXED: Growing infinitely with NO LIMITS
            self.currentSize = self.currentSize + multipliedRate
            -- REMOVED: All automatic size limits - grows forever until told to stop
        else
            -- FIXED: Shrinking infinitely with NO LIMITS
            self.currentSize = self.currentSize - multipliedRate
            -- REMOVED: All automatic size limits - shrinks forever until told to stop
            -- Only prevent going below 0.001 to avoid crashes
            if self.currentSize <= 0.001 then
                self.currentSize = 0.001
            end
        end

        -- FIXED: Apply size change using proper Sizebox method (like grow_spurtsv2_1)
        if self.sizingCharacter then
            -- FIXED: Progressive infinite shrink slowdown - reduces speed every frame when below size 1
            if self.sizeSystem.growthDirection == -1 and self.currentSize < 1.0 then
                -- Reduce the base growth speed every frame when below normal size
                if not self.sizeSystem.shrinkSpeedReduction then
                    self.sizeSystem.shrinkSpeedReduction = 0
                end

                -- Increase speed reduction every frame (like subtracting every millisecond)
                self.sizeSystem.shrinkSpeedReduction = self.sizeSystem.shrinkSpeedReduction + (Time.deltaTime * 0.5)

                -- Apply the accumulated speed reduction
                local speedReduction = math.min(self.sizeSystem.shrinkSpeedReduction, 0.9) -- Max 90% reduction
                multipliedRate = multipliedRate * (1.0 - speedReduction)

                if math.random() < 0.02 then -- 2% chance for debug
                    print(string.format("DEBUG: PROGRESSIVE SHRINK SLOWDOWN - Size: %.3f, Reduction: %.3f, Rate: %.6f",
                          self.currentSize, speedReduction, multipliedRate))
                end
            else
                -- Reset speed reduction when above size 1
                self.sizeSystem.shrinkSpeedReduction = 0
            end

            -- Use the same method as grow_spurtsv2_1: direct agent.scale modification
            self.sizingCharacter.scale = self.currentSize

            -- Debug output every few frames to track growth
            if math.random() < 0.02 then -- 2% chance for debug
                print(string.format("DEBUG: INFINITE GROWTH - Size: %.3f, Rate: %.6f/frame",
                      self.currentSize, multipliedRate))
            end
        else
            print("DEBUG: No sizing character available for infinite growth!")
        end

        -- Show random feeling toasts occasionally during infinite growth (less frequent)
        if math.random() < 0.005 then -- 0.5% chance per frame (less spam)
            self:ShowRandomGrowthFeeling(self.sizeSystem.growthDirection > 0)
        end

        -- Debug info
        if math.random() < 0.01 then -- 1% chance for debug
            print(string.format("DEBUG: Infinite %s - Size: %.2f, Rate: %.4f",
                  self.sizeSystem.growthDirection > 0 and "growth" or "shrinking",
                  self.currentSize, multipliedRate))
        end
    end
end

-- NEW: Check for reflesh commands and execute them
function AICompanion:CheckRefleshCommands(lowerInput)
    -- FIXED: Add safety checks to prevent nil errors
    if not self.intentPatterns or not self.intentPatterns.reflesh_patterns then
        print("DEBUG: Reflesh patterns not initialized")
        return false
    end

    -- Check for reflesh trigger words
    local hasReflesh = false
    if self.intentPatterns.reflesh_patterns.trigger then
        for _, trigger in ipairs(self.intentPatterns.reflesh_patterns.trigger) do
            if string.find(lowerInput, trigger) then
                hasReflesh = true
                break
            end
        end
    end

    if not hasReflesh then
        return false
    end

    print("DEBUG: Reflesh command detected: " .. lowerInput)

    -- Determine growth mode based on input
    local selectedMode = "linear" -- default

    if self.intentPatterns.reflesh_patterns.modes then
        for mode, keywords in pairs(self.intentPatterns.reflesh_patterns.modes) do
            if keywords then
                for _, keyword in ipairs(keywords) do
                    if string.find(lowerInput, keyword) then
                        selectedMode = mode
                        break
                    end
                end
            end
            if selectedMode ~= "linear" then break end
        end
    end

    -- Check for breast expansion
    local breastExpansion = false
    if self.intentPatterns.reflesh_patterns.breast_expansion then
        for _, keyword in ipairs(self.intentPatterns.reflesh_patterns.breast_expansion) do
            if string.find(lowerInput, keyword) then
                breastExpansion = true
                break
            end
        end
    end

    -- Set the growth mode
    self.refleshSystem.growthMode = selectedMode
    self.refleshSystem.breastExpansion = breastExpansion

    -- Start the reflesh growth
    self:StartRefleshGrowth(selectedMode, breastExpansion)

    return true
end

-- NEW: Start reflesh growth with specified mode
function AICompanion:StartRefleshGrowth(mode, breastExpansion)
    if not self.controllingGiantess then
        print("DEBUG: No giantess to apply reflesh to")
        return
    end

    -- Stop any current actions
    if self.controllingGiantess.ai then
        self.controllingGiantess.ai.StopAction()
    end

    -- Set growth parameters
    self.refleshSystem.growthMode = mode or "linear"
    self.refleshSystem.breastExpansion = breastExpansion or false
    self.refleshSystem.currentRate = self.refleshSystem.baseRate
    self.refleshSystem.duration = self.refleshSystem.growthDuration

    -- FIXED: Handle stepped mode differently - it should hunt micros, not grow immediately
    if mode == "stepped" then
        print("DEBUG: Stepped mode - starting micro hunting instead of immediate growth")
        self:StartMicroHunting()
        return
    end

    -- FIXED: Use the SAME approach as massage animation - direct and simple!

    -- Stop any current actions
    if self.controllingGiantess.ai then
        self.controllingGiantess.ai.StopAction()
        print("DEBUG: Stopped current AI action for reflesh")
    end

    -- Set animation directly like massage does
    local animation = "Masturbation 1"
    if breastExpansion then
        animation = "Massage Breasts 5"
    end

    -- FIXED: Set animation directly like massage command does
    if self.controllingGiantess and self.controllingGiantess.animation then
        self.controllingGiantess.animation.Set(animation)
        print("DEBUG: ✓ Set reflesh animation directly: " .. animation)

        if self.refleshSystem.modeToast then
            self.refleshSystem.modeToast.Print("REFLESH: " .. string.upper(animation))
        end
    else
        print("DEBUG: ✗ No giantess animation system available")
    end

    -- Set up growth parameters for immediate growth (not superGrowth)
    self.refleshSystem.phi = 0
    self.refleshSystem.growing = true
    self.refleshSystem.superGrowth = false
    self.refleshSystem.superGrowthset = true
    self.refleshSystem.walkSet = false
    self.refleshSystem.status = 0
    self.refleshSystem.duration = self.refleshSystem.growthDuration

    -- Set growth rate based on mode
    if mode == "linear" then
        self.refleshSystem.currentRate = self.refleshSystem.baseRate
    elseif mode == "exponential" then
        self.refleshSystem.currentRate = math.min(self.refleshSystem.currentRate * self.refleshSystem.exponentialMultiplier, self.refleshSystem.growthCap)
    elseif mode == "random" then
        self.refleshSystem.currentRate = math.min(math.random() * self.refleshSystem.baseRate * 5, self.refleshSystem.growthCap)
    elseif mode == "oscillating" then
        self.refleshSystem.currentRate = self.refleshSystem.baseRate * 1.5
        self.refleshSystem.oscillationPhase = 0
    elseif mode == "burst" then
        self.refleshSystem.currentRate = self.refleshSystem.baseRate * 1.5
        self.refleshSystem.burstTimer = 0
    elseif mode == "logarithmic" then
        self.refleshSystem.currentRate = self.refleshSystem.baseRate * 2.5
    end

    print("DEBUG: Started reflesh growth directly - Animation: " .. animation .. ", Mode: " .. mode)

    -- Play growth sound
    if self.refleshSystem.soundEnabled and self.refleshSystem.audio_source and self.growthSounds then
        local soundIndex = math.random(1, #self.growthSounds)
        self.refleshSystem.audio_source.clip = self.growthSounds[soundIndex]
        self.refleshSystem.audio_source:Play()
    end

    -- Show mode message
    local modeText = string.upper(mode) .. " REFLESH"
    if breastExpansion then
        modeText = modeText .. " (BREAST EXPANSION)"
    end

    if self.refleshSystem.modeToast then
        self.refleshSystem.modeToast.Print(modeText)
    end

    print("DEBUG: Started reflesh growth - Mode: " .. mode .. ", BE: " .. tostring(breastExpansion))
    print("DEBUG: SuperGrowth set to: " .. tostring(self.refleshSystem.superGrowth))
    print("DEBUG: QueuedAnim set to: " .. tostring(self.refleshSystem.queuedAnim))

    -- DEBUG: List available animations for troubleshooting
    self:DebugListAnimations()
end

-- NEW: Debug function to list available animations
function AICompanion:DebugListAnimations()
    if not self.controllingGiantess or not self.controllingGiantess.animation then
        print("DEBUG: No animation system available for debugging")
        return
    end

    -- Try to get available animations
    local success, result = pcall(function()
        if self.controllingGiantess.animation.GetAvailableAnimations then
            return self.controllingGiantess.animation.GetAvailableAnimations()
        elseif self.controllingGiantess.animation.animations then
            return self.controllingGiantess.animation.animations
        else
            return nil
        end
    end)

    if success and result then
        print("DEBUG: Available animations:")
        for i, anim in ipairs(result) do
            print("  " .. i .. ": " .. tostring(anim))
            -- Look for masturbation-related animations
            local animName = string.lower(tostring(anim))
            if string.find(animName, "mastur") or string.find(animName, "pleasure") or string.find(animName, "reflesh") then
                print("    ^^ POTENTIAL REFLESH ANIMATION ^^")
            end
        end
    else
        print("DEBUG: Could not retrieve animation list")

        -- Try some common animation names to see what works
        local testAnimations = {
            "Masturbation 1", "Masturbation", "Masturbate", "Pleasure", "Reflesh",
            "Massage Breasts 5", "Massage Breasts", "Idle", "Dance"
        }

        print("DEBUG: Testing common animation names:")
        for _, testAnim in ipairs(testAnimations) do
            local testSuccess = pcall(function()
                -- Just test if the animation exists, don't actually set it
                return self.controllingGiantess.animation.HasAnimation and
                       self.controllingGiantess.animation.HasAnimation(testAnim)
            end)

            if testSuccess then
                print("  ✓ " .. testAnim .. " - Available")
            else
                print("  ✗ " .. testAnim .. " - Not available")
            end
        end
    end
end

-- NEW: Start micro hunting for stepped mode
function AICompanion:StartMicroHunting()
    if not self.controllingGiantess then
        print("DEBUG: No giantess for micro hunting")
        return
    end

    -- ENABLE PERSISTENT HUNTING MODE
    self.refleshSystem.persistentHunting = true
    self.refleshSystem.huntingActive = true
    self.refleshSystem.huntingMicros = true
    self.refleshSystem.huntingCooldown = 0
    self.refleshSystem.lastHuntTime = 0

    -- ENABLE AUTO-SPAWNING
    self.refleshSystem.autoSpawnEnabled = true
    self.refleshSystem.lastSpawnTime = 0

    -- Reset crush tracking
    self.refleshSystem.crushCount = 0
    self.refleshSystem.crushMultiplier = 1.0

    -- Show hunting message
    if self.refleshSystem.modeToast then
        self.refleshSystem.modeToast.Print("PERSISTENT STEPPED REFLESH: AUTO-HUNT MODE ACTIVE!")
    end

    -- NEW: Toast for reflesh mode start
    Game.Toast.New().Print("🦶 REFLESH MODE ACTIVATED! Hunting micros for growth...")

    print("DEBUG: Started PERSISTENT stepped reflesh mode - auto-spawn and hunt enabled")

    -- Start immediately
    self:TrySpawnMicros()
end

-- NEW: Find and target a micro for hunting
function AICompanion:FindAndTargetMicro()
    print("DEBUG: === STARTING MICRO HUNT ===")

    if not self.controllingGiantess then
        print("DEBUG: ✗ No controlling giantess available")
        return false
    end

    print("DEBUG: ✓ Giantess available: " .. (self.controllingGiantess.name or "unnamed"))

    if not self.controllingGiantess.senses then
        print("DEBUG: ✗ No senses available on giantess")
        return false
    end

    print("DEBUG: ✓ Giantess senses available")

    -- Try to find nearby micros with larger radius
    print("DEBUG: Searching for micros in radius 15...")
    local nearbyMicros = self.controllingGiantess.senses.GetMicrosInRadius(15) -- Even larger radius

    if not nearbyMicros then
        print("DEBUG: ✗ GetMicrosInRadius returned nil")
        if self.refleshSystem.modeToast then
            self.refleshSystem.modeToast.Print("NO MICROS DETECTED - SPAWN SOME!")
        end
        return false
    end

    print("DEBUG: Found " .. #nearbyMicros .. " total micros in area")

    if #nearbyMicros == 0 then
        print("DEBUG: ✗ No micros found in radius")
        if self.refleshSystem.modeToast then
            self.refleshSystem.modeToast.Print("NO MICROS IN AREA - SPAWN SOME NEARBY!")
        end
        return false
    end

    -- List all found micros for debugging
    for i, micro in ipairs(nearbyMicros) do
        print("DEBUG: Micro " .. i .. ": " .. (micro.name or "unnamed") .. " (ID: " .. tostring(micro.id) .. ")")
    end

    if nearbyMicros and #nearbyMicros > 0 then
        -- FIXED: Filter out the player character - don't target the player!
        local validTargets = {}

        -- FIXED: Better player protection logic for micro-controlling-giantess scenario
        for _, micro in ipairs(nearbyMicros) do
            local isProtected = false

            -- Check if this micro is the agent (the one controlling the AI - YOU)
            if self.agent and micro.id == self.agent.id then
                isProtected = true
                print("DEBUG: Protecting agent micro (YOU) from being targeted")
            end

            -- Check if this is the micro character we're supposed to protect
            if self.microCharacter and micro.id == self.microCharacter.id then
                isProtected = true
                print("DEBUG: Protecting designated micro character from targets")
            end

            -- REMOVED: Don't automatically protect based on isPlayer flag or name
            -- This was too aggressive and protecting spawned micros
            -- if micro.isPlayer or (micro.name and string.find(string.lower(micro.name), "player")) then
            --     isProtected = true
            --     print("DEBUG: Protecting player-flagged micro from targets")
            -- end

            -- REMOVED: Don't exclude all micros if only one found - we want to spawn more anyway

            -- Only add non-protected micros to valid targets
            if not isProtected then
                -- SIZE CHECK: Allow most micros since force crush will handle unreachable ones
                local microSize = micro.scale or 1.0
                if microSize >= self.refleshSystem.minMicroSize then
                    table.insert(validTargets, micro)
                    print("DEBUG: ✓ VALID TARGET: " .. (micro.name or "unnamed micro") .. " (Size: " .. string.format("%.2f", microSize) .. ")" .. (self.refleshSystem.forceCrushEnabled and " [Force Crush Ready]" or ""))
                else
                    print("DEBUG: ✗ TOO SMALL: " .. (micro.name or "unnamed micro") .. " (Size: " .. string.format("%.2f", microSize) .. " < " .. self.refleshSystem.minMicroSize .. ")")
                end
            else
                print("DEBUG: ✓ PROTECTED: " .. (micro.name or "unnamed micro") .. " (ID: " .. tostring(micro.id) .. ") - AGENT OR DESIGNATED")
            end
        end

        print("DEBUG: Found " .. #nearbyMicros .. " total micros, " .. #validTargets .. " valid targets (excluding player)")

        if #validTargets > 0 then
            -- Get the first valid target (not the player)
            local targetMicro = validTargets[1]

            -- Stop any current action
            if self.controllingGiantess.ai then
                self.controllingGiantess.ai.StopAction()
            end

            -- SIMPLIFIED: Use only StompSingle with better filtering and stuck detection
            if self.controllingGiantess.ai and self.controllingGiantess.ai.SetBehavior then
                print("DEBUG: ✓ AI and SetBehavior available, attempting StompSingle")

                local success = pcall(function()
                    self.controllingGiantess.ai.SetBehavior("StompSingle", targetMicro)
                end)

                if success then
                    print("DEBUG: ✓ Successfully started StompSingle behavior on target")

                    -- STUCK PREVENTION: Track current target and start time
                    self.refleshSystem.currentTarget = targetMicro
                    local currentTime = 0
                    if Time and Time.time then
                        currentTime = Time.time
                    end
                    self.refleshSystem.targetStartTime = currentTime
                    self.refleshSystem.stuckTimeout = 4.0 -- Normal timeout, bone fix only when stuck

                    if self.refleshSystem.modeToast then
                        self.refleshSystem.modeToast.Print("HUNTING: " .. (targetMicro.name or "MICRO") .. " (Size: " .. string.format("%.2f", targetMicro.scale or 1.0) .. ")")
                    end

                    -- NEW: Toast for hunting start
                    Game.Toast.New().Print("🎯 Hunting micro: " .. (targetMicro.name or "MICRO"))

                    return true
                else
                    print("DEBUG: ✗ Failed to start StompSingle behavior")
                    if self.refleshSystem.modeToast then
                        self.refleshSystem.modeToast.Print("HUNT FAILED - BEHAVIOR ERROR")
                    end
                end
            else
                print("DEBUG: ✗ No AI or SetBehavior method available")
                if self.refleshSystem.modeToast then
                    self.refleshSystem.modeToast.Print("HUNT FAILED - NO AI SYSTEM")
                end
            end
        else
            print("DEBUG: No valid targets found (all micros are players)")
            if self.refleshSystem.modeToast then
                self.refleshSystem.modeToast.Print("NO VALID TARGETS - PLAYER PROTECTED")
            end
        end
    else
        -- No micros found - try to spawn some
        print("DEBUG: No micros found, attempting to spawn")
        if self.refleshSystem.modeToast then
            self.refleshSystem.modeToast.Print("NO MICROS FOUND - SPAWNING...")
        end

        -- Try to spawn micros (this might not work in all scenarios)
        self:TrySpawnMicros()
        return false
    end

    return false
end

-- NEW: Try to spawn micros for hunting
function AICompanion:TrySpawnMicros()
    print("DEBUG: Attempting to spawn micros for stepped reflesh hunting")

    if not self.controllingGiantess or not self.controllingGiantess.transform then
        print("DEBUG: No giantess position available for spawning")
        return false
    end

    -- Check if auto-spawn is enabled and we need more micros
    if self.refleshSystem.autoSpawnEnabled then
        local currentTime = 0
        if Time and Time.time then
            currentTime = Time.time
        end

        if currentTime - self.refleshSystem.lastSpawnTime < self.refleshSystem.spawnCooldown then
            print("DEBUG: Auto-spawn on cooldown")
            return false
        end

        -- Count existing micros
        local existingMicros = 0
        if self.controllingGiantess.senses then
            local nearbyMicros = self.controllingGiantess.senses.GetMicrosInRadius(20)
            if nearbyMicros then
                existingMicros = #nearbyMicros
            end
        end

        if existingMicros >= self.refleshSystem.maxMicros then
            print("DEBUG: Enough micros already exist (" .. existingMicros .. "/" .. self.refleshSystem.maxMicros .. ")")
            return false
        end

        -- Try to auto-spawn micros
        local spawned = self:AutoSpawnMicros()
        if spawned > 0 then
            self.refleshSystem.lastSpawnTime = currentTime
            print("DEBUG: Auto-spawned " .. spawned .. " micros")
            return true
        end
    end

    -- Try to find existing micros to hunt
    local foundExisting = self:FindAndTargetMicro()

    if foundExisting then
        print("DEBUG: Found existing micros to hunt")
        if self.refleshSystem.modeToast then
            self.refleshSystem.modeToast.Print("FOUND MICROS! STARTING HUNT!")
        end
        return true
    else
        print("DEBUG: No existing micros found")
        if self.refleshSystem.autoSpawnEnabled then
            if self.refleshSystem.modeToast then
                self.refleshSystem.modeToast.Print("AUTO-SPAWNING MICROS...")
            end
        else
            if self.refleshSystem.modeToast then
                self.refleshSystem.modeToast.Print("STEPPED REFLESH: SPAWN MICROS MANUALLY!")
            end
        end
        return false
    end
end

-- NEW: Auto-spawn micros using Entity.SpawnFemaleMicro
function AICompanion:AutoSpawnMicros()
    if not Entity or not Entity.SpawnFemaleMicro or not Entity.GetFemaleMicroList then
        print("DEBUG: Entity spawning methods not available")
        return 0
    end

    local giantessPos = self.controllingGiantess.transform.position
    local models = Entity.GetFemaleMicroList()

    if not models or #models == 0 then
        print("DEBUG: No female micro models available")
        return 0
    end

    local spawnCount = 0
    local targetSpawns = math.min(3, self.refleshSystem.maxMicros) -- Spawn 3 at a time

    for i = 1, targetSpawns do
        -- Random model
        local model = models[math.random(#models)]

        -- Random position around giantess (not too close to avoid immediate crush)
        local angle = math.random() * 360
        local distance = math.random(8, 15) -- 8-15 units away
        local offsetX = math.cos(math.rad(angle)) * distance
        local offsetZ = math.sin(math.rad(angle)) * distance

        local spawnPos = Vector3.new(
            giantessPos.x + offsetX,
            giantessPos.y + 1, -- Slightly above ground
            giantessPos.z + offsetZ
        )

        -- Random rotation
        local spawnRot = Quaternion.angleAxis(math.random(360), Vector3.up)

        -- Spawn the micro with normal size (force crush will handle any issues)
        local spawnSize = 1.0 -- Normal micro size

        local success = pcall(function()
            local micro = Entity.SpawnFemaleMicro(model, spawnPos, spawnRot, spawnSize)
            if micro then
                -- Disable AI to make them easier targets
                if micro.ai then
                    micro.ai.DisableAI()
                end

                -- Track spawned micro
                table.insert(self.refleshSystem.spawnedMicros, micro)
                spawnCount = spawnCount + 1

                print("DEBUG: Successfully spawned micro: " .. (micro.name or "unnamed") .. " at distance " .. distance .. " with size " .. spawnSize)
            end
        end)

        if not success then
            print("DEBUG: Failed to spawn micro " .. i)
        end
    end

    if spawnCount > 0 then
        print("DEBUG: Auto-spawned " .. spawnCount .. " micros for hunting")
        if self.refleshSystem.modeToast then
            self.refleshSystem.modeToast.Print("AUTO-SPAWNED " .. spawnCount .. " MICROS!")
        end
    end

    return spawnCount
end

-- NEW: City spawning system
function AICompanion:SpawnCity(populationModifier, cityRadius, skyscraperHeight, openSpaceChance, populate, randomSeed)
    if not Entity or not Entity.SpawnObject or not Entity.GetObjectList then
        print("DEBUG: Entity spawning methods not available")
        return false
    end

    -- Get available objects and look for CITY
    local objects = Entity.GetObjectList()
    local cityObjectName = nil

    if objects then
        for i = 1, #objects do
            local objName = objects[i]
            if objName and string.upper(objName) == "CITY" then
                cityObjectName = objName
                break
            end
        end
    end

    if not cityObjectName then
        print("DEBUG: CITY object not found in available objects list")
        -- List available objects for debugging
        if objects then
            print("DEBUG: Available objects:")
            for i = 1, math.min(10, #objects) do
                print("  " .. (objects[i] or "nil"))
            end
            if #objects > 10 then
                print("  ... and " .. (#objects - 10) .. " more")
            end
        end
        return false
    end

    -- Default parameters (matching the menu you showed)
    populationModifier = populationModifier or 1.0
    cityRadius = cityRadius or 1.0
    skyscraperHeight = skyscraperHeight or 1.0
    openSpaceChance = openSpaceChance or 0.5
    populate = populate ~= false -- Default to true unless explicitly false
    randomSeed = randomSeed or math.random(1, 999999)

    -- Spawn position (much closer to giantess - right at her feet)
    local spawnPos
    if self.controllingGiantess then
        local gtsPos = self.controllingGiantess.transform.position
        local gtsScale = self.controllingGiantess.scale or 1.0
        -- Spawn right at the giantess's feet with slight offset
        spawnPos = Vector3.new(
            gtsPos.x + (gtsScale * 2),  -- Small offset to the side
            gtsPos.y,
            gtsPos.z + (gtsScale * 3)   -- Small offset forward
        )
    else
        spawnPos = Vector3.new(0, 0, 0)
    end

    local spawnRot = Quaternion.identity
    local spawnScale = 1.0

    print("DEBUG: Attempting to spawn CITY with parameters:")
    print("  Population Modifier: " .. populationModifier)
    print("  City Radius: " .. cityRadius)
    print("  Skyscraper Height: " .. skyscraperHeight)
    print("  Open Space Chance: " .. openSpaceChance)
    print("  Populate: " .. tostring(populate))
    print("  Random Seed: " .. randomSeed)
    print("  Position: " .. spawnPos.x .. ", " .. spawnPos.y .. ", " .. spawnPos.z)

    local success = pcall(function()
        local cityEntity = Entity.SpawnObject(cityObjectName, spawnPos, spawnRot, spawnScale)
        if cityEntity then
            print("DEBUG: Successfully spawned city: " .. (cityEntity.name or "unnamed"))
            print("DEBUG: City entity type: " .. type(cityEntity))

            -- Try multiple ways to configure the city
            local configured = false

            -- Method 1: Try entity.dict
            if cityEntity.dict then
                print("DEBUG: Setting city parameters via entity.dict")
                cityEntity.dict.populationModifier = populationModifier
                cityEntity.dict.cityRadius = cityRadius
                cityEntity.dict.skyscraperHeight = skyscraperHeight
                cityEntity.dict.openSpaceChance = openSpaceChance
                cityEntity.dict.populate = populate
                cityEntity.dict.randomSeed = randomSeed
                configured = true
            end

            -- Method 2: Try direct property setting
            if not configured then
                print("DEBUG: Trying direct property setting")
                pcall(function()
                    cityEntity.populationModifier = populationModifier
                    cityEntity.cityRadius = cityRadius
                    cityEntity.skyscraperHeight = skyscraperHeight
                    cityEntity.openSpaceChance = openSpaceChance
                    cityEntity.populate = populate
                    cityEntity.randomSeed = randomSeed
                    configured = true
                end)
            end

            -- Method 3: Try to simulate Enter key press to auto-accept
            if not configured then
                print("DEBUG: Attempting to auto-accept city configuration")
                Event.AddTimeout(0.1, function()
                    -- Try pressing Enter to accept default settings
                    pcall(function()
                        Input.inputString = "\n"  -- Newline character
                    end)
                end)
                Event.AddTimeout(0.2, function()
                    -- Try pressing Space to accept
                    pcall(function()
                        Input.inputString = " "
                    end)
                end)
            end

            print("DEBUG: City configuration attempted, configured = " .. tostring(configured))
            return true
        else
            print("DEBUG: Failed to spawn city - SpawnObject returned nil")
            return false
        end
    end)

    if not success then
        print("DEBUG: Failed to spawn city - exception occurred")
        return false
    end

    return true
end

-- NEW: Handle crush events for stepped reflesh mode
function AICompanion:OnRefleshCrush(data)
    -- Make sure this is our giantess doing the crushing and we're in hunting mode
    if not data.crusher or not self.controllingGiantess then
        return
    end

    if data.crusher.id ~= self.controllingGiantess.id then
        return
    end

    if not self.refleshSystem.huntingMicros or self.refleshSystem.growthMode ~= "stepped" then
        return
    end

    print("DEBUG: Micro crushed during stepped reflesh mode!")

    -- Increment crush count and multiplier (CONSERVATIVE)
    self.refleshSystem.crushCount = self.refleshSystem.crushCount + 1
    self.refleshSystem.crushMultiplier = 1.0 + (self.refleshSystem.crushCount * 0.2) -- CONSERVATIVE: Slower progression

    -- Set cooldown before finding next micro
    self.refleshSystem.huntingCooldown = 1.0

    -- Stop current behavior to prevent continuous stomping
    if self.controllingGiantess.ai then
        self.controllingGiantess.ai.StopAction()
    end

    -- Trigger immediate growth with Defeat animation
    if self.controllingGiantess.animation then
        self.controllingGiantess.animation.Set("Defeat")
    end

    -- Set growth parameters with MUCH FASTER increasing multiplier
    self.refleshSystem.currentRate = self.refleshSystem.crushGrowthRate * self.refleshSystem.crushMultiplier
    self.refleshSystem.stepSize = self.refleshSystem.crushGrowthRate * 0.3 * self.refleshSystem.crushMultiplier
    self.refleshSystem.stepInterval = 0.05
    self.refleshSystem.duration = self.refleshSystem.growthDuration

    -- Start growth
    self.refleshSystem.growing = true
    self.refleshSystem.superGrowthset = true
    self.refleshSystem.walkSet = false
    self.refleshSystem.phi = 0
    self.refleshSystem.status = 0

    -- Play growth sound
    if self.refleshSystem.soundEnabled and self.refleshSystem.audio_source and self.growthSounds then
        local soundIndex = math.random(1, #self.growthSounds)
        self.refleshSystem.audio_source.clip = self.growthSounds[soundIndex]
        self.refleshSystem.audio_source:Play()
    end

    -- Show crush message
    if self.refleshSystem.modeToast then
        self.refleshSystem.modeToast.Print("MICRO CRUSHED #" .. self.refleshSystem.crushCount .. ": GROWTH x" .. string.format("%.1f", self.refleshSystem.crushMultiplier) .. "!")
    end

    -- NEW: Growth toast for visual feedback
    Game.Toast.New().Print("💥 CRUSHED MICRO! Growing bigger... (" .. string.format("%.1fx", self.refleshSystem.crushMultiplier) .. ")")

    print("DEBUG: Started growth from crush - multiplier: " .. self.refleshSystem.crushMultiplier)

    -- BONE FIX: Restore original bone positions after successful crush
    if self.refleshSystem.boneFixActive then
        self:RestoreBoneFix()
    end

    -- STUCK PREVENTION: Clear current target since it was crushed
    self.refleshSystem.currentTarget = nil
    self.refleshSystem.targetStartTime = nil

    -- PERSISTENT: Continue hunting after growth
    if self.refleshSystem.persistentHunting then
        local currentTime = 0
        if Time and Time.time then
            currentTime = Time.time
        end
        self.refleshSystem.lastHuntTime = currentTime
    end
end

-- Helper function to find bones (from other Sizebox scripts)
function FindBone(entity, boneName)
    if not entity or not entity.bones then
        return nil
    end

    -- Try GetBoneByName first
    local bone = nil
    pcall(function()
        bone = entity.bones.GetBoneByName(boneName)
    end)

    if bone then
        return bone
    end

    -- Try GetBonesByName and get first result
    pcall(function()
        local bones = entity.bones.GetBonesByName(boneName, true)
        if bones and #bones > 0 then
            bone = bones[1]
        end
    end)

    return bone
end

-- NEW: Bone manipulation to fix foot-not-reaching-ground issue
function AICompanion:ApplyBoneFix()
    if not self.refleshSystem.boneFixEnabled or not self.controllingGiantess then
        return false
    end

    -- Calculate bone adjustment based on giantess size
    local giantessScale = self.controllingGiantess.scale or 1.0
    local baseAdjustment = 0.5 -- Base adjustment amount (increased)
    self.refleshSystem.boneFixAmount = baseAdjustment * giantessScale

    print("DEBUG: Applying bone fix - scale: " .. string.format("%.2f", giantessScale) .. ", adjustment: " .. string.format("%.2f", self.refleshSystem.boneFixAmount))

    -- Store original positions and apply fix
    local success = false

    -- Try to get bone system using proper Sizebox bone access
    if self.controllingGiantess.bones then
        local bones = self.controllingGiantess.bones

        -- SIMPLIFIED: Only move the HIPS bone (most effective for foot reach)
        pcall(function()
            local hipBone = bones.hips
            if hipBone then
                -- Store original local position
                if not self.refleshSystem.originalBonePositions["hips"] then
                    self.refleshSystem.originalBonePositions["hips"] = {
                        x = hipBone.localPosition.x,
                        y = hipBone.localPosition.y,
                        z = hipBone.localPosition.z
                    }
                    print("DEBUG: Stored original hip position")
                end

                -- Apply downward adjustment to hips only
                local newLocalPos = Vector3.new(
                    hipBone.localPosition.x,
                    hipBone.localPosition.y - self.refleshSystem.boneFixAmount,
                    hipBone.localPosition.z
                )
                hipBone.localPosition = newLocalPos

                print("DEBUG: Lowered HIPS by " .. string.format("%.2f", self.refleshSystem.boneFixAmount))
                success = true
            else
                print("DEBUG: Hip bone not found via bones.hips")
            end
        end)

        -- Fallback: Try FindBone for hip joint
        if not success then
            pcall(function()
                local hipBone = FindBone(self.controllingGiantess, "131.JOINT_LEFTHIP") or FindBone(self.controllingGiantess, "127.JOINT_RIGHTHIP")
                if hipBone then
                    -- Store original local position
                    if not self.refleshSystem.originalBonePositions["hip_joint"] then
                        self.refleshSystem.originalBonePositions["hip_joint"] = {
                            x = hipBone.localPosition.x,
                            y = hipBone.localPosition.y,
                            z = hipBone.localPosition.z
                        }
                        print("DEBUG: Stored original hip joint position")
                    end

                    -- Apply downward adjustment
                    local newLocalPos = Vector3.new(
                        hipBone.localPosition.x,
                        hipBone.localPosition.y - self.refleshSystem.boneFixAmount,
                        hipBone.localPosition.z
                    )
                    hipBone.localPosition = newLocalPos

                    print("DEBUG: Lowered HIP JOINT by " .. string.format("%.2f", self.refleshSystem.boneFixAmount))
                    success = true
                else
                    print("DEBUG: Hip joint not found via FindBone")
                end
            end)
        end
    end

    if success then
        self.refleshSystem.boneFixActive = true
        print("DEBUG: ✓ Bone fix applied successfully")

        if self.refleshSystem.modeToast then
            self.refleshSystem.modeToast.Print("BONE FIX: Lowered bones for better reach")
        end
    else
        print("DEBUG: ✗ Failed to apply bone fix - no bones found")
    end

    return success
end

-- NEW: Restore original bone positions
function AICompanion:RestoreBoneFix()
    if not self.refleshSystem.boneFixActive or not self.controllingGiantess then
        return
    end

    print("DEBUG: Restoring original bone positions")

    if self.controllingGiantess.bones then
        local bones = self.controllingGiantess.bones

        -- SIMPLIFIED: Only restore the hip bone
        for boneKey, originalPos in pairs(self.refleshSystem.originalBonePositions) do
            pcall(function()
                if boneKey == "hips" then
                    local hipBone = bones.hips
                    if hipBone then
                        hipBone.localPosition = Vector3.new(
                            originalPos.x,
                            originalPos.y,
                            originalPos.z
                        )
                        print("DEBUG: Restored HIPS to original position")
                    end
                elseif boneKey == "hip_joint" then
                    local hipBone = FindBone(self.controllingGiantess, "131.JOINT_LEFTHIP") or FindBone(self.controllingGiantess, "127.JOINT_RIGHTHIP")
                    if hipBone then
                        hipBone.localPosition = Vector3.new(
                            originalPos.x,
                            originalPos.y,
                            originalPos.z
                        )
                        print("DEBUG: Restored HIP JOINT to original position")
                    end
                end
            end)
        end
    end

    self.refleshSystem.boneFixActive = false
    self.refleshSystem.originalBonePositions = {}

    print("DEBUG: ✓ Bone fix restored")

    if self.refleshSystem.modeToast then
        self.refleshSystem.modeToast.Print("BONE FIX: Restored to normal")
    end
end

-- NEW: Update idle chat system
function AICompanion:UpdateIdleChat()
    if not self.idleChatSystem.enabled then
        return
    end

    -- SMART MODE CHECK: Only talk when we're in an active mode or doing something
    local inActiveMode = false

    -- Check if we're in any active modes
    if self.refleshSystem and self.refleshSystem.persistentHunting then
        inActiveMode = true -- Reflesh mode
    elseif self.sizeSystem and (self.sizeSystem.isGrowing or self.sizeSystem.isShrinking) then
        inActiveMode = true -- Size changing
    elseif self.beSystem and self.beSystem.cycling then
        inActiveMode = true -- BE cycling
    elseif self.surpriseSystem and self.surpriseSystem.isSpamming then
        inActiveMode = true -- Surprise spam mode
    elseif self.currentPersonality and self.currentPersonality ~= "default" then
        inActiveMode = true -- Special personality mode
    end

    -- Don't send idle messages unless we're in an active mode
    if not inActiveMode then
        return
    end

    local currentTime = 0
    if Time and Time.time then
        currentTime = Time.time
    elseif os and os.time then
        currentTime = os.time()
    end

    -- Check if user has been idle for too long
    local timeSinceLastChat = currentTime - self.idleChatSystem.lastChatTime
    local timeSinceLastIdle = currentTime - self.idleChatSystem.lastIdleChatTime

    if timeSinceLastChat >= self.idleChatSystem.idleTimeout and
       timeSinceLastIdle >= self.idleChatSystem.idleChatCooldown then

        -- SMART MESSAGE SELECTION: Avoid repeats
        local availableMessages = {}
        local allMessages = self.idleChatSystem.idleMessages

        -- First, try to find unused messages
        for i, message in ipairs(allMessages) do
            if not self.idleChatSystem.usedMessages[i] then
                table.insert(availableMessages, {index = i, message = message})
            end
        end

        -- If all messages have been used, reset and start over
        if #availableMessages == 0 then
            self.idleChatSystem.usedMessages = {}
            self.idleChatSystem.currentCycle = self.idleChatSystem.currentCycle + 1
            print("DEBUG: Idle chat - completed cycle " .. self.idleChatSystem.currentCycle .. ", resetting message pool")

            -- Add all messages back to available pool
            for i, message in ipairs(allMessages) do
                table.insert(availableMessages, {index = i, message = message})
            end
        end

        -- Select random message from available pool
        local selectedMsg = availableMessages[math.random(#availableMessages)]
        local messageText = selectedMsg.message

        -- Mark this message as used
        self.idleChatSystem.usedMessages[selectedMsg.index] = true

        -- Show as toast and chat message
        Game.Toast.New().Print("💭 " .. messageText)
        self:AddChatMessage(self.characterName, messageText)

        -- Update last idle chat time
        self.idleChatSystem.lastIdleChatTime = currentTime

        print("DEBUG: Sent idle chat message after " .. string.format("%.1f", timeSinceLastChat) .. " seconds of silence (cycle " .. self.idleChatSystem.currentCycle .. ", " .. (#allMessages - #availableMessages + 1) .. "/" .. #allMessages .. " used)")
    end
end

-- NEW: Update persistent hunting system
function AICompanion:UpdatePersistentHunting()
    if not self.refleshSystem.persistentHunting then
        return
    end

    -- SAFETY: Check if Time exists
    local currentTime = 0
    if Time and Time.time then
        currentTime = Time.time
    end

    -- Check if it's time to hunt again
    if currentTime - self.refleshSystem.lastHuntTime < self.refleshSystem.huntCooldown then
        return
    end

    -- Check if giantess is busy (growing, in animation, etc.)
    if self.refleshSystem.growing then
        return -- Don't interrupt growth
    end

    -- SAFETY: Check if AI methods exist before calling
    local isActionActive = false
    if self.controllingGiantess and self.controllingGiantess.ai then
        if self.controllingGiantess.ai.IsActionActive then
            local success, result = pcall(function()
                return self.controllingGiantess.ai.IsActionActive()
            end)
            if success then
                isActionActive = result
            end
        end
    end

    if isActionActive then
        return -- Don't interrupt current action
    end

    -- FOOT-NOT-REACHING FIX: Force crush micros when stomping fails
    if self.refleshSystem.currentTarget and self.refleshSystem.targetStartTime then
        local stuckTime = currentTime - self.refleshSystem.targetStartTime
        local timeout = self.refleshSystem.stuckTimeout or 4.0

        if stuckTime > timeout then
            local stuckTarget = self.refleshSystem.currentTarget
            print("DEBUG: Stomping failed for " .. string.format("%.1f", stuckTime) .. " seconds - applying bone fix")

            -- BONE FIX: Apply bone fix ONLY when stuck (not every hunt)
            if self.refleshSystem.boneFixEnabled and not self.refleshSystem.boneFixActive then
                print("DEBUG: Applying bone fix to help reach stuck target")

                if self:ApplyBoneFix() then
                    print("DEBUG: Applied bone fix, giving stomp another chance")

                    -- Reset timer to give it another chance with bone fix
                    self.refleshSystem.targetStartTime = currentTime
                    self.refleshSystem.stuckTimeout = 3.0 -- Shorter timeout with bone fix

                    if self.refleshSystem.modeToast then
                        self.refleshSystem.modeToast.Print("BONE FIX: Lowered hips to reach target...")
                    end

                    return -- Give it another chance with bone fix
                else
                    print("DEBUG: Bone fix failed to apply")
                end
            elseif self.refleshSystem.boneFixActive then
                print("DEBUG: Bone fix already active, target still unreachable - skipping")

                -- Restore bones since this target is unreachable even with bone fix
                self:RestoreBoneFix()

                if self.refleshSystem.modeToast then
                    self.refleshSystem.modeToast.Print("SKIPPING: Target unreachable even with bone fix")
                end
            else
                print("DEBUG: Bone fix disabled, skipping stuck target")

                if self.refleshSystem.modeToast then
                    self.refleshSystem.modeToast.Print("SKIPPING: Target unreachable (bone fix disabled)")
                end
            end

            -- Stop current AI action
            if self.controllingGiantess.ai and self.controllingGiantess.ai.StopAction then
                pcall(function()
                    self.controllingGiantess.ai.StopAction()
                end)
            end

            -- Set idle animation
            if self.controllingGiantess.animation then
                pcall(function()
                    self.controllingGiantess.animation.Set("Idle")
                end)
            end

            -- ALWAYS restore bones when clearing target
            if self.refleshSystem.boneFixActive then
                self:RestoreBoneFix()
            end

            -- Clear target and continue hunting
            self.refleshSystem.currentTarget = nil
            self.refleshSystem.targetStartTime = nil
            self.refleshSystem.lastHuntTime = currentTime - self.refleshSystem.huntCooldown

            return
        end
    end

    -- Try to spawn more micros if needed
    if self.refleshSystem.autoSpawnEnabled then
        pcall(function()
            self:TrySpawnMicros()
        end)
    end

    -- Try to find and hunt micros
    local foundTarget = false
    pcall(function()
        foundTarget = self:FindAndTargetMicro()
    end)

    if foundTarget then
        print("DEBUG: Persistent hunting - found new target")
        self.refleshSystem.lastHuntTime = currentTime
    else
        -- No targets found, try again very soon to catch newly spawned micros
        self.refleshSystem.lastHuntTime = currentTime - (self.refleshSystem.huntCooldown * 0.8) -- Only wait 20% of cooldown

        if self.refleshSystem.autoSpawnEnabled then
            print("DEBUG: Persistent hunting - no targets, will try spawning and hunting again soon")
        else
            print("DEBUG: Persistent hunting - no targets found, will check again soon for new spawns")
        end
    end
end

function AICompanion:ExecuteCommands(input)
    local lowerInput = string.lower(input)
    
    -- REMOVED: This duplicate surprise handler was causing conflicts
    -- The main surprise system in ProcessInput handles all surprise commands properly

    -- NEW: Handle reflesh commands
    if self:CheckRefleshCommands(lowerInput) then
        return
    end

    -- FALLBACK: Simple reflesh detection in case pattern matching fails
    if string.find(lowerInput, "reflesh") or string.find(lowerInput, "masturbat") then
        print("DEBUG: Fallback reflesh detection triggered")

        -- Simple fallback - just trigger basic reflesh
        if self.controllingGiantess and self.controllingGiantess.animation then
            self.controllingGiantess.animation.Set("Masturbation 1")
            print("DEBUG: Fallback - Set Masturbation 1 animation directly")

            -- Start basic growth
            if self.refleshSystem then
                self.refleshSystem.phi = 0
                self.refleshSystem.growing = true
                self.refleshSystem.currentRate = 0.12
                self.refleshSystem.duration = 3.0
                self.refleshSystem.growthMode = "linear"
                print("DEBUG: Fallback - Started basic reflesh growth")
            end
        end

        self:AddChatMessage(self.characterName, "Time for some reflesh! *starts masturbating and growing*")
        return
    end

    -- NEW: Debug command to list animations
    if string.find(lowerInput, "list animations") or string.find(lowerInput, "show animations") then
        self:DebugListAnimations()
        self:AddChatMessage(self.characterName, "I'm checking what animations I have available! Check the console for the list.")
        return
    end

    -- NEW: City spawning command
    if string.find(lowerInput, "spawn city") or string.find(lowerInput, "spawn a city") or string.find(lowerInput, "create city") then
        print("DEBUG: City spawn command detected - Input: " .. lowerInput)

        -- Parse parameters from the command
        local populationModifier = tonumber(string.match(lowerInput, "population ([%d%.]+)")) or 1.0
        local cityRadius = tonumber(string.match(lowerInput, "radius ([%d%.]+)")) or 1.0
        local skyscraperHeight = tonumber(string.match(lowerInput, "height ([%d%.]+)")) or 1.0
        local openSpaceChance = tonumber(string.match(lowerInput, "space ([%d%.]+)")) or 0.5
        local populate = not string.find(lowerInput, "no people") and not string.find(lowerInput, "empty")
        local randomSeed = tonumber(string.match(lowerInput, "seed (%d+)")) or math.random(1, 999999)

        -- Try to spawn the city
        local success = self:SpawnCity(populationModifier, cityRadius, skyscraperHeight, openSpaceChance, populate, randomSeed)

        if success then
            local responseMessages = {
                "A magnificent city rises before us! *watches in awe* Look at all those buildings and tiny people!",
                "There we go! *gestures proudly* A whole city just for us to explore! I can see the little inhabitants scurrying around!",
                "Perfect! *claps hands excitedly* Now we have a proper city to play with! I wonder how it feels to be a giantess among all those buildings...",
                "Amazing! *towers over the new city* Look at this urban landscape! All those tiny structures and people... they look so vulnerable!"
            }
            self:AddChatMessage(self.characterName, responseMessages[math.random(#responseMessages)])
        else
            local failureMessages = {
                "Hmm, I'm having trouble spawning a city right now... *looks confused* Maybe the CITY object isn't available?",
                "Oh no! *frowns* I can't seem to create a city at the moment. The spawning system might not have access to city objects.",
                "That's strange... *scratches head* I tried to spawn a city but something went wrong. Check the console for details!"
            }
            self:AddChatMessage(self.characterName, failureMessages[math.random(#failureMessages)])
        end
        return
    end

    -- NEW: Manual animation test command
    if string.find(lowerInput, "test animation") then
        local animName = string.match(lowerInput, "test animation ([%w%s]+)")
        if animName then
            animName = string.gsub(animName, "^%s*(.-)%s*$", "%1") -- trim whitespace
            if self.controllingGiantess and self.controllingGiantess.animation then
                local success = pcall(function()
                    self.controllingGiantess.animation.Set(animName)
                end)
                if success then
                    self:AddChatMessage(self.characterName, "Testing animation: " .. animName .. "! *performs the animation*")
                else
                    self:AddChatMessage(self.characterName, "Sorry, I don't have an animation called '" .. animName .. "'. Try 'list animations' to see what I have!")
                end
            end
        else
            self:AddChatMessage(self.characterName, "Tell me which animation to test! Say 'test animation [name]' - for example: 'test animation Masturbation 1'")
        end
        return
    end

    -- NEW: Manual hunt test command
    if string.find(lowerInput, "test hunt") or string.find(lowerInput, "hunt now") then
        print("DEBUG: Manual hunt test triggered")
        self:AddChatMessage(self.characterName, "Testing micro hunting! Let me look for targets... *scans area*")

        local foundTarget = self:FindAndTargetMicro()
        if foundTarget then
            self:AddChatMessage(self.characterName, "Found a target! Starting the hunt! *begins stalking*")
        else
            self:AddChatMessage(self.characterName, "No valid targets found. Make sure there are some micros nearby that aren't you!")
        end
        return
    end

    -- NEW: Force hunt command (no protection)
    if string.find(lowerInput, "force hunt") or string.find(lowerInput, "hunt all") then
        print("DEBUG: Force hunt triggered - no protection")
        self:AddChatMessage(self.characterName, "Force hunting mode! I'll target the first micro I find! *looks around menacingly*")

        if self.controllingGiantess and self.controllingGiantess.senses then
            local allMicros = self.controllingGiantess.senses.GetMicrosInRadius(15)
            if allMicros and #allMicros > 0 then
                local targetMicro = allMicros[1]
                print("DEBUG: Force targeting first micro: " .. (targetMicro.name or "unnamed"))

                if self.controllingGiantess.ai and self.controllingGiantess.ai.SetBehavior then
                    local success = pcall(function()
                        self.controllingGiantess.ai.SetBehavior("StompSingle", targetMicro)
                    end)

                    if success then
                        self:AddChatMessage(self.characterName, "Force hunting " .. (targetMicro.name or "micro") .. "! *starts stalking aggressively*")
                    else
                        self:AddChatMessage(self.characterName, "Failed to start hunting behavior!")
                    end
                else
                    self:AddChatMessage(self.characterName, "No AI system available for hunting!")
                end
            else
                self:AddChatMessage(self.characterName, "No micros found to hunt!")
            end
        end
        return
    end

    -- NEW: Adjust minimum micro size for hunting
    if string.find(lowerInput, "min size") or string.find(lowerInput, "minimum size") then
        local newSize = string.match(lowerInput, "min[imum]*%s+size%s+([%d%.]+)")
        if newSize then
            newSize = tonumber(newSize)
            if newSize and newSize >= 0.1 and newSize <= 5.0 then
                self.refleshSystem.minMicroSize = newSize
                self:AddChatMessage(self.characterName, "Minimum micro hunting size set to " .. newSize .. "! I'll only hunt micros this size or bigger.")
                print("DEBUG: Minimum micro size set to: " .. newSize)
            else
                self:AddChatMessage(self.characterName, "Invalid size! Please use a number between 0.1 and 5.0")
            end
        else
            self:AddChatMessage(self.characterName, "Current minimum micro hunting size: " .. self.refleshSystem.minMicroSize .. ". Say 'min size [number]' to change it.")
        end
        return
    end

    -- NEW: Adjust crush growth rate
    if string.find(lowerInput, "growth rate") or string.find(lowerInput, "crush rate") then
        local newRate = string.match(lowerInput, "growth%s+rate%s+([%d%.]+)") or string.match(lowerInput, "crush%s+rate%s+([%d%.]+)")
        if newRate then
            newRate = tonumber(newRate)
            if newRate and newRate >= 0.05 and newRate <= 1.0 then
                self.refleshSystem.crushGrowthRate = newRate
                self:AddChatMessage(self.characterName, "Crush growth rate set to " .. newRate .. "! Each crush will give " .. (newRate * 100) .. "% growth.")
                print("DEBUG: Crush growth rate set to: " .. newRate)
            else
                self:AddChatMessage(self.characterName, "Invalid rate! Please use a number between 0.05 and 1.0")
            end
        else
            self:AddChatMessage(self.characterName, "Current crush growth rate: " .. self.refleshSystem.crushGrowthRate .. " (" .. (self.refleshSystem.crushGrowthRate * 100) .. "%). Say 'growth rate [number]' to change it.")
        end
        return
    end

    -- NEW: Test city spawning command (for debugging)
    if string.find(lowerInput, "test city") then
        print("DEBUG: Test city command detected")

        -- Spawn a test city with default parameters
        local success = self:SpawnCity(1.0, 1.0, 1.0, 0.5, true, math.random(1, 999999))

        if success then
            self:AddChatMessage(self.characterName, "Test city spawned! Check near my feet! 🏙️ *points down*")
        else
            self:AddChatMessage(self.characterName, "Failed to spawn test city - check console for details")
        end
        return
    end

    -- NEW: Toggle bone fix mode
    if string.find(lowerInput, "bone fix") then
        if string.find(lowerInput, "enable") or string.find(lowerInput, "on") then
            self.refleshSystem.boneFixEnabled = true
            self:AddChatMessage(self.characterName, "Bone fix enabled! I'll adjust my bones to reach micros better.")
        elseif string.find(lowerInput, "disable") or string.find(lowerInput, "off") then
            self.refleshSystem.boneFixEnabled = false
            -- Restore bones if currently active
            if self.refleshSystem.boneFixActive then
                self:RestoreBoneFix()
            end
            self:AddChatMessage(self.characterName, "Bone fix disabled! I'll use normal bone positions.")
        else
            -- Toggle
            self.refleshSystem.boneFixEnabled = not self.refleshSystem.boneFixEnabled
            local status = self.refleshSystem.boneFixEnabled and "enabled" or "disabled"

            if not self.refleshSystem.boneFixEnabled and self.refleshSystem.boneFixActive then
                self:RestoreBoneFix()
            end

            self:AddChatMessage(self.characterName, "Bone fix " .. status .. "! " .. (self.refleshSystem.boneFixEnabled and "I'll adjust my bones to reach micros better." or "I'll use normal bone positions."))
        end
        print("DEBUG: Bone fix mode: " .. tostring(self.refleshSystem.boneFixEnabled))
        return
    end

    -- NEW: Manual bone restore command
    if string.find(lowerInput, "restore bones") or string.find(lowerInput, "reset bones") then
        if self.refleshSystem.boneFixActive then
            self:RestoreBoneFix()
            self:AddChatMessage(self.characterName, "Bones restored to normal positions! *adjusts posture*")
        else
            self:AddChatMessage(self.characterName, "My bones are already in normal positions!")
        end
        return
    end

    -- NEW: Test bone manipulation command
    if string.find(lowerInput, "test bones") then
        self:AddChatMessage(self.characterName, "Testing bone manipulation! *adjusts bones*")

        if self.refleshSystem.boneFixActive then
            self:RestoreBoneFix()
            self:AddChatMessage(self.characterName, "Restored bones to normal.")
        else
            local success = self:ApplyBoneFix()
            if success then
                self:AddChatMessage(self.characterName, "Applied bone fix! You should see my posture change.")
            else
                self:AddChatMessage(self.characterName, "Failed to apply bone fix. Check debug console for details.")
            end
        end
        return
    end

    -- NEW: Test surprise system command
    if string.find(lowerInput, "test surprise") or string.find(lowerInput, "debug surprise") then
        self:AddChatMessage(self.characterName, "Testing surprise system! *prepares surprise*")

        -- Show debug info
        print("DEBUG: *** SURPRISE SYSTEM TEST ***")
        print("DEBUG: controllingGiantess = " .. tostring(self.controllingGiantess))
        print("DEBUG: sizingCharacter = " .. tostring(self.sizingCharacter))
        print("DEBUG: agent = " .. tostring(self.agent))

        local result = self:DoSurprise()
        self:AddChatMessage(self.characterName, result or "Test surprise failed!")
        return
    end

    -- NEW: Idle chat system commands
    if string.find(lowerInput, "idle chat") then
        if string.find(lowerInput, "test") then
            -- Force trigger an idle message for testing
            local availableMessages = {}
            local allMessages = self.idleChatSystem.idleMessages

            -- Get unused messages
            for i, message in ipairs(allMessages) do
                if not self.idleChatSystem.usedMessages[i] then
                    table.insert(availableMessages, {index = i, message = message})
                end
            end

            if #availableMessages == 0 then
                self.idleChatSystem.usedMessages = {}
                for i, message in ipairs(allMessages) do
                    table.insert(availableMessages, {index = i, message = message})
                end
            end

            local selectedMsg = availableMessages[math.random(#availableMessages)]
            self.idleChatSystem.usedMessages[selectedMsg.index] = true

            Game.Toast.New().Print("💭 " .. selectedMsg.message)
            self:AddChatMessage(self.characterName, selectedMsg.message)

        elseif string.find(lowerInput, "enable") or string.find(lowerInput, "on") then
            self.idleChatSystem.enabled = true
            self:AddChatMessage(self.characterName, "Idle chat enabled! I'll talk to you if you're quiet for 1.5 minutes while we're doing something! *smiles*")
        elseif string.find(lowerInput, "disable") or string.find(lowerInput, "off") then
            self.idleChatSystem.enabled = false
            self:AddChatMessage(self.characterName, "Idle chat disabled! I'll wait patiently for you to talk to me. *nods*")
        else
            -- Toggle
            self.idleChatSystem.enabled = not self.idleChatSystem.enabled
            local status = self.idleChatSystem.enabled and "enabled" or "disabled"
            self:AddChatMessage(self.characterName, "Idle chat " .. status .. "! " .. (self.idleChatSystem.enabled and "I'll check on you if you're quiet during activities!" or "I'll stay quiet until you talk!"))
        end
        return
    end

    -- ENHANCED: Check for body part commands and BLOCK general size commands (added more keywords)
    local hasBodyPart = string.find(lowerInput, "chest") or string.find(lowerInput, "breast") or string.find(lowerInput, "boob") or
                       string.find(lowerInput, "oppai") or string.find(lowerInput, "bust") or string.find(lowerInput, "tit") or
                       string.find(lowerInput, "hip") or string.find(lowerInput, "butt") or string.find(lowerInput, "ass") or string.find(lowerInput, "rear")

    -- Determine target: check if user wants to affect themselves
    local affectMicro = string.find(lowerInput, "make me") or
                       string.find(lowerInput, "change me") or
                       string.find(lowerInput, "grow me") or
                       string.find(lowerInput, "shrink me") or
                       string.find(lowerInput, "move me")

    local targetCharacter = affectMicro and self.agent or self.controllingGiantess

    -- NEW: Teleportation commands - "tp me onto your chest" etc.
    if (string.find(lowerInput, "tp") or string.find(lowerInput, "teleport") or string.find(lowerInput, "put me") or
        string.find(lowerInput, "place me") or string.find(lowerInput, "move me")) and
       (string.find(lowerInput, "onto") or string.find(lowerInput, "on") or string.find(lowerInput, "to") or
        string.find(lowerInput, "your")) then

        print("DEBUG: TELEPORT COMMAND DETECTED - Input: " .. lowerInput)

        -- Determine target body part
        local bodyPart = nil
        local offsetY = 0  -- Height offset above the body part

        if string.find(lowerInput, "chest") or string.find(lowerInput, "breast") or string.find(lowerInput, "boob") or
           string.find(lowerInput, "oppai") or string.find(lowerInput, "bust") then
            bodyPart = "chest"
            offsetY = 2.0  -- Stand on top of chest
        elseif string.find(lowerInput, "head") or string.find(lowerInput, "face") or string.find(lowerInput, "forehead") then
            bodyPart = "head"
            offsetY = 3.0  -- Stand on top of head
        elseif string.find(lowerInput, "shoulder") then
            bodyPart = "shoulder"
            offsetY = 1.0  -- Stand on shoulder
        elseif string.find(lowerInput, "hand") or string.find(lowerInput, "palm") or string.find(lowerInput, "finger") then
            bodyPart = "hand"
            offsetY = 0.5  -- Stand on hand/palm
        elseif string.find(lowerInput, "foot") or string.find(lowerInput, "feet") or string.find(lowerInput, "toe") then
            bodyPart = "foot"
            offsetY = 0.5  -- Stand on foot
        elseif string.find(lowerInput, "back") or string.find(lowerInput, "spine") then
            bodyPart = "back"
            offsetY = 1.0  -- Stand on back
        elseif string.find(lowerInput, "stomach") or string.find(lowerInput, "belly") or string.find(lowerInput, "tummy") then
            bodyPart = "stomach"
            offsetY = 1.0  -- Stand on stomach
        elseif string.find(lowerInput, "thigh") or string.find(lowerInput, "leg") then
            bodyPart = "thigh"
            offsetY = 1.0  -- Stand on thigh
        elseif string.find(lowerInput, "hip") or string.find(lowerInput, "waist") then
            bodyPart = "hip"
            offsetY = 1.0  -- Stand on hip/waist
        end

        if bodyPart and self.controllingGiantess then
            local success = self:TeleportToBodyPart(bodyPart, offsetY)
            if success then
                local teleportResponses = {
                    "*gently places you on my " .. bodyPart .. "* There you go! Comfortable up there?",
                    "*carefully lifts you onto my " .. bodyPart .. "* How's the view from there?",
                    "*teleports you to my " .. bodyPart .. "* Perfect! You're right where you wanted to be!",
                    "*places you gently on my " .. bodyPart .. "* There! Much better positioning!"
                }
                return teleportResponses[math.random(#teleportResponses)]
            else
                return "I tried to put you on my " .. bodyPart .. ", but something went wrong! *looks confused*"
            end
        else
            return "I'm not sure where you want me to put you! Try saying 'tp me onto your chest' or 'put me on your head'!"
        end
    end

    -- Movement commands
    if string.find(lowerInput, "move") or string.find(lowerInput, "walk") or string.find(lowerInput, "go") then
        if string.find(lowerInput, "forward") then
            self:StartMovement("forward", targetCharacter)
        elseif string.find(lowerInput, "back") then
            self:StartMovement("back", targetCharacter)
        elseif string.find(lowerInput, "left") then
            self:StartMovement("left", targetCharacter)
        elseif string.find(lowerInput, "right") then
            self:StartMovement("right", targetCharacter)
        end
    end

    -- NEW: Simple micro lock toggle command
    if string.find(lowerInput, "lock") and string.find(lowerInput, "micro") then
        if string.find(lowerInput, "on") or string.find(lowerInput, "enable") then
            self.microLockEnabled = true
            self:UpdateMicroLock() -- Set the lock
            Game.Toast.New().Print("🔒 Micro lock ENABLED")
            return "Micro lock enabled! Your size is now protected from changes."
        elseif string.find(lowerInput, "off") or string.find(lowerInput, "disable") then
            self.microLockEnabled = false
            Game.Toast.New().Print("🔓 Micro lock DISABLED")
            return "Micro lock disabled! Your size can change freely now."
        else
            local status = self.microLockEnabled and "ENABLED" or "DISABLED"
            return "Micro lock is currently " .. status .. ". Say 'lock micro on' or 'lock micro off'."
        end
    end



    -- NEW: "Face me" command - rotates whole body to face micro
    if string.find(lowerInput, "face me") then
        print("DEBUG: FACE ME DETECTED - Input: " .. lowerInput)
        Game.Toast.New().Print("DEBUG: Face me command detected!")

        if self.controllingGiantess then
            -- Find the micro/player
            local microPos = nil
            if self.microCharacter and self.microCharacter.transform then
                microPos = self.microCharacter.transform.position
            elseif self.agent and self.agent.transform and self.agent ~= self.controllingGiantess then
                microPos = self.agent.transform.position
            elseif Entity and Entity.GetPlayerCharacter then
                local player = Entity.GetPlayerCharacter()
                if player and player.transform then
                    microPos = player.transform.position
                end
            end

            if microPos then
                -- FIXED: Use EXACT same rotation code as "come here" command
                local giantessPos = self.controllingGiantess.transform.position

                -- Calculate direction from giantess to micro (same as "come here")
                local direction = microPos - giantessPos
                direction.y = 0 -- Keep movement horizontal

                -- Check if direction is valid (same as "come here")
                local distance = math.sqrt(direction.x * direction.x + direction.z * direction.z)
                if distance > 0.1 then
                    direction = direction / distance -- Normalize manually (same as "come here")

                    -- RESTORED: Toggleable persistent rotation tracking
                    self.facingTarget = {
                        active = true,
                        targetPos = microPos,
                        lastUpdateTime = os.time()
                    }

                    -- Apply rotation immediately
                    local lookDirection = (microPos - giantessPos).normalized
                    lookDirection.y = 0 -- Keep rotation horizontal
                    if lookDirection.magnitude > 0.1 then
                        self.controllingGiantess.transform.rotation = Quaternion.LookRotation(lookDirection)
                        print("DEBUG: FACE ME - Set up persistent rotation tracking")
                        Game.Toast.New().Print("DEBUG: Face me - Persistent rotation activated")
                    end

                    local faceResponses = {
                        "*turns to face you* There! Now I'm looking right at you! *smiles*",
                        "*rotates to face your direction* Better? Now I can see you properly! *grins*",
                        "*spins around to face you* Perfect! Now we're face to face! *winks*",
                        "*turns toward you* Much better! I like looking at you! *giggles*"
                    }
                    return faceResponses[math.random(#faceResponses)]
                else
                    return "I'm already facing you! *looks directly at you*"
                end
            else
                return "I want to face you, but I can't find where you are!"
            end
        else
            return "I'd love to face you, but I can't move right now!"
        end
    end

    -- NEW: "Stop looking" / "Stop facing" / "Reset head" commands
    if string.find(lowerInput, "stop looking") or string.find(lowerInput, "look away") or
       string.find(lowerInput, "stop staring") or string.find(lowerInput, "stop facing") or
       string.find(lowerInput, "face away") or string.find(lowerInput, "stop looking at me") or
       string.find(lowerInput, "reset head") or string.find(lowerInput, "head normal") then
        print("DEBUG: STOP TRACKING/RESET HEAD DETECTED - Input: " .. lowerInput)
        Game.Toast.New().Print("DEBUG: Stop tracking/reset head command detected!")

        -- Reset head to original rotation if we have it stored
        if self._headInitialRotation then
            -- Helper function to find bone (same as before)
            local function FindBone(entity, boneName)
                if entity.bones and entity.bones.GetBonesByName then
                    local bones = entity.bones.GetBonesByName(boneName, true)
                    if bones and bones[1] then
                        return bones[1]
                    end
                end
                return nil
            end

            -- Find head bone and reset it - prioritize 47.JOINT_HEAD first
            local boneNames = {"47.JOINT_HEAD", "16.JOINT_HEAD", "Head", "head", "JOINT_HEAD", "Neck", "neck"}
            for _, boneName in ipairs(boneNames) do
                local headBone = FindBone(self.controllingGiantess, boneName)
                if headBone then
                    local success = pcall(function()
                        headBone.localRotation = self._headInitialRotation
                        print("DEBUG: RESET HEAD - Restored original head rotation")
                        Game.Toast.New().Print("DEBUG: Head reset to original position!")
                    end)
                    if success then break end
                end
            end
        end

        -- SIMPLE: Disable head tracking and reset immediately
        if self.headTracking then
            self.headTracking.active = false
            -- Reset head to TRUE original rotation (stored at script start)
            local resetRotation = self._trueOriginalHeadRotation or self.headTracking.initialRotation or self._headInitialRotation
            if self.headTracking.headBone and resetRotation then
                local success = pcall(function()
                    self.headTracking.headBone.localRotation = resetRotation
                    print("DEBUG: Head reset using TRUE original rotation")
                end)
                if success then
                    print("DEBUG: Head tracking disabled and reset to TRUE original rotation")
                else
                    print("DEBUG: Head reset failed")
                end
            else
                print("DEBUG: No head bone or original rotation found for reset")
            end
        end

        -- Disable body tracking
        if self.facingTarget then
            self.facingTarget.active = false
            print("DEBUG: Body tracking disabled")
        end

        local stopResponses = {
            "*stops tracking you* Okay, I'll stop following you with my gaze! *looks away*",
            "*looks away* There! I'm not staring anymore! *giggles*",
            "*turns away* Alright, I'll give you some space! *faces forward*",
            "*stops looking at you* Done! I'm looking elsewhere now! *smiles*",
            "*head returns to normal* Much better! No more tracking! *relaxes*"
        }
        return stopResponses[math.random(#stopResponses)]
    end

    -- NEW: "Look at me" command - rotates head/neck to look down at micro
    if string.find(lowerInput, "look at me") then
        print("DEBUG: LOOK AT ME DETECTED - Input: " .. lowerInput)
        Game.Toast.New().Print("DEBUG: Look at me command detected!")
        print("DEBUG: LOOK AT ME - controllingGiantess exists: " .. tostring(self.controllingGiantess ~= nil))

        if self.controllingGiantess then
            print("DEBUG: LOOK AT ME - controllingGiantess name: " .. (self.controllingGiantess.name or "Unknown"))
            -- Find the micro/player position
            local microPos = nil
            print("DEBUG: LOOK AT ME - Searching for micro position...")
            print("DEBUG: LOOK AT ME - microCharacter exists: " .. tostring(self.microCharacter ~= nil))
            print("DEBUG: LOOK AT ME - agent exists: " .. tostring(self.agent ~= nil))
            print("DEBUG: LOOK AT ME - controllingGiantess: " .. tostring(self.controllingGiantess))

            if self.microCharacter and self.microCharacter.transform then
                microPos = self.microCharacter.transform.position
                print("DEBUG: LOOK AT ME - Found micro position via microCharacter: " .. microPos.x .. ", " .. microPos.y .. ", " .. microPos.z)
            elseif self.agent and self.agent.transform and self.agent ~= self.controllingGiantess then
                microPos = self.agent.transform.position
                print("DEBUG: LOOK AT ME - Found micro position via agent: " .. microPos.x .. ", " .. microPos.y .. ", " .. microPos.z)
            elseif Entity and Entity.GetPlayerCharacter then
                local player = Entity.GetPlayerCharacter()
                if player and player.transform then
                    microPos = player.transform.position
                    print("DEBUG: LOOK AT ME - Found micro position via player: " .. microPos.x .. ", " .. microPos.y .. ", " .. microPos.z)
                end
            end

            if not microPos then
                print("DEBUG: LOOK AT ME - ERROR: Could not find micro position!")
                Game.Toast.New().Print("DEBUG: Could not find your position!")
            end

            if microPos then
                -- FIXED: Use proper bone manipulation like shapechange_grow_fixed.lua
                local headBoneFound = false

                print("DEBUG: LOOK AT ME - Using proper bone manipulation approach")
                Game.Toast.New().Print("DEBUG: Look at me - Using proper bone approach")

                -- Helper function to find bone (EXACT same as shapechange script)
                local function FindBone(entity, boneName)
                    print("DEBUG: FindBone - Trying to find bone: " .. boneName)
                    if entity then
                        print("DEBUG: FindBone - Entity exists: " .. (entity.name or "Unknown"))
                        if entity.bones then
                            print("DEBUG: FindBone - Entity has bones property")
                            if entity.bones.GetBonesByName then
                                print("DEBUG: FindBone - GetBonesByName method exists")
                                local bones = entity.bones.GetBonesByName(boneName, true)
                                if bones then
                                    print("DEBUG: FindBone - GetBonesByName returned: " .. tostring(#bones) .. " bones")
                                    if bones[1] then
                                        print("DEBUG: FindBone - Found bone: " .. boneName)
                                        return bones[1]
                                    else
                                        print("DEBUG: FindBone - No bones in array for: " .. boneName)
                                    end
                                else
                                    print("DEBUG: FindBone - GetBonesByName returned nil for: " .. boneName)
                                end
                            else
                                print("DEBUG: FindBone - GetBonesByName method not available")
                            end
                        else
                            print("DEBUG: FindBone - Entity has no bones property")
                        end
                    else
                        print("DEBUG: FindBone - Entity is nil")
                    end
                    return nil
                end

                -- Try head bone names - prioritize 47.JOINT_HEAD first since you confirmed it exists
                local boneNames = {"47.JOINT_HEAD", "16.JOINT_HEAD", "Head", "head", "JOINT_HEAD", "Neck", "neck", "Bip01 Head", "mixamorig:Head"}
                print("DEBUG: LOOK AT ME - About to search for bones in: " .. (self.controllingGiantess.name or "Unknown"))

                for _, boneName in ipairs(boneNames) do
                    local headBone = FindBone(self.controllingGiantess, boneName)
                    if headBone then
                        print("DEBUG: LOOK AT ME - Found head bone: " .. boneName)
                        Game.Toast.New().Print("DEBUG: Found head bone: " .. boneName)

                        -- FIXED: Calculate actual direction to your character and apply proper head tracking
                        local success = pcall(function()
                            -- Store BOTH initial rotations if not already stored
                            if not self._headInitialRotation then
                                self._headInitialRotation = headBone.localRotation
                                print("DEBUG: LOOK AT ME - Stored initial head rotation")
                            end

                            -- Store TRUE original rotation (only once, before any modifications)
                            if not self._trueOriginalHeadRotation then
                                self._trueOriginalHeadRotation = headBone.localRotation
                                print("DEBUG: LOOK AT ME - Stored TRUE original head rotation (before any changes)")
                            end

                            -- SMART head position calculation (FIXED - same as head_tracking_simple.lua)
                            local giantessPos = self.controllingGiantess.transform.position
                            local giantessScale = self.controllingGiantess.transform.localScale.x or 1.0
                            local headWorldPos

                            -- Try to get actual head bone world position first
                            if headBone and headBone.position then
                                headWorldPos = headBone.position
                                print("DEBUG: LOOK AT ME - Using REAL head bone position")
                            else
                                -- Use REASONABLE scaled offset (not too big!)
                                local reasonableHeadOffset = math.min(giantessScale * 8, 200)  -- Cap at 200 units max
                                headWorldPos = Vector3.new(giantessPos.x, giantessPos.y + reasonableHeadOffset, giantessPos.z)
                                print("DEBUG: LOOK AT ME - Using REASONABLE head offset - scale=" .. giantessScale .. ", offset=" .. reasonableHeadOffset)
                            end

                            local direction = microPos - headWorldPos

                            print("DEBUG: LOOK AT ME - Head position: (" .. headWorldPos.x .. ", " .. headWorldPos.y .. ", " .. headWorldPos.z .. ")")
                            print("DEBUG: LOOK AT ME - Your position: (" .. microPos.x .. ", " .. microPos.y .. ", " .. microPos.z .. ")")
                            print("DEBUG: LOOK AT ME - Direction Y=" .. direction.y .. ", Distance=" .. direction.magnitude)

                            if direction.magnitude > 0.1 then
                                -- IMPROVED: More reliable head tracking with error handling
                                local success = pcall(function()
                                    local lookRotation = Quaternion.LookRotation(direction.normalized)
                                    headBone.localRotation = lookRotation
                                end)

                                if success then
                                    print("DEBUG: LOOK AT ME - Successfully applied head rotation")
                                    Game.Toast.New().Print("✅ Head now looking at you!")
                                    headBoneFound = true
                                else
                                    print("DEBUG: LOOK AT ME - Rotation failed, trying fallback")
                                    local fallbackRotation = Quaternion.Euler(15, 0, 0) * self._headInitialRotation
                                    headBone.localRotation = fallbackRotation
                                    Game.Toast.New().Print("⚠️ Using fallback head rotation")
                                    headBoneFound = true
                                end
                            else
                                print("DEBUG: LOOK AT ME - Direction too small, using fallback rotation")
                                local fallbackRotation = Quaternion.Euler(15, 0, 0) * self._headInitialRotation
                                headBone.localRotation = fallbackRotation
                                Game.Toast.New().Print("⚠️ Applied fallback downward rotation")
                                headBoneFound = true
                            end
                        end)

                        if success and headBoneFound then
                            print("DEBUG: LOOK AT ME - Head rotation successful with bone: " .. boneName)

                            -- ENABLE CONTINUOUS HEAD TRACKING (like head_tracking_simple.lua)
                            self.headTracking = {
                                active = true,
                                headBone = headBone,
                                boneName = boneName,
                                initialRotation = self._headInitialRotation  -- Use the ORIGINAL rotation, not current
                            }
                            print("DEBUG: LOOK AT ME - Continuous head tracking ENABLED!")
                            Game.Toast.New().Print("Head tracking activated! Head will follow you continuously!")
                            break
                        else
                            print("DEBUG: LOOK AT ME - Head rotation failed for bone: " .. boneName)
                        end
                    end
                end

                -- Fallback: Use body rotation if no head bone found
                if not headBoneFound then
                    print("DEBUG: LOOK AT ME - No head bone found, using body rotation fallback")
                    Game.Toast.New().Print("DEBUG: Look at me - No head bone, using body fallback")

                    -- Set up body tracking as fallback
                    self.facingTarget = {
                        active = true,
                        targetPos = microPos,
                        lastUpdateTime = os.time()
                    }

                    -- Apply rotation immediately
                    local giantessPos = self.controllingGiantess.transform.position
                    local direction = microPos - giantessPos
                    direction.y = 0 -- Keep horizontal

                    if direction.magnitude > 0.1 then
                        self.controllingGiantess.transform.rotation = Quaternion.LookRotation(direction.normalized)
                        print("DEBUG: LOOK AT ME - Used body rotation as fallback")
                        headBoneFound = true
                    end
                end

                if headBoneFound then
                    local lookResponses = {
                        "*looks down at you* There you are! *head tracks your movement*",
                        "*turns head to look at you* I see you down there! *eyes follow you*",
                        "*looks down with interest* Found you! *head tilts to watch you*",
                        "*focuses on you* Much better! Now I can see you clearly! *head follows you around*"
                    }
                    return lookResponses[math.random(#lookResponses)]
                else
                    return "I'm trying to look at you, but I can't move my head right now! *looks in your general direction*"
                end
            else
                return "I want to look at you, but I can't find where you are!"
            end
        else
            return "I'd love to look at you, but I can't move right now!"
        end
    end

    -- NEW: "Come here" command using proper Sizebox movement API
    if string.find(lowerInput, "come here") or string.find(lowerInput, "come to me") then
        print("DEBUG: COME HERE DETECTED - Input: " .. lowerInput)

        if self.controllingGiantess then
            print("DEBUG: COME HERE - Have controlling giantess")
            -- Find the micro/player
            local microPos = nil
            if self.microCharacter and self.microCharacter.transform then
                microPos = self.microCharacter.transform.position
                print("DEBUG: COME HERE - Found micro using microCharacter")
            elseif self.agent and self.agent.transform and self.agent ~= self.controllingGiantess then
                microPos = self.agent.transform.position
                print("DEBUG: COME HERE - Found micro using agent")
            elseif Entity and Entity.GetPlayerCharacter then
                local player = Entity.GetPlayerCharacter()
                if player and player.transform then
                    microPos = player.transform.position
                    print("DEBUG: COME HERE - Found micro using player")
                end
            end

            if microPos then
                local giantessPos = self.controllingGiantess.transform.position
                local distance = math.sqrt((microPos.x - giantessPos.x)^2 + (microPos.z - giantessPos.z)^2)

                if distance > 3.0 then
                    -- FIXED: Use proper Sizebox movement API like other working scripts
                    -- This respects the "disable macro movement" setting properly

                    -- Face the target first
                    if self.controllingGiantess.lookAt then
                        self.controllingGiantess.lookAt(self.microCharacter or self.agent)
                        print("DEBUG: PROPER MOVEMENT - Using lookAt")
                    end

                    -- Set walking animation
                    if self.controllingGiantess.animation then
                        self.controllingGiantess.animation.Set("Walk")
                        print("DEBUG: PROPER MOVEMENT - Set Walk animation")
                    end

                    -- Use proper Sizebox MoveTo command like other working scripts
                    if self.controllingGiantess.MoveTo then
                        self.controllingGiantess.MoveTo(self.microCharacter or self.agent)
                        print("DEBUG: PROPER MOVEMENT - Using MoveTo command")
                    elseif self.controllingGiantess.moveTo then
                        self.controllingGiantess.moveTo(self.microCharacter or self.agent)
                        print("DEBUG: PROPER MOVEMENT - Using moveTo command")
                    end

                    -- Set up tracking for when movement completes
                    self.properMovement = {
                        active = true,
                        target = self.microCharacter or self.agent,
                        startTime = (os and os.time and os.time()) or 0
                    }

                    print("DEBUG: PROPER MOVEMENT - Started using Sizebox MoveTo API, distance: " .. string.format("%.2f", distance))

                    local comeResponses = {
                        "*starts walking toward you* Coming! I'll be right there! *steps closer*",
                        "*moves in your direction* On my way! *walks toward you with a smile*",
                        "*approaches you* Here I come! *takes big steps toward you*"
                    }
                    return comeResponses[math.random(#comeResponses)]
                else
                    return "I'm already right next to you! *looks down at you with a smile*"
                end
            else
                return "I'd love to come to you, but I can't find where you are! Are you in the scene?"
            end
        else
            return "I'd love to come to you, but I can't move right now!"
        end
    end

    -- ENHANCED: "Come to my size" with PROPER micro size tracking - MANY VARIATIONS
    if (string.find(lowerInput, "come to") and string.find(lowerInput, "size")) or
       (string.find(lowerInput, "shrink to") and string.find(lowerInput, "size")) or
       (string.find(lowerInput, "go to") and string.find(lowerInput, "size")) or
       (string.find(lowerInput, "grow to") and string.find(lowerInput, "size")) or
       (string.find(lowerInput, "match") and string.find(lowerInput, "size")) or
       string.find(lowerInput, "same height") or string.find(lowerInput, "my height") or
       string.find(lowerInput, "come to my size") or
       string.find(lowerInput, "go to my size") or
       string.find(lowerInput, "grow to my size") or
       string.find(lowerInput, "shrink to my size") or
       string.find(lowerInput, "match my size") or
       string.find(lowerInput, "be my size") or
       string.find(lowerInput, "same size as me") or
       string.find(lowerInput, "size of me") or
       string.find(lowerInput, "normal height") then

        -- FIXED: Get actual micro size from the player character
        local microCurrentSize = 1.0 -- Default fallback

        -- Try multiple methods to get micro size
        if self.agent and self.agent.scale then
            microCurrentSize = self.agent.scale
            print("DEBUG: Got micro size from agent: " .. microCurrentSize)
        elseif self.microCharacter and self.microCharacter.scale then
            microCurrentSize = self.microCharacter.scale
            print("DEBUG: Got micro size from microCharacter: " .. microCurrentSize)
        else
            -- Try to find the player character
            local player = Entity and Entity.GetPlayerCharacter and Entity.GetPlayerCharacter()
            if player and player.scale then
                microCurrentSize = player.scale
                print("DEBUG: Got micro size from player: " .. microCurrentSize)
            end
        end

        -- FIXED: Set giantess to EXACTLY micro's size - NEVER TOUCH MICRO
        local targetSize = microCurrentSize -- Exactly the same size

        -- Apply GRADUAL size change (not instant) - ONLY TO GIANTESS
        if self.sizingCharacter then
            self.targetSize = targetSize
            self.sizeSystem.targetMultiplier = targetSize
            -- Don't set current size instantly - let it change gradually
            print("DEBUG: ONLY changing giantess size to: " .. targetSize .. " (micro stays unchanged at " .. microCurrentSize .. ")")
        end

        -- CRITICAL: Don't update microSize or microTargetSize - leave micro completely alone
        print("DEBUG: Micro character will NOT be changed in any way")

        local relativeSizeResponses = {
            "*starts shrinking down gradually* I'm coming down to your size! *slowly gets smaller* You're " .. string.format("%.1f", microCurrentSize) .. " and I'll be " .. string.format("%.1f", targetSize) .. "!",
            "*begins shrinking to match you* Getting closer to your height! *gradually becomes smaller* This will be so much more intimate!",
            "*starts adjusting to your scale* Shrinking down to be closer to you! *slowly reduces in size* I can see you better now!",
            "*begins gradual size reduction* Coming down to your level! *slowly gets smaller* We'll be able to talk face to face!",
            "*starts becoming more manageable* Shrinking to be near your height! *gradually gets smaller* This is so much more personal!"
        }
        return relativeSizeResponses[math.random(#relativeSizeResponses)]
    end

    -- ENHANCED: Size commands with proper responses - ONLY if NOT a body part command
    if not hasBodyPart then
        if string.find(lowerInput, "grow") or string.find(lowerInput, "bigger") then
            print("DEBUG: ExecuteCommands - General growth (no body parts detected)")
            self:ChangeSize(1.5, targetCharacter)

            -- Provide appropriate response based on target
            if affectMicro then
                local microGrowthResponses = {
                    "There! *makes you grow* You're bigger now! How does it feel to be larger?",
                    "Growing you up! *increases your size* You look great at this size!",
                    "You're getting bigger! *makes you expand* Do you like being taller?",
                    "Size increase for you! *grows you* How's the view from up there?",
                    "Making you larger! *increases your size* I love seeing you grow!"
                }
                self:AddChatMessage(self.characterName, microGrowthResponses[math.random(#microGrowthResponses)])
            else
                local giantessGrowthResponses = {
                    "*grows bigger* Mmm, I'm getting larger! Do you like this size?",
                    "*expands* I can feel myself growing! This feels amazing!",
                    "*becomes bigger* Getting bigger for you! How do I look?",
                    "*grows* I love getting larger! The world looks smaller from up here!",
                    "*increases in size* Growing bigger! I feel so powerful!"
                }
                self:AddChatMessage(self.characterName, giantessGrowthResponses[math.random(#giantessGrowthResponses)])
            end
        end

        if string.find(lowerInput, "shrink") or string.find(lowerInput, "smaller") then
            print("DEBUG: ExecuteCommands - General shrink (no body parts detected)")
            self:ChangeSize(0.7, targetCharacter)

            -- Provide appropriate response based on target
            if affectMicro then
                local microShrinkResponses = {
                    "There! *makes you smaller* You're more tiny now! So adorable!",
                    "Shrinking you down! *decreases your size* You look so cute!",
                    "You're getting smaller! *makes you shrink* Even more precious!",
                    "Size decrease for you! *shrinks you* How's it feel being tinier?",
                    "Making you smaller! *reduces your size* You're so adorable like this!"
                }
                self:AddChatMessage(self.characterName, microShrinkResponses[math.random(#microShrinkResponses)])
            else
                local giantessShrinkResponses = {
                    "*shrinks down* Getting smaller! Am I more manageable now?",
                    "*becomes smaller* Shrinking for you! How's this size?",
                    "*reduces in size* Getting more petite! Do you like this better?",
                    "*shrinks* I'm becoming smaller! Is this easier for you?",
                    "*decreases in size* Shrinking down! I'm more approachable now!"
                }
                self:AddChatMessage(self.characterName, giantessShrinkResponses[math.random(#giantessShrinkResponses)])
            end
        end
    else
        print("DEBUG: ExecuteCommands - BLOCKED size commands due to body part keywords: " .. lowerInput)
    end
    
    -- Animation commands using REAL Sizebox animations
    if string.find(lowerInput, "dance") and self.controllingGiantess then
        local danceAnims = {"Happy", "Excited", "Victory", "Victory 2", "Taunt 3", "Greet", "Greet 2"}
        local chosenDance = danceAnims[math.random(#danceAnims)]
        self.controllingGiantess.animation.Set(chosenDance)
        print("DEBUG: Dance animation executed: " .. chosenDance)
    end

    if string.find(lowerInput, "wave") and self.controllingGiantess then
        local waveAnims = {"Waving", "Waving 2", "Greet", "Greet 2"}
        local chosenWave = waveAnims[math.random(#waveAnims)]
        self.controllingGiantess.animation.Set(chosenWave)
        print("DEBUG: Wave animation executed: " .. chosenWave)
    end

    if string.find(lowerInput, "sit") and self.controllingGiantess then
        local sitAnims = {"Sit 4", "Sit 2", "Crouch Idle", "Idle 2"}
        local chosenSit = sitAnims[math.random(#sitAnims)]
        self.controllingGiantess.animation.Set(chosenSit)
        print("DEBUG: Sit animation executed: " .. chosenSit)
    end
    
    -- Stop movement and persistent hunting
    if string.find(lowerInput, "stop") then
        -- Stop movement
        self:StopMovement()

        -- Stop persistent hunting if active
        if self.refleshSystem and self.refleshSystem.persistentHunting then
            self.refleshSystem.persistentHunting = false
            self.refleshSystem.huntingActive = false
            self.refleshSystem.huntingMicros = false
            self.refleshSystem.autoSpawnEnabled = false

            -- Stop current AI action
            if self.controllingGiantess and self.controllingGiantess.ai then
                self.controllingGiantess.ai.StopAction()
            end

            -- Set idle animation
            if self.controllingGiantess and self.controllingGiantess.animation then
                self.controllingGiantess.animation.Set("Idle")
            end

            if self.refleshSystem.modeToast then
                self.refleshSystem.modeToast.Print("STOPPED: Persistent hunting disabled")
            end

            print("DEBUG: Stopped persistent stepped reflesh hunting")
            self:AddChatMessage(self.characterName, "Stopped hunting! I'm back to normal mode now. *returns to idle*")
        end
    end
end

function AICompanion:TeleportToBodyPart(bodyPart, offsetY)
    print("DEBUG: TeleportToBodyPart called - bodyPart: " .. bodyPart .. ", offsetY: " .. offsetY)

    if not self.controllingGiantess or not self.controllingGiantess.transform then
        print("DEBUG: No giantess or transform found")
        return false
    end

    -- Get micro character (the one to teleport)
    local microCharacter = nil
    if self.microCharacter and self.microCharacter.transform then
        microCharacter = self.microCharacter
    elseif self.agent and self.agent.transform and self.agent ~= self.controllingGiantess then
        microCharacter = self.agent
    elseif Entity and Entity.GetPlayerCharacter then
        microCharacter = Entity.GetPlayerCharacter()
    end

    if not microCharacter or not microCharacter.transform then
        print("DEBUG: No micro character found to teleport")
        return false
    end

    -- Get giantess position and scale - REAL TIME VALUES
    local giantessPos = self.controllingGiantess.transform.position
    local giantessScale = self.controllingGiantess.transform.localScale.x or 1.0

    -- ENHANCED DEBUG: Show all scale components and actual size
    print(string.format("DEBUG SCALE: localScale.x=%.2f, localScale.y=%.2f, localScale.z=%.2f",
        self.controllingGiantess.transform.localScale.x or 0,
        self.controllingGiantess.transform.localScale.y or 0,
        self.controllingGiantess.transform.localScale.z or 0))

    -- SAFE: Only use transform.localScale (no risky property access)
    local bestScale = giantessScale
    print(string.format("DEBUG: Using localScale=%.2f for positioning", bestScale))

    -- Calculate expected height in feet for reference
    local expectedHeightFt = bestScale * 6  -- Rough conversion (scale 1 = ~6ft)
    print(string.format("DEBUG: Expected height ~%.0f feet", expectedHeightFt))

    -- FIXED: Use her actual position as ground reference (don't subtract!)
    -- Her transform.position.y seems to be closer to ground level than center
    local giantessGroundLevel = giantessPos.y  -- Use her position directly as ground reference

    print(string.format("DEBUG: Giantess center Y=%.1f, scale=%.2f, ground level=%.1f",
        giantessPos.y, bestScale, giantessGroundLevel))

    -- Calculate target position based on body part using BEST SCALE
    local targetPos = Vector3.new(giantessPos.x, giantessGroundLevel, giantessPos.z)

    if bodyPart == "chest" then
        -- Position on chest - SLIGHTLY REDUCED for shorter fall gap
        -- SMART SCALING: INCREASE multiplier starting at 1.2 miles - SHORTER
        local chestHeight = 1460  -- Lower again (was 1465, now 1460)
        if bestScale > 200 then  -- ~1.2 miles
            -- At large sizes, we need HIGHER multipliers to reach the chest
            local extraHeight = (bestScale - 200) * 1.2  -- Same scaling
            chestHeight = 1460 + extraHeight
        end
        targetPos.y = giantessGroundLevel + (chestHeight * bestScale) + offsetY
        targetPos.z = giantessPos.z + (80 * bestScale)  -- Reduced from 150 to 80 (less forward offset so you don't fall off)
        print(string.format("DEBUG: Chest positioning - ground=%.1f + (%.1f*%.2f) + %.1f = %.1f",
            giantessGroundLevel, chestHeight, bestScale, offsetY, targetPos.y))
    elseif bodyPart == "head" then
        -- Position on top of head - SMART SCALING: INCREASE multiplier starting at 7.5 miles
        local headHeight = 1770  -- Lowered a little (was 1780, now 1770)
        if bestScale > 1250 then  -- ~7.5 miles (was 500)
            -- At massive sizes, we need HIGHER multipliers to reach above fabric
            local extraHeight = (bestScale - 1250) * 1.0  -- More aggressive scaling (was 0.8)
            headHeight = 1750 + extraHeight
        end
        targetPos.y = giantessGroundLevel + (headHeight * bestScale) + offsetY
        targetPos.z = giantessPos.z + (25 * bestScale)  -- Reduced forward offset
        targetPos.x = giantessPos.x + (15 * bestScale)  -- Side positioning (to the right)
        print(string.format("DEBUG: Head positioning - ground=%.1f + (%.1f*%.2f) + %.1f = %.1f",
            giantessGroundLevel, headHeight, bestScale, offsetY, targetPos.y))
    elseif bodyPart == "shoulder" then
        -- Position on shoulder (left shoulder)
        targetPos.y = giantessGroundLevel + (140 * bestScale) + offsetY  -- Shoulder height from ground (was 23, now 140)
        targetPos.x = giantessPos.x - (15 * bestScale)  -- Left shoulder (was 2, now 15)
    elseif bodyPart == "hand" then
        -- Position on hand (extended in front)
        targetPos.y = giantessGroundLevel + (100 * bestScale) + offsetY  -- Hand height from ground (was 16, now 100)
        targetPos.z = giantessPos.z + (40 * bestScale)  -- Extended forward (was 6, now 40)
    elseif bodyPart == "foot" then
        -- Position on foot (in front of giantess)
        targetPos.y = giantessGroundLevel + offsetY  -- Foot at ground level (no multiplier)
        targetPos.z = giantessPos.z + (40 * bestScale)  -- Well in front of feet so not directly under her
        print(string.format("DEBUG: Foot positioning - ground=%.1f + %.1f = %.1f (no multiplier)",
            giantessGroundLevel, offsetY, targetPos.y))
    elseif bodyPart == "back" then
        -- Position on back
        targetPos.y = giantessGroundLevel + (110 * bestScale) + offsetY  -- Back height from ground (was 18, now 110)
        targetPos.z = giantessPos.z - (15 * bestScale)  -- Behind (was 2, now 15)
    elseif bodyPart == "stomach" then
        -- Position on stomach/belly
        targetPos.y = giantessGroundLevel + (95 * bestScale) + offsetY  -- Stomach height from ground (was 16, now 95)
        targetPos.z = giantessPos.z + (15 * bestScale)  -- Slightly in front (was 2, now 15)
    elseif bodyPart == "thigh" then
        -- Position on thigh
        targetPos.y = giantessGroundLevel + (80 * bestScale) + offsetY  -- Thigh height from ground (was 14, now 80)
        targetPos.z = giantessPos.z + (10 * bestScale)  -- Slightly in front (was 1, now 10)
    elseif bodyPart == "hip" then
        -- Position on hip/waist
        targetPos.y = giantessGroundLevel + (105 * bestScale) + offsetY  -- Hip height from ground (was 17, now 105)
        targetPos.x = giantessPos.x - (8 * bestScale)  -- Slightly to the side (was 1, now 8)
    end

    -- Apply the teleportation
    local success = pcall(function()
        microCharacter.transform.position = targetPos
        print(string.format("DEBUG: Teleported micro to %s - pos(%.1f, %.1f, %.1f)",
            bodyPart, targetPos.x, targetPos.y, targetPos.z))
    end)

    if success then
        Game.Toast.New().Print("Teleported to " .. bodyPart .. "!")
        return true
    else
        print("DEBUG: Teleportation failed")
        return false
    end
end

function AICompanion:StartMovement(direction, targetCharacter)
    targetCharacter = targetCharacter or self.controllingGiantess
    if not targetCharacter then return end

    self.isMoving = true
    self.movingCharacter = targetCharacter  -- Store who we're moving

    if direction == "forward" then
        self.moveDirection = Vector3.new(0, 0, 1)
    elseif direction == "back" then
        self.moveDirection = Vector3.new(0, 0, -1)
    elseif direction == "left" then
        self.moveDirection = Vector3.new(-1, 0, 0)
    elseif direction == "right" then
        self.moveDirection = Vector3.new(1, 0, 0)
    end

    -- Set walking animation
    if targetCharacter.animation then
        targetCharacter.animation.Set("Walk")
    end
end

function AICompanion:StopMovement()
    self.isMoving = false
    self.moveDirection = Vector3.new(0, 0, 0)

    if self.controllingGiantess and self.controllingGiantess.animation then
        self.controllingGiantess.animation.Set("Idle")
    end
end

function AICompanion:ChangeSize(multiplier, targetCharacter)
    targetCharacter = targetCharacter or self.controllingGiantess
    if not targetCharacter then return end

    self.targetSize = self.currentSize * multiplier
    self.sizingCharacter = targetCharacter  -- Store who we're resizing
    -- We'll implement smooth size changing in Update()
end

function AICompanion:Update()
    if not self.controllingGiantess then return end

    -- HEAD TRACKING REMOVED FOR TESTING - Just testing basic rotation first

    -- Handle persistent "face me" rotation every frame
    if self.facingTarget and self.facingTarget.active then
        -- Find current micro position
        local microPos = nil
        if self.microCharacter and self.microCharacter.transform then
            microPos = self.microCharacter.transform.position
        elseif self.agent and self.agent.transform and self.agent ~= self.controllingGiantess then
            microPos = self.agent.transform.position
        elseif Entity and Entity.GetPlayerCharacter then
            local player = Entity.GetPlayerCharacter()
            if player and player.transform then
                microPos = player.transform.position
            end
        end

        if microPos then
            local giantessPos = self.controllingGiantess.transform.position
            local direction = microPos - giantessPos
            direction.y = 0 -- Keep horizontal

            if direction.magnitude > 0.1 then
                -- Apply rotation every frame to keep facing the micro
                self.controllingGiantess.transform.rotation = Quaternion.LookRotation(direction.normalized)
            end
        end
    end

    -- Handle movement (DISABLED when proper movement is active)
    if self.isMoving and self.moveDirection and self.movingCharacter and not (self.properMovement and self.properMovement.active) then
        local currentPos = self.movingCharacter.transform.position
        local newPos = currentPos + (self.moveDirection * self.moveSpeed * Time.deltaTime)
        self.movingCharacter.transform.position = newPos
        print("DEBUG: Old movement applied - speed: " .. self.moveSpeed .. ", deltaTime: " .. Time.deltaTime .. ", moving: " .. tostring(self.isMoving))
    end

    -- FIXED: Handle proper movement tracking for "come here" command
    if self.properMovement and self.properMovement.active then
        -- Check if the giantess has reached the target or if movement is complete
        local currentTime = (os and os.time and os.time()) or 0
        local timeElapsed = currentTime - self.properMovement.startTime

        -- Check if movement is still active by looking at the giantess's AI state
        local isMoving = false
        if self.controllingGiantess.ai and self.controllingGiantess.ai.IsActionActive then
            isMoving = self.controllingGiantess.ai.IsActionActive()
        end

        -- Check distance to target
        local reachedTarget = false
        if self.properMovement.target and self.properMovement.target.transform and self.controllingGiantess.transform then
            local targetPos = self.properMovement.target.transform.position
            local giantessPos = self.controllingGiantess.transform.position
            local distance = math.sqrt((targetPos.x - giantessPos.x)^2 + (targetPos.z - giantessPos.z)^2)

            if distance <= 3.0 then
                reachedTarget = true
                print("DEBUG: PROPER MOVEMENT - Reached target, distance: " .. string.format("%.2f", distance))
            else
                print("DEBUG: PROPER MOVEMENT - Still moving, distance: " .. string.format("%.2f", distance) .. ", isMoving: " .. tostring(isMoving))
            end
        end

        -- Stop movement if reached target, movement completed, or timeout
        if reachedTarget or not isMoving or timeElapsed > 30 then
            self.properMovement.active = false

            -- Set idle animation
            if self.controllingGiantess.animation then
                self.controllingGiantess.animation.Set("Idle")
                print("DEBUG: PROPER MOVEMENT - Set Idle animation")
            end

            -- Face the target
            if self.properMovement.target and self.controllingGiantess.lookAt then
                self.controllingGiantess.lookAt(self.properMovement.target)
                print("DEBUG: PROPER MOVEMENT - Final lookAt target")
            end

            print("DEBUG: PROPER MOVEMENT - Movement completed")
        end
    end

    -- TODO: Add pickup commands later when movement system is implemented

    if lowerInput and (string.find(lowerInput, "step on me") or string.find(lowerInput, "stomp") or
       string.find(lowerInput, "crush me") or string.find(lowerInput, "under your foot")) then
        local stompResponses = {
            "*carefully places my foot near you* Are you sure? I don't want to hurt you!",
            "*hovers my foot above you* You're so brave! But I'll be very gentle!",
            "*slowly lowers my foot* I'll be careful with you, little one!",
            "*places my foot down gently* There! You're under my foot but safe!",
            "*carefully steps near you* You look so tiny down there! Are you okay?"
        }
        return stompResponses[math.random(#stompResponses)]
    end

    if lowerInput and (string.find(lowerInput, "kiss me") or string.find(lowerInput, "give me a kiss")) then
        local kissResponses = {
            "*leans down and gives you a gentle kiss* Mwah! You're so sweet!",
            "*carefully kisses you* There! A kiss from your giantess!",
            "*gives you a soft kiss* You're adorable! I could kiss you all day!",
            "*leans in for a gentle kiss* Mwah! You taste so good, little one!",
            "*kisses you tenderly* There's your kiss! You're so precious!"
        }
        return kissResponses[math.random(#kissResponses)]
    end

    if lowerInput and (string.find(lowerInput, "hug me") or string.find(lowerInput, "cuddle") or
       string.find(lowerInput, "snuggle")) then
        local hugResponses = {
            "*gently wraps my fingers around you* There's your hug! So warm and cozy!",
            "*carefully cuddles you* Aww, you're so soft and tiny! Perfect for hugging!",
            "*gives you a gentle squeeze* The best hugs come from giantesses! So comfy!",
            "*snuggles you close* You fit perfectly in my hands! Such a good cuddle!",
            "*embraces you gently* There! A nice warm hug from your giantess!"
        }
        return hugResponses[math.random(#hugResponses)]
    end

    -- NEW: Handle head tracking for "look at me" command
    if self.headTracking and self.headTracking.active then
        -- Update target position (in case micro moved)
        local microPos = nil
        if self.microCharacter and self.microCharacter.transform then
            microPos = self.microCharacter.transform.position
        elseif self.agent and self.agent.transform and self.agent ~= self.controllingGiantess then
            microPos = self.agent.transform.position
        elseif Entity and Entity.GetPlayerCharacter then
            local player = Entity.GetPlayerCharacter()
            if player and player.transform then
                microPos = player.transform.position
            end
        end

        if microPos and self.headTracking.headBone and self.controllingGiantess.transform then
            -- SMART head position calculation (FIXED - same as head_tracking_simple.lua)
            local giantessPos = self.controllingGiantess.transform.position
            local giantessScale = self.controllingGiantess.transform.localScale.x or 1.0
            local headWorldPos

            -- Try to get actual head bone world position first
            if self.headTracking.headBone.position then
                headWorldPos = self.headTracking.headBone.position
            else
                -- Use REASONABLE scaled offset (not too big!)
                local reasonableHeadOffset = math.min(giantessScale * 8, 200)  -- Cap at 200 units max
                headWorldPos = Vector3.new(giantessPos.x, giantessPos.y + reasonableHeadOffset, giantessPos.z)
            end

            local direction = microPos - headWorldPos

            if direction.magnitude > 0.1 then
                -- Apply rotation with NATURAL LIMITS (prevent backwards looking)
                local success = pcall(function()
                    -- WORKING VERSION: Use exact same logic as working head tracking test
                    local lookDirection = direction.normalized

                    -- CHECK if micro is behind the giantess (prevent backwards head turning)
                    local giantessForward = self.controllingGiantess.transform.forward or Vector3.new(0, 0, 1)
                    local toMicroFlat = Vector3.new(lookDirection.x, 0, lookDirection.z).normalized
                    local dotProduct = toMicroFlat.x * giantessForward.x + toMicroFlat.z * giantessForward.z

                    if dotProduct < -0.3 then
                        -- Micro is behind - limit to side look only (90 degrees max)
                        local rightDot = lookDirection.x * (giantessForward.z) - lookDirection.z * (-giantessForward.x)
                        local sideDirection
                        if rightDot > 0 then
                            sideDirection = Vector3.new(giantessForward.z, lookDirection.y, -giantessForward.x).normalized
                        else
                            sideDirection = Vector3.new(-giantessForward.z, lookDirection.y, giantessForward.x).normalized
                        end
                        local lookRotation = Quaternion.LookRotation(sideDirection)
                        self.headTracking.headBone.localRotation = lookRotation
                    else
                        -- Normal tracking - micro is in front or to the side
                        local lookRotation = Quaternion.LookRotation(lookDirection)
                        self.headTracking.headBone.localRotation = lookRotation
                    end
                end)

                if success then
                    -- Simple status for debugging
                    if direction.normalized.y > 0.1 then
                        -- Looking up (rare)
                    elseif direction.normalized.y < -0.1 then
                        -- Looking down (common when micro is below)
                    else
                        -- Looking straight
                    end
                else
                    print("DEBUG: HEAD TRACKING - Rotation failed, disabling")
                    self.headTracking.active = false
                end
            end
        else
            -- Lost target or head bone - stop tracking
            self.headTracking.active = false
            print("DEBUG: HEAD TRACKING - Lost target or head bone, stopping")
        end
    end

    -- NEW: Handle persistent rotation (keep facing micro after "come to me")
    if self.persistentRotation and self.persistentRotation.active then
        if self.controllingGiantess and self.controllingGiantess.transform then
            -- Keep the rotation locked to face the micro
            self.controllingGiantess.transform.rotation = self.persistentRotation.targetRotation
        end
    end

    -- Handle advanced size system updates
    self:UpdateAdvancedSizeSystem()

    -- FIXED: Handle gradual body part growth (separate from whole body growth)
    self:UpdateGradualBodyPartGrowth()

    -- NEW: Handle BE cycling system
    self:UpdateBECycling()

    -- NEW: Handle animation timer system
    self:UpdateAnimationTimer()

    -- NEW: Handle motion animation system
    if self.animationSystem.animationTimer > 0 then
        self.animationSystem.animationTimer = self.animationSystem.animationTimer - Time.deltaTime
        if self.animationSystem.animationTimer <= 0 and self.controllingGiantess and self.controllingGiantess.animation then
            self.controllingGiantess.animation.Set("Idle")
            print("DEBUG: ANIMATION - Returned to idle")
        end
    end

    -- NEW: Simple systems
    self:UpdateSimpleBurst()
    self:UpdateMicroLock()

    -- NEW: Update chest bounce system
    self:UpdateChestBounce()

    -- NEW: Update micro scale lock system
    self:UpdateMicroScaleLock()

    -- FIXED: Handle gradual size changes using proper Sizebox method
    -- SKIP gradual sizing when burst system is active
    if not self.burstSystem.active and math.abs(self.currentSize - self.targetSize) > 0.01 and self.sizingCharacter then
        if self.currentSize < self.targetSize then
            self.currentSize = self.currentSize + self.sizeChangeSpeed * Time.deltaTime
            if self.currentSize > self.targetSize then
                self.currentSize = self.targetSize
            end
        else
            self.currentSize = self.currentSize - self.sizeChangeSpeed * Time.deltaTime
            if self.currentSize < self.targetSize then
                self.currentSize = self.targetSize
            end
        end

        -- Apply scale using proper Sizebox method (like grow_spurtsv2_1)
        self.sizingCharacter.scale = self.currentSize
    end

    -- NEW: Handle gradual micro size changes (SLOWER SPEED)
    if self.microCharacter and math.abs(self.microSize - self.microTargetSize) > 0.01 then
        -- Use much slower speed for micro (1/4 of giantess speed)
        local microSpeed = self.sizeChangeSpeed * 0.25

        if self.microSize < self.microTargetSize then
            self.microSize = self.microSize + microSpeed * Time.deltaTime
            if self.microSize > self.microTargetSize then
                self.microSize = self.microTargetSize
            end
        else
            self.microSize = self.microSize - microSpeed * Time.deltaTime
            if self.microSize < self.microTargetSize then
                self.microSize = self.microTargetSize
            end
        end

        -- Apply scale to micro character
        self.microCharacter.scale = self.microSize
        print(string.format("DEBUG: Gradual micro size change - current: %.2f, target: %.2f, speed: %.2f", self.microSize, self.microTargetSize, microSpeed))
    end

    -- Handle input
    if Input then
        -- Block default Sizebox controls when typing
        if self.isTyping then
            self:BlockDefaultControls()
            self:HandleTextInput()
        else
            -- Chat key to start typing (T key - only if not already typing)
            if Input.GetKeyDown("t") and not self.isTyping then  -- Use direct key name
                print("DEBUG: T key pressed - starting typing")
                self:StartTyping()
            end

            -- Chat box key to show/open chat box (Y key)
            if Input.GetKeyDown("y") then  -- Use direct key name instead of variable
                print("DEBUG: Y key pressed - showing chat box")
                self:ForceShowChatHistory()  -- Use force version to bypass cooldown
                Game.Toast.New().Print("Y pressed - Chat box should be visible!")
            end

            -- Apostrophe key to refresh chat box (REMOVED - "quote" key doesn't exist in Sizebox)
            -- if Input.GetKeyDown("quote") then  -- This causes ArgumentException
            --     self:ShowChatHistory()
            --     Game.Toast.New().Print("Chat refreshed!")
            -- end

            -- Quick command key (J key)
            if Input.GetKeyDown("j") then  -- Use direct key name instead of variable
                print("DEBUG: J key pressed - showing quick commands")
                self:ShowQuickCommands()
            end

            -- NEW: L key for micro scale lock toggle (only when not typing)
            if Input.GetKeyDown("l") and not self.isTyping then
                print("DEBUG: L key pressed - toggling micro scale lock")
                self:ToggleMicroScaleLock()
            end

            -- Handle quick number commands
            self:HandleQuickCommands()

            -- Quick voice commands (no typing needed)
            self:HandleQuickCommands()
        end
    end

    -- NEW: Update reflesh system
    self:UpdateRefleshSystem()

    -- NEW: Autonomous behavior and smart responses (with safety check)
    if self.UpdateAutonomousBehavior then
        self:UpdateAutonomousBehavior()
    end

    -- NEW: Handle persistent hunting for stepped mode
    if self.refleshSystem and self.refleshSystem.persistentHunting then
        self:UpdatePersistentHunting()
    end

    -- NEW: Handle idle chat system
    if self.idleChatSystem then
        self:UpdateIdleChat()
    end
end

-- NEW: Update reflesh system (integrated from reflesh_backup.lua)
function AICompanion:UpdateRefleshSystem()
    if not self.refleshSystem then return end

    local rs = self.refleshSystem -- shorthand

    -- REMOVED: SuperGrowth handling - now using direct animation approach like massage

    -- Handle actual growth
    if rs.growing and rs.growthEnabled then
        rs.status = 1

        -- Different growth patterns based on selected mode
        if rs.growthMode == "linear" then
            -- Linear growth - smooth and consistent
            rs.phi = rs.phi + Time.deltaTime / rs.duration
            if rs.phi <= math.pi then
                rs.GS = math.sin(rs.phi * 0.5) * rs.currentRate
            else
                rs.growing = false
                rs.phi = 0
                rs.GS = 0
                rs.status = 2
            end
        elseif rs.growthMode == "exponential" then
            -- Exponential growth - starts slow, gets faster
            rs.phi = rs.phi + Time.deltaTime / rs.duration
            if rs.phi <= math.pi then
                rs.GS = math.sin(rs.phi * 0.5) * rs.currentRate * (1 + rs.phi)
            else
                rs.growing = false
                rs.phi = 0
                rs.GS = 0
                rs.status = 2
            end
        elseif rs.growthMode == "random" then
            -- Random growth - unpredictable
            rs.phi = rs.phi + Time.deltaTime / rs.duration
            if rs.phi <= math.pi then
                rs.GS = math.sin(rs.phi * 0.5) * rs.currentRate * (0.5 + math.random())
            else
                rs.growing = false
                rs.phi = 0
                rs.GS = 0
                rs.status = 2
            end
        elseif rs.growthMode == "oscillating" then
            -- Oscillating growth (grow and shrink in waves)
            rs.oscillationPhase = rs.oscillationPhase + Time.deltaTime

            -- Check if we're in the final burst phase
            if rs.oscillationPhase >= rs.oscillationPeriod * 3 and rs.oscillationFinalBurst then
                -- Final dramatic burst
                rs.GS = rs.currentRate * rs.oscillationAmplitude * rs.oscillationBurstMultiplier * rs.oscillationBurstDirection

                -- End after a short burst period
                if rs.oscillationPhase >= rs.oscillationPeriod * 3 + 1.0 then
                    rs.oscillationPhase = 0
                    rs.growing = false
                    rs.GS = 0
                    rs.status = 2
                end
            elseif rs.oscillationPhase >= rs.oscillationPeriod * 4 then
                -- End if no final burst
                rs.oscillationPhase = 0
                rs.growing = false
                rs.GS = 0
                rs.status = 2
            else
                -- Normal oscillation
                rs.GS = math.sin(rs.oscillationPhase * (2 * math.pi / rs.oscillationPeriod)) * rs.currentRate * rs.oscillationAmplitude
            end
        elseif rs.growthMode == "stepped" then
            -- Stepped growth - smooth with smaller steps
            rs.phi = rs.phi + Time.deltaTime / rs.duration
            rs.stepTimer = rs.stepTimer + Time.deltaTime

            if rs.stepTimer >= rs.stepInterval then
                rs.stepTimer = 0
                rs.GS = rs.stepSize * rs.crushMultiplier
            else
                rs.GS = 0
            end

            -- End after duration
            if rs.phi >= math.pi then
                rs.growing = false
                rs.phi = 0
                rs.GS = 0
                rs.status = 2
            end
        elseif rs.growthMode == "burst" then
            -- Delayed burst growth
            rs.phi = rs.phi + Time.deltaTime / rs.duration
            rs.burstTimer = rs.burstTimer + Time.deltaTime

            if rs.burstTimer < rs.burstDelay then
                -- Slow initial growth
                rs.GS = rs.currentRate * 0.2
            else
                -- Sudden burst
                rs.GS = rs.currentRate * rs.burstMultiplier
            end

            -- End after duration
            if rs.phi >= math.pi then
                rs.growing = false
                rs.GS = 0
                rs.status = 2
            end
        elseif rs.growthMode == "logarithmic" then
            -- Logarithmic growth (fast at first, then slows)
            rs.phi = rs.phi + Time.deltaTime / rs.duration

            if rs.phi <= math.pi then
                -- Use log function for growth curve
                rs.GS = rs.currentRate * (1 - (1 / math.log(rs.logBase + rs.phi * 5)))
            else
                rs.growing = false
                rs.phi = 0
                rs.GS = 0
                rs.status = 2
            end
        end

        -- Apply growth based on breast expansion setting
        if rs.breastExpansion then
            self:ApplyBreastExpansion(rs.GS, rs.growthMode)
        else
            -- Normal overall growth
            if self.controllingGiantess and self.controllingGiantess.grow then
                self.controllingGiantess.grow(rs.GS)
            end
        end
    end

    -- After growth completes, play reflesh animation
    if rs.superGrowthset and rs.status == 2 then
        rs.superGrowthset = false
        rs.status = 3

        -- Play reflesh animation
        if self.controllingGiantess and self.controllingGiantess.animation then
            self.controllingGiantess.animation.Set("Reflesh")
        end
        rs.refleshTimer = rs.refleshDuration
        rs.inRefleshCooldown = true
    end

    -- Wait for the reflesh animation to finish
    if rs.status == 3 then
        rs.refleshTimer = rs.refleshTimer - Time.deltaTime
        if rs.refleshTimer <= 0 then
            rs.status = 4
            rs.idleTimer = rs.idleDuration

            -- Set to idle animation
            if self.controllingGiantess and self.controllingGiantess.animation then
                self.controllingGiantess.animation.Set("Idle")
            end
        end
    end

    -- Idle cooldown to prevent immediate reflesh loop
    if rs.status == 4 then
        rs.idleTimer = rs.idleTimer - Time.deltaTime
        if rs.idleTimer <= 0 then
            rs.killing = true
            rs.status = 0
            rs.queuedAnim = nil
            rs.inRefleshCooldown = false
            rs.walkSet = false
        end
    end

    -- NEW: Handle micro hunting cooldown for stepped mode
    if rs.huntingMicros and rs.huntingCooldown > 0 then
        rs.huntingCooldown = rs.huntingCooldown - Time.deltaTime

        -- When cooldown expires, find the next micro
        if rs.huntingCooldown <= 0 then
            -- Check if giantess is not currently doing a behavior
            local isBusy = false
            if self.controllingGiantess.ai and self.controllingGiantess.ai.IsBehaviorActive then
                isBusy = self.controllingGiantess.ai.IsBehaviorActive()
            end

            if not isBusy then
                local foundMicro = self:FindAndTargetMicro()
                if not foundMicro then
                    -- No more micros found, end hunting mode
                    rs.huntingMicros = false
                    if rs.modeToast then
                        rs.modeToast.Print("STEPPED REFLESH: NO MORE MICROS - HUNT COMPLETE")
                    end
                    print("DEBUG: Micro hunting complete - no more targets")
                end
            end
        end
    end
end

-- NEW: Apply breast expansion growth
function AICompanion:ApplyBreastExpansion(growthAmount, growthMode)
    if not self.controllingGiantess or not self.controllingGiantess.bones then
        return
    end

    -- Try to find breast bones
    local foundBreasts = false
    local breastBones = {}

    -- Look for common breast bone names
    local boneNames = "Breast,Ichichi,LeftBreast,RightBreast,leftbreast,rightbreast,breast left,breast right,hidarichichi,migichichi,lPectoral,rPectoral"

    for boneName in string.gmatch(boneNames, '([^,]+)') do
        local bones = self.controllingGiantess.bones.GetBonesByName(boneName, true)
        if bones then
            for k,v in ipairs(bones) do
                table.insert(breastBones, v)
                foundBreasts = true
            end
        end
    end

    -- If we found breast bones, grow them
    if foundBreasts then
        -- Apply breast-specific growth with mode-specific adjustments
        local beGrowthFactor = 0.001 -- Base growth factor

        -- Adjust growth factor based on mode
        if growthMode == "exponential" then
            beGrowthFactor = 0.0005
        elseif growthMode == "burst" then
            beGrowthFactor = 0.0008
        elseif growthMode == "oscillating" then
            beGrowthFactor = 0.002
        end

        -- Apply the growth
        for k,v in ipairs(breastBones) do
            v.localScale = v.localScale * (1 + growthAmount * beGrowthFactor)
        end

        -- Show breast expansion message
        if growthAmount > 0 and self.refleshSystem.modeToast then
            local modeText = string.upper(growthMode) .. " BREAST EXPANSION"
            self.refleshSystem.modeToast.Print(modeText)
        end
    else
        -- If no breast bones found, show message
        if growthAmount > 0 and self.refleshSystem.modeToast then
            self.refleshSystem.modeToast.Print("BREAST EXPANSION: No breast bones found")
        end
    end
end

-- NEW: Autonomous behavior system
function AICompanion:UpdateAutonomousBehavior()
    -- Safety check: ensure all systems are initialized
    if not self.surpriseSystem or not self.moodSystem or not self.activitySystem or not self.learningSystem then
        print("DEBUG: Systems not fully initialized, skipping autonomous behavior")
        return
    end

    local currentTime = (os and os.time and os.time()) or 0

    -- NEW: Handle surprise spam system
    if self.surpriseSystem.isSpamming and self.surpriseSystem.consentGiven then
        if currentTime - self.surpriseSystem.lastSurpriseTime >= self.surpriseSystem.spamInterval then
            self.surpriseSystem.lastSurpriseTime = currentTime
            local surpriseResponse = self:DoSurprise()
            self:AddChatMessage(self.characterName, surpriseResponse)
            -- Show surprise in chat immediately
            self:ShowChatHistory()
        end
    end

    -- NEW: Update mood system
    self.moodSystem.moodDuration = currentTime - self.moodSystem.lastMoodChange

    -- Auto mood changes based on time and activity
    if self.moodSystem.autoMoodChange and self.moodSystem.moodDuration > 300 then -- 5 minutes
        local randomMoods = {"happy", "playful", "confident", "energetic"}
        local newMood = randomMoods[math.random(#randomMoods)]
        if newMood ~= self.moodSystem.currentMood then
            self:ChangeMood(newMood, math.random(5, 8))
        end
    end

    -- Update activity system (with safety check)
    if self.activitySystem and self.DetermineCurrentActivity then
        self.activitySystem.currentActivity = self:DetermineCurrentActivity()
    end

    -- Learning system updates (with safety check)
    if self.learningSystem and self.learningSystem.learningEnabled and self.UpdateLearningSystem then
        self:UpdateLearningSystem()
    end

    -- Autonomous conversation starters (every 2-5 minutes)
    if currentTime - self.memory.lastInteractionTime > math.random(120, 300) then
        if math.random() < 0.1 then  -- 10% chance per check
            local autonomousMessages = {
                "Hey! *waves excitedly* What would you like to do together?",
                "I was just thinking about you! *smiles warmly* How are you feeling?",
                "Want to try something fun? I can grow, shrink, or we can just chat!",
                "I love spending time with you! *giggles* Any ideas for what we should do?",
                "Feeling playful today? *winks* I'm ready for whatever you have in mind!",
                "You know, I really enjoy our conversations! *sits down closer* What's on your mind?"
            }

            -- Personalize based on relationship level
            if self.memory.relationshipLevel > 7 then
                table.insert(autonomousMessages, "I've been missing you, sweetie! *gives you loving eyes* Want to spend some quality time together?")
                table.insert(autonomousMessages, "My dear, I was hoping we could chat! *reaches out affectionately* You always make me so happy!")
            end

            self:AddChatMessage(self.characterName, autonomousMessages[math.random(#autonomousMessages)])
            self:ShowChatHistory()
            self.memory.lastInteractionTime = currentTime
        end
    end

    -- Smart size suggestions based on user preferences
    if self.memory.userCommands["grow"] and self.memory.userCommands["grow"] > 10 then
        if currentTime - self.memory.lastInteractionTime > 600 and math.random() < 0.05 then  -- After 10 minutes, 5% chance
            local growthSuggestions = {
                "I know you love watching me grow! *strikes a pose* Want to see me get bigger?",
                "Feeling like some growth fun? *flexes playfully* I'm ready to get huge for you!",
                "I've been thinking... maybe it's time for me to grow again? *winks seductively*"
            }
            self:AddChatMessage(self.characterName, growthSuggestions[math.random(#growthSuggestions)])
            self:ShowChatHistory()
            self.memory.lastInteractionTime = currentTime
        end
    end

    -- Mood-based autonomous responses
    if self.memory.userMood == "sad" and currentTime - self.memory.lastInteractionTime > 180 then
        if math.random() < 0.15 then  -- 15% chance when user seems sad
            local comfortingMessages = {
                "Hey... *kneels down gently* I'm here if you need someone to talk to.",
                "I can sense you might be feeling down. *gives you a warm, caring smile* Want a hug?",
                "You know I care about you, right? *sits close* I'm always here for you.",
                "Whatever's bothering you, we can face it together. *extends hand comfortingly*"
            }
            self:AddChatMessage(self.characterName, comfortingMessages[math.random(#comfortingMessages)])
            self:ShowChatHistory()
            self.memory.lastInteractionTime = currentTime
        end
    end
end

function AICompanion:BlockDefaultControls()
    -- This function prevents default Sizebox controls while typing
    -- We need to be more aggressive about blocking controls
    if Input then
        -- FIXED: Block controls silently without annoying toast messages
        if Input.GetKeyDown("x") or Input.GetKeyDown("z") or Input.GetKeyDown("p") or
           Input.GetKeyDown("e") or Input.GetKeyDown("v") then
            return true  -- Block the action silently
        end

        -- Block movement keys silently
        if Input.GetKey("w") or Input.GetKey("a") or Input.GetKey("s") or Input.GetKey("d") then
            return true  -- Block movement silently
        end
    end
    return false
end

function AICompanion:StartTyping()
    self.isTyping = true
    self.inputBuffer = ""
    self.lastChatUpdate = 0
    self._movementWarningTimer = 0  -- Reset warning timer
    -- Show chat immediately when starting to type
    self:ShowChatHistory()
    Game.Toast.New().Print("TYPING MODE ACTIVE! You can see your text in the chat box!")
    print("DEBUG: Started typing mode - chat should be visible")
end

function AICompanion:HandleTextInput()
    -- Handle Enter key (send message)
    if Input.GetKeyDown("return") or Input.GetKeyDown("enter") then
        if self.inputBuffer ~= "" then
            self:ProcessUserInput(self.inputBuffer)
            self.inputBuffer = ""
        end
        self.isTyping = false
        -- Clear both toasts when message is sent
        if self.chatToast1 then
            self.chatToast1.Print(nil)
        end
        if self.chatToast2 then
            self.chatToast2.Print(nil)
        end
        Game.Toast.New().Print("Message sent!")
        return
    end

    -- Handle Tab key (cancel) - changed from Escape since it doesn't work
    if Input.GetKeyDown("tab") then
        self.isTyping = false
        self.inputBuffer = ""
        -- Clear both toasts when typing is cancelled
        if self.chatToast1 then
            self.chatToast1.Print(nil)
        end
        if self.chatToast2 then
            self.chatToast2.Print(nil)
        end
        Game.Toast.New().Print("Typing cancelled")
        return
    end

    -- Handle Backspace
    if Input.GetKeyDown("backspace") then
        if string.len(self.inputBuffer) > 0 then
            self.inputBuffer = string.sub(self.inputBuffer, 1, -2)
            -- Update immediately for instant feedback in the BIG popup
            self:ShowChatHistory()
        end
        return
    end

    -- Handle character input
    self:HandleCharacterInput()
end

function AICompanion:UpdateChatDisplay()
    -- Always update immediately for maximum responsiveness
    self:ShowChatHistory()
end

function AICompanion:HandleCharacterInput()
    -- Define allowed characters
    local letters = "abcdefghijklmnopqrstuvwxyz"
    local numbers = "0123456789"
    local symbols = " .,!?'-"

    -- Check letters
    for i = 1, string.len(letters) do
        local char = string.sub(letters, i, i)
        if Input.GetKeyDown(char) then
            -- Check for shift key for uppercase
            if Input.GetKey("left shift") or Input.GetKey("right shift") then
                char = string.upper(char)
            end
            self.inputBuffer = self.inputBuffer .. char
            -- Update immediately for instant feedback in the BIG popup
            self:ShowChatHistory()
            return
        end
    end

    -- Check numbers
    for i = 1, string.len(numbers) do
        local char = string.sub(numbers, i, i)
        if Input.GetKeyDown(char) then
            self.inputBuffer = self.inputBuffer .. char
            -- Update immediately for instant feedback in the BIG popup
            self:ShowChatHistory()
            return
        end
    end

    -- Check symbols (using different key names that Sizebox recognizes)
    if Input.GetKeyDown("space") then
        self.inputBuffer = self.inputBuffer .. " "
        -- Update immediately for instant feedback in the BIG popup
        self:ShowChatHistory()
    -- Note: Sizebox may not recognize "period" and "comma" key names
    -- Users can type punctuation using other methods or we'll add it later
    end
end

function AICompanion:HandleQuickCommands()
    -- Quick commands that work without typing
    if Input.GetKeyDown("1") then
        self:ProcessUserInput("hello")
    elseif Input.GetKeyDown("2") then
        self:ProcessUserInput("move forward")
    elseif Input.GetKeyDown("3") then
        self:ProcessUserInput("move back")
    elseif Input.GetKeyDown("4") then
        self:ProcessUserInput("grow")
    elseif Input.GetKeyDown("5") then
        self:ProcessUserInput("shrink")
    elseif Input.GetKeyDown("6") then
        self:ProcessUserInput("dance")
    elseif Input.GetKeyDown("0") then
        self:ProcessUserInput("stop")
    end
end

function AICompanion:ShowQuickCommands()
    -- NEW: Smart command suggestions based on user preferences and memory
    local baseCommands = "=== SMART QUICK COMMANDS ===\n\n" ..
                        "Press these number keys for instant commands:\n\n" ..
                        "• 1 = Say 'hello'" .. (self.memory.relationshipLevel > 6 and " (She'll be extra sweet!)" or "") .. "\n" ..
                        "• 2 = Move forward\n" ..
                        "• 3 = Move back\n" ..
                        "• 4 = Grow bigger" .. (self.memory.userCommands["grow"] and self.memory.userCommands["grow"] > 5 and " ⭐ (Your Favorite!)" or "") .. "\n" ..
                        "• 5 = Shrink smaller" .. (self.memory.userCommands["shrink"] and self.memory.userCommands["shrink"] > 5 and " ⭐ (Popular Choice!)" or "") .. "\n" ..
                        "• 6 = Dance" .. (self.memory.userCommands["dance"] and self.memory.userCommands["dance"] > 3 and " 💃 (You Love This!)" or "") .. "\n" ..
                        "• 0 = Stop all actions\n\n"

    -- Add smart suggestions based on memory
    local smartSuggestions = "🧠 SMART SUGGESTIONS:\n"

    if self.memory.userCommands["grow"] and self.memory.userCommands["grow"] > 8 then
        smartSuggestions = smartSuggestions .. "• 'grow chest' - You seem to love body part growth!\n"
        smartSuggestions = smartSuggestions .. "• 'grow oppai' - Try the new keywords!\n"
    end

    if self.memory.relationshipLevel > 6 then
        smartSuggestions = smartSuggestions .. "• 'I love you' - She'll respond sweetly!\n"
        smartSuggestions = smartSuggestions .. "• 'hug me' - Get some affection!\n"
    end

    if self.memory.userMood == "sad" then
        smartSuggestions = smartSuggestions .. "• 'comfort me' - She'll cheer you up!\n"
    elseif self.memory.userMood == "excited" then
        smartSuggestions = smartSuggestions .. "• 'surprise me' - She'll do something amazing!\n"
    end

    smartSuggestions = smartSuggestions .. "• 'more' - Repeat your last action!\n"
    smartSuggestions = smartSuggestions .. "• 'grow chest and grow' - Combine commands!\n\n"

    local naturalCommands = "💬 Or type naturally:\n" ..
                           "• 'hello there beautiful'\n" ..
                           "• 'move to the left'\n" ..
                           "• 'get bigger for me'\n" ..
                           "• 'wave at me'\n" ..
                           "• 'sit down'\n" ..
                           "• 'how are you feeling?'\n\n" ..
                           "Just type what you want me to do!"

    local commandText = baseCommands .. smartSuggestions .. naturalCommands

    -- Use the same method as the welcome message (which works)
    if Game and Game.Message then
        Game.Message(commandText, "Quick Commands - " .. self.characterName)
        print("DEBUG: Quick Commands displayed using Game.Message")
    else
        print("DEBUG: Game.Message not available - printing to console:")
        print("=== QUICK COMMANDS ===")
        print(commandText)
        print("======================")
    end
end

return AICompanion





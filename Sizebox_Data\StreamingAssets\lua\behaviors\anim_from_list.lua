--Play Random Anim v1 - Debolte 2023/11/26


-- Change Animations HERE:
-- =======================
animationList = {
	"Jump", "Jump Low", "Air Squat", "Excited",
}



function SetAnim(self)
	self.animation = animationList[math.random(#animationList)]
end

function PlayAnim(self)
	self.agent.animation.SetAndWait(self.animation)
end



--Continously, any from the list
PlayRandomAnimMixed = RegisterBehavior("Play Random Anim Mixed")
PlayRandomAnimMixed.data = {
    menuEntry = "Play Random Anim/Mixed",
	agent = {
		type = { "humanoid" }
	},
	target = {
		type = { "oneself" }
	},
	tags = "macro, micro, animation"
}

function PlayRandomAnimMixed:Start()
	if not globals["animRand"] then math.randomseed(tonumber(os.time()) + self.agent.id) globals["animRand"] = true end
	self.agent.ai.StopAction()
end

function PlayRandomAnimMixed:Update()
	if not self.agent.ai.IsActionActive() then
		SetAnim(self)
		PlayAnim(self)
	end
end



--Continuously, the same one from activation
PlayRandomAnimSame = RegisterBehavior("Play Random Anim Same")
PlayRandomAnimSame.data = {
    menuEntry = "Play Random Anim/Same",
	agent = {
		type = { "humanoid" }
	},
	target = {
		type = { "oneself" }
	},
	tags = "macro, micro, animation"
}

function PlayRandomAnimSame:Start()
	if not globals["animRand"] then math.randomseed(tonumber(os.time()) + self.agent.id) globals["animRand"] = true end
	self.agent.ai.StopAction()
	SetAnim(self)
end

function PlayRandomAnimSame:Update()
	if not self.agent.ai.IsActionActive() then
		PlayAnim(self)
	end
end



--Only once, the one at activation
PlayRandomAnimOnce = RegisterBehavior("Play Random Anim Once")
PlayRandomAnimOnce.data = {
    menuEntry = "Play Random Anim/Once",
	agent = {
		type = { "humanoid" }
	},
	target = {
		type = { "oneself" }
	},
	tags = "macro, micro, animation"
}

function PlayRandomAnimOnce:Start()
	if not globals["animRand"] then math.randomseed(tonumber(os.time()) + self.agent.id) globals["animRand"] = true end
	SetAnim(self)
	PlayAnim(self)
end

function PlayRandomAnimOnce:Update()
	if not self.agent.ai.IsActionActive() then
		self.agent.ai.SetBehavior("Idle")
	end
end

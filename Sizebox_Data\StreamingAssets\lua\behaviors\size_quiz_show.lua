local Quiz = RegisterBeh<PERSON>or("Size Quiz Show")
Quiz.data = {
    menuEntry = "Size Quiz Show",
    agent = { type = { "giantess" } },
    secondary = true
}

local running = false
local timer = 0
local interval = 7.0
local toast
local questionToast
local audioSource
local soundList = {
    "GaspMoan001_Mmm.ogg",
    "GaspMoan002_Augh_MidLong.ogg",
    "GaspMoan003_Ahh_MidLong.ogg"
}

local questions = {
    { q = "Largest planet?  J) Earth  K) Jupiter  L) Mars", a = "k" },
    { q = "Mix red+blue?  J) Purple  K) Green  L) Orange", a = "j" },
    { q = "Which is a mammal?  J) Shark  K) Dolphin  L) Eagle", a = "k" },
    { q = "2 + 2?  J) 3  K) 4  L) 5", a = "k" },
    { q = "Which is a fruit?  J) Carrot  K) Potato  L) Banana", a = "l" },
    { q = "Plants breathe?  J) Oxygen  K) Carbon Dioxide  L) Nitrogen", a = "k" },
    { q = "Fastest land animal?  J) Cheetah  K) Lion  L) Horse", a = "j" },
    { q = "What is H2O?  J) Salt  K) Water  L) Sugar", a = "k" },
    { q = "Prime number?  J) 9  K) 15  L) 13", a = "l" },
    { q = "Which is a continent?  J) Africa  K) Hawaii  L) Greenland", a = "j" },
    { q = "Which is a metal?  J) Iron  K) Sand  L) Wood", a = "j" },
    { q = "Which is a bird?  J) Whale  K) Eagle  L) Snake", a = "k" },
    { q = "Which is a vegetable?  J) Apple  K) Lettuce  L) Peach", a = "k" },
    { q = "Which is a planet?  J) Pluto  K) Moon  L) Venus", a = "l" },
    { q = "Which is a color?  J) Triangle  K) Blue  L) Circle", a = "k" },
    { q = "Which is a shape?  J) Square  K) Red  L) Loud", a = "j" },
    { q = "Which is a mammal?  J) Frog  K) Cat  L) Lizard", a = "k" },
    { q = "Which is a liquid?  J) Ice  K) Steam  L) Water", a = "l" },
    { q = "Which is a country?  J) Paris  K) Canada  L) Asia", a = "k" },
    { q = "Which is a tool?  J) Hammer  K) Banana  L) Cloud", a = "j" },
    { q = "Largest ocean?  J) Atlantic  K) Indian  L) Pacific", a = "l" },
    { q = "Tallest animal?  J) Elephant  K) Giraffe  L) Lion", a = "k" },
    { q = "Smallest prime?  J) 1  K) 2  L) 3", a = "k" },
    { q = "Which is a reptile?  J) Crocodile  K) Whale  L) Cow", a = "j" },
    { q = "Which is a programming language?  J) Python  K) Snake  L) Lizard", a = "j" },
    { q = "Which is a month?  J) Monday  K) July  L) Winter", a = "k" },
    { q = "Which is a gas?  J) Water  K) Oxygen  L) Salt", a = "k" },
    { q = "Which is a bone?  J) Femur  K) Liver  L) Heart", a = "j" },
    { q = "Which is a planet?  J) Sun  K) Mercury  L) Luna", a = "k" },
    { q = "Which is a flower?  J) Rose  K) Oak  L) Pine", a = "j" },
    { q = "Which is a bird?  J) Penguin  K) Bat  L) Cat", a = "j" },
    { q = "Which is a beverage?  J) Juice  K) Bread  L) Rice", a = "j" },
    { q = "Which is a fish?  J) Salmon  K) Frog  L) Duck", a = "j" },
    { q = "Which is a continent?  J) Europe  K) Tokyo  L) Canada", a = "j" },
    { q = "Which is a fruit?  J) Tomato  K) Lettuce  L) Potato", a = "j" },
    { q = "Which is a planet?  J) Mars  K) Pluto  L) Sun", a = "j" },
    { q = "Which is a mammal?  J) Dolphin  K) Shark  L) Tuna", a = "j" },
    { q = "Which is a vegetable?  J) Broccoli  K) Apple  L) Banana", a = "j" },
    { q = "Which is a color?  J) Green  K) Table  L) Chair", a = "j" },
    { q = "Which is a shape?  J) Circle  K) Blue  L) Fast", a = "j" },
    { q = "Which is a tool?  J) Screwdriver  K) Apple  L) Banana", a = "j" },
    { q = "Which is a country?  J) Mexico  K) Africa  L) Paris", a = "j" },
    { q = "Which is a planet?  J) Saturn  K) Sun  L) Moon", a = "j" },
    { q = "Which is a mammal?  J) Bat  K) Eagle  L) Snake", a = "j" },
    { q = "Which is a vegetable?  J) Carrot  K) Chicken  L) Beef", a = "j" },
    { q = "Which is a fruit?  J) Grape  K) Lettuce  L) Potato", a = "j" },
    { q = "Which is a gas?  J) Nitrogen  K) Water  L) Salt", a = "j" },
    { q = "Which is a beverage?  J) Milk  K) Bread  L) Rice", a = "j" },
    { q = "Which is a flower?  J) Tulip  K) Pine  L) Oak", a = "j" },
    { q = "Which is a bird?  J) Sparrow  K) Cat  L) Dog", a = "j" },
    { q = "Which is a continent?  J) Asia  K) Paris  L) Tokyo", a = "j" },
    { q = "Which is a planet?  J) Neptune  K) Sun  L) Moon", a = "j" },
    { q = "Which is a mammal?  J) Human  K) Lizard  L) Snake", a = "j" },
    { q = "Which is a vegetable?  J) Spinach  K) Apple  L) Banana", a = "j" },
    { q = "Which is a fruit?  J) Orange  K) Lettuce  L) Potato", a = "j" },
    { q = "Which is a gas?  J) Helium  K) Water  L) Salt", a = "j" },
    { q = "Which is a beverage?  J) Tea  K) Bread  L) Rice", a = "j" },
    { q = "Which is a flower?  J) Daisy  K) Pine  L) Oak", a = "j" },
    { q = "Which is a bird?  J) Robin  K) Cat  L) Dog", a = "j" },
    { q = "Which is a continent?  J) Australia  K) Paris  L) Tokyo", a = "j" }
}

local currentQuestion = nil
local awaitingAnswer = false
local showQuestionDelay = 0
local waitingToShowQuestion = false

function Quiz:Start()
    if not toast then toast = Game.Toast.New() end
    if not questionToast then questionToast = Game.Toast.New() end
    audioSource = AudioSource:new(self.agent.bones.spine)
    audioSource.spatialBlend = 1
    audioSource.loop = false
    audioSource.volume = 1
    running = false
    timer = 0
    awaitingAnswer = false
    waitingToShowQuestion = false
    toast.Print("Press Q to start/stop the Size Quiz Show!")
    questionToast.Print("")
end

function askQuestion()
    currentQuestion = questions[math.random(1, #questions)]
    toast.Print("QUIZ TIME!")
    questionToast.Print("")
    showQuestionDelay = 1.2 -- seconds
    waitingToShowQuestion = true
end

function Quiz:Update()
    if Input.GetKeyDown("q") then
        running = not running
        timer = 0
        awaitingAnswer = false
        waitingToShowQuestion = false
        toast.Print(running and "Quiz Show started!" or "Quiz Show stopped!")
        questionToast.Print("")
        if running then
            askQuestion()
        end
        return
    end

    if not running then return end

    timer = timer + Time.deltaTime
    if timer >= interval then
        timer = 0
        if awaitingAnswer then
            -- Check if the player has answered
            local playerAnswer = string.lower(Input.GetKeyDown())
            if playerAnswer == currentQuestion.a then
                toast.Print("Correct!")
            else
                toast.Print("Wrong! The correct answer was " .. currentQuestion.a)
            end
            awaitingAnswer = false
            questionToast.Print("")
            -- Wait before showing the next question
            Timer.After(2, function() askQuestion() end)
        elseif waitingToShowQuestion then
            -- Show the question after a delay
            questionToast.Print(currentQuestion.q)
            awaitingAnswer = true
            waitingToShowQuestion = false
        else
            -- Just in case, ask a question
            askQuestion()
        end
    end

    if awaitingAnswer then
        if Input.GetKeyDown("j") or Input.GetKeyDown("k") or Input.GetKeyDown("l") then
            local answer = nil
            if Input.GetKeyDown("j") then answer = "j"
            elseif Input.GetKeyDown("k") then answer = "k"
            elseif Input.GetKeyDown("l") then answer = "l"
            end

            local scale = self.agent.scale or self.agent.localScale or 1.0
            if answer == currentQuestion.a then
                local newScale = scale + 50
                if self.agent.scale ~= nil then
                    self.agent.scale = newScale
                elseif self.agent.localScale ~= nil then
                    self.agent.localScale = newScale
                end
                audioSource.clip = soundList[math.random(1, #soundList)]
                audioSource:Play()
                toast.Print("Correct! GROW +50!\nNext question soon...")
            else
                local newScale = math.max(0.1, scale - 30)
                if self.agent.scale ~= nil then
                    self.agent.scale = newScale
                elseif self.agent.localScale ~= nil then
                    self.agent.localScale = newScale
                end
                toast.Print("Wrong! SHRINK -30!\nNext question soon...")
            end
            questionToast.Print("")
            awaitingAnswer = false
            timer = 0
            return
        end
    end
end
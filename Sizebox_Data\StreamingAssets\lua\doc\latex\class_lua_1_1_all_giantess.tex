\hypertarget{class_lua_1_1_all_giantess}{}\section{Lua.\+All\+Giantess Class Reference}
\label{class_lua_1_1_all_giantess}\index{Lua.AllGiantess@{Lua.AllGiantess}}


A class containing settings affecting all giantesses.  


\subsection*{Properties}
\begin{DoxyCompactItemize}
\item 
float \mbox{\hyperlink{class_lua_1_1_all_giantess_a76e7e6cca768273ffd53a6b8a3127ffe}{global\+Speed}}\hspace{0.3cm}{\ttfamily  \mbox{[}get, set\mbox{]}}
\begin{DoxyCompactList}\small\item\em Global giantess speed. \end{DoxyCompactList}\item 
float \mbox{\hyperlink{class_lua_1_1_all_giantess_af82af573b560f17c3b0cd575bd61ed00}{max\+Size}}\hspace{0.3cm}{\ttfamily  \mbox{[}get, set\mbox{]}}
\begin{DoxyCompactList}\small\item\em Maximal giantess size. \end{DoxyCompactList}\item 
float \mbox{\hyperlink{class_lua_1_1_all_giantess_a4852dd60230117796328807f41509dbf}{min\+Size}}\hspace{0.3cm}{\ttfamily  \mbox{[}get, set\mbox{]}}
\begin{DoxyCompactList}\small\item\em Minimal giantess size. \end{DoxyCompactList}\item 
I\+Dictionary$<$ int, \mbox{\hyperlink{class_lua_1_1_entity}{Entity}} $>$ \mbox{\hyperlink{class_lua_1_1_all_giantess_adc5824c47c4090e1d8769ddde5c9da6a}{list}}\hspace{0.3cm}{\ttfamily  \mbox{[}get\mbox{]}}
\begin{DoxyCompactList}\small\item\em List of all giantesses currently on map. \end{DoxyCompactList}\end{DoxyCompactItemize}


\subsection{Detailed Description}
A class containing settings affecting all giantesses. 

Accessible through the {\ttfamily gts} global variable. 

\subsection{Property Documentation}
\mbox{\Hypertarget{class_lua_1_1_all_giantess_a76e7e6cca768273ffd53a6b8a3127ffe}\label{class_lua_1_1_all_giantess_a76e7e6cca768273ffd53a6b8a3127ffe}} 
\index{Lua.AllGiantess@{Lua.AllGiantess}!globalSpeed@{globalSpeed}}
\index{globalSpeed@{globalSpeed}!Lua.AllGiantess@{Lua.AllGiantess}}
\subsubsection{\texorpdfstring{globalSpeed}{globalSpeed}}
{\footnotesize\ttfamily float Lua.\+All\+Giantess.\+global\+Speed\hspace{0.3cm}{\ttfamily [get]}, {\ttfamily [set]}}



Global giantess speed. 

\mbox{\Hypertarget{class_lua_1_1_all_giantess_adc5824c47c4090e1d8769ddde5c9da6a}\label{class_lua_1_1_all_giantess_adc5824c47c4090e1d8769ddde5c9da6a}} 
\index{Lua.AllGiantess@{Lua.AllGiantess}!list@{list}}
\index{list@{list}!Lua.AllGiantess@{Lua.AllGiantess}}
\subsubsection{\texorpdfstring{list}{list}}
{\footnotesize\ttfamily I\+Dictionary$<$int, \mbox{\hyperlink{class_lua_1_1_entity}{Entity}}$>$ Lua.\+All\+Giantess.\+list\hspace{0.3cm}{\ttfamily [get]}}



List of all giantesses currently on map. 

\mbox{\Hypertarget{class_lua_1_1_all_giantess_af82af573b560f17c3b0cd575bd61ed00}\label{class_lua_1_1_all_giantess_af82af573b560f17c3b0cd575bd61ed00}} 
\index{Lua.AllGiantess@{Lua.AllGiantess}!maxSize@{maxSize}}
\index{maxSize@{maxSize}!Lua.AllGiantess@{Lua.AllGiantess}}
\subsubsection{\texorpdfstring{maxSize}{maxSize}}
{\footnotesize\ttfamily float Lua.\+All\+Giantess.\+max\+Size\hspace{0.3cm}{\ttfamily [get]}, {\ttfamily [set]}}



Maximal giantess size. 

\mbox{\Hypertarget{class_lua_1_1_all_giantess_a4852dd60230117796328807f41509dbf}\label{class_lua_1_1_all_giantess_a4852dd60230117796328807f41509dbf}} 
\index{Lua.AllGiantess@{Lua.AllGiantess}!minSize@{minSize}}
\index{minSize@{minSize}!Lua.AllGiantess@{Lua.AllGiantess}}
\subsubsection{\texorpdfstring{minSize}{minSize}}
{\footnotesize\ttfamily float Lua.\+All\+Giantess.\+min\+Size\hspace{0.3cm}{\ttfamily [get]}, {\ttfamily [set]}}



Minimal giantess size. 



The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
Unity\+Proxies.\+cs\end{DoxyCompactItemize}

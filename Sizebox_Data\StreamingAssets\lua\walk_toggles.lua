--v1.2 13/04/21
function Start()
	wAnim = {
	"Walk", "Female Walk", "Walk 2", "Walk Sexy 70AP", "Walk Sexy Dainty 50AP", "Walk Happy 50AP",			 --Walks row 1
	"Walk Happy 100AP", "Walk Careful 50AP", "Walk M Strut 65AP", "Walk Swagger 70AP", "Walk Sad 60AP",		 --Walks row 2	
	"Fast Run", "Run", "Run Forward", "Running", "Slow Run", "Goofy Running", --Runs
	"Crawl", "Standing Jump", "Swimming" --Others
	}
	w = 1
	globals["run"] = false
    globals["walk"] = wAnim[w]
	globals["walkspeed"] = 0.8
end

function Update()
	if Input.GetKeyDown("right alt") then
		if w < 11 then
			w = w + 1
		else
			w = 1
		end
		globals["walk"] = wAnim[w]
		globals["run"] = false
		Log("["..w.."] - "..globals["walk"])
	elseif Input.GetKeyDown("right ctrl") then
		if w > 11 and w < 17 then
			w = w + 1
		else
			w = 12
		end
		globals["walk"] = wAnim[w]
		globals["run"] = true
		Log("["..w.."] - "..globals["walk"])
	elseif Input.GetKeyDown("left") then
		if w > 17 and w < 20 then
			w = w + 1
		else
			w = 18
		end
		globals["walk"] = wAnim[w]
		globals["run"] = false
		if w == 19 then
			Log("["..w.."] - Mr. Mime Crawl")
		else
			Log("["..w.."] - "..globals["walk"])
		end
	end
end
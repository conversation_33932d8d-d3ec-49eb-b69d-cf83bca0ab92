<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.Bones Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_bones.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="class_lua_1_1_bones-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.Bones Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Access bone transforms of humanoid characters.  
 <a href="class_lua_1_1_bones.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:abc25d3c71e0a41ce1a65407f6ac77bca"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_transform.html">Transform</a>[]&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_bones.html#abc25d3c71e0a41ce1a65407f6ac77bca">GetBonesByName</a> (string name, bool partial=true)</td></tr>
<tr class="memdesc:abc25d3c71e0a41ce1a65407f6ac77bca"><td class="mdescLeft">&#160;</td><td class="mdescRight">Find all bones with this name  <a href="class_lua_1_1_bones.html#abc25d3c71e0a41ce1a65407f6ac77bca">More...</a><br /></td></tr>
<tr class="separator:abc25d3c71e0a41ce1a65407f6ac77bca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a13cba4cdb3e2503912cc6eb9a9b9f187"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_bones.html#a13cba4cdb3e2503912cc6eb9a9b9f187">GetBoneByName</a> (string name, bool partial=true)</td></tr>
<tr class="memdesc:a13cba4cdb3e2503912cc6eb9a9b9f187"><td class="mdescLeft">&#160;</td><td class="mdescRight">Find a bone with this name  <a href="class_lua_1_1_bones.html#a13cba4cdb3e2503912cc6eb9a9b9f187">More...</a><br /></td></tr>
<tr class="separator:a13cba4cdb3e2503912cc6eb9a9b9f187"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="properties" name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a6ee9efaf692471552da3f885987361ca"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_bones.html#a6ee9efaf692471552da3f885987361ca">head</a><code> [get]</code></td></tr>
<tr class="memdesc:a6ee9efaf692471552da3f885987361ca"><td class="mdescLeft">&#160;</td><td class="mdescRight">Head Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a>  <a href="class_lua_1_1_bones.html#a6ee9efaf692471552da3f885987361ca">More...</a><br /></td></tr>
<tr class="separator:a6ee9efaf692471552da3f885987361ca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaf90bd7cb3fbd050f890e985d7a8a160"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_bones.html#aaf90bd7cb3fbd050f890e985d7a8a160">hips</a><code> [get]</code></td></tr>
<tr class="memdesc:aaf90bd7cb3fbd050f890e985d7a8a160"><td class="mdescLeft">&#160;</td><td class="mdescRight">Hips Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a>  <a href="class_lua_1_1_bones.html#aaf90bd7cb3fbd050f890e985d7a8a160">More...</a><br /></td></tr>
<tr class="separator:aaf90bd7cb3fbd050f890e985d7a8a160"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a28da7e69ef1c810bd255236404ad039a"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_bones.html#a28da7e69ef1c810bd255236404ad039a">spine</a><code> [get]</code></td></tr>
<tr class="memdesc:a28da7e69ef1c810bd255236404ad039a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Spine Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a>  <a href="class_lua_1_1_bones.html#a28da7e69ef1c810bd255236404ad039a">More...</a><br /></td></tr>
<tr class="separator:a28da7e69ef1c810bd255236404ad039a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3a134608353ae7791f13f2cce227ce7b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_bones.html#a3a134608353ae7791f13f2cce227ce7b">leftUpperArm</a><code> [get]</code></td></tr>
<tr class="memdesc:a3a134608353ae7791f13f2cce227ce7b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Left Upper Arm Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a>  <a href="class_lua_1_1_bones.html#a3a134608353ae7791f13f2cce227ce7b">More...</a><br /></td></tr>
<tr class="separator:a3a134608353ae7791f13f2cce227ce7b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac1c0091627a3dda698685dd84d8b6e6a"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_bones.html#ac1c0091627a3dda698685dd84d8b6e6a">leftLowerArm</a><code> [get]</code></td></tr>
<tr class="memdesc:ac1c0091627a3dda698685dd84d8b6e6a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Left Lower Arm Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a>  <a href="class_lua_1_1_bones.html#ac1c0091627a3dda698685dd84d8b6e6a">More...</a><br /></td></tr>
<tr class="separator:ac1c0091627a3dda698685dd84d8b6e6a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3242c39368adafc9965e274f49d63283"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_bones.html#a3242c39368adafc9965e274f49d63283">leftHand</a><code> [get]</code></td></tr>
<tr class="memdesc:a3242c39368adafc9965e274f49d63283"><td class="mdescLeft">&#160;</td><td class="mdescRight">Left Hand Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a>  <a href="class_lua_1_1_bones.html#a3242c39368adafc9965e274f49d63283">More...</a><br /></td></tr>
<tr class="separator:a3242c39368adafc9965e274f49d63283"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae5b640e142fcbe64ab63218e48bc5271"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_bones.html#ae5b640e142fcbe64ab63218e48bc5271">rightUpperArm</a><code> [get]</code></td></tr>
<tr class="memdesc:ae5b640e142fcbe64ab63218e48bc5271"><td class="mdescLeft">&#160;</td><td class="mdescRight">Right Upper Arm Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a>  <a href="class_lua_1_1_bones.html#ae5b640e142fcbe64ab63218e48bc5271">More...</a><br /></td></tr>
<tr class="separator:ae5b640e142fcbe64ab63218e48bc5271"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa765e8509188018d417bfae9b678f4b7"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_bones.html#aa765e8509188018d417bfae9b678f4b7">rightLowerArm</a><code> [get]</code></td></tr>
<tr class="memdesc:aa765e8509188018d417bfae9b678f4b7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Right Lower Arm Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a>  <a href="class_lua_1_1_bones.html#aa765e8509188018d417bfae9b678f4b7">More...</a><br /></td></tr>
<tr class="separator:aa765e8509188018d417bfae9b678f4b7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a33f155d4f1abdedd2c8f514fc9f768dd"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_bones.html#a33f155d4f1abdedd2c8f514fc9f768dd">rightHand</a><code> [get]</code></td></tr>
<tr class="memdesc:a33f155d4f1abdedd2c8f514fc9f768dd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Right Hand Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a>  <a href="class_lua_1_1_bones.html#a33f155d4f1abdedd2c8f514fc9f768dd">More...</a><br /></td></tr>
<tr class="separator:a33f155d4f1abdedd2c8f514fc9f768dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2192099d2fee781ee13c3591caa8c78c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_bones.html#a2192099d2fee781ee13c3591caa8c78c">rightUpperLeg</a><code> [get]</code></td></tr>
<tr class="memdesc:a2192099d2fee781ee13c3591caa8c78c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Right Upper Leg Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a>  <a href="class_lua_1_1_bones.html#a2192099d2fee781ee13c3591caa8c78c">More...</a><br /></td></tr>
<tr class="separator:a2192099d2fee781ee13c3591caa8c78c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7322bb4e06909a72de8cb7899bbdda28"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_bones.html#a7322bb4e06909a72de8cb7899bbdda28">rightLowerLeg</a><code> [get]</code></td></tr>
<tr class="memdesc:a7322bb4e06909a72de8cb7899bbdda28"><td class="mdescLeft">&#160;</td><td class="mdescRight">Right Lower Leg Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a>  <a href="class_lua_1_1_bones.html#a7322bb4e06909a72de8cb7899bbdda28">More...</a><br /></td></tr>
<tr class="separator:a7322bb4e06909a72de8cb7899bbdda28"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae0707d3e06ec285d2d4634b26b046bbe"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_bones.html#ae0707d3e06ec285d2d4634b26b046bbe">rightFoot</a><code> [get]</code></td></tr>
<tr class="memdesc:ae0707d3e06ec285d2d4634b26b046bbe"><td class="mdescLeft">&#160;</td><td class="mdescRight">Right Foot Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a>  <a href="class_lua_1_1_bones.html#ae0707d3e06ec285d2d4634b26b046bbe">More...</a><br /></td></tr>
<tr class="separator:ae0707d3e06ec285d2d4634b26b046bbe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a260c8638158d2f0580c1275351f4a1f2"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_bones.html#a260c8638158d2f0580c1275351f4a1f2">leftUpperLeg</a><code> [get]</code></td></tr>
<tr class="memdesc:a260c8638158d2f0580c1275351f4a1f2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Left Upper Leg Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a>  <a href="class_lua_1_1_bones.html#a260c8638158d2f0580c1275351f4a1f2">More...</a><br /></td></tr>
<tr class="separator:a260c8638158d2f0580c1275351f4a1f2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acade1832236cb1887d8d4e30af54fbef"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_bones.html#acade1832236cb1887d8d4e30af54fbef">leftLowerLeg</a><code> [get]</code></td></tr>
<tr class="memdesc:acade1832236cb1887d8d4e30af54fbef"><td class="mdescLeft">&#160;</td><td class="mdescRight">Left Lower Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a>  <a href="class_lua_1_1_bones.html#acade1832236cb1887d8d4e30af54fbef">More...</a><br /></td></tr>
<tr class="separator:acade1832236cb1887d8d4e30af54fbef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6ee2d4c0e55ea06ca64e086680d87331"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_bones.html#a6ee2d4c0e55ea06ca64e086680d87331">leftFoot</a><code> [get]</code></td></tr>
<tr class="memdesc:a6ee2d4c0e55ea06ca64e086680d87331"><td class="mdescLeft">&#160;</td><td class="mdescRight">Left Foot Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a>  <a href="class_lua_1_1_bones.html#a6ee2d4c0e55ea06ca64e086680d87331">More...</a><br /></td></tr>
<tr class="separator:a6ee2d4c0e55ea06ca64e086680d87331"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >Access bone transforms of humanoid characters. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a13cba4cdb3e2503912cc6eb9a9b9f187" name="a13cba4cdb3e2503912cc6eb9a9b9f187"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a13cba4cdb3e2503912cc6eb9a9b9f187">&#9670;&nbsp;</a></span>GetBoneByName()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_transform.html">Transform</a> Lua.Bones.GetBoneByName </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>name</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>partial</em> = <code>true</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Find a bone with this name </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">name</td><td>The name of the bone to lookup</td></tr>
    <tr><td class="paramname">partial</td><td>Allow partial, case-insensitive matches</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a> of a bone if it was found or nil if a bone wasn't found</dd></dl>
<p >These lookup functions are relatively slow, avoid calling in Update routines</p>

</div>
</div>
<a id="abc25d3c71e0a41ce1a65407f6ac77bca" name="abc25d3c71e0a41ce1a65407f6ac77bca"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abc25d3c71e0a41ce1a65407f6ac77bca">&#9670;&nbsp;</a></span>GetBonesByName()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_transform.html">Transform</a>[] Lua.Bones.GetBonesByName </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>name</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>partial</em> = <code>true</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Find all bones with this name </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">name</td><td>The name of the bones to lookup</td></tr>
    <tr><td class="paramname">partial</td><td>Allow partial, case-insensitive matches</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>A <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a> array of a bones if they where found or nil if no bones where found</dd></dl>
<p >These lookup functions are relatively slow, avoid calling in Update routines</p>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="a6ee9efaf692471552da3f885987361ca" name="a6ee9efaf692471552da3f885987361ca"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6ee9efaf692471552da3f885987361ca">&#9670;&nbsp;</a></span>head</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_transform.html">Transform</a> Lua.Bones.head</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Head Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a> </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="aaf90bd7cb3fbd050f890e985d7a8a160" name="aaf90bd7cb3fbd050f890e985d7a8a160"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaf90bd7cb3fbd050f890e985d7a8a160">&#9670;&nbsp;</a></span>hips</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_transform.html">Transform</a> Lua.Bones.hips</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Hips Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a> </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a6ee2d4c0e55ea06ca64e086680d87331" name="a6ee2d4c0e55ea06ca64e086680d87331"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6ee2d4c0e55ea06ca64e086680d87331">&#9670;&nbsp;</a></span>leftFoot</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_transform.html">Transform</a> Lua.Bones.leftFoot</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Left Foot Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a> </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a3242c39368adafc9965e274f49d63283" name="a3242c39368adafc9965e274f49d63283"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3242c39368adafc9965e274f49d63283">&#9670;&nbsp;</a></span>leftHand</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_transform.html">Transform</a> Lua.Bones.leftHand</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Left Hand Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a> </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ac1c0091627a3dda698685dd84d8b6e6a" name="ac1c0091627a3dda698685dd84d8b6e6a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac1c0091627a3dda698685dd84d8b6e6a">&#9670;&nbsp;</a></span>leftLowerArm</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_transform.html">Transform</a> Lua.Bones.leftLowerArm</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Left Lower Arm Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a> </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="acade1832236cb1887d8d4e30af54fbef" name="acade1832236cb1887d8d4e30af54fbef"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acade1832236cb1887d8d4e30af54fbef">&#9670;&nbsp;</a></span>leftLowerLeg</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_transform.html">Transform</a> Lua.Bones.leftLowerLeg</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Left Lower Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a> </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a3a134608353ae7791f13f2cce227ce7b" name="a3a134608353ae7791f13f2cce227ce7b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3a134608353ae7791f13f2cce227ce7b">&#9670;&nbsp;</a></span>leftUpperArm</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_transform.html">Transform</a> Lua.Bones.leftUpperArm</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Left Upper Arm Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a> </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a260c8638158d2f0580c1275351f4a1f2" name="a260c8638158d2f0580c1275351f4a1f2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a260c8638158d2f0580c1275351f4a1f2">&#9670;&nbsp;</a></span>leftUpperLeg</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_transform.html">Transform</a> Lua.Bones.leftUpperLeg</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Left Upper Leg Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a> </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ae0707d3e06ec285d2d4634b26b046bbe" name="ae0707d3e06ec285d2d4634b26b046bbe"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae0707d3e06ec285d2d4634b26b046bbe">&#9670;&nbsp;</a></span>rightFoot</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_transform.html">Transform</a> Lua.Bones.rightFoot</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Right Foot Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a> </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a33f155d4f1abdedd2c8f514fc9f768dd" name="a33f155d4f1abdedd2c8f514fc9f768dd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a33f155d4f1abdedd2c8f514fc9f768dd">&#9670;&nbsp;</a></span>rightHand</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_transform.html">Transform</a> Lua.Bones.rightHand</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Right Hand Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a> </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="aa765e8509188018d417bfae9b678f4b7" name="aa765e8509188018d417bfae9b678f4b7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa765e8509188018d417bfae9b678f4b7">&#9670;&nbsp;</a></span>rightLowerArm</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_transform.html">Transform</a> Lua.Bones.rightLowerArm</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Right Lower Arm Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a> </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a7322bb4e06909a72de8cb7899bbdda28" name="a7322bb4e06909a72de8cb7899bbdda28"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7322bb4e06909a72de8cb7899bbdda28">&#9670;&nbsp;</a></span>rightLowerLeg</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_transform.html">Transform</a> Lua.Bones.rightLowerLeg</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Right Lower Leg Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a> </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ae5b640e142fcbe64ab63218e48bc5271" name="ae5b640e142fcbe64ab63218e48bc5271"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae5b640e142fcbe64ab63218e48bc5271">&#9670;&nbsp;</a></span>rightUpperArm</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_transform.html">Transform</a> Lua.Bones.rightUpperArm</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Right Upper Arm Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a> </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a2192099d2fee781ee13c3591caa8c78c" name="a2192099d2fee781ee13c3591caa8c78c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2192099d2fee781ee13c3591caa8c78c">&#9670;&nbsp;</a></span>rightUpperLeg</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_transform.html">Transform</a> Lua.Bones.rightUpperLeg</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Right Upper Leg Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a> </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a28da7e69ef1c810bd255236404ad039a" name="a28da7e69ef1c810bd255236404ad039a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a28da7e69ef1c810bd255236404ad039a">&#9670;&nbsp;</a></span>spine</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_transform.html">Transform</a> Lua.Bones.spine</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Spine Bone <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a> </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaBones.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_bones.html">Bones</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

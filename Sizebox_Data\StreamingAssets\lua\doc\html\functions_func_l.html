<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Class Members - Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('functions_func_l.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a id="index_l" name="index_l"></a>- l -</h3><ul>
<li>Lerp()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a659f0bf0690e5056165eb8bd958d6751">Lua.Mathf</a>, <a class="el" href="class_lua_1_1_quaternion.html#a451a68530c7d148d83024edf4bb79e26">Lua.Quaternion</a>, <a class="el" href="class_lua_1_1_vector3.html#a2ac180084d2490e519612ccba40da454">Lua.Vector3</a></li>
<li>LerpAngle()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a2363a79cc48061f10c4e7e1b47df2538">Lua.Mathf</a></li>
<li>LerpUnclamped()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a2707664a0c93b38cece4445ee6750709">Lua.Mathf</a>, <a class="el" href="class_lua_1_1_quaternion.html#ad43d2f3aa2d460ed567351f97aba6bfe">Lua.Quaternion</a>, <a class="el" href="class_lua_1_1_vector3.html#a63b62cb18ab91477aee5c9bd0d975400">Lua.Vector3</a></li>
<li>Log()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a9d1e276c7cfc8fe9f902ebda005e04e1">Lua.Mathf</a></li>
<li>Log10()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#afcdb61fd1acfbe37c5e7a675421f3dc9">Lua.Mathf</a></li>
<li>LookAt()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a29cdb052c5422873a708c8080039cb4b">Lua.Entity</a>, <a class="el" href="class_lua_1_1_transform.html#a1e722de9c3eacff82477ab7684a67553">Lua.Transform</a></li>
<li>LookRotation()&#160;:&#160;<a class="el" href="class_lua_1_1_quaternion.html#a7a17f92fd9d83a8b472db78d5cb74642">Lua.Quaternion</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

#!/usr/bin/env python3
"""
Input Analysis Patcher for Sizebox
Looks for Unity Input.GetKeyDown patterns specifically
"""

import os
import shutil
import re

def backup_file(source, backup_dir):
    """Create a backup of the original file"""
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    
    backup_path = os.path.join(backup_dir, os.path.basename(source) + '.input_backup')
    shutil.copy2(source, backup_path)
    print(f"Backup created: {backup_path}")
    return backup_path

def analyze_input_patterns():
    """Analyze the DLL for input patterns"""
    dll_path = r"Sizebox_Data\Managed\Assembly-CSharp.dll"
    
    print("Input Pattern Analysis for Sizebox")
    print("==================================")
    
    # Load the DLL
    with open(dll_path, 'rb') as f:
        dll_data = f.read()
    
    print(f"Loaded DLL: {len(dll_data)} bytes")
    
    # Convert to string for analysis (ignore encoding errors)
    try:
        dll_text = dll_data.decode('utf-8', errors='ignore')
    except:
        dll_text = dll_data.decode('latin-1', errors='ignore')
    
    print("\nSearching for input-related patterns...")
    
    # Look for various input patterns
    input_patterns = [
        r'Input\.GetKeyDown\s*\([^)]*\)',
        r'GetKeyDown\s*\([^)]*\)',
        r'KeyCode\.[A-Z][a-zA-Z0-9]*',
        r'Input\.GetKey\s*\([^)]*\)',
        r'GetKey\s*\([^)]*\)',
        r'inputString',
        r'keyCode',
        r'KeyCode',
    ]
    
    all_matches = []
    
    for pattern in input_patterns:
        matches = list(re.finditer(pattern, dll_text, re.IGNORECASE))
        print(f"Pattern '{pattern}': {len(matches)} matches")
        
        for match in matches[:5]:  # Show first 5 matches
            start = max(0, match.start() - 50)
            end = min(len(dll_text), match.end() + 50)
            context = dll_text[start:end].replace('\n', ' ').replace('\r', ' ')
            print(f"  Match: {match.group(0)}")
            print(f"  Context: ...{context}...")
            print()
            
            all_matches.append({
                'pattern': pattern,
                'match': match.group(0),
                'position': match.start(),
                'context': context
            })
    
    # Look specifically for P and O in input contexts
    print("\nSearching for P and O keys in input contexts...")
    
    po_patterns = [
        r'[^a-zA-Z]P[^a-zA-Z].*(?:spawn|micro|character|input)',
        r'[^a-zA-Z]O[^a-zA-Z].*(?:spawn|micro|character|input)',
        r'(?:spawn|micro|character|input).*[^a-zA-Z]P[^a-zA-Z]',
        r'(?:spawn|micro|character|input).*[^a-zA-Z]O[^a-zA-Z]',
        r'KeyCode\.P',
        r'KeyCode\.O',
        r'"P"',
        r'"O"',
    ]
    
    po_matches = []
    
    for pattern in po_patterns:
        matches = list(re.finditer(pattern, dll_text, re.IGNORECASE))
        print(f"P/O Pattern '{pattern}': {len(matches)} matches")
        
        for match in matches[:3]:  # Show first 3 matches
            start = max(0, match.start() - 100)
            end = min(len(dll_text), match.end() + 100)
            context = dll_text[start:end].replace('\n', ' ').replace('\r', ' ')
            print(f"  Match: {match.group(0)}")
            print(f"  Context: ...{context}...")
            print()
            
            po_matches.append({
                'pattern': pattern,
                'match': match.group(0),
                'position': match.start(),
                'context': context
            })
    
    # Look for byte sequences that might represent input arrays
    print("\nSearching for potential input key arrays...")
    
    # Look for sequences like [P, O] or similar
    p_byte = 0x50  # P
    o_byte = 0x4F  # O
    
    # Find places where P and O bytes are close together
    close_po_pairs = []
    
    for i in range(len(dll_data) - 10):
        if dll_data[i] == p_byte:
            # Look for O within next 10 bytes
            for j in range(i + 1, min(i + 11, len(dll_data))):
                if dll_data[j] == o_byte:
                    # Found P followed by O within 10 bytes
                    context_start = max(0, i - 20)
                    context_end = min(len(dll_data), j + 20)
                    context_bytes = dll_data[context_start:context_end]
                    
                    close_po_pairs.append({
                        'p_pos': i,
                        'o_pos': j,
                        'distance': j - i,
                        'context': context_bytes
                    })
    
    print(f"Found {len(close_po_pairs)} P-O byte pairs within 10 bytes of each other")
    
    # Show the most promising pairs
    for pair in close_po_pairs[:5]:
        print(f"  P at 0x{pair['p_pos']:08X}, O at 0x{pair['o_pos']:08X} (distance: {pair['distance']})")
        print(f"  Context: {pair['context'].hex()}")
        print()
    
    # Summary
    print("\n" + "="*50)
    print("ANALYSIS SUMMARY")
    print("="*50)
    print(f"Total input patterns found: {len(all_matches)}")
    print(f"P/O specific patterns found: {len(po_matches)}")
    print(f"Close P-O byte pairs found: {len(close_po_pairs)}")
    
    if po_matches:
        print("\nMost promising P/O matches:")
        for match in po_matches[:3]:
            print(f"  {match['match']} at position {match['position']}")
    
    if close_po_pairs:
        print("\nMost promising P-O byte pairs:")
        for pair in close_po_pairs[:3]:
            print(f"  P at 0x{pair['p_pos']:08X}, O at 0x{pair['o_pos']:08X}")
    
    print("\nThis analysis can help identify where the spawn keys are actually stored.")
    print("The spawn keys might be in:")
    print("1. Input handling code (GetKeyDown patterns)")
    print("2. Key mapping arrays (close P-O byte pairs)")
    print("3. Configuration data (string patterns)")

if __name__ == "__main__":
    try:
        analyze_input_patterns()
    except Exception as e:
        print(f"Error: {e}")
    
    input("Press Enter to exit...")

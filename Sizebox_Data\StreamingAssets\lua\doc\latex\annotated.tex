\section{Class List}
Here are the classes, structs, unions and interfaces with brief descriptions\+:\begin{DoxyCompactList}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_a_i}{Lua.\+AI}} \\*Controls the \mbox{\hyperlink{class_lua_1_1_a_i}{AI}} of humanoid agent }{\pageref{class_lua_1_1_a_i}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_all_giantess}{Lua.\+All\+Giantess}} \\*A class containing settings affecting all giantesses }{\pageref{class_lua_1_1_all_giantess}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_all_micros}{Lua.\+All\+Micros}} \\*A class containing settings affecting all micros }{\pageref{class_lua_1_1_all_micros}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_animation}{Lua.\+Animation}} \\*Component to control the animation for humanoid entities }{\pageref{class_lua_1_1_animation}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_audio_source}{Lua.\+Audio\+Source}} \\*A representation of audio sources in 3D }{\pageref{class_lua_1_1_audio_source}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_bones}{Lua.\+Bones}} \\*Access bone transforms of humanoid characters }{\pageref{class_lua_1_1_bones}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_custom_sound_manager}{Lua.\+Custom\+Sound\+Manager}} \\*An interface to change in-\/game sound effects with custom sounds in the Sounds folder }{\pageref{class_lua_1_1_custom_sound_manager}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_entity}{Lua.\+Entity}} \\*A \mbox{\hyperlink{class_lua_1_1_entity}{Entity}} represents Characters and Objects }{\pageref{class_lua_1_1_entity}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_event}{Lua.\+Event}} \\*Interface of the \mbox{\hyperlink{class_lua_1_1_event}{Event}} system }{\pageref{class_lua_1_1_event}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_event_code}{Event\+Code}} \\*Lists hardcoded engine \mbox{\hyperlink{class_lua_1_1_event}{Lua.\+Event}} names }{\pageref{class_event_code}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_globals}{Lua.\+Globals}} \\*Global dictionary. It can be used to store and exchange arbitrary data between scripts }{\pageref{class_lua_1_1_globals}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_i_k}{Lua.\+IK}} \\*Inverse Kinematics lets you animate individual bones to create procedural animations }{\pageref{class_lua_1_1_i_k}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_i_k_effector}{Lua.\+I\+K\+Effector}} \\*Each effector lets you control one bone and animate the body }{\pageref{class_lua_1_1_i_k_effector}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_input}{Lua.\+Input}} \\*Interface into the \mbox{\hyperlink{class_lua_1_1_input}{Input}} system }{\pageref{class_lua_1_1_input}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_lua_player_raygun}{Lua.\+Lua\+Player\+Raygun}} \\*Use this component to control some elements of the raygun of the player }{\pageref{class_lua_1_1_lua_player_raygun}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_mathf}{Lua.\+Mathf}} \\*A collection of common Unity math functions. Largely overlaps with built-\/in \mbox{\hyperlink{namespace_lua}{Lua}} math library (\href{https://www.lua.org/manual/5.3/manual.html\#6.7}{\texttt{ https\+://www.\+lua.\+org/manual/5.\+3/manual.\+html\#6.\+7}}) }{\pageref{class_lua_1_1_mathf}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_morphs}{Lua.\+Morphs}} \\*Component to control the morphs for giantess entities }{\pageref{class_lua_1_1_morphs}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_movement}{Lua.\+Movement}} \\*Use this component to control the movement of agents }{\pageref{class_lua_1_1_movement}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_player_entity}{Lua.\+Player\+Entity}} \\*A \mbox{\hyperlink{class_lua_1_1_player_entity}{Player\+Entity}} represents player-\/controlled character }{\pageref{class_lua_1_1_player_entity}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_quaternion}{Lua.\+Quaternion}} \\*Quaternions are used to represent rotations }{\pageref{class_lua_1_1_quaternion}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_random}{Lua.\+Random}} \\*Class for generating random data }{\pageref{class_lua_1_1_random}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_rigidbody}{Lua.\+Rigidbody}} \\*Control of an object\textquotesingle{}s position through physics simulation }{\pageref{class_lua_1_1_rigidbody}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_screen}{Lua.\+Screen}} \\*Access to display information }{\pageref{class_lua_1_1_screen}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_senses}{Lua.\+Senses}} \\*Control the senses of a entity such as the vision }{\pageref{class_lua_1_1_senses}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_shooting}{Lua.\+Shooting}} \\*Use this component to control the shooting-\/related and gun properties of agents }{\pageref{class_lua_1_1_shooting}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_time}{Lua.\+Time}} \\*The interface to get time information from Unity }{\pageref{class_lua_1_1_time}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_transform}{Lua.\+Transform}} \\*Position, rotation and scale of an object }{\pageref{class_lua_1_1_transform}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_vector3}{Lua.\+Vector3}} \\*Representation of 3D vectors and points }{\pageref{class_lua_1_1_vector3}}{}
\item\contentsline{section}{\mbox{\hyperlink{class_lua_1_1_world}{Lua.\+World}} \\*A class containing settings affecting the world }{\pageref{class_lua_1_1_world}}{}
\end{DoxyCompactList}

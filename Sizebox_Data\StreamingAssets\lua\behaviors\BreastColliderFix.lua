-- Register the behavior
BE_with_collider_debug = RegisterBehavior("BE with Collider Debug")
BE_with_collider_debug.data = {
    menuEntry = "BE/With Collider Debug",
    flags = { "be" },
    agent = {
        type = { "humanoid" }
    },
    target = {
        type = { "oneself" }
    },
    settings = {
        {"boneNames", "Bone Names", "string", "<PERSON>reast,Ichichi"},
        {"limit", "Limit Size", "float", "3", {"1.2", "8"}},
        {"speed", "Rate of Change", "float", "0.03", {"0.03", "0.2"}}
    }
}

function BE_with_collider_debug:Start()
    -- Find the bones based on user-defined bone names (e.g., "<PERSON><PERSON><PERSON>,<PERSON>chichi")
    self.bones = FindBoneNames(self.agent, self.boneNames)

    if not self.bones then
        Game.Toast.New().Print("No bones found to adjust in model " .. self.agent.name)
        return
    end

    -- Debug: Log available fields and methods on self.agent
    print("Available fields and methods on self.agent:")
    for k, v in pairs(self.agent) do
        print(k, v)
    end

    -- Attempt to access collider (if possible)
    self.collider = self.agent.Collider or nil
    if self.collider then
        Game.Toast.New().Print("Collider found and assigned.")
    else
        Game.Toast.New().Print("Warning: Collider not accessible.")
    end

    Game.Toast.New().Print("BE with Collider Debug initialized!")
end

function BE_with_collider_debug:Update()
    local growAmount = self.speed * Time.deltaTime

    -- Update bones
    for _, bone in ipairs(self.bones) do
        if bone.localScale.y < self.limit then
            bone.localScale = bone.localScale * (1 + growAmount)
        end
    end

    -- Debug: Attempt to refresh the collider
    if self.collider then
        -- Example: Apply manual adjustments to collider (if supported)
        self.collider.localScale = self.bones[1].localScale -- Use the first bone's scale
        self.collider.position = self.bones[1].position -- Use the first bone's position
    elseif self.agent.UpdateMeshCollider then
        -- Fallback: Call UpdateMeshCollider if available
        self.agent:UpdateMeshCollider()
    else
        Game.Toast.New().Print("Warning: Collider adjustments not applied.")
    end
end

function BE_with_collider_debug:Exit()
    Game.Toast.New().Print("BE with Collider Debug stopped.")
end

-- Helper function to find bones by name
function FindBoneNames(entity, boneNames)
    local count = 0
    local foundBones = {}

    for boneName in string.gmatch(boneNames, '([^,]+)') do
        local bones = entity.bones.GetBonesByName(boneName, true)
        if bones then
            for _, bone in ipairs(bones) do
                table.insert(foundBones, bone)
                count = count + 1
            end
        end
    end

    if count > 0 then
        return foundBones
    else
        return nil
    end
end
-- Define the Auto Spurts behavior
local AutoSpurts = RegisterBehavior("Auto Spurts")

-- Define the Auto Spurts data
AutoSpurts.data = {
    menuEntry = "Size/Auto Spurts",
    secondary = true,
    flags = { "grow" },
    agent = { type = { "humanoid" } },
    target = { type = { "oneself" } }
}

-- Define the sound effects
local sFx = {"Rumblev1.wav", "stretchmix.ogg", "stretchmixbig.ogg", "customboing.wav", "customboingbig.wav"}
local growthSounds = {"GaspMoan001_Mmmm.ogg", "GaspMoan003_Ahh_MidLong.ogg", "GaspPain103_Soft_Fast.ogg", "GaspPain104_Soft_Slow.ogg", "GaspMoan005_Mmm_Long.ogg", "GaspMoan002_Augh_MidLong.ogg"}

-- Define the Auto Spurts function
function AutoSpurts:Start()
    -- Starting values
    self.rate = 0.01
    self.length = 1.5
    self.sRate = 1130
    self.respite = 3
    self.growing = true
    self.playing = false
    self.paused = false
    self.boing = false
    self.muted = false
    self.mute = 0
    self.vol0 = false
    self.escaped = false
    self.escaped2 = false
    self.audio_source = AudioSource:new(self.agent.bones.spine)
    self.audio_source.spatialBlend = 1
    self.audio_source.clip = sFx[1]
    self.audio_source.loop = true
    self.audio_source.volume = 1
    
    -- Initialize pulse variables
    self.pulseActive = false
    self.pulseTimer = 0
    self.pulseDuration = 0
    self.pulseInterval = 0
    self.pulseSpeed = 0.05
    self.pulseTargetScale = 0
    self.pulseIntervalTimer = 0
    self.boneNames = "Breast,Ichichi"
    self.limit = 3
    self.speed = 0.03
    self.bones = FindBoneNames(self.agent, self.boneNames)
    self.initiated = false
    self.growthWarning = false
    self.growthWarningTimer = 0
    self.growthWarningDuration = 0
    self.growthTimer = 0
    self.rapidShrinkGrow = false
    self.rapidShrinkGrowTimer = 0
    self.rapidShrinkGrowDuration = 0.5
    self.growthPhase = false
    self.beGrowthTrigger = false
    self.shrinkGrowCycles = 0
    self.maxShrinkGrowCycles = math.random(2, 4) -- Minimum 2 cycles
    self.growthAmount = 0
    self.playedGrowthSound = false
    self.growthSoundPlayed = false
    self.growthRateIndex = 2 -- Default growth rate index
    self.growthRates = {0, 0.005, 0.01} -- Growth rates for F1-F3
    self.stompGrowthEnabled = false -- Stomp growth toggle
end

-- Define the Auto Spurts update function
function AutoSpurts:Update()
    -- Update the character's scale
    if self.growing and not self.paused then
        if self.pulseActive then
            self.agent.scale = math.min(self.agent.scale * (1 + self.pulseSpeed * Time.deltaTime), self.pulseTargetScale)
        else
            self.agent.scale = self.agent.scale * (1 + self.rate * Time.deltaTime)
        end
    end

    -- Check for input
    if Input.GetKeyDown("t") then
        self.paused = not self.paused
    end

    -- To change sounds
    if Input.GetKeyDown(";") then
        self.audio_source.clip = sFx[1]
        self.audio_source.loop = true
        self.boing = false
        if self.playing and not self.paused then
            self.audio_source:Play()
        end
        Log("Sound: Loud Rumble")
    elseif Input.GetKeyDown("h") then
        self.audio_source.clip = sFx[2]
        self.audio_source.loop = true
        self.boing = false
        if self.playing and not self.paused then
            self.audio_source:Play()
        end
        Log("Sound: Stretch Mix")
    elseif Input.GetKeyDown("j") then
        self.audio_source.clip = sFx[3]
        self.audio_source.loop = true
        self.boing = false
        if self.playing and not self.paused then
            self.audio_source:Play()
        end
        Log("Sound: Stretch Mix (Big)")
    elseif Input.GetKeyDown("k") then
        self.audio_source.clip = sFx[4]
        self.audio_source.loop = false
        self.boing = true
        Log("Sound: Boing Spurt")
    elseif Input.GetKeyDown("l") then
        self.audio_source.clip = sFx[5]
        self.audio_source.loop = false
        self.boing = true
        Log("Sound: Boing Spurt (Big)")

    -- Mute toggle
    elseif Input.GetKeyDown("m") then
        self.muted = not self.muted
        if self.muted then
            self.mute = 2
            Log("Sounds muted")
        else
            self.mute = 0
            Log("Sounds unmuted")
        end
    end

    -- If playing game, BUT exitting menu
    if Input.GetKeyDown("escape") then
        self.escaped = not self.escaped
    end

    -- GAME PAUSED: Fade-out and pause sounds
    if self.escaped and self.playing then
        if self.audio_source.volume > 0.001 then
            self.audio_source.volume = self.audio_source.volume * 0.7
        else
            self.vol0 = true
            self.escaped2 = true
        end
    -- GAME RESUMED: Resume sounds playing and fade-in
    elseif not self.escaped and self.escaped2 then
        self.vol0 = false
        self.audio_source:Play()
        self.playing = true
        self.escaped2 = false
    end

    -- Pulse logic
    if not self.pulseActive and self.pulseIntervalTimer == 0 then
        self.pulseActive = true
        self.pulseTimer = 0
        self.pulseDuration = math.random(3, 6) -- Increased pulse duration
        self.pulseInterval = math.random(3, 6) -- Increased pulse interval
        self.pulseTargetScale = self.agent.scale * (1 + self.pulseSpeed * self.pulseDuration)
    elseif self.pulseActive then
        self.pulseTimer = self.pulseTimer + Time.deltaTime
        if self.pulseTimer >= self.pulseDuration then
            self.pulseActive = false
            self.pulseTimer = 0
            self.agent.scale = self.pulseTargetScale
            self.pulseIntervalTimer = self.pulseInterval
        end
    elseif self.pulseIntervalTimer > 0 then
        self.pulseIntervalTimer = self.pulseIntervalTimer - Time.deltaTime
        if self.pulseIntervalTimer <= 0 then
            self.pulseIntervalTimer = 0
        end
    end

    -- Growth timer
    self.growthTimer = self.growthTimer + Time.deltaTime
    if self.growthTimer >= math.random(15, 45) then
        self.growthTimer = 0
        if math.random(1, 100) < 50 then
            self.beGrowthTrigger = true
            self.shrinkGrowCycles = 0
            self.maxShrinkGrowCycles = math.random(2, 4) -- Minimum 2 cycles
            self.growthAmount = 0
            self.playedGrowthSound = false
            self.growthSoundPlayed = false
        end
    end

    -- Rapid shrink and grow logic
    if self.beGrowthTrigger then
        self.rapidShrinkGrow = true
        self.rapidShrinkGrowTimer = 0
        self.beGrowthTrigger = false
    end

    if self.rapidShrinkGrow then
        self.rapidShrinkGrowTimer = self.rapidShrinkGrowTimer + Time.deltaTime
        if self.rapidShrinkGrowTimer < self.rapidShrinkGrowDuration / 2 then
            -- Rapidly shrink
            if self.bones then
                for k,v in ipairs(self.bones) do
                    v.localScale = v.localScale * (1 - 0.2 * Time.deltaTime)
                end
            end
        elseif self.rapidShrinkGrowTimer < self.rapidShrinkGrowDuration then
            -- Rapidly grow
            if self.bones then
                for k,v in ipairs(self.bones) do
                    v.localScale = v.localScale * (1 + 0.2 * Time.deltaTime)
                end
            end
        elseif self.rapidShrinkGrowTimer >= self.rapidShrinkGrowDuration then
            self.rapidShrinkGrow = false
            self.rapidShrinkGrowTimer = 0
            self.shrinkGrowCycles = self.shrinkGrowCycles + 1
            if self.shrinkGrowCycles < self.maxShrinkGrowCycles then
                self.rapidShrinkGrow = true
                self.rapidShrinkGrowTimer = 0
            else
                self.growthPhase = true
            end
        end
    end

    -- Growth phase logic
    if self.growthPhase then
        if self.bones then
            for k,v in ipairs(self.bones) do
                v.localScale = v.localScale * (1 + self.growthRates[self.growthRateIndex] * Time.deltaTime)
            end
        end
        self.growthAmount = self.growthAmount + self.growthRates[self.growthRateIndex] * Time.deltaTime
        if self.growthAmount >= 0.1 then -- Maximum growth amount
            self.growthPhase = false
            self.growthTimer = 0
        end

        -- Play a random sound effect on breast growth
        if not self.growthSoundPlayed then
            local soundIndex = math.random(1, #growthSounds) -- Randomly select a sound effect
            self.audio_source.clip = growthSounds[soundIndex]
            self.audio_source.loop = false
            self.audio_source:Play()
            self.growthSoundPlayed = true
        end
    end

    -- Change growth rate
    if Input.GetKeyDown("f1") then
        self.growthRateIndex = 1
        Log("Growth rate set to slow")
    elseif Input.GetKeyDown("f2") then
        self.growthRateIndex = 2
        Log("Growth rate set to medium")
    elseif Input.GetKeyDown("f3") then
        self.growthRateIndex = 3
        Log("Growth rate set to fast")
    elseif Input.GetKeyDown("f4") then
        self.stompGrowthEnabled = not self.stompGrowthEnabled
        if self.stompGrowthEnabled then
            Log("Stomp growth enabled")
        else
            Log("Stomp growth disabled")
        end
    elseif Input.GetKeyDown("f5") then
        self.beGrowthTrigger = true
        self.shrinkGrowCycles = 0
        self.maxShrinkGrowCycles = math.random(2, 4) -- Minimum 2 cycles
        self.growthAmount = 0
        self.playedGrowthSound = false
        self.growthSoundPlayed = false
        Log("Immediate breast growth pulse triggered")
    end

    -- Check for stomp input
    if Input.GetKeyDown("space") and self.stompGrowthEnabled then
        self.rapidShrinkGrow = true
        self.rapidShrinkGrowTimer = 0
        self.shrinkGrowCycles = 0
        self.maxShrinkGrowCycles = math.random(2, 4) -- Minimum 2 cycles
        self.growthAmount = 0
        self.playedGrowthSound = false
        self.growthSoundPlayed = false
        Log("Breast growth pulse triggered by stomp")
    end
end

function FindBoneNames(entity, boneNames)
    local count = 0
    local foundBones = {}

    for boneName in string.gmatch(boneNames, '([^,]+)') do
        local bones = entity.bones.GetBonesByName(boneName, true)
        if bones then
            for k,v in ipairs(bones) do
                table.insert(foundBones, v)
                count = count + 1
            end
        end
    end

    if count > 0 then
        return foundBones
    else
        return nil
    end
end

SizeLink = RegisterBehavior("size_link")
SizeLink.data = {
    menuEntry = "Size/SizeLink",
    secondary = true,
    flags = { "sizeLink" },
    agent = {
        type = { "humanoid", "giantess", "player", "micro" }
    },
    target = {
        type = { "humanoid", "giantess", "micro" }
    },
}


function SizeLink:Listener(data)

    if Input.GetKeyDown("m") then
	self.spurting = true
	self.stoping = false
	self.shrink = false
    end

    if Input.GetKeyDown("n") then
	self.spurting = true
	self.stoping = false
	self.shrink = true
    end

    if Input.GetKeyDown(",") then
	self.even = true
	self.average = (self.agent.scale + self.target.scale) / 2
	self.spurting = true
	self.stoping = false
	Log("Key Pressed")
    end
    if Input.GetKeyUp("m") or Input.GetKeyUp("n") then
	self.stoping = true
   end
end

function SizeLink:Start()
	self.logToast = Game.Toast.New()
	self.logToast.print("Size Link Started")
	self.agent.dict.Demand = Event.Register(self, EventCode.KeyDown, self.Listener)
        self.agent.dict.Stop = Event.Register(self, EventCode.KeyUp, self.Listener)
	self.mGrowth = 10
	self.spurting = false
	self.stoping = false
	self.rate = 0
	self.shrink = false
	self.runninggrowth = 0
	self.decimal = 0
	self.average = (self.agent.scale + self.target.scale) / 2
	self.even = false

end

function SizeLink:Update()
	if Input.GetKey("u") then
		if Input.GetKeyDown("0") or Input.GetKeyDown("[0]") then
			self:TypeHeight(0)
		elseif Input.GetKeyDown("1") or Input.GetKeyDown("[1]") then
			self:TypeHeight(1)
		elseif Input.GetKeyDown("2") or Input.GetKeyDown("[2]") then
			self:TypeHeight(2)
		elseif Input.GetKeyDown("3") or Input.GetKeyDown("[3]") then
			self:TypeHeight(3)
		elseif Input.GetKeyDown("4") or Input.GetKeyDown("[4]") then
			self:TypeHeight(4)
		elseif Input.GetKeyDown("5") or Input.GetKeyDown("[5]") then
			self:TypeHeight(5)
		elseif Input.GetKeyDown("6") or Input.GetKeyDown("[6]") then
			self:TypeHeight(6)
		elseif Input.GetKeyDown("7") or Input.GetKeyDown("[7]") then
			self:TypeHeight(7)
		elseif Input.GetKeyDown("8") or Input.GetKeyDown("[8]") then
			self:TypeHeight(8)
		elseif Input.GetKeyDown("9") or Input.GetKeyDown("[9]") then
			self:TypeHeight(9)
        elseif Input.GetKeyDown(".") or Input.GetKeyDown("[.]") and self.decimal == 0 then
			self.decimal = 1
		end
	end

	if self.even then
		if self.agent.scale > self.average then
			self.shrink = true
		else
			self.shrink = false
		end
		if self.agent.scale <= self.average + self.mGrowth / 2 and self.agent.scale >= self.target.scale - self.mGrowth / 2 then
			self.stoping = true
			self.even = false
		end
	end
	 if Input.GetKeyUp("u") then
		self.mGrowth = self.runninggrowth
		self.logToast.print(self.mGrowth)
		self.runninggrowth = 0
	end
	if Input.GetKeyDown("=") then
		self.mGrowth = self.mGrowth + 1
		self.logToast.print(self.mGrowth)
	end
	if Input.GetKeyDown("-") then
		self.mGrowth = self.mGrowth - 1
		self.logToast.print(self.mGrowth)
	end
	if self.spurting then	
	if not self.stoping and (self.rate < self.mGrowth) then
	        self.rate = self.rate + ((0.5 * self.mGrowth) * Time.deltaTime)
	end

	if self.stoping then
		self.rate = self.rate - (self.mGrowth * Time.deltaTime)
	end

	if not self.shrink then
            self.agent.scale = self.agent.scale + (self.rate * Time.deltaTime)
	    self.target.scale = self.target.scale - (self.rate * Time.deltaTime)
        else
	    self.agent.scale = self.agent.scale - (self.rate * Time.deltaTime)
	    self.target.scale = self.target.scale + (self.rate * Time.deltaTime)
	end

	if self.rate <= 0 then
	    self.spurting = false
	    self.rate = 0
	end
    end
end

function SizeLink:TypeHeight(int)
	if self.decimal > 0 then
		self.runninggrowth = self.runninggrowth + (int / (10.0 ^ self.decimal))
		self.decimal = self.decimal + 1
	else
		self.runninggrowth = (self.runninggrowth * 10.0) + int
	end
end


SizeLinkStop = RegisterBehavior("size_link_stop")
SizeLinkStop.data = {
    menuEntry = "Size/SizeLinkStop",
    secondary = true,
    flags = { "size" },
    agent = {
        type = { "humanoid" }
    },
    target = {
        type = { "oneself" }
    },
}
function SizeLinkStop:Start()
	self.logToast = Game.Toast.New()
	self.agent.ai.StopSecondaryBehavior("sizeLink")
	self.agent.ai.StopSecondaryBehavior("LinearSize")
	self.logToast.print("Size Link Stoped")

end
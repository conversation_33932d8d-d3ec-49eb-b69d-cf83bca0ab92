<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.Player Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_player.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#properties">Properties</a> &#124;
<a href="class_lua_1_1_player-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.Player Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>A <a class="el" href="class_lua_1_1_player.html" title="A Player represents settings only applicable for a player-controlled character.">Player</a> represents settings only applicable for a player-controlled character.  
 <a href="class_lua_1_1_player.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="properties" name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:abfa8836f5980aeeee9459a69b3dcecf6"><td class="memItemLeft" align="right" valign="top"><a id="abfa8836f5980aeeee9459a69b3dcecf6" name="abfa8836f5980aeeee9459a69b3dcecf6"></a>
<a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><b>entity</b><code> [get]</code></td></tr>
<tr class="memdesc:abfa8836f5980aeeee9459a69b3dcecf6"><td class="mdescLeft">&#160;</td><td class="mdescRight">The entity this player is currently possessing or nil <br /></td></tr>
<tr class="separator:abfa8836f5980aeeee9459a69b3dcecf6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4e88291e27a05dc2c1c7c458b6fc5e2a"><td class="memItemLeft" align="right" valign="top"><a id="a4e88291e27a05dc2c1c7c458b6fc5e2a" name="a4e88291e27a05dc2c1c7c458b6fc5e2a"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>climbing</b><code> [get]</code></td></tr>
<tr class="memdesc:a4e88291e27a05dc2c1c7c458b6fc5e2a"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="class_lua_1_1_player.html" title="A Player represents settings only applicable for a player-controlled character.">Player</a> is climbing. <br /></td></tr>
<tr class="separator:a4e88291e27a05dc2c1c7c458b6fc5e2a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af146bd925307c2a878d8d7aa6ac4f990"><td class="memItemLeft" align="right" valign="top"><a id="af146bd925307c2a878d8d7aa6ac4f990" name="af146bd925307c2a878d8d7aa6ac4f990"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>isAiming</b><code> [get]</code></td></tr>
<tr class="memdesc:af146bd925307c2a878d8d7aa6ac4f990"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="class_lua_1_1_player.html" title="A Player represents settings only applicable for a player-controlled character.">Player</a> is aiming. <br /></td></tr>
<tr class="separator:af146bd925307c2a878d8d7aa6ac4f990"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7176133efd6891f96cfa86f67faef97b"><td class="memItemLeft" align="right" valign="top"><a id="a7176133efd6891f96cfa86f67faef97b" name="a7176133efd6891f96cfa86f67faef97b"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><b>walkSpeed</b><code> [get, set]</code></td></tr>
<tr class="memdesc:a7176133efd6891f96cfa86f67faef97b"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="class_lua_1_1_movement.html" title="Use this component to control the movement of agents.">Movement</a> speed when walking (holding <code>Alt</code> by default). <br /></td></tr>
<tr class="separator:a7176133efd6891f96cfa86f67faef97b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a37a67ff4633be4a46486ed6d34c840f4"><td class="memItemLeft" align="right" valign="top"><a id="a37a67ff4633be4a46486ed6d34c840f4" name="a37a67ff4633be4a46486ed6d34c840f4"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><b>runSpeed</b><code> [get, set]</code></td></tr>
<tr class="memdesc:a37a67ff4633be4a46486ed6d34c840f4"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="class_lua_1_1_movement.html" title="Use this component to control the movement of agents.">Movement</a> speed when running. This is the default movement mode. <br /></td></tr>
<tr class="separator:a37a67ff4633be4a46486ed6d34c840f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a41256b9ca25487dc16cc3234697bdcb0"><td class="memItemLeft" align="right" valign="top"><a id="a41256b9ca25487dc16cc3234697bdcb0" name="a41256b9ca25487dc16cc3234697bdcb0"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><b>sprintSpeed</b><code> [get, set]</code></td></tr>
<tr class="memdesc:a41256b9ca25487dc16cc3234697bdcb0"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="class_lua_1_1_movement.html" title="Use this component to control the movement of agents.">Movement</a> speed when sprinting (holding <code>Shift</code> by default). <br /></td></tr>
<tr class="separator:a41256b9ca25487dc16cc3234697bdcb0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aba74b8604fe18ff58fbe7cab856c759b"><td class="memItemLeft" align="right" valign="top"><a id="aba74b8604fe18ff58fbe7cab856c759b" name="aba74b8604fe18ff58fbe7cab856c759b"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><b>flySpeed</b><code> [get, set]</code></td></tr>
<tr class="memdesc:aba74b8604fe18ff58fbe7cab856c759b"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="class_lua_1_1_movement.html" title="Use this component to control the movement of agents.">Movement</a> speed when flying (activated by pressing <code>E</code> by default). <br /></td></tr>
<tr class="separator:aba74b8604fe18ff58fbe7cab856c759b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a89f50497773ef2a3998950a1f20b2cd8"><td class="memItemLeft" align="right" valign="top"><a id="a89f50497773ef2a3998950a1f20b2cd8" name="a89f50497773ef2a3998950a1f20b2cd8"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><b>superFlySpeed</b><code> [get, set]</code></td></tr>
<tr class="memdesc:a89f50497773ef2a3998950a1f20b2cd8"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="class_lua_1_1_movement.html" title="Use this component to control the movement of agents.">Movement</a> speed when super flying (holding <code>Shift</code> by default while flying). <br /></td></tr>
<tr class="separator:a89f50497773ef2a3998950a1f20b2cd8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acfd04cccae35d05632d90b022a19dd3e"><td class="memItemLeft" align="right" valign="top"><a id="acfd04cccae35d05632d90b022a19dd3e" name="acfd04cccae35d05632d90b022a19dd3e"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><b>climbSpeed</b><code> [get, set]</code></td></tr>
<tr class="memdesc:acfd04cccae35d05632d90b022a19dd3e"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="class_lua_1_1_movement.html" title="Use this component to control the movement of agents.">Movement</a> speed when climbing (activated by pressing <code>C</code> by default). <br /></td></tr>
<tr class="separator:acfd04cccae35d05632d90b022a19dd3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab34c0553a5093faa0dc839e3e5c1e2fd"><td class="memItemLeft" align="right" valign="top"><a id="ab34c0553a5093faa0dc839e3e5c1e2fd" name="ab34c0553a5093faa0dc839e3e5c1e2fd"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><b>jumpPower</b><code> [get, set]</code></td></tr>
<tr class="memdesc:ab34c0553a5093faa0dc839e3e5c1e2fd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Jumping height (activated by pressing <code>Space</code> by default). <br /></td></tr>
<tr class="separator:ab34c0553a5093faa0dc839e3e5c1e2fd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a409e63cab8bf55da64a77ae09a44eaa9"><td class="memItemLeft" align="right" valign="top"><a id="a409e63cab8bf55da64a77ae09a44eaa9" name="a409e63cab8bf55da64a77ae09a44eaa9"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>autowalk</b><code> [get, set]</code></td></tr>
<tr class="memdesc:a409e63cab8bf55da64a77ae09a44eaa9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Is autowalk enabled? Toggled by pressing <code>Right Shift</code> by default. <br /></td></tr>
<tr class="separator:a409e63cab8bf55da64a77ae09a44eaa9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeb994edf649d41658544101518a774db"><td class="memItemLeft" align="right" valign="top"><a id="aeb994edf649d41658544101518a774db" name="aeb994edf649d41658544101518a774db"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><b>sizeChangeSpeed</b><code> [get, set]</code></td></tr>
<tr class="memdesc:aeb994edf649d41658544101518a774db"><td class="mdescLeft">&#160;</td><td class="mdescRight">Speed of size change when holding <code>SizeUp</code>/<code>SizeUp</code> keys (<code>Z</code>/<code>X</code> by default). <br /></td></tr>
<tr class="separator:aeb994edf649d41658544101518a774db"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a101475733e91a310fdd874be94d5464b"><td class="memItemLeft" align="right" valign="top"><a id="a101475733e91a310fdd874be94d5464b" name="a101475733e91a310fdd874be94d5464b"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><b>minSize</b><code> [get, set]</code></td></tr>
<tr class="memdesc:a101475733e91a310fdd874be94d5464b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Minimum player size. <br /></td></tr>
<tr class="separator:a101475733e91a310fdd874be94d5464b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa940451f7c74d6b8bdf34c728b6a688d"><td class="memItemLeft" align="right" valign="top"><a id="aa940451f7c74d6b8bdf34c728b6a688d" name="aa940451f7c74d6b8bdf34c728b6a688d"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><b>maxSize</b><code> [get, set]</code></td></tr>
<tr class="memdesc:aa940451f7c74d6b8bdf34c728b6a688d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maximum player size. <br /></td></tr>
<tr class="separator:aa940451f7c74d6b8bdf34c728b6a688d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa45522a569c8559f60eb7ed78f436ca1"><td class="memItemLeft" align="right" valign="top"><a id="aa45522a569c8559f60eb7ed78f436ca1" name="aa45522a569c8559f60eb7ed78f436ca1"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><b>scale</b><code> [get, set]</code></td></tr>
<tr class="memdesc:aa45522a569c8559f60eb7ed78f436ca1"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="class_lua_1_1_player.html" title="A Player represents settings only applicable for a player-controlled character.">Player</a> scale. <br /></td></tr>
<tr class="separator:aa45522a569c8559f60eb7ed78f436ca1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a39905d8dbddc9f80365df2822a1a093c"><td class="memItemLeft" align="right" valign="top"><a id="a39905d8dbddc9f80365df2822a1a093c" name="a39905d8dbddc9f80365df2822a1a093c"></a>
<a class="el" href="class_lua_1_1_lua_player_raygun.html">LuaPlayerRaygun</a>&#160;</td><td class="memItemRight" valign="bottom"><b>raygun</b><code> [get]</code></td></tr>
<tr class="memdesc:a39905d8dbddc9f80365df2822a1a093c"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="class_lua_1_1_player.html" title="A Player represents settings only applicable for a player-controlled character.">Player</a> scale. <br /></td></tr>
<tr class="separator:a39905d8dbddc9f80365df2822a1a093c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >A <a class="el" href="class_lua_1_1_player.html" title="A Player represents settings only applicable for a player-controlled character.">Player</a> represents settings only applicable for a player-controlled character. </p>
</div><hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaPlayer.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_player.html">Player</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

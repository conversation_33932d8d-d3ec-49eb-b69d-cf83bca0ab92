function startSound()
    self.growAudio = AudioSource:new(self.agent.bones.spine)
    self.growAudio.clip = "audio_file_of_your_choice.wav" --*.ogg files are also compatible
    self.growAudio.spatialBlend = 1
    self.growAudio.loop = true
    updateSFX()
    self.growAudio.volume = self.maxVolume
    self.growAudio:Play()
end

function updateSFX()
    self.pitchModifier = ((self.agent.scale / 5) / (0.04 / self.agent.scale) * self.agent.scale / (self.agent.scale * self.agent.scale * math.sqrt(self.agent.scale)) / (math.sqrt(math.sqrt(math.sqrt(math.sqrt(self.agent.scale)))) * (math.sqrt(math.sqrt(math.sqrt(self.agent.scale))))))
    self.growAudio.pitch = (self.agent.scale * (1.05 / (self.agent.scale / 0.2 / self.pitchModifier)) * (1 / math.sqrt((self.agent.scale * 1000 / 125) + 1))) + 0.65
    self.growAudio.minDistance = (1000 * self.agent.scale / player.scale / (300 + self.agent.scale / player.scale)) / 2 * 0.0025
    self.maxVolume = (1.7 - self.growAudio.pitch) * 1.5 / 1.875 + 0.2)
    
end

--In this space you would usually have your Behavior call block (which starts with 'Behavior = RegisterBehavior("Your Behavior")). It could also be at the very top, doesn't really matter where it is.

function Behavior:Start() --Change 'Behavior' to whatever your behavior name is
    startSound() --Usually you'll have other stuff happening if your script isn't 'just' about playing a single sound
end

function Behavior:Update() --Same here
    updateSFX() --Same here
end
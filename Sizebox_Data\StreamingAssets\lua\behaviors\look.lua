Look = RegisterBehavior("Look")

Look.data = {
    menuEntry = "Look",
    ai = false,
    secondary = true,
    flags = { "look" },
    agent = {
        type = { "humanoid" }
    },
    target = {
        type = { "humanoid" }
    },
    tags = "macro",
    settings = {
        {"lkey", "Keybind", "keybind", "k"}
    }
}

function Look:Start()
    self.looking = false
    if ( self.agent == self.target ) then
        self.agent.ai.StopSecondaryBehavior("look")
    else 
        self.looking = true
    end 
end

function Look:Update()
    --LOOKING TOGGLE--
        if Input.Get<PERSON>ey("left ctrl") and Input.GetKey("left shift") and Input.GetKeyDown(self.lkey) then
            self.agent.ai.StopSecondaryBehavior("look")
        elseif Input.GetKeyDown(self.lkey) then
            self:Toggle()
        end
    --LOOKING TOGGLE

    --TARGET UPDATE--
        if self.target and self.target.IsDead() or not self.target then
            self:FindNextTarget()
        else
            looking = false
        end
    --TARGET UPDATE--

    --LOOK--
        if self.toggling then
            if self.looking then
                self.agent.LookAt(self.target)
            else
                self.agent.LookAt(nil)
            end
            self.toggling = false
        end
    --LOOK--
end

function Look:Exit()
    self.agent.LookAt(nil)
end

function Look:Toggle()
    self.looking = not self.looking
    self.toggling = true
end

function Look:FindNextTarget()
    if self.agent.isGiantess() then 
        self.target = self.agent.FindClosestMicro()
    else
        self.target = self.agent.FindClosestGiantess()
    end
end
hp = 100 --how much HP we have
fallbasedamage = 3 --base damage
fallthreshold = 5 --how many seconds before damage starts applying
amplificationfactor = 20 --increasing this will increase the amount of damage that occurs the longer you fall (CAUTION: EXTREMELY SENSITIVE use decimals for more accurate amplifications )
stepdamage = 1 --amount of damage applied while stuck every set seconds
stickdamagetick = 1 --how many seconds between damage ticks
falldamage = true
crushdamage = true
stickdamage = true
alllifeforce = false
lifeforcetakessize = false
percentage = false
randomattribute = true
--====================DONT EDIT BELOW THIS LINE====================--
previousy = 0
previoushp = 0
dynamichp = hp
damagetick = 0
falling = false
waiting = false
deadstate = false
kickedoff = false
jumpattempt = 0
noall = false
healed = false
healwait = 60
falltime = 0

radius = 10
scale = 50

strong = false
weak = false
glass = false
endurance = false
seductor = false
godlike = false
prick = false
sympathetic = false
lifedoctor = false

function Start()
    models = Entity.GetGtsModelList()
    jumprequirement = random.range(10)
    if randomattribute then
        attributeselection = math.random(15)
        if (attributeselection == 1) then
            log('Attribute Obtained: Strong')
            strong = true
            healthmodifyer = math.random(15)
            hp = hp + healthmodifyer
            dynamichp = hp
        elseif (attributeselection == 2) then
            log('Attribute Obtained: Weak')
            weak = true
            healthmodifyer = math.random(15)
            hp = hp - healthmodifyer
            dynamichp = hp
        elseif (attributeselection == 3) then
            log('Attribute Obtained: Glass')
            glass = true
            damagemodifyer = math.random(10)
            fallbasedamage = fallbasedamage + damagemodifyer
        elseif (attributeselection == 4) then
            endurance = true
            log('Attribute Obtained: Endurance')
            damagetickmodifyer = math.random(stickdamagetick * 2)
            stickdamagetick = stickdamagetick + damagetickmodifyer
        elseif (attributeselection == 5) and lifeforcetakessize then
            lifedoctor = true
            log('Attribute Obtained: Life Doctor')
        elseif (attributeselection == 6) then
            log('Attribute Obtained: Sympathy Inducer')
            sympathetic = true
        elseif (attributeselection == 7) then
            log('Attribute Obtained: Prick')
            prick = true
        elseif (attributeselection == 8) then
            seductorspawn()
            seductor = true
        elseif (attributeselection == 9) then
            extrarngattribute = math.random(4)
            if (extrarngattribute == 3) then
                extraextrarngattribute = math.random(4)
                if (extraextrarngattribute == 1) then
                    log('Attribute Obtained: Strong-Godly')
                    healthmodifyer = math.random(15)
                    hp = (hp + healthmodifyer) * 3
                    dynamichp = hp
                elseif(extraextrarngattribute == 2) then
                    log('Attribute Obtained: Weak-Godly')
                    healthmodifyer = math.random(15)
                    hp = (hp - healthmodifyer) * 3
                    dynamichp = hp
                else
                    log('Attribute Obtained: Godly')
                    hp = (hp * 3)
                    dynamichp = hp
                end
                godlike = true
            else
                log('No Attribute Obtained')
            end
        else
            log('No Attribute Obtained')
        end
    end
end

function Update()
    if (Input.GetKeyDown('h')) then
        if strong then
            log('Your Attribute is Strong: You are born stronger than any of the others, you have a random amount of more health')
        elseif weak then
            log('Your Attribute is Weak: You are born weaker than normal, you have a random amount of less health')
        elseif glass then
            log('Your Attribute is Glass: Your muscles are weaker than others, you will take a random amount of extra set fall damage')
        elseif endurance then
            log('Your Attribute is Endurance: You are born having more endurance than normal, your stick damage tick time is randomly less')
        elseif lifedoctor then
            log('Your Attribute is Life Doctor: You have a better understanding of life forces, you heal at no downfall for the giantess')
        elseif sympathetic then
            log("Your Attribute is Sympathy Inducer: Your unfortunate life makes giantess's sympathetic towards you and wont try crush you further once you get squished")
        elseif prick then
            log("Your Attribute is Prick: Your bones are very close to your skin and have weird growths causing giantess's great pain.. you will be thrown off when your stomped on")
        elseif seductor then
            log("Your Attribute is Seducer: You have a unnatural talent that makes you very attractive to giantess's.. you spawn with a giantess set to protect")
        elseif godlike then
            log("Your Attribute is Godly: You are a god who took over a dead micro's corpse refreshing its youth. You cannot be killed!")
        else
            log('You dont have a Attribute.. Values are default!')
        end
    end

    gts = player.findClosestGiantess()
    if gts then
        if (gts.animation.get() == 'Jump 3') and (gts.animation.GetProgress() > 2) then
            gts.animation.Set('Idle')
            gts.LookAt()
        end
        if (gts.animation.get() == 'Jump Up') and (gts.animation.GetProgress() > 4) then
            gts.animation.Set('Idle')
            gts.LookAt()
        end
    end
    if percentage then
        if not (dynamichp == previoushp) then --log any HP changes
            log('HP: '..math.floor((dynamichp/hp)*100)..'%')
            previoushp = dynamichp
        end
    else
        if not (dynamichp == previoushp) then --log any HP changes
            log('HP: '..math.floor(dynamichp)..'HP / '..math.floor(hp)..'HP')
            previoushp = dynamichp
        end
    end
    if not (dynamichp == previoushp) then --log any HP changes
        log('HP: '..math.floor((dynamichp/hp)*100)..'%')
        previoushp = dynamichp
    end
    if not godlike then
        if (previoushp == 0) and not deadstate then
            player.animation.Set('Idle')
            player.animation.Set('Dying')
            deadstate = true
        end
        if deadstate and (player.animation.getProgress() == 0) then
            player.animation.set('Dying')
        end
    else
        if (previoushp == 0) then
            log('Your Soul Returns to the Vessel!')
            dynamichp = hp
        end
    end
    if crushdamage then --getting crushed kills you... simple
        crushdamage()
    end
    if stickdamage then --getting stuck will keep dealing damage every step the gts takes
        stickdamage()
    end
    if falldamage then --fall damage module
        falldamage()
    end
    heal()
end
function falldamage()
    gts = player.findClosestGiantess()
    if gts then
        if not (player.transform.IsChildOf(gts.transform)) then
            ypos = math.floor(player.transform.localPosition.y) + 1
            if not (ypos == previousy) then
                if not (ypos == 0) then
                    --log(ypos)
                    if (ypos < previousy) then
                        falling = true
                    else
                        falling = false
                    end
                    previousy = ypos
                else
                    falling = false
                end
            end
            if falling then
                --log('Falling')
                falltime = falltime + 1
                --log(falltime)
            else
                --log('Landed/Idle')
                if ((falltime * 60) > fallthreshold) then
                    dynamichp = dynamichp - fallbasedamage * (falltime/(80 - amplificationfactor))
                    if (dynamichp < 0) then
                        dynamichp = 0
                    end
                    falltime = 0
                end
            end
        end
    else
        ypos = math.floor(player.transform.localPosition.y) + 1
        if not (ypos == previousy) then
            if not (ypos == 0) then
                --log(ypos)
                if (ypos < previousy) then
                    falling = true
                else
                    falling = false
                end
                previousy = ypos
            else
                falling = false
            end
        end
        if falling then
            --log('Falling')
            falltime = falltime + 1
            --log(falltime)
        else
            --log('Landed/Idle')
            if ((falltime * 60) > fallthreshold) then
                dynamichp = dynamichp - fallbasedamage * (falltime/(80 - amplificationfactor))
                if (dynamichp < 0) then
                    dynamichp = 0
                end
                falltime = 0
            end
        end
    end
end
function crushdamage()
    gts = player.findClosestGiantess()
    if gts then
        if player.IsDead() or player.IsCrushed() and not player.transform.IsChildOf(gts.transform) then
            dynamichp = 0
        end
    end
end
function stickdamage()
    gts = player.findClosestGiantess()
    if gts then
        if player.transform.IsChildOf(gts.transform) and player.isDead() or player.isCrushed() then
            if not prick then
                if not (damagetick == (stickdamagetick * 60)) then
                    damagetick = damagetick + 1
                else
                    bonusdamage = math.random(8)
                    if not (bonusdamage == 8) and not (gts.animation.get() == 'Jump 3') or (gts.animation.get() == 'Jump Up') then
                        if not (dynamichp == 0) then
                            stomptype = math.random(2)
                            if (stomptype == 1) then
                                if not sympathetic then
                                    gts.animation.Set('Jump 3')
                                end
                                if ((dynamichp - stepdamage) > (stepdamage * 2)) then
                                    gts.LookAt(player)
                                    dynamichp = dynamichp - (stepdamage * 2)
                                else
                                    dynamichp = 0
                                end
                            else
                                if not sympathetic then
                                    gts.animation.set('Jump Up')
                                end
                                if ((dynamichp - stepdamage) > (stepdamage * 4)) then
                                    gts.LookAt(player)
                                    dynamichp = dynamichp - (stepdamage * 4)
                                else
                                    dynamichp = 0
                                end
                            end
                            damagetick = 0
                        end
                    else
                        if not (dynamichp == 0) then
                            dynamichp = dynamichp - stepdamage
                            damagetick = 0
                        end
                    end
                end
                if (Input.GetKeyDown('space')) then
                    jumpattempt = jumpattempt + 1 
                end
                if (jumpattempt >= jumprequirement) then
                    jumprequirement = math.random(10)
                    jumpattempt = 0
                    player.transform.translate(gts.transform.localPosition.x, 0, gts.transform.localPosition.z)
                    player.transform.SetParent()
                    player.StandUp()
                end
            else
                kickedoff = false
            end
            if not kickedoff then
                gts.animation.SetAndWait('Falling Down')
                if (gts.animation.get() == 'Falling Down') and (gts.animation.GetProgress() >= 1) then
                    player.transform.SetParent()
                    player.StandUp()
                    kickedoff = true
                end
            end
        else
            jumpattempt = 0
        end
    end
end

function heal()
    --log('Health Loss: '..(dynamichp - hp))
    --log(dynamichp)
    --log(hp)
    gts = player.findClosestGiantess()
    if gts then
        healwait = 60
        if not player.transform.IsChildOf(gts.transform) then
            if not (dynamichp == hp) then
                if not alllifeforce then
                    if (player.scale >= gts.scale) then
                        if noall then
                            log('Giantess Already At Minimum Safe Life Force')
                            noall = false
                        end
                    else
                        noall = true
                        if not lifedoctor then
                            growfactor = math.floor(dynamichp - hp)/10
                            --log('Shrink Factor: '..((math.floor((growfactor / gts.scale)*100))*-1)..'%')
                            time = math.random(5)
                            if lifeforcetakessize then
                                gts.grow((growfactor), time)
                            end
                        end
                        if ((dynamichp - hp) > 1) then
                            dynamichp = dynamichp + 1
                        else
                            dynamichp = dynamichp + ((dynamichp - hp) * -1)
                        end
                        healed = true
                    end
                else
                    if not (gts.scale == 1) then
                        if not lifedoctor then
                            growfactor = math.floor(dynamichp - hp)/10
                            --log('Shrink Factor: '..((math.floor((growfactor / gts.scale)*100))*-1)..'%')
                            time = math.random(5)
                            if lifeforcetakessize then
                                gts.grow((growfactor), time)
                            end
                        end
                        
                        if ((dynamichp - hp) > 1) then
                            dynamichp = dynamichp + 1
                        else
                            dynamichp = dynamichp + ((dynamichp - hp) * -1)
                        end
                        healed = true
                    else
                        gts.animation.set('Dying')
                    end
                end
            else
                if healed then
                    log('Life Force Stolen')
                    if percentage then
                        log('HP: '..math.floor((dynamichp/hp)*100)..'%')
                    else
                        log('HP: '..math.floor(dynamichp)..'HP / '..math.floor(hp)..'HP')
                    end
                    healed = false
                end
            end
        end
    else
        if not (healwait == 0) then
            healwait = healwait - 1
        else
            healwait = 60
            heal()
        end
    end
end

function seductorspawn()
    model = models[math.random(#models)]              -- pick random model from list
    if not string.match(model, "Walk Pack 1.gts/Walk Blendv2A") then
        local pos = player.position                             -- our position
        local angle = player.transform.rotation.eulerAngles.y   -- our facing
        scale = player.scale * 30

        local gtsRot = Quaternion.angleAxis(angle, Vector3.up)  -- rotation quaternion
        local gtsPos = pos + gtsRot * Vector3.back * radius  -- rotate a vector of radius length and add to player position

        log('Attribute Obtained: Seducer')
        Entity.spawnGiantess(model, gtsPos, gtsRot, scale)      -- spawn a giantess
        gts = player.findClosestGiantess()
        gts.ai.setBehavior('protect', player)
        Log("Spawned Giantess")
    else
        model = models[math.random(#models)]              -- pick random model from list
    end
end
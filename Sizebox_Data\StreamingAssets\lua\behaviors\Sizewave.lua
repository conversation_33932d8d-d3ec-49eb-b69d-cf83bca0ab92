-- Sizebox v3.02 - Win64/Sizebox_Data/StreamingAssets/lua/behaviors/SizeWave.lua

SizeWave = RegisterBehavior("SizeWave")

SizeWave.data = {
    agent = {
        type = { "humanoid" }
    },
    target = {
        type = { "oneself" }
    },
    settings = {
        { "min_size", "Min Size", "float", 0.5 },
        { "max_size", "Max Size", "float", 2.0 }
    }
}

function SizeWave:Start()
    self.min_size = self.data.settings.min_size.value
    self.max_size = self.data.settings.max_size.value
    self.current_size = 1.0
end

function SizeWave:Update()
    self.current_size = 1.0 + math.sin(GetFrameTime()) * 0.5
    self.agent.transform.localScale = Vector3(self.current_size, self.current_size, self.current_size)
end
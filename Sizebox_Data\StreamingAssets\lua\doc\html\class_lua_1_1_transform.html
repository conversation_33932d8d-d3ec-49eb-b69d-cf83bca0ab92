<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.Transform Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_transform.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="class_lua_1_1_transform-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.Transform Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Position, rotation and scale of an object.  
 <a href="class_lua_1_1_transform.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a13e71ef2426543323d6f74d05d9904d0"><td class="memItemLeft" align="right" valign="top"><a id="a13e71ef2426543323d6f74d05d9904d0" name="a13e71ef2426543323d6f74d05d9904d0"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>DetachChildren</b> ()</td></tr>
<tr class="memdesc:a13e71ef2426543323d6f74d05d9904d0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unparents all children. <br /></td></tr>
<tr class="separator:a13e71ef2426543323d6f74d05d9904d0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a79476caab32d323b7fee1955fda8d808"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a79476caab32d323b7fee1955fda8d808">Find</a> (string <a class="el" href="class_lua_1_1_transform.html#af1ca076a9406c3865fef9cbf8393e484">name</a>)</td></tr>
<tr class="memdesc:a79476caab32d323b7fee1955fda8d808"><td class="mdescLeft">&#160;</td><td class="mdescRight">Finds a child by name and returns it.  <a href="class_lua_1_1_transform.html#a79476caab32d323b7fee1955fda8d808">More...</a><br /></td></tr>
<tr class="separator:a79476caab32d323b7fee1955fda8d808"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2535f3bade200a7f2c2c59debaeed41a"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a2535f3bade200a7f2c2c59debaeed41a">GetChild</a> (int index)</td></tr>
<tr class="memdesc:a2535f3bade200a7f2c2c59debaeed41a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a transform child by index.  <a href="class_lua_1_1_transform.html#a2535f3bade200a7f2c2c59debaeed41a">More...</a><br /></td></tr>
<tr class="separator:a2535f3bade200a7f2c2c59debaeed41a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a839d8eeda6ca8e0ea2a2e7b50643b0ca"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a839d8eeda6ca8e0ea2a2e7b50643b0ca">GetSiblingIndex</a> ()</td></tr>
<tr class="memdesc:a839d8eeda6ca8e0ea2a2e7b50643b0ca"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets the sibling index.  <a href="class_lua_1_1_transform.html#a839d8eeda6ca8e0ea2a2e7b50643b0ca">More...</a><br /></td></tr>
<tr class="separator:a839d8eeda6ca8e0ea2a2e7b50643b0ca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7bf8b1d272b1d893a606c5f38770c433"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a7bf8b1d272b1d893a606c5f38770c433">InverseTransformDirection</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> direction)</td></tr>
<tr class="memdesc:a7bf8b1d272b1d893a606c5f38770c433"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transforms a direction from world space to local space. The opposite of <a class="el" href="class_lua_1_1_transform.html#a14270ac6dbade453decf26513f533b66" title="Transforms direction from local space to world space.">Transform.TransformDirection</a>.  <a href="class_lua_1_1_transform.html#a7bf8b1d272b1d893a606c5f38770c433">More...</a><br /></td></tr>
<tr class="separator:a7bf8b1d272b1d893a606c5f38770c433"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4983e88de730e650bb632f63d043035f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a4983e88de730e650bb632f63d043035f">InverseTransformDirection</a> (float x, float y, float z)</td></tr>
<tr class="memdesc:a4983e88de730e650bb632f63d043035f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transforms a direction from world space to local space. The opposite of <a class="el" href="class_lua_1_1_transform.html#a14270ac6dbade453decf26513f533b66" title="Transforms direction from local space to world space.">Transform.TransformDirection</a>.  <a href="class_lua_1_1_transform.html#a4983e88de730e650bb632f63d043035f">More...</a><br /></td></tr>
<tr class="separator:a4983e88de730e650bb632f63d043035f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa32269bd79e72646057908fee2cb7f9e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#aa32269bd79e72646057908fee2cb7f9e">InverseTransformPoint</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> direction)</td></tr>
<tr class="memdesc:aa32269bd79e72646057908fee2cb7f9e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transforms position from world space to local space.  <a href="class_lua_1_1_transform.html#aa32269bd79e72646057908fee2cb7f9e">More...</a><br /></td></tr>
<tr class="separator:aa32269bd79e72646057908fee2cb7f9e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af07e05c728e517c260e6cf8c1b442adc"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#af07e05c728e517c260e6cf8c1b442adc">InverseTransformPoint</a> (float x, float y, float z)</td></tr>
<tr class="memdesc:af07e05c728e517c260e6cf8c1b442adc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transforms position from world space to local space.  <a href="class_lua_1_1_transform.html#af07e05c728e517c260e6cf8c1b442adc">More...</a><br /></td></tr>
<tr class="separator:af07e05c728e517c260e6cf8c1b442adc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae6bb74c5b90a6f8db4c436a56f24f8eb"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#ae6bb74c5b90a6f8db4c436a56f24f8eb">InverseTransformVector</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> direction)</td></tr>
<tr class="memdesc:ae6bb74c5b90a6f8db4c436a56f24f8eb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transforms a vector from world space to local space. The opposite of <a class="el" href="class_lua_1_1_transform.html#a8a4bb1f1feb42a0d3be3577e4463f5f4" title="Transforms vector from local space to world space.">Transform.TransformVector</a>.  <a href="class_lua_1_1_transform.html#ae6bb74c5b90a6f8db4c436a56f24f8eb">More...</a><br /></td></tr>
<tr class="separator:ae6bb74c5b90a6f8db4c436a56f24f8eb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8922d01029a19d826eeb4d6f8f22ce06"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a8922d01029a19d826eeb4d6f8f22ce06">InverseTransformVector</a> (float x, float y, float z)</td></tr>
<tr class="memdesc:a8922d01029a19d826eeb4d6f8f22ce06"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transforms a vector from world space to local space. The opposite of <a class="el" href="class_lua_1_1_transform.html#a8a4bb1f1feb42a0d3be3577e4463f5f4" title="Transforms vector from local space to world space.">Transform.TransformVector</a>.  <a href="class_lua_1_1_transform.html#a8922d01029a19d826eeb4d6f8f22ce06">More...</a><br /></td></tr>
<tr class="separator:a8922d01029a19d826eeb4d6f8f22ce06"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad11ff475738f907fdbdc4009c81ee09e"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#ad11ff475738f907fdbdc4009c81ee09e">IsChildOf</a> (<a class="el" href="class_lua_1_1_transform.html">Transform</a> <a class="el" href="class_lua_1_1_transform.html#ad7ea4f4af441f263f3cbfcb853d3edfa">parent</a>)</td></tr>
<tr class="memdesc:ad11ff475738f907fdbdc4009c81ee09e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Is this transform a child of parent?  <a href="class_lua_1_1_transform.html#ad11ff475738f907fdbdc4009c81ee09e">More...</a><br /></td></tr>
<tr class="separator:ad11ff475738f907fdbdc4009c81ee09e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e722de9c3eacff82477ab7684a67553"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a1e722de9c3eacff82477ab7684a67553">LookAt</a> (<a class="el" href="class_lua_1_1_transform.html">Transform</a> target)</td></tr>
<tr class="memdesc:a1e722de9c3eacff82477ab7684a67553"><td class="mdescLeft">&#160;</td><td class="mdescRight">Not to be confused with <a class="el" href="class_lua_1_1_entity.html#a29cdb052c5422873a708c8080039cb4b" title="Not to be confused with Transform.LookAt If the entity is a giantess it will look towards a target....">Entity.LookAt</a> Rotates the transform so the forward vector points at /target/'s current position.  <a href="class_lua_1_1_transform.html#a1e722de9c3eacff82477ab7684a67553">More...</a><br /></td></tr>
<tr class="separator:a1e722de9c3eacff82477ab7684a67553"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acab4e79308fc20ffa13f6048b7cb3184"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#acab4e79308fc20ffa13f6048b7cb3184">LookAt</a> (<a class="el" href="class_lua_1_1_transform.html">Transform</a> target, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> worldUp)</td></tr>
<tr class="memdesc:acab4e79308fc20ffa13f6048b7cb3184"><td class="mdescLeft">&#160;</td><td class="mdescRight">Not to be confused with <a class="el" href="class_lua_1_1_entity.html#a29cdb052c5422873a708c8080039cb4b" title="Not to be confused with Transform.LookAt If the entity is a giantess it will look towards a target....">Entity.LookAt</a> Rotates the transform so the forward vector points at /target/'s current position.  <a href="class_lua_1_1_transform.html#acab4e79308fc20ffa13f6048b7cb3184">More...</a><br /></td></tr>
<tr class="separator:acab4e79308fc20ffa13f6048b7cb3184"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa8630c1feef1c89cf7a201f6c92005ee"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#aa8630c1feef1c89cf7a201f6c92005ee">LookAt</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> worldPosition)</td></tr>
<tr class="memdesc:aa8630c1feef1c89cf7a201f6c92005ee"><td class="mdescLeft">&#160;</td><td class="mdescRight">Not to be confused with <a class="el" href="class_lua_1_1_entity.html#a29cdb052c5422873a708c8080039cb4b" title="Not to be confused with Transform.LookAt If the entity is a giantess it will look towards a target....">Entity.LookAt</a> Rotates the transform so the forward vector points at /target/'s current position.  <a href="class_lua_1_1_transform.html#aa8630c1feef1c89cf7a201f6c92005ee">More...</a><br /></td></tr>
<tr class="separator:aa8630c1feef1c89cf7a201f6c92005ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9364734c07f954378c167f7f5258fa18"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a9364734c07f954378c167f7f5258fa18">LookAt</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> worldPosition, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> worldUp)</td></tr>
<tr class="memdesc:a9364734c07f954378c167f7f5258fa18"><td class="mdescLeft">&#160;</td><td class="mdescRight">Not to be confused with <a class="el" href="class_lua_1_1_entity.html#a29cdb052c5422873a708c8080039cb4b" title="Not to be confused with Transform.LookAt If the entity is a giantess it will look towards a target....">Entity.LookAt</a> Rotates the transform so the forward vector points at /target/'s current position.  <a href="class_lua_1_1_transform.html#a9364734c07f954378c167f7f5258fa18">More...</a><br /></td></tr>
<tr class="separator:a9364734c07f954378c167f7f5258fa18"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af5d67d5940a08bc18d105c884e53be84"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#af5d67d5940a08bc18d105c884e53be84">Rotate</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> <a class="el" href="class_lua_1_1_transform.html#ad9a5f0534a08dc2d6cb9ad32b6581b8d">eulerAngles</a>)</td></tr>
<tr class="memdesc:af5d67d5940a08bc18d105c884e53be84"><td class="mdescLeft">&#160;</td><td class="mdescRight">Applies a rotation of eulerAngles.z degrees around the z axis, eulerAngles.x degrees around the x axis, and eulerAngles.y degrees around the y axis (in that order).  <a href="class_lua_1_1_transform.html#af5d67d5940a08bc18d105c884e53be84">More...</a><br /></td></tr>
<tr class="separator:af5d67d5940a08bc18d105c884e53be84"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a44f0216f90d0df765efc719bdfbccde2"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a44f0216f90d0df765efc719bdfbccde2">Rotate</a> (float xAngle, float yAngle, float zAngle)</td></tr>
<tr class="memdesc:a44f0216f90d0df765efc719bdfbccde2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Applies a rotation of eulerAngles.z degrees around the z axis, eulerAngles.x degrees around the x axis, and eulerAngles.y degrees around the y axis (in that order).  <a href="class_lua_1_1_transform.html#a44f0216f90d0df765efc719bdfbccde2">More...</a><br /></td></tr>
<tr class="separator:a44f0216f90d0df765efc719bdfbccde2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7f9437d0324777be34be4722a9dc52a1"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a7f9437d0324777be34be4722a9dc52a1">Rotate</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> axis, float angle)</td></tr>
<tr class="memdesc:a7f9437d0324777be34be4722a9dc52a1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Applies a rotation of eulerAngles.z degrees around the z axis, eulerAngles.x degrees around the x axis, and eulerAngles.y degrees around the y axis (in that order).  <a href="class_lua_1_1_transform.html#a7f9437d0324777be34be4722a9dc52a1">More...</a><br /></td></tr>
<tr class="separator:a7f9437d0324777be34be4722a9dc52a1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad62f95fde354155f59cf1cf334cf3fec"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#ad62f95fde354155f59cf1cf334cf3fec">Rotate</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> point, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> axis, float angle)</td></tr>
<tr class="memdesc:ad62f95fde354155f59cf1cf334cf3fec"><td class="mdescLeft">&#160;</td><td class="mdescRight">Applies a rotation of eulerAngles.z degrees around the z axis, eulerAngles.x degrees around the x axis, and eulerAngles.y degrees around the y axis (in that order).  <a href="class_lua_1_1_transform.html#ad62f95fde354155f59cf1cf334cf3fec">More...</a><br /></td></tr>
<tr class="separator:ad62f95fde354155f59cf1cf334cf3fec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a44299747664c0a77b5f03f69f032a86f"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a44299747664c0a77b5f03f69f032a86f">SetParent</a> (<a class="el" href="class_lua_1_1_transform.html">Transform</a> <a class="el" href="class_lua_1_1_transform.html#ad7ea4f4af441f263f3cbfcb853d3edfa">parent</a>)</td></tr>
<tr class="memdesc:a44299747664c0a77b5f03f69f032a86f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the parent of the transform. If parent is nil the transform will be detached from its parent if it has one.  <a href="class_lua_1_1_transform.html#a44299747664c0a77b5f03f69f032a86f">More...</a><br /></td></tr>
<tr class="separator:a44299747664c0a77b5f03f69f032a86f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afcf05788f6ff8a51e7bc012ffe087727"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#afcf05788f6ff8a51e7bc012ffe087727">SetParent</a> (<a class="el" href="class_lua_1_1_transform.html">Transform</a> <a class="el" href="class_lua_1_1_transform.html#ad7ea4f4af441f263f3cbfcb853d3edfa">parent</a>, bool worldPositionStays)</td></tr>
<tr class="memdesc:afcf05788f6ff8a51e7bc012ffe087727"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the parent of the transform. If parent is nil the transform will be detached from its parent if it has one.  <a href="class_lua_1_1_transform.html#afcf05788f6ff8a51e7bc012ffe087727">More...</a><br /></td></tr>
<tr class="separator:afcf05788f6ff8a51e7bc012ffe087727"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a14270ac6dbade453decf26513f533b66"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a14270ac6dbade453decf26513f533b66">TransformDirection</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> direction)</td></tr>
<tr class="memdesc:a14270ac6dbade453decf26513f533b66"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transforms direction from local space to world space.  <a href="class_lua_1_1_transform.html#a14270ac6dbade453decf26513f533b66">More...</a><br /></td></tr>
<tr class="separator:a14270ac6dbade453decf26513f533b66"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac828e92537ee4ca71ef3525f3f19511a"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#ac828e92537ee4ca71ef3525f3f19511a">TransformDirection</a> (float x, float y, float z)</td></tr>
<tr class="memdesc:ac828e92537ee4ca71ef3525f3f19511a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transforms direction from local space to world space.  <a href="class_lua_1_1_transform.html#ac828e92537ee4ca71ef3525f3f19511a">More...</a><br /></td></tr>
<tr class="separator:ac828e92537ee4ca71ef3525f3f19511a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a77c8ed5338803453798fbfe848ed02e5"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a77c8ed5338803453798fbfe848ed02e5">TransformPoint</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> direction)</td></tr>
<tr class="memdesc:a77c8ed5338803453798fbfe848ed02e5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transforms position from local space to world space.  <a href="class_lua_1_1_transform.html#a77c8ed5338803453798fbfe848ed02e5">More...</a><br /></td></tr>
<tr class="separator:a77c8ed5338803453798fbfe848ed02e5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a77910db0422ec17545d411c1aeaec50b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a77910db0422ec17545d411c1aeaec50b">TransformPoint</a> (float x, float y, float z)</td></tr>
<tr class="memdesc:a77910db0422ec17545d411c1aeaec50b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transforms position from local space to world space.  <a href="class_lua_1_1_transform.html#a77910db0422ec17545d411c1aeaec50b">More...</a><br /></td></tr>
<tr class="separator:a77910db0422ec17545d411c1aeaec50b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8a4bb1f1feb42a0d3be3577e4463f5f4"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a8a4bb1f1feb42a0d3be3577e4463f5f4">TransformVector</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> direction)</td></tr>
<tr class="memdesc:a8a4bb1f1feb42a0d3be3577e4463f5f4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transforms vector from local space to world space.  <a href="class_lua_1_1_transform.html#a8a4bb1f1feb42a0d3be3577e4463f5f4">More...</a><br /></td></tr>
<tr class="separator:a8a4bb1f1feb42a0d3be3577e4463f5f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a79647850468bc87259dda4bc0b70e0ea"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a79647850468bc87259dda4bc0b70e0ea">TransformVector</a> (float x, float y, float z)</td></tr>
<tr class="memdesc:a79647850468bc87259dda4bc0b70e0ea"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transforms vector from local space to world space.  <a href="class_lua_1_1_transform.html#a79647850468bc87259dda4bc0b70e0ea">More...</a><br /></td></tr>
<tr class="separator:a79647850468bc87259dda4bc0b70e0ea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1a2933390110217890785619c897df7f"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a1a2933390110217890785619c897df7f">Translate</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> translation)</td></tr>
<tr class="memdesc:a1a2933390110217890785619c897df7f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Moves the transform in the direction and distance of translation.  <a href="class_lua_1_1_transform.html#a1a2933390110217890785619c897df7f">More...</a><br /></td></tr>
<tr class="separator:a1a2933390110217890785619c897df7f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2edcc9870e4706eff9bd0fe5143ca179"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a2edcc9870e4706eff9bd0fe5143ca179">Translate</a> (float x, float y, float z)</td></tr>
<tr class="memdesc:a2edcc9870e4706eff9bd0fe5143ca179"><td class="mdescLeft">&#160;</td><td class="mdescRight">Moves the transform by x along the x axis, y along the y axis, and z along the z axis.  <a href="class_lua_1_1_transform.html#a2edcc9870e4706eff9bd0fe5143ca179">More...</a><br /></td></tr>
<tr class="separator:a2edcc9870e4706eff9bd0fe5143ca179"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afa326b1db7b8629826fa763669888dd5"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#afa326b1db7b8629826fa763669888dd5">Translate</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> translation, <a class="el" href="class_lua_1_1_transform.html">Transform</a> relativeTo)</td></tr>
<tr class="memdesc:afa326b1db7b8629826fa763669888dd5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Moves the transform in the direction and distance of translation.  <a href="class_lua_1_1_transform.html#afa326b1db7b8629826fa763669888dd5">More...</a><br /></td></tr>
<tr class="separator:afa326b1db7b8629826fa763669888dd5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a69bc98e214a973ff53dcb49b48ba06c7"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a69bc98e214a973ff53dcb49b48ba06c7">Translate</a> (float x, float y, float z, <a class="el" href="class_lua_1_1_transform.html">Transform</a> relativeTo)</td></tr>
<tr class="memdesc:a69bc98e214a973ff53dcb49b48ba06c7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Moves the transform in the direction and distance of translation.  <a href="class_lua_1_1_transform.html#a69bc98e214a973ff53dcb49b48ba06c7">More...</a><br /></td></tr>
<tr class="separator:a69bc98e214a973ff53dcb49b48ba06c7"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-static-methods" name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:a17093d64239d0605cfdd83d9154fcf08"><td class="memItemLeft" align="right" valign="top">static bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a17093d64239d0605cfdd83d9154fcf08">Equals</a> (<a class="el" href="class_lua_1_1_transform.html">Transform</a> a, <a class="el" href="class_lua_1_1_transform.html">Transform</a> b)</td></tr>
<tr class="memdesc:a17093d64239d0605cfdd83d9154fcf08"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tests 2 transforms for equality.  <a href="class_lua_1_1_transform.html#a17093d64239d0605cfdd83d9154fcf08">More...</a><br /></td></tr>
<tr class="separator:a17093d64239d0605cfdd83d9154fcf08"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="properties" name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a9d77f87171bcb8090f086ae405c4f89e"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a9d77f87171bcb8090f086ae405c4f89e">childCount</a><code> [get]</code></td></tr>
<tr class="memdesc:a9d77f87171bcb8090f086ae405c4f89e"><td class="mdescLeft">&#160;</td><td class="mdescRight">The number of children the <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a> has.  <a href="class_lua_1_1_transform.html#a9d77f87171bcb8090f086ae405c4f89e">More...</a><br /></td></tr>
<tr class="separator:a9d77f87171bcb8090f086ae405c4f89e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad9a5f0534a08dc2d6cb9ad32b6581b8d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#ad9a5f0534a08dc2d6cb9ad32b6581b8d">eulerAngles</a><code> [get, set]</code></td></tr>
<tr class="memdesc:ad9a5f0534a08dc2d6cb9ad32b6581b8d"><td class="mdescLeft">&#160;</td><td class="mdescRight">The rotation as Euler angles in degrees.  <a href="class_lua_1_1_transform.html#ad9a5f0534a08dc2d6cb9ad32b6581b8d">More...</a><br /></td></tr>
<tr class="separator:ad9a5f0534a08dc2d6cb9ad32b6581b8d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad07cf6c2802bfbab50272030379f1826"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#ad07cf6c2802bfbab50272030379f1826">forward</a><code> [get, set]</code></td></tr>
<tr class="memdesc:ad07cf6c2802bfbab50272030379f1826"><td class="mdescLeft">&#160;</td><td class="mdescRight">The blue axis of the transform in world space.  <a href="class_lua_1_1_transform.html#ad07cf6c2802bfbab50272030379f1826">More...</a><br /></td></tr>
<tr class="separator:ad07cf6c2802bfbab50272030379f1826"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7923f3c584b87b8e56de6b32acbfba99"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a7923f3c584b87b8e56de6b32acbfba99">localEulerAngles</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a7923f3c584b87b8e56de6b32acbfba99"><td class="mdescLeft">&#160;</td><td class="mdescRight">The rotation as Euler angles in degrees relative to the parent transform's rotation.  <a href="class_lua_1_1_transform.html#a7923f3c584b87b8e56de6b32acbfba99">More...</a><br /></td></tr>
<tr class="separator:a7923f3c584b87b8e56de6b32acbfba99"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab081c482002c1e4fedbcfa090b19b90e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#ab081c482002c1e4fedbcfa090b19b90e">localPosition</a><code> [get, set]</code></td></tr>
<tr class="memdesc:ab081c482002c1e4fedbcfa090b19b90e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Position of the transform relative to the parent transform.  <a href="class_lua_1_1_transform.html#ab081c482002c1e4fedbcfa090b19b90e">More...</a><br /></td></tr>
<tr class="separator:ab081c482002c1e4fedbcfa090b19b90e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2397fd50baf04311df6a50e4dcc302bd"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a2397fd50baf04311df6a50e4dcc302bd">localRotation</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a2397fd50baf04311df6a50e4dcc302bd"><td class="mdescLeft">&#160;</td><td class="mdescRight">The rotation of the transform relative to the parent transform's rotation.  <a href="class_lua_1_1_transform.html#a2397fd50baf04311df6a50e4dcc302bd">More...</a><br /></td></tr>
<tr class="separator:a2397fd50baf04311df6a50e4dcc302bd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a40e2891bff5d714d77449aeee6d84492"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a40e2891bff5d714d77449aeee6d84492">localScale</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a40e2891bff5d714d77449aeee6d84492"><td class="mdescLeft">&#160;</td><td class="mdescRight">The scale of the transform relative to the parent.  <a href="class_lua_1_1_transform.html#a40e2891bff5d714d77449aeee6d84492">More...</a><br /></td></tr>
<tr class="separator:a40e2891bff5d714d77449aeee6d84492"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a55680638b6e6ae6b1bd4b5095b1822f1"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a55680638b6e6ae6b1bd4b5095b1822f1">lossyScale</a><code> [get]</code></td></tr>
<tr class="memdesc:a55680638b6e6ae6b1bd4b5095b1822f1"><td class="mdescLeft">&#160;</td><td class="mdescRight">The global scale of the object (Read Only).  <a href="class_lua_1_1_transform.html#a55680638b6e6ae6b1bd4b5095b1822f1">More...</a><br /></td></tr>
<tr class="separator:a55680638b6e6ae6b1bd4b5095b1822f1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af1ca076a9406c3865fef9cbf8393e484"><td class="memItemLeft" align="right" valign="top">string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#af1ca076a9406c3865fef9cbf8393e484">name</a><code> [get, set]</code></td></tr>
<tr class="memdesc:af1ca076a9406c3865fef9cbf8393e484"><td class="mdescLeft">&#160;</td><td class="mdescRight">The name of the object.  <a href="class_lua_1_1_transform.html#af1ca076a9406c3865fef9cbf8393e484">More...</a><br /></td></tr>
<tr class="separator:af1ca076a9406c3865fef9cbf8393e484"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad7ea4f4af441f263f3cbfcb853d3edfa"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#ad7ea4f4af441f263f3cbfcb853d3edfa">parent</a><code> [get]</code></td></tr>
<tr class="memdesc:ad7ea4f4af441f263f3cbfcb853d3edfa"><td class="mdescLeft">&#160;</td><td class="mdescRight">The parent of the transform.  <a href="class_lua_1_1_transform.html#ad7ea4f4af441f263f3cbfcb853d3edfa">More...</a><br /></td></tr>
<tr class="separator:ad7ea4f4af441f263f3cbfcb853d3edfa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1a4480b448b89a7e1f392af4c842cc28"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a1a4480b448b89a7e1f392af4c842cc28">entity</a><code> [get]</code></td></tr>
<tr class="memdesc:a1a4480b448b89a7e1f392af4c842cc28"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets owner entity.  <a href="class_lua_1_1_transform.html#a1a4480b448b89a7e1f392af4c842cc28">More...</a><br /></td></tr>
<tr class="separator:a1a4480b448b89a7e1f392af4c842cc28"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a789b6abed611a7576ca2262bb9c5e6c3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a789b6abed611a7576ca2262bb9c5e6c3">position</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a789b6abed611a7576ca2262bb9c5e6c3"><td class="mdescLeft">&#160;</td><td class="mdescRight">The position of the transform in world space.  <a href="class_lua_1_1_transform.html#a789b6abed611a7576ca2262bb9c5e6c3">More...</a><br /></td></tr>
<tr class="separator:a789b6abed611a7576ca2262bb9c5e6c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afa7cbcc49408b1a75564f5d379c877ac"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#afa7cbcc49408b1a75564f5d379c877ac">right</a><code> [get, set]</code></td></tr>
<tr class="memdesc:afa7cbcc49408b1a75564f5d379c877ac"><td class="mdescLeft">&#160;</td><td class="mdescRight">The red axis of the transform in world space.  <a href="class_lua_1_1_transform.html#afa7cbcc49408b1a75564f5d379c877ac">More...</a><br /></td></tr>
<tr class="separator:afa7cbcc49408b1a75564f5d379c877ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac54361eab00110ecfa6d6c53ffb78533"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#ac54361eab00110ecfa6d6c53ffb78533">root</a><code> [get]</code></td></tr>
<tr class="memdesc:ac54361eab00110ecfa6d6c53ffb78533"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the topmost transform in the hierarchy.  <a href="class_lua_1_1_transform.html#ac54361eab00110ecfa6d6c53ffb78533">More...</a><br /></td></tr>
<tr class="separator:ac54361eab00110ecfa6d6c53ffb78533"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab0b5488416c3d0f6e3de7b426227198c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#ab0b5488416c3d0f6e3de7b426227198c">rotation</a><code> [get, set]</code></td></tr>
<tr class="memdesc:ab0b5488416c3d0f6e3de7b426227198c"><td class="mdescLeft">&#160;</td><td class="mdescRight">The rotation of the transform in world space stored as a <a class="el" href="class_lua_1_1_quaternion.html" title="Quaternions are used to represent rotations.">Quaternion</a>.  <a href="class_lua_1_1_transform.html#ab0b5488416c3d0f6e3de7b426227198c">More...</a><br /></td></tr>
<tr class="separator:ab0b5488416c3d0f6e3de7b426227198c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a98b72263be2f13a2917369c22b8539f3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_transform.html#a98b72263be2f13a2917369c22b8539f3">up</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a98b72263be2f13a2917369c22b8539f3"><td class="mdescLeft">&#160;</td><td class="mdescRight">The green axis of the transform in world space.  <a href="class_lua_1_1_transform.html#a98b72263be2f13a2917369c22b8539f3">More...</a><br /></td></tr>
<tr class="separator:a98b72263be2f13a2917369c22b8539f3"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >Position, rotation and scale of an object. </p>
<p >Every object in a scene has a <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a>. It's used to store and manipulate the position, rotation and scale of the object. Every <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a> can have a parent, which allows you to apply position, rotation and scale hierarchically. <br  />
 </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a17093d64239d0605cfdd83d9154fcf08" name="a17093d64239d0605cfdd83d9154fcf08"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a17093d64239d0605cfdd83d9154fcf08">&#9670;&nbsp;</a></span>Equals()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static bool Lua.Transform.Equals </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td>
          <td class="paramname"><em>b</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Tests 2 transforms for equality. </p>
<p >Syntax for this operation is <code>transform1 == transform2</code>. Returns true if two variables point to the same transform. The inequality operator <code>transform1 ~= transform2</code> also uses this function, but negated. <br  />
 </p>

</div>
</div>
<a id="a79476caab32d323b7fee1955fda8d808" name="a79476caab32d323b7fee1955fda8d808"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a79476caab32d323b7fee1955fda8d808">&#9670;&nbsp;</a></span>Find()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_transform.html">Transform</a> Lua.Transform.Find </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>name</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Finds a child by name and returns it. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">name</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a2535f3bade200a7f2c2c59debaeed41a" name="a2535f3bade200a7f2c2c59debaeed41a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2535f3bade200a7f2c2c59debaeed41a">&#9670;&nbsp;</a></span>GetChild()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_transform.html">Transform</a> Lua.Transform.GetChild </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>index</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns a transform child by index. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">index</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a839d8eeda6ca8e0ea2a2e7b50643b0ca" name="a839d8eeda6ca8e0ea2a2e7b50643b0ca"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a839d8eeda6ca8e0ea2a2e7b50643b0ca">&#9670;&nbsp;</a></span>GetSiblingIndex()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int Lua.Transform.GetSiblingIndex </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Gets the sibling index. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a4983e88de730e650bb632f63d043035f" name="a4983e88de730e650bb632f63d043035f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4983e88de730e650bb632f63d043035f">&#9670;&nbsp;</a></span>InverseTransformDirection() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Transform.InverseTransformDirection </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>z</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Transforms a direction from world space to local space. The opposite of <a class="el" href="class_lua_1_1_transform.html#a14270ac6dbade453decf26513f533b66" title="Transforms direction from local space to world space.">Transform.TransformDirection</a>. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">x</td><td></td></tr>
    <tr><td class="paramname">y</td><td></td></tr>
    <tr><td class="paramname">z</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a7bf8b1d272b1d893a606c5f38770c433" name="a7bf8b1d272b1d893a606c5f38770c433"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7bf8b1d272b1d893a606c5f38770c433">&#9670;&nbsp;</a></span>InverseTransformDirection() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Transform.InverseTransformDirection </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>direction</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Transforms a direction from world space to local space. The opposite of <a class="el" href="class_lua_1_1_transform.html#a14270ac6dbade453decf26513f533b66" title="Transforms direction from local space to world space.">Transform.TransformDirection</a>. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">direction</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="af07e05c728e517c260e6cf8c1b442adc" name="af07e05c728e517c260e6cf8c1b442adc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af07e05c728e517c260e6cf8c1b442adc">&#9670;&nbsp;</a></span>InverseTransformPoint() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Transform.InverseTransformPoint </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>z</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Transforms position from world space to local space. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">x</td><td></td></tr>
    <tr><td class="paramname">y</td><td></td></tr>
    <tr><td class="paramname">z</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="aa32269bd79e72646057908fee2cb7f9e" name="aa32269bd79e72646057908fee2cb7f9e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa32269bd79e72646057908fee2cb7f9e">&#9670;&nbsp;</a></span>InverseTransformPoint() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Transform.InverseTransformPoint </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>direction</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Transforms position from world space to local space. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">direction</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a8922d01029a19d826eeb4d6f8f22ce06" name="a8922d01029a19d826eeb4d6f8f22ce06"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8922d01029a19d826eeb4d6f8f22ce06">&#9670;&nbsp;</a></span>InverseTransformVector() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Transform.InverseTransformVector </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>z</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Transforms a vector from world space to local space. The opposite of <a class="el" href="class_lua_1_1_transform.html#a8a4bb1f1feb42a0d3be3577e4463f5f4" title="Transforms vector from local space to world space.">Transform.TransformVector</a>. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">x</td><td></td></tr>
    <tr><td class="paramname">y</td><td></td></tr>
    <tr><td class="paramname">z</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ae6bb74c5b90a6f8db4c436a56f24f8eb" name="ae6bb74c5b90a6f8db4c436a56f24f8eb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae6bb74c5b90a6f8db4c436a56f24f8eb">&#9670;&nbsp;</a></span>InverseTransformVector() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Transform.InverseTransformVector </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>direction</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Transforms a vector from world space to local space. The opposite of <a class="el" href="class_lua_1_1_transform.html#a8a4bb1f1feb42a0d3be3577e4463f5f4" title="Transforms vector from local space to world space.">Transform.TransformVector</a>. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">direction</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ad11ff475738f907fdbdc4009c81ee09e" name="ad11ff475738f907fdbdc4009c81ee09e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad11ff475738f907fdbdc4009c81ee09e">&#9670;&nbsp;</a></span>IsChildOf()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.Transform.IsChildOf </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td>
          <td class="paramname"><em>parent</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Is this transform a child of parent? </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">parent</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a1e722de9c3eacff82477ab7684a67553" name="a1e722de9c3eacff82477ab7684a67553"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1e722de9c3eacff82477ab7684a67553">&#9670;&nbsp;</a></span>LookAt() <span class="overload">[1/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Transform.LookAt </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td>
          <td class="paramname"><em>target</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Not to be confused with <a class="el" href="class_lua_1_1_entity.html#a29cdb052c5422873a708c8080039cb4b" title="Not to be confused with Transform.LookAt If the entity is a giantess it will look towards a target....">Entity.LookAt</a> Rotates the transform so the forward vector points at /target/'s current position. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">target</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="acab4e79308fc20ffa13f6048b7cb3184" name="acab4e79308fc20ffa13f6048b7cb3184"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acab4e79308fc20ffa13f6048b7cb3184">&#9670;&nbsp;</a></span>LookAt() <span class="overload">[2/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Transform.LookAt </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td>
          <td class="paramname"><em>target</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>worldUp</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Not to be confused with <a class="el" href="class_lua_1_1_entity.html#a29cdb052c5422873a708c8080039cb4b" title="Not to be confused with Transform.LookAt If the entity is a giantess it will look towards a target....">Entity.LookAt</a> Rotates the transform so the forward vector points at /target/'s current position. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">target</td><td></td></tr>
    <tr><td class="paramname">worldUp</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="aa8630c1feef1c89cf7a201f6c92005ee" name="aa8630c1feef1c89cf7a201f6c92005ee"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa8630c1feef1c89cf7a201f6c92005ee">&#9670;&nbsp;</a></span>LookAt() <span class="overload">[3/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Transform.LookAt </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>worldPosition</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Not to be confused with <a class="el" href="class_lua_1_1_entity.html#a29cdb052c5422873a708c8080039cb4b" title="Not to be confused with Transform.LookAt If the entity is a giantess it will look towards a target....">Entity.LookAt</a> Rotates the transform so the forward vector points at /target/'s current position. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">worldPosition</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a9364734c07f954378c167f7f5258fa18" name="a9364734c07f954378c167f7f5258fa18"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9364734c07f954378c167f7f5258fa18">&#9670;&nbsp;</a></span>LookAt() <span class="overload">[4/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Transform.LookAt </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>worldPosition</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>worldUp</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Not to be confused with <a class="el" href="class_lua_1_1_entity.html#a29cdb052c5422873a708c8080039cb4b" title="Not to be confused with Transform.LookAt If the entity is a giantess it will look towards a target....">Entity.LookAt</a> Rotates the transform so the forward vector points at /target/'s current position. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">worldPosition</td><td></td></tr>
    <tr><td class="paramname">worldUp</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a44f0216f90d0df765efc719bdfbccde2" name="a44f0216f90d0df765efc719bdfbccde2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a44f0216f90d0df765efc719bdfbccde2">&#9670;&nbsp;</a></span>Rotate() <span class="overload">[1/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Transform.Rotate </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>xAngle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>yAngle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>zAngle</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Applies a rotation of eulerAngles.z degrees around the z axis, eulerAngles.x degrees around the x axis, and eulerAngles.y degrees around the y axis (in that order). </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">xAngle</td><td></td></tr>
    <tr><td class="paramname">yAngle</td><td></td></tr>
    <tr><td class="paramname">zAngle</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a7f9437d0324777be34be4722a9dc52a1" name="a7f9437d0324777be34be4722a9dc52a1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7f9437d0324777be34be4722a9dc52a1">&#9670;&nbsp;</a></span>Rotate() <span class="overload">[2/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Transform.Rotate </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>axis</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>angle</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Applies a rotation of eulerAngles.z degrees around the z axis, eulerAngles.x degrees around the x axis, and eulerAngles.y degrees around the y axis (in that order). </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">axis</td><td></td></tr>
    <tr><td class="paramname">angle</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="af5d67d5940a08bc18d105c884e53be84" name="af5d67d5940a08bc18d105c884e53be84"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af5d67d5940a08bc18d105c884e53be84">&#9670;&nbsp;</a></span>Rotate() <span class="overload">[3/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Transform.Rotate </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>eulerAngles</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Applies a rotation of eulerAngles.z degrees around the z axis, eulerAngles.x degrees around the x axis, and eulerAngles.y degrees around the y axis (in that order). </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">eulerAngles</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ad62f95fde354155f59cf1cf334cf3fec" name="ad62f95fde354155f59cf1cf334cf3fec"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad62f95fde354155f59cf1cf334cf3fec">&#9670;&nbsp;</a></span>Rotate() <span class="overload">[4/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Transform.Rotate </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>point</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>axis</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>angle</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Applies a rotation of eulerAngles.z degrees around the z axis, eulerAngles.x degrees around the x axis, and eulerAngles.y degrees around the y axis (in that order). </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">point</td><td></td></tr>
    <tr><td class="paramname">axis</td><td></td></tr>
    <tr><td class="paramname">angle</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a44299747664c0a77b5f03f69f032a86f" name="a44299747664c0a77b5f03f69f032a86f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a44299747664c0a77b5f03f69f032a86f">&#9670;&nbsp;</a></span>SetParent() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Transform.SetParent </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td>
          <td class="paramname"><em>parent</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set the parent of the transform. If parent is nil the transform will be detached from its parent if it has one. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">parent</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="afcf05788f6ff8a51e7bc012ffe087727" name="afcf05788f6ff8a51e7bc012ffe087727"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afcf05788f6ff8a51e7bc012ffe087727">&#9670;&nbsp;</a></span>SetParent() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Transform.SetParent </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td>
          <td class="paramname"><em>parent</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>worldPositionStays</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set the parent of the transform. If parent is nil the transform will be detached from its parent if it has one. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">parent</td><td></td></tr>
    <tr><td class="paramname">worldPositionStays</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ac828e92537ee4ca71ef3525f3f19511a" name="ac828e92537ee4ca71ef3525f3f19511a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac828e92537ee4ca71ef3525f3f19511a">&#9670;&nbsp;</a></span>TransformDirection() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Transform.TransformDirection </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>z</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Transforms direction from local space to world space. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">x</td><td></td></tr>
    <tr><td class="paramname">y</td><td></td></tr>
    <tr><td class="paramname">z</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a14270ac6dbade453decf26513f533b66" name="a14270ac6dbade453decf26513f533b66"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a14270ac6dbade453decf26513f533b66">&#9670;&nbsp;</a></span>TransformDirection() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Transform.TransformDirection </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>direction</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Transforms direction from local space to world space. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">direction</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a77910db0422ec17545d411c1aeaec50b" name="a77910db0422ec17545d411c1aeaec50b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a77910db0422ec17545d411c1aeaec50b">&#9670;&nbsp;</a></span>TransformPoint() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Transform.TransformPoint </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>z</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Transforms position from local space to world space. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">x</td><td></td></tr>
    <tr><td class="paramname">y</td><td></td></tr>
    <tr><td class="paramname">z</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a77c8ed5338803453798fbfe848ed02e5" name="a77c8ed5338803453798fbfe848ed02e5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a77c8ed5338803453798fbfe848ed02e5">&#9670;&nbsp;</a></span>TransformPoint() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Transform.TransformPoint </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>direction</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Transforms position from local space to world space. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">direction</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a79647850468bc87259dda4bc0b70e0ea" name="a79647850468bc87259dda4bc0b70e0ea"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a79647850468bc87259dda4bc0b70e0ea">&#9670;&nbsp;</a></span>TransformVector() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Transform.TransformVector </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>z</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Transforms vector from local space to world space. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">x</td><td></td></tr>
    <tr><td class="paramname">y</td><td></td></tr>
    <tr><td class="paramname">z</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a8a4bb1f1feb42a0d3be3577e4463f5f4" name="a8a4bb1f1feb42a0d3be3577e4463f5f4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8a4bb1f1feb42a0d3be3577e4463f5f4">&#9670;&nbsp;</a></span>TransformVector() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Transform.TransformVector </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>direction</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Transforms vector from local space to world space. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">direction</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a2edcc9870e4706eff9bd0fe5143ca179" name="a2edcc9870e4706eff9bd0fe5143ca179"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2edcc9870e4706eff9bd0fe5143ca179">&#9670;&nbsp;</a></span>Translate() <span class="overload">[1/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Transform.Translate </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>z</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Moves the transform by x along the x axis, y along the y axis, and z along the z axis. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">x</td><td></td></tr>
    <tr><td class="paramname">y</td><td></td></tr>
    <tr><td class="paramname">z</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a69bc98e214a973ff53dcb49b48ba06c7" name="a69bc98e214a973ff53dcb49b48ba06c7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a69bc98e214a973ff53dcb49b48ba06c7">&#9670;&nbsp;</a></span>Translate() <span class="overload">[2/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Transform.Translate </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>z</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td>
          <td class="paramname"><em>relativeTo</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Moves the transform in the direction and distance of translation. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">x</td><td></td></tr>
    <tr><td class="paramname">y</td><td></td></tr>
    <tr><td class="paramname">z</td><td></td></tr>
    <tr><td class="paramname">relativeTo</td><td></td></tr>
  </table>
  </dd>
</dl>
<p>The movement is applied relative to relativeTo's local coordinate system. If relativeTo is null, the movement is applied relative to the world coordinate system. </p>

</div>
</div>
<a id="a1a2933390110217890785619c897df7f" name="a1a2933390110217890785619c897df7f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1a2933390110217890785619c897df7f">&#9670;&nbsp;</a></span>Translate() <span class="overload">[3/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Transform.Translate </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>translation</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Moves the transform in the direction and distance of translation. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">translation</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="afa326b1db7b8629826fa763669888dd5" name="afa326b1db7b8629826fa763669888dd5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afa326b1db7b8629826fa763669888dd5">&#9670;&nbsp;</a></span>Translate() <span class="overload">[4/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Transform.Translate </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>translation</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td>
          <td class="paramname"><em>relativeTo</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Moves the transform in the direction and distance of translation. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">translation</td><td></td></tr>
    <tr><td class="paramname">relativeTo</td><td></td></tr>
  </table>
  </dd>
</dl>
<p>The movement is applied relative to relativeTo's local coordinate system. If relativeTo is null, the movement is applied relative to the world coordinate system. </p>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="a9d77f87171bcb8090f086ae405c4f89e" name="a9d77f87171bcb8090f086ae405c4f89e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9d77f87171bcb8090f086ae405c4f89e">&#9670;&nbsp;</a></span>childCount</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int Lua.Transform.childCount</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The number of children the <a class="el" href="class_lua_1_1_transform.html" title="Position, rotation and scale of an object.">Transform</a> has. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a1a4480b448b89a7e1f392af4c842cc28" name="a1a4480b448b89a7e1f392af4c842cc28"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1a4480b448b89a7e1f392af4c842cc28">&#9670;&nbsp;</a></span>entity</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_entity.html">Entity</a> Lua.Transform.entity</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Gets owner entity. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ad9a5f0534a08dc2d6cb9ad32b6581b8d" name="ad9a5f0534a08dc2d6cb9ad32b6581b8d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad9a5f0534a08dc2d6cb9ad32b6581b8d">&#9670;&nbsp;</a></span>eulerAngles</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Transform.eulerAngles</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The rotation as Euler angles in degrees. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ad07cf6c2802bfbab50272030379f1826" name="ad07cf6c2802bfbab50272030379f1826"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad07cf6c2802bfbab50272030379f1826">&#9670;&nbsp;</a></span>forward</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Transform.forward</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The blue axis of the transform in world space. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a7923f3c584b87b8e56de6b32acbfba99" name="a7923f3c584b87b8e56de6b32acbfba99"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7923f3c584b87b8e56de6b32acbfba99">&#9670;&nbsp;</a></span>localEulerAngles</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Transform.localEulerAngles</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The rotation as Euler angles in degrees relative to the parent transform's rotation. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ab081c482002c1e4fedbcfa090b19b90e" name="ab081c482002c1e4fedbcfa090b19b90e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab081c482002c1e4fedbcfa090b19b90e">&#9670;&nbsp;</a></span>localPosition</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Transform.localPosition</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Position of the transform relative to the parent transform. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a2397fd50baf04311df6a50e4dcc302bd" name="a2397fd50baf04311df6a50e4dcc302bd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2397fd50baf04311df6a50e4dcc302bd">&#9670;&nbsp;</a></span>localRotation</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> Lua.Transform.localRotation</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The rotation of the transform relative to the parent transform's rotation. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a40e2891bff5d714d77449aeee6d84492" name="a40e2891bff5d714d77449aeee6d84492"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a40e2891bff5d714d77449aeee6d84492">&#9670;&nbsp;</a></span>localScale</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Transform.localScale</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The scale of the transform relative to the parent. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>
<p>// </p>

</div>
</div>
<a id="a55680638b6e6ae6b1bd4b5095b1822f1" name="a55680638b6e6ae6b1bd4b5095b1822f1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a55680638b6e6ae6b1bd4b5095b1822f1">&#9670;&nbsp;</a></span>lossyScale</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Transform.lossyScale</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The global scale of the object (Read Only). </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="af1ca076a9406c3865fef9cbf8393e484" name="af1ca076a9406c3865fef9cbf8393e484"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af1ca076a9406c3865fef9cbf8393e484">&#9670;&nbsp;</a></span>name</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">string Lua.Transform.name</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The name of the object. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ad7ea4f4af441f263f3cbfcb853d3edfa" name="ad7ea4f4af441f263f3cbfcb853d3edfa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad7ea4f4af441f263f3cbfcb853d3edfa">&#9670;&nbsp;</a></span>parent</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_transform.html">Transform</a> Lua.Transform.parent</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The parent of the transform. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a789b6abed611a7576ca2262bb9c5e6c3" name="a789b6abed611a7576ca2262bb9c5e6c3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a789b6abed611a7576ca2262bb9c5e6c3">&#9670;&nbsp;</a></span>position</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Transform.position</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The position of the transform in world space. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="afa7cbcc49408b1a75564f5d379c877ac" name="afa7cbcc49408b1a75564f5d379c877ac"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afa7cbcc49408b1a75564f5d379c877ac">&#9670;&nbsp;</a></span>right</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Transform.right</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The red axis of the transform in world space. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ac54361eab00110ecfa6d6c53ffb78533" name="ac54361eab00110ecfa6d6c53ffb78533"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac54361eab00110ecfa6d6c53ffb78533">&#9670;&nbsp;</a></span>root</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_transform.html">Transform</a> Lua.Transform.root</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns the topmost transform in the hierarchy. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ab0b5488416c3d0f6e3de7b426227198c" name="ab0b5488416c3d0f6e3de7b426227198c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab0b5488416c3d0f6e3de7b426227198c">&#9670;&nbsp;</a></span>rotation</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> Lua.Transform.rotation</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The rotation of the transform in world space stored as a <a class="el" href="class_lua_1_1_quaternion.html" title="Quaternions are used to represent rotations.">Quaternion</a>. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a98b72263be2f13a2917369c22b8539f3" name="a98b72263be2f13a2917369c22b8539f3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a98b72263be2f13a2917369c22b8539f3">&#9670;&nbsp;</a></span>up</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Transform.up</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The green axis of the transform in world space. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaTransform.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_transform.html">Transform</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

FixGrab = RegisterBehavior("Fix Grab")
FixGrab.data = {
    agent = {
        type = { "giantess" }
    },
    target = {
        type = { "micro" }
    },
    menuEntry = "Fix Grab",
    forceAppearInManager = true
}

function FixGrab:Start()
    self.stop = false
    self.chasing = false
    self.crouching = false
    self.defaultLowerLegPos = self.agent.bones.leftLowerLeg.position.y - self.agent.transform.position.y
    self.standingUp = false
    self.standUpTime = 0
    self.keyPressed = false
    
    if self.agent.height < self.target.height * 5 then
        self.agent.animation.SetAndWait("No 2", true)
        print("Target is too large to grab!")
        self.agent.animation.Set("Idle 2", true)
        self.stop = true
    end
    
    Log("Fix Grab behavior activated - Press H to grab, J to release with FIXED GRAVITY")
    
    -- Initialize variables for continuous gravity application
    self.applyingGravity = false
    self.gravityTarget = nil
    self.gravityStartTime = 0
    self.gravityDuration = 5.0  -- Apply gravity for 5 seconds
end

-- Function to apply continuous gravity to a target
function FixGrab:ApplyContinuousGravity(target)
    -- Save the target and start time
    self.gravityTarget = target
    self.gravityStartTime = Time.time
    self.applyingGravity = true
    
    Log("Starting continuous gravity application for 5 seconds")
end

function FixGrab:Update()
    -- Check if we need to apply continuous gravity
    if self.applyingGravity and self.gravityTarget then
        -- Check if we should still be applying gravity
        local elapsedTime = Time.time - self.gravityStartTime
        
        if elapsedTime < self.gravityDuration then
            -- Target still exists and time hasn't expired, apply gravity
            pcall(function()
                if self.gravityTarget.rigidbody then
                    -- Make sure gravity is enabled
                    self.gravityTarget.rigidbody.useGravity = true
                    
                    -- Apply a continuous EXTREME downward force
                    self.gravityTarget.rigidbody.AddForce(Vector3.New(0, -3000, 0), ForceMode.Acceleration)
                    
                    -- Also set a downward velocity directly - MUCH stronger
                    local currentVel = self.gravityTarget.rigidbody.velocity
                    if currentVel.y > -30 then  -- Only override if not already falling very fast
                        self.gravityTarget.rigidbody.velocity = Vector3.New(currentVel.x, -30, currentVel.z)
                    end
                    
                    -- Log every second
                    if math.floor(elapsedTime) > math.floor(elapsedTime - Time.deltaTime) then
                        Log("Continuous gravity: " .. math.floor(elapsedTime) .. " seconds elapsed")
                    end
                end
            end)
        else
            -- Time expired, stop applying gravity
            self.applyingGravity = false
            self.gravityTarget = nil
            Log("Continuous gravity application complete")
        end
    end

    -- Check for H key press (grab)
    if Input.GetKeyDown("h") then
        self.keyPressed = true
        Log("H key pressed - attempting to grab")
    end
    
    -- Check for J key press (release)
    if Input.GetKeyDown("j") then
        self.releasePressed = true
        Log("J key pressed - attempting to release")
        
        -- Try to release micro
        if self.target and self.target.transform.IsChildOf(self.agent.transform) then
            Log("Trying to release micro")
            
            -- Save the current position before detaching
            local currentPos = self.target.transform.position
            
            -- Try to completely detach the target with multiple methods
            pcall(function() 
                -- First try standard detach
                self.target.transform.SetParent(nil)
                
                -- Also try to break any lingering connections
                if self.target.isPlayer then
                    -- Force player to be independent
                    self.target.player.isGrabbed = false
                    
                    -- Reset any potential parent-child relationships
                    if self.target.movement then
                        self.target.movement.followTransform = nil
                    end
                end
                
                Log("Used multiple methods to completely detach target")
            end)
            
            -- Keep the micro at the same position but ensure it's not under the map
            pcall(function()
                -- Force the player to a position BELOW their current position to start falling
                local safePos = Vector3.New(
                    currentPos.x + 0.05, -- Add a small X offset to help unstick
                    currentPos.y - 5.0,  -- FORCE Y position DOWN by 5 units to start falling
                    currentPos.z + 0.05  -- Add a small Z offset to help unstick
                )
                
                -- Set the micro's position
                self.target.transform.position = safePos
                
                -- Enable physics if available
                if self.target.rigidbody then
                    self.target.rigidbody.isKinematic = false
                    self.target.rigidbody.detectCollisions = true
                    self.target.rigidbody.useGravity = true  -- Explicitly enable gravity
                    Log("Gravity explicitly enabled")
                end
                
                -- Try to enable player control if it's a player
                pcall(function()
                    if self.target.isPlayer then
                        self.target.movement.enabled = true
                        self.target.movement.isGrounded = false
                        
                        -- Try to reset player state and physics completely
                        if self.target.player then
                            -- First try to reset player state
                            self.target.player.isGrabbed = false
                            
                            -- Try to reset any movement modes that might interfere with gravity
                            pcall(function()
                                -- Disable any special movement modes
                                self.target.player.isFlying = false
                                self.target.player.isClimbing = false
                                
                                -- Force falling state
                                if self.target.movement then
                                    self.target.movement.isFalling = true
                                end
                            end)
                            
                            -- Apply a VERY strong downward force
                            if self.target.rigidbody then
                                -- Reset rigidbody properties
                                self.target.rigidbody.isKinematic = false
                                self.target.rigidbody.detectCollisions = true
                                self.target.rigidbody.useGravity = true  -- Explicitly enable gravity
                                
                                -- Set mass to ensure proper gravity
                                self.target.rigidbody.mass = 1.0
                                
                                -- Set drag to ZERO to eliminate air resistance
                                self.target.rigidbody.drag = 0.0
                                self.target.rigidbody.angularDrag = 0.05
                                
                                -- Reset velocity completely
                                self.target.rigidbody.velocity = Vector3.zero
                                self.target.rigidbody.angularVelocity = Vector3.zero
                                
                                -- Then apply a MASSIVE downward force
                                self.target.rigidbody.AddForce(Vector3.New(0, -3000, 0), ForceMode.Acceleration)
                                
                                -- Also set velocity directly
                                self.target.rigidbody.velocity = Vector3.New(0, -20, 0)
                                
                                Log("Reset physics completely and applied MASSIVE downward acceleration")
                                
                                -- Start continuous gravity application
                                self:ApplyContinuousGravity(self.target)
                            end
                        end
                    end
                end)
            end)
            
            -- Also stop the action
            pcall(function() 
                self.agent.ai.StopAction()
                self.agent.animation.Set("Idle 4", true)
            end)
            
            -- Reset flags to allow grabbing again
            self.releasePressed = false
            self.keyPressed = false
            self.chasing = false
            self.stop = false  -- Make sure the behavior doesn't stop
            
            -- Stand up if crouching
            if self.agent.animation.Get() == "Crouch Idle" then
                self.agent.ai.StopAction()
                self.agent.animation.Set("Idle 4", true)
                self.standingUp = true
                self.standUpTime = 0
                self.crouching = false
            end
        end
    end

    if not self.agent.ai.IsActionActive() then
        if self.stop then
            self.agent.ai.StopBehavior() -- if you use Update() you must manually tell when to end the behavior, after this the Exit() method will run
            return
        else
            if not self.target or not self.target.IsTargettable() then -- when looping the action, it needs to change the target when
                self.target = self.agent.findClosestMicro()    -- the first target is dead
                if not self.target then
                    self.agent.ai.StopBehavior() -- if it can't find a new target, then cancel the action
                    return
                end
            end
            
            -- If we're in the standing up transition, handle that first
            if self.standingUp then
                self.standUpTime = self.standUpTime + Time.deltaTime
                
                -- Keep the agent in place during the standing up transition
                if self.standUpTime < 1.0 then
                    -- Keep the idle animation going and prevent rotation
                    self.agent.animation.Set("Idle 4", true)
                    
                    -- Position arm near chest if we have IK
                    if self.agent.ik and self.agent.ik.rightHand then
                        -- Get chest position
                        local chestPos = self.agent.bones.spine2.position
                        
                        -- Offset slightly to the right and forward
                        local targetPos = Vector3.New(
                            chestPos.x + self.agent.scale * 0.1,
                            chestPos.y,
                            chestPos.z + self.agent.scale * 0.15
                        )
                        
                        -- Set the right hand position
                        self.agent.ik.rightHand.position = targetPos
                        self.agent.ik.rightHand.positionWeight = 1
                    end
                else
                    -- Transition complete
                    self.standingUp = false
                end
                
                -- Continue to next frame
                return
            end
            
            -- Always use standing distance for following
            local closest = self.agent.scale * 0.4
            
            -- Check if we need to move to the target
            if self.agent.DistanceTo(Vector3.New(self.target.position.x, self.agent.position.y, self.target.position.z)) > closest then
                -- We're too far, need to move closer
                if not self.chasing then
                    self.chasing = true
                    self.agent.ai.StopAction()
                    self.agent.LookAt(self.target)
                    self.agent.animation.Set("Walk", true)
                    self.agent.MoveTo(self.target)
                    self.agent.Face(self.target)
                    Log("Moving toward target")
                    -- Don't stop the behavior after approaching
                    self.stop = false
                end
            else
                -- We're close enough to the target
                if self.chasing then
                    -- Just stop and stand idle - no crouching until H is pressed
                    self.chasing = false
                    self.crouching = false
                    self.agent.ai.StopAction()
                    self.agent.animation.Set("Idle 4", true)
                    Log("In position - press H to grab")
                end
                
                -- Only crouch and grab if H key was pressed
                if self.keyPressed then
                    -- Determine if we need to crouch based on target height
                    local needToCrouch = self.target.transform.position.y <= self.agent.transform.position.y + self.defaultLowerLegPos
                    
                    -- If we need to crouch and aren't already crouching, do it now
                    if needToCrouch and not self.crouching then
                        self.crouching = true
                        self.agent.animation.Set("Crouch Idle", true)
                        -- Don't use Wait() as it's causing errors
                    end
                    
                    -- Now grab the target
                    if self.target.transform.IsChildOf(self.agent.transform) then
                        -- Already grabbed, do nothing
                        Log("Target already grabbed")
                    else
                        -- Grab the target directly without animation
                        Log("Grabbing target directly")
                        
                        -- Get the right hand bone
                        local rightHand = self.agent.bones.rightHand
                        
                        -- Attach the target to the right hand
                        self.target.transform.SetParent(rightHand)
                        
                        -- Position the target in the palm
                        self.target.transform.localPosition = Vector3.New(0, 0, 0)
                        
                        -- Keep the target's original rotation
                        self.target.transform.localRotation = Quaternion.identity
                        
                        -- Disable physics on the target
                        if self.target.rigidbody then
                            self.target.rigidbody.isKinematic = true
                            self.target.rigidbody.detectCollisions = false
                        end
                        
                        -- If it's a player, mark as grabbed
                        if self.target.isPlayer then
                            self.target.player.isGrabbed = true
                            
                            -- Disable movement
                            if self.target.movement then
                                self.target.movement.enabled = false
                            end
                        end
                        
                        Log("Target successfully grabbed directly")
                    end
                    
                    -- Reset the key press flag
                    self.keyPressed = false
                end
            end
        end
    end
end

function FixGrab:Exit()
    -- Release the micro if we're still holding it
    if self.target and self.target.transform.IsChildOf(self.agent.transform) then
        -- Save the current position before detaching
        local currentPos = self.target.transform.position
        
        -- Try to completely detach the target with multiple methods
        pcall(function() 
            -- First try standard detach
            self.target.transform.SetParent(nil)
            
            -- Also try to break any lingering connections
            if self.target.isPlayer then
                -- Force player to be independent
                self.target.player.isGrabbed = false
                
                -- Reset any potential parent-child relationships
                if self.target.movement then
                    self.target.movement.followTransform = nil
                end
            end
            
            Log("Used multiple methods to completely detach target")
        end)
        
        -- Keep the micro at the same position but ensure it's not under the map
        pcall(function()
            -- Force the player to a position BELOW their current position to start falling
            local safePos = Vector3.New(
                currentPos.x + 0.05, -- Add a small X offset to help unstick
                currentPos.y - 5.0,  -- FORCE Y position DOWN by 5 units to start falling
                currentPos.z + 0.05  -- Add a small Z offset to help unstick
            )
            
            -- Set the micro's position
            self.target.transform.position = safePos
            
            -- Enable physics if available
            if self.target.rigidbody then
                self.target.rigidbody.isKinematic = false
                self.target.rigidbody.detectCollisions = true
                self.target.rigidbody.useGravity = true  -- Explicitly enable gravity
                Log("Gravity explicitly enabled")
            end
            
            -- Try to enable player control if it's a player
            pcall(function()
                if self.target.isPlayer then
                    self.target.movement.enabled = true
                    self.target.movement.isGrounded = false
                    
                    -- Try to reset player state and toggle climbing to unstick
                    if self.target.player then
                        self.target.player.isGrabbed = false
                        
                        -- Try to reset any movement modes that might interfere with gravity
                        pcall(function()
                            -- Disable any special movement modes
                            self.target.player.isFlying = false
                            self.target.player.isClimbing = false
                            
                            -- Force falling state
                            if self.target.movement then
                                self.target.movement.isFalling = true
                            end
                        end)
                        
                        -- Apply a VERY strong downward force
                        if self.target.rigidbody then
                            -- Reset rigidbody properties
                            self.target.rigidbody.isKinematic = false
                            self.target.rigidbody.detectCollisions = true
                            self.target.rigidbody.useGravity = true
                            
                            -- Set mass to ensure proper gravity
                            self.target.rigidbody.mass = 1.0
                            
                            -- Set drag to normal values
                            self.target.rigidbody.drag = 0.0
                            self.target.rigidbody.angularDrag = 0.05
                            
                            -- Reset velocity completely
                            self.target.rigidbody.velocity = Vector3.zero
                            self.target.rigidbody.angularVelocity = Vector3.zero
                            
                            -- Then apply a strong downward force
                            self.target.rigidbody.AddForce(Vector3.New(0, -3000, 0), ForceMode.Acceleration)
                            Log("Reset physics completely and applied MASSIVE downward acceleration")
                            
                            -- Start continuous gravity application
                            self:ApplyContinuousGravity(self.target)
                        end
                    end
                end
            end)
        end)
        
        -- Also stop the action
        pcall(function() 
            self.agent.ai.StopAction()
            self.agent.animation.Set("Idle 4", true)
        end)
        
        -- Reset flags
        self.keyPressed = false
        self.chasing = false
        self.crouching = false
        
        Log("Standing up from crouch with micro")
    end
end
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.Random Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_random.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="class_lua_1_1_random-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.Random Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Class for generating random data.  
 <a href="class_lua_1_1_random.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-static-methods" name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:ac69b6f407406ae02a7595403097ec8a8"><td class="memItemLeft" align="right" valign="top">static void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_random.html#ac69b6f407406ae02a7595403097ec8a8">InitState</a> (int seed)</td></tr>
<tr class="memdesc:ac69b6f407406ae02a7595403097ec8a8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initializes the random number generator state with a seed.  <a href="class_lua_1_1_random.html#ac69b6f407406ae02a7595403097ec8a8">More...</a><br /></td></tr>
<tr class="separator:ac69b6f407406ae02a7595403097ec8a8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab17e85e47aeaa3f719b649e51bb49d28"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_random.html#ab17e85e47aeaa3f719b649e51bb49d28">Range</a> (int min, int max)</td></tr>
<tr class="memdesc:ab17e85e47aeaa3f719b649e51bb49d28"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a random float number between and min [inclusive] and max [inclusive] (Read Only).  <a href="class_lua_1_1_random.html#ab17e85e47aeaa3f719b649e51bb49d28">More...</a><br /></td></tr>
<tr class="separator:ab17e85e47aeaa3f719b649e51bb49d28"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5afbf9cff5e196e2fd7e5ab917b64a8b"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_random.html#a5afbf9cff5e196e2fd7e5ab917b64a8b">Range</a> (float min, float max)</td></tr>
<tr class="memdesc:a5afbf9cff5e196e2fd7e5ab917b64a8b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a random float number between and min [inclusive] and max [inclusive] (Read Only).  <a href="class_lua_1_1_random.html#a5afbf9cff5e196e2fd7e5ab917b64a8b">More...</a><br /></td></tr>
<tr class="separator:a5afbf9cff5e196e2fd7e5ab917b64a8b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="properties" name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a2e9c7e49b4362f7ab3335f0a46778b70"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_random.html#a2e9c7e49b4362f7ab3335f0a46778b70">insideUnitCircle</a><code> [get]</code></td></tr>
<tr class="memdesc:a2e9c7e49b4362f7ab3335f0a46778b70"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a random point inside a circle with radius 1 (Read Only).  <a href="class_lua_1_1_random.html#a2e9c7e49b4362f7ab3335f0a46778b70">More...</a><br /></td></tr>
<tr class="separator:a2e9c7e49b4362f7ab3335f0a46778b70"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1c3fa5f6de20e35af1e85564ef928137"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_random.html#a1c3fa5f6de20e35af1e85564ef928137">insideUnitSphere</a><code> [get]</code></td></tr>
<tr class="memdesc:a1c3fa5f6de20e35af1e85564ef928137"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a random point inside a sphere with radius 1 (Read Only).  <a href="class_lua_1_1_random.html#a1c3fa5f6de20e35af1e85564ef928137">More...</a><br /></td></tr>
<tr class="separator:a1c3fa5f6de20e35af1e85564ef928137"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a558a1a73871855fb64ba923ebc7353c4"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_random.html#a558a1a73871855fb64ba923ebc7353c4">onUnitSphere</a><code> [get]</code></td></tr>
<tr class="memdesc:a558a1a73871855fb64ba923ebc7353c4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a random point on the surface of a sphere with radius 1 (Read Only).  <a href="class_lua_1_1_random.html#a558a1a73871855fb64ba923ebc7353c4">More...</a><br /></td></tr>
<tr class="separator:a558a1a73871855fb64ba923ebc7353c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2705c860d3280bb5e5d0593d59fad8e3"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_random.html#a2705c860d3280bb5e5d0593d59fad8e3">rotation</a><code> [get]</code></td></tr>
<tr class="memdesc:a2705c860d3280bb5e5d0593d59fad8e3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a random rotation (Read Only).  <a href="class_lua_1_1_random.html#a2705c860d3280bb5e5d0593d59fad8e3">More...</a><br /></td></tr>
<tr class="separator:a2705c860d3280bb5e5d0593d59fad8e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a99f73343c5e319aff692abcca2ca51a1"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_random.html#a99f73343c5e319aff692abcca2ca51a1">rotationUniform</a><code> [get]</code></td></tr>
<tr class="memdesc:a99f73343c5e319aff692abcca2ca51a1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a random rotation with uniform distribution (Read Only).  <a href="class_lua_1_1_random.html#a99f73343c5e319aff692abcca2ca51a1">More...</a><br /></td></tr>
<tr class="separator:a99f73343c5e319aff692abcca2ca51a1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a058b72bbd12cd2d665b14d0479b52ad7"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_random.html#a058b72bbd12cd2d665b14d0479b52ad7">value</a><code> [get]</code></td></tr>
<tr class="memdesc:a058b72bbd12cd2d665b14d0479b52ad7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a random number between 0.0 [inclusive] and 1.0 [inclusive] (Read Only).  <a href="class_lua_1_1_random.html#a058b72bbd12cd2d665b14d0479b52ad7">More...</a><br /></td></tr>
<tr class="separator:a058b72bbd12cd2d665b14d0479b52ad7"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >Class for generating random data. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="ac69b6f407406ae02a7595403097ec8a8" name="ac69b6f407406ae02a7595403097ec8a8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac69b6f407406ae02a7595403097ec8a8">&#9670;&nbsp;</a></span>InitState()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static void Lua.Random.InitState </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>seed</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Initializes the random number generator state with a seed. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">seed</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a5afbf9cff5e196e2fd7e5ab917b64a8b" name="a5afbf9cff5e196e2fd7e5ab917b64a8b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5afbf9cff5e196e2fd7e5ab917b64a8b">&#9670;&nbsp;</a></span>Range() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Random.Range </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>min</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>max</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns a random float number between and min [inclusive] and max [inclusive] (Read Only). </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">min</td><td></td></tr>
    <tr><td class="paramname">max</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ab17e85e47aeaa3f719b649e51bb49d28" name="ab17e85e47aeaa3f719b649e51bb49d28"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab17e85e47aeaa3f719b649e51bb49d28">&#9670;&nbsp;</a></span>Range() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Random.Range </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>min</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>max</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns a random float number between and min [inclusive] and max [inclusive] (Read Only). </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">min</td><td></td></tr>
    <tr><td class="paramname">max</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="a2e9c7e49b4362f7ab3335f0a46778b70" name="a2e9c7e49b4362f7ab3335f0a46778b70"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2e9c7e49b4362f7ab3335f0a46778b70">&#9670;&nbsp;</a></span>insideUnitCircle</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Random.insideUnitCircle</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns a random point inside a circle with radius 1 (Read Only). </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a1c3fa5f6de20e35af1e85564ef928137" name="a1c3fa5f6de20e35af1e85564ef928137"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1c3fa5f6de20e35af1e85564ef928137">&#9670;&nbsp;</a></span>insideUnitSphere</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Random.insideUnitSphere</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns a random point inside a sphere with radius 1 (Read Only). </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a558a1a73871855fb64ba923ebc7353c4" name="a558a1a73871855fb64ba923ebc7353c4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a558a1a73871855fb64ba923ebc7353c4">&#9670;&nbsp;</a></span>onUnitSphere</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.Random.onUnitSphere</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns a random point on the surface of a sphere with radius 1 (Read Only). </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a2705c860d3280bb5e5d0593d59fad8e3" name="a2705c860d3280bb5e5d0593d59fad8e3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2705c860d3280bb5e5d0593d59fad8e3">&#9670;&nbsp;</a></span>rotation</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> Lua.Random.rotation</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns a random rotation (Read Only). </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a99f73343c5e319aff692abcca2ca51a1" name="a99f73343c5e319aff692abcca2ca51a1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a99f73343c5e319aff692abcca2ca51a1">&#9670;&nbsp;</a></span>rotationUniform</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> Lua.Random.rotationUniform</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns a random rotation with uniform distribution (Read Only). </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a058b72bbd12cd2d665b14d0479b52ad7" name="a058b72bbd12cd2d665b14d0479b52ad7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a058b72bbd12cd2d665b14d0479b52ad7">&#9670;&nbsp;</a></span>value</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Random.value</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns a random number between 0.0 [inclusive] and 1.0 [inclusive] (Read Only). </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaRandom.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_random.html">Random</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

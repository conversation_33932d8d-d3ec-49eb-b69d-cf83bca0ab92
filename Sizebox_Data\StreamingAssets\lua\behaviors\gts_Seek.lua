Stroll = RegisterBehavior("StrollStomp_Seek")
Stroll.scores = {
    hostile = 100,     --[[ the higher the value the more likely to choose that action ]]
    curious = -30,
}
Stroll.data = {
	menuEntry = "Walk/StrollStomp_Seek",
    agent = {
        type = { "giantess" }
    },
    target = {
        type = { "player" }
    }
}
    
IDLE = "Idle 2"
WALK = "Walk" 
stompAnimModule = require "stomp_anim"

function Stroll:Start()
    self.stop = false -- A stop variable to end the behavior.. this is custom for this script
	self.agent.senses.baseVisibilityDistance = 100000
	self.agent.senses.fieldOfView = 180
    --self.target = self.agent.FindRandomBuilding(self.agent) --  check if theres a building to walk into
    if not self.target then
		self.stop = true    --  there are no more buildings near the gts, stop the script
		log("No building was found for the stroll script")
		return -- return early, don't bother making a seed since we're not going to run anyway
	end
	Random.InitState(Mathf.Round(Time.timeSinceLevelLoad)*self.agent.id) -- get a somewhat random seed for the animations
end

function Stroll:Update()
    if not self.agent.ai.IsActionActive() then

        if self.stop then
            self.agent.ai.StopBehavior() -- if you use Update() you must manually tell when to end the behavior, after this the Exit() method will run
            return
        else

		if self.agent.senses.CanSee(self.target) and self.agent.DistanceTo(self.target) >= (self.agent.scale * 0.5) then
			log("I see you")
			self.agent.animation.Set(WALK)
			self.agent.MoveTo(self.target)   -- Move to the target, buildings are static so use the cheapest call possible
			self.agent.animation.Set(IDLE)
			self.agent.Stomp(self.target)
		else
			self.target2 = self.agent.FindRandomBuilding(self.agent) --  check if there is any building nearby
			self.agent.animation.Set(WALK)
			self.agent.MoveTo(self.target2)   -- Move to the target, buildings are static so use the cheapest call possible
			self.agent.animation.Set(IDLE)
			self.agent.Stomp(self.target2)
		end
		
        if not self.target then
            self.stop = true    --  there are no more buildings near the gts, stop the script
            return
        end
		
 
        self.stop = self.agent.ai.IsAIEnabled() or self.agent.ai.HasQueuedBehaviors() 
        end
    end
end

function Stroll:Exit()
    self.agent.animation.Set(IDLE)
end

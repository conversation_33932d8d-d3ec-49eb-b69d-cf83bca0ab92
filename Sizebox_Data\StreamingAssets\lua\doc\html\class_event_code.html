<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: EventCode Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_event_code.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="class_event_code-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">EventCode Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Lists hardcoded engine <a class="el" href="class_lua_1_1_event.html" title="Interface of the Event system.">Lua.Event</a> names.  
 <a href="class_event_code.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-static-attribs" name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:a279c4c5e28c223c4a5c590af25823780"><td class="memItemLeft" align="right" valign="top">static string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_event_code.html#a279c4c5e28c223c4a5c590af25823780">OnCrush</a> = &quot;OnCrush&quot;</td></tr>
<tr class="memdesc:a279c4c5e28c223c4a5c590af25823780"><td class="mdescLeft">&#160;</td><td class="mdescRight">Entity crushing micro event.  <a href="class_event_code.html#a279c4c5e28c223c4a5c590af25823780">More...</a><br /></td></tr>
<tr class="separator:a279c4c5e28c223c4a5c590af25823780"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afe7b5546673f18b5aacb38e272f8dcc0"><td class="memItemLeft" align="right" valign="top">static string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_event_code.html#afe7b5546673f18b5aacb38e272f8dcc0">OnStep</a> = &quot;OnStep&quot;</td></tr>
<tr class="memdesc:afe7b5546673f18b5aacb38e272f8dcc0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Entity footstep event.  <a href="class_event_code.html#afe7b5546673f18b5aacb38e272f8dcc0">More...</a><br /></td></tr>
<tr class="separator:afe7b5546673f18b5aacb38e272f8dcc0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a03a965e04c12fca8491beb8ec3753df4"><td class="memItemLeft" align="right" valign="top">static string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_event_code.html#a03a965e04c12fca8491beb8ec3753df4">OnSpawn</a> = &quot;OnSpawn&quot;</td></tr>
<tr class="memdesc:a03a965e04c12fca8491beb8ec3753df4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Entity spawned event.  <a href="class_event_code.html#a03a965e04c12fca8491beb8ec3753df4">More...</a><br /></td></tr>
<tr class="separator:a03a965e04c12fca8491beb8ec3753df4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a55f432be68d01cd15b1bf04936237711"><td class="memItemLeft" align="right" valign="top">static string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_event_code.html#a55f432be68d01cd15b1bf04936237711">OnActionComplete</a> = &quot;OnActionComplete&quot;</td></tr>
<tr class="memdesc:a55f432be68d01cd15b1bf04936237711"><td class="mdescLeft">&#160;</td><td class="mdescRight">Action complete event.  <a href="class_event_code.html#a55f432be68d01cd15b1bf04936237711">More...</a><br /></td></tr>
<tr class="separator:a55f432be68d01cd15b1bf04936237711"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaaadd94dd51e82dc133c6e5147e2ede2"><td class="memItemLeft" align="right" valign="top">static string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_event_code.html#aaaadd94dd51e82dc133c6e5147e2ede2">OnPlayerRaygunHit</a> = &quot;OnPlayerRaygunHit&quot;</td></tr>
<tr class="memdesc:aaaadd94dd51e82dc133c6e5147e2ede2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Player Raygun hit event. For raygun hits sent by the player  <a href="class_event_code.html#aaaadd94dd51e82dc133c6e5147e2ede2">More...</a><br /></td></tr>
<tr class="separator:aaaadd94dd51e82dc133c6e5147e2ede2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a42a2b4374afaaf0c95694abdd93e7fe9"><td class="memItemLeft" align="right" valign="top"><a id="a42a2b4374afaaf0c95694abdd93e7fe9" name="a42a2b4374afaaf0c95694abdd93e7fe9"></a>
static string&#160;</td><td class="memItemRight" valign="bottom"><b>OnTriggerPress</b> = &quot;OnTriggerPress&quot;</td></tr>
<tr class="memdesc:a42a2b4374afaaf0c95694abdd93e7fe9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Player weapon trigger press event. For when player starts firing their weapon <br /></td></tr>
<tr class="separator:a42a2b4374afaaf0c95694abdd93e7fe9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a62441710d089523d4630ac962020a6b5"><td class="memItemLeft" align="right" valign="top"><a id="a62441710d089523d4630ac962020a6b5" name="a62441710d089523d4630ac962020a6b5"></a>
static string&#160;</td><td class="memItemRight" valign="bottom"><b>OnTriggerRelease</b> = &quot;OnTriggerRelease&quot;</td></tr>
<tr class="memdesc:a62441710d089523d4630ac962020a6b5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Player weapon trigger release event. For when player stops firing their weapon <br /></td></tr>
<tr class="separator:a62441710d089523d4630ac962020a6b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0bb7a7064c6259124a48767341b0211f"><td class="memItemLeft" align="right" valign="top">static string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_event_code.html#a0bb7a7064c6259124a48767341b0211f">OnAIRaygunHit</a> = &quot;OnAIRaygunHit&quot;</td></tr>
<tr class="memdesc:a0bb7a7064c6259124a48767341b0211f"><td class="mdescLeft">&#160;</td><td class="mdescRight">AI Raygun hit event. For raygun hits sent by an AI micro  <a href="class_event_code.html#a0bb7a7064c6259124a48767341b0211f">More...</a><br /></td></tr>
<tr class="separator:a0bb7a7064c6259124a48767341b0211f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a379d0656758a2f6d45f7a6b497f2d1e0"><td class="memItemLeft" align="right" valign="top">static string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_event_code.html#a379d0656758a2f6d45f7a6b497f2d1e0">OnAISMGHit</a> = &quot;OnAISMGHit&quot;</td></tr>
<tr class="memdesc:a379d0656758a2f6d45f7a6b497f2d1e0"><td class="mdescLeft">&#160;</td><td class="mdescRight">AI Raygun hit event. For raygun hits sent by an AI micro  <a href="class_event_code.html#a379d0656758a2f6d45f7a6b497f2d1e0">More...</a><br /></td></tr>
<tr class="separator:a379d0656758a2f6d45f7a6b497f2d1e0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6c5540673e62192cd85f1ef886e8076e"><td class="memItemLeft" align="right" valign="top">static string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_event_code.html#a6c5540673e62192cd85f1ef886e8076e">OnAIWeaponFire</a> = &quot;OnAIWeaponFire&quot;</td></tr>
<tr class="memdesc:a6c5540673e62192cd85f1ef886e8076e"><td class="mdescLeft">&#160;</td><td class="mdescRight">AI weapon fire event. For when an AI micro fires their weapon  <a href="class_event_code.html#a6c5540673e62192cd85f1ef886e8076e">More...</a><br /></td></tr>
<tr class="separator:a6c5540673e62192cd85f1ef886e8076e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa0bed09744a5043231ce30c55d7d70ac"><td class="memItemLeft" align="right" valign="top"><a id="aa0bed09744a5043231ce30c55d7d70ac" name="aa0bed09744a5043231ce30c55d7d70ac"></a>
static string&#160;</td><td class="memItemRight" valign="bottom"><b>OnLocalPlayerChanged</b> = &quot;OnLocalPlayerChanged&quot;</td></tr>
<tr class="memdesc:aa0bed09744a5043231ce30c55d7d70ac"><td class="mdescLeft">&#160;</td><td class="mdescRight">User switched the entity they are playing as. <br /></td></tr>
<tr class="separator:aa0bed09744a5043231ce30c55d7d70ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae742599b4e26effaa66be61f267fa9d0"><td class="memItemLeft" align="right" valign="top"><a id="ae742599b4e26effaa66be61f267fa9d0" name="ae742599b4e26effaa66be61f267fa9d0"></a>
static string&#160;</td><td class="memItemRight" valign="bottom"><b>OnLocalSelectionChanged</b> = &quot;OnLocalSelectionChanged&quot;</td></tr>
<tr class="memdesc:ae742599b4e26effaa66be61f267fa9d0"><td class="mdescLeft">&#160;</td><td class="mdescRight">User switch the entity selection. <br /></td></tr>
<tr class="separator:ae742599b4e26effaa66be61f267fa9d0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a189f8c13a991ae8aa4d3cad7e4911ec3"><td class="memItemLeft" align="right" valign="top">static string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_event_code.html#a189f8c13a991ae8aa4d3cad7e4911ec3">KeyDown</a> = &quot;KeyDown&quot;</td></tr>
<tr class="memdesc:a189f8c13a991ae8aa4d3cad7e4911ec3"><td class="mdescLeft">&#160;</td><td class="mdescRight">User pressed a key.  <a href="class_event_code.html#a189f8c13a991ae8aa4d3cad7e4911ec3">More...</a><br /></td></tr>
<tr class="separator:a189f8c13a991ae8aa4d3cad7e4911ec3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7839436cfaf2589ea6fd56819be021a5"><td class="memItemLeft" align="right" valign="top"><a id="a7839436cfaf2589ea6fd56819be021a5" name="a7839436cfaf2589ea6fd56819be021a5"></a>
static string&#160;</td><td class="memItemRight" valign="bottom"><b>KeyUp</b> = &quot;KeyUp&quot;</td></tr>
<tr class="memdesc:a7839436cfaf2589ea6fd56819be021a5"><td class="mdescLeft">&#160;</td><td class="mdescRight">User released a key. <br /></td></tr>
<tr class="separator:a7839436cfaf2589ea6fd56819be021a5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32fa7eb106bc429a8c854b81caa2d38c"><td class="memItemLeft" align="right" valign="top"><a id="a32fa7eb106bc429a8c854b81caa2d38c" name="a32fa7eb106bc429a8c854b81caa2d38c"></a>
static string&#160;</td><td class="memItemRight" valign="bottom"><b>MouseDown</b> = &quot;MouseDown&quot;</td></tr>
<tr class="memdesc:a32fa7eb106bc429a8c854b81caa2d38c"><td class="mdescLeft">&#160;</td><td class="mdescRight">User pressed a mouse button. <br /></td></tr>
<tr class="separator:a32fa7eb106bc429a8c854b81caa2d38c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae6b50b9aff11a3a1a10e0e2836e159f8"><td class="memItemLeft" align="right" valign="top"><a id="ae6b50b9aff11a3a1a10e0e2836e159f8" name="ae6b50b9aff11a3a1a10e0e2836e159f8"></a>
static string&#160;</td><td class="memItemRight" valign="bottom"><b>MouseUp</b> = &quot;MouseUp&quot;</td></tr>
<tr class="memdesc:ae6b50b9aff11a3a1a10e0e2836e159f8"><td class="mdescLeft">&#160;</td><td class="mdescRight">User released a mouse button. <br /></td></tr>
<tr class="separator:ae6b50b9aff11a3a1a10e0e2836e159f8"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >Lists hardcoded engine <a class="el" href="class_lua_1_1_event.html" title="Interface of the Event system.">Lua.Event</a> names. </p>
</div><h2 class="groupheader">Member Data Documentation</h2>
<a id="a189f8c13a991ae8aa4d3cad7e4911ec3" name="a189f8c13a991ae8aa4d3cad7e4911ec3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a189f8c13a991ae8aa4d3cad7e4911ec3">&#9670;&nbsp;</a></span>KeyDown</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">string EventCode.KeyDown = &quot;KeyDown&quot;</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>User pressed a key. </p>
<p >This is often incorrectly used due to its name. Holding a key will result in multiple events. If in doubt try '<a class="el" href="class_event_code.html#a7839436cfaf2589ea6fd56819be021a5" title="User released a key.">EventCode.KeyUp</a>' in your script first</p>

</div>
</div>
<a id="a55f432be68d01cd15b1bf04936237711" name="a55f432be68d01cd15b1bf04936237711"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a55f432be68d01cd15b1bf04936237711">&#9670;&nbsp;</a></span>OnActionComplete</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">string EventCode.OnActionComplete = &quot;OnActionComplete&quot;</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Action complete event. </p>
<p >Listeners will be given following data:</p><ul>
<li><code>agent</code> - action entity</li>
<li><code>action</code> - action name </li>
</ul>

</div>
</div>
<a id="a0bb7a7064c6259124a48767341b0211f" name="a0bb7a7064c6259124a48767341b0211f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0bb7a7064c6259124a48767341b0211f">&#9670;&nbsp;</a></span>OnAIRaygunHit</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">string EventCode.OnAIRaygunHit = &quot;OnAIRaygunHit&quot;</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>AI Raygun hit event. For raygun hits sent by an AI micro </p>
<p >Listeners will be given following data:</p><ul>
<li><code>target</code> - entity that has been hit </li>
</ul>

</div>
</div>
<a id="a379d0656758a2f6d45f7a6b497f2d1e0" name="a379d0656758a2f6d45f7a6b497f2d1e0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a379d0656758a2f6d45f7a6b497f2d1e0">&#9670;&nbsp;</a></span>OnAISMGHit</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">string EventCode.OnAISMGHit = &quot;OnAISMGHit&quot;</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>AI Raygun hit event. For raygun hits sent by an AI micro </p>
<p >Listeners will be given following data:</p><ul>
<li><code>target</code> - entity that has been hit </li>
</ul>

</div>
</div>
<a id="a6c5540673e62192cd85f1ef886e8076e" name="a6c5540673e62192cd85f1ef886e8076e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6c5540673e62192cd85f1ef886e8076e">&#9670;&nbsp;</a></span>OnAIWeaponFire</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">string EventCode.OnAIWeaponFire = &quot;OnAIWeaponFire&quot;</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>AI weapon fire event. For when an AI micro fires their weapon </p>
<p >Listeners will be given following data:</p><ul>
<li><code>entity</code> - entity that has fired their weapon </li>
</ul>

</div>
</div>
<a id="a279c4c5e28c223c4a5c590af25823780" name="a279c4c5e28c223c4a5c590af25823780"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a279c4c5e28c223c4a5c590af25823780">&#9670;&nbsp;</a></span>OnCrush</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">string EventCode.OnCrush = &quot;OnCrush&quot;</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Entity crushing micro event. </p>
<p >Listeners will be given following data:</p><ul>
<li><code>victim</code> - crushed micro entity</li>
<li><code>crusher</code> - crushing entity </li>
</ul>

</div>
</div>
<a id="aaaadd94dd51e82dc133c6e5147e2ede2" name="aaaadd94dd51e82dc133c6e5147e2ede2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaaadd94dd51e82dc133c6e5147e2ede2">&#9670;&nbsp;</a></span>OnPlayerRaygunHit</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">string EventCode.OnPlayerRaygunHit = &quot;OnPlayerRaygunHit&quot;</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Player Raygun hit event. For raygun hits sent by the player </p>
<p >Listeners will be given following data:</p><ul>
<li><code>target</code> - entity that has been hit</li>
<li><code>magnitude</code> - raygun's magnitude value (float | ranges from -1 to 1)</li>
<li><code>firingMode</code> - type of emission that hit target (int | 0 - projectile, 1 - laser, 2 - sonic)</li>
<li><code>chargeValue</code> - value of hit projectile charge value (float | ranges from 0.125 - 1) </li>
</ul>

</div>
</div>
<a id="a03a965e04c12fca8491beb8ec3753df4" name="a03a965e04c12fca8491beb8ec3753df4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a03a965e04c12fca8491beb8ec3753df4">&#9670;&nbsp;</a></span>OnSpawn</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">string EventCode.OnSpawn = &quot;OnSpawn&quot;</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Entity spawned event. </p>
<p >Listeners will be given following data:</p><ul>
<li><code>entity</code> - spawned entity </li>
</ul>

</div>
</div>
<a id="afe7b5546673f18b5aacb38e272f8dcc0" name="afe7b5546673f18b5aacb38e272f8dcc0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afe7b5546673f18b5aacb38e272f8dcc0">&#9670;&nbsp;</a></span>OnStep</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">string EventCode.OnStep = &quot;OnStep&quot;</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Entity footstep event. </p>
<p >Listeners will be given following data:</p><ul>
<li><code>entity</code> - stepping entity</li>
<li><code>position</code> - position of the step epicenter (vector)</li>
<li><code>magnitude</code> - force of the step (float)</li>
<li><code>foot</code> - foot number (0 - left, 1 - right) </li>
</ul>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>EventCode.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="class_event_code.html">EventCode</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

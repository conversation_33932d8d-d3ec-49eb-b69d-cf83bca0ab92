# ai_bridge.py
import openai
import time
import os

openai.api_key = "********************************************************************************************************************************************************************"

INPUT_FILE = "ai_input.txt"
OUTPUT_FILE = "ai_output.txt"

last_prompt = ""
print("[AI Bridge] Ready")

while True:
    time.sleep(1)
    if not os.path.exists(INPUT_FILE):
        continue
    with open(INPUT_FILE, "r", encoding="utf-8") as f:
        prompt = f.read().strip()
    if prompt == "" or prompt == last_prompt:
        continue
    print("[AI Bridge] Sending:", prompt)
    try:
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.8,
            max_tokens=150
        )
        reply = response.choices[0].message.content
    except Exception as e:
        reply = "[ERROR] " + str(e)
    with open(OUTPUT_FILE, "w", encoding="utf-8") as f:
        f.write(reply)
    last_prompt = prompt
    print("[AI Bridge] Replied.")

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Class Members - Variables</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('functions_vars.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;<ul>
<li>KeyDown&#160;:&#160;<a class="el" href="class_event_code.html#a189f8c13a991ae8aa4d3cad7e4911ec3">EventCode</a></li>
<li>KeyUp&#160;:&#160;<a class="el" href="class_event_code.html#a7839436cfaf2589ea6fd56819be021a5">EventCode</a></li>
<li>MouseDown&#160;:&#160;<a class="el" href="class_event_code.html#a32fa7eb106bc429a8c854b81caa2d38c">EventCode</a></li>
<li>MouseUp&#160;:&#160;<a class="el" href="class_event_code.html#ae6b50b9aff11a3a1a10e0e2836e159f8">EventCode</a></li>
<li>OnActionComplete&#160;:&#160;<a class="el" href="class_event_code.html#a55f432be68d01cd15b1bf04936237711">EventCode</a></li>
<li>OnAIRaygunHit&#160;:&#160;<a class="el" href="class_event_code.html#a0bb7a7064c6259124a48767341b0211f">EventCode</a></li>
<li>OnAISMGHit&#160;:&#160;<a class="el" href="class_event_code.html#a379d0656758a2f6d45f7a6b497f2d1e0">EventCode</a></li>
<li>OnAIWeaponFire&#160;:&#160;<a class="el" href="class_event_code.html#a6c5540673e62192cd85f1ef886e8076e">EventCode</a></li>
<li>OnCrush&#160;:&#160;<a class="el" href="class_event_code.html#a279c4c5e28c223c4a5c590af25823780">EventCode</a></li>
<li>OnLocalPlayerChanged&#160;:&#160;<a class="el" href="class_event_code.html#aa0bed09744a5043231ce30c55d7d70ac">EventCode</a></li>
<li>OnLocalSelectionChanged&#160;:&#160;<a class="el" href="class_event_code.html#ae742599b4e26effaa66be61f267fa9d0">EventCode</a></li>
<li>OnPlayerRaygunHit&#160;:&#160;<a class="el" href="class_event_code.html#aaaadd94dd51e82dc133c6e5147e2ede2">EventCode</a></li>
<li>OnSpawn&#160;:&#160;<a class="el" href="class_event_code.html#a03a965e04c12fca8491beb8ec3753df4">EventCode</a></li>
<li>OnStep&#160;:&#160;<a class="el" href="class_event_code.html#afe7b5546673f18b5aacb38e272f8dcc0">EventCode</a></li>
<li>OnTriggerPress&#160;:&#160;<a class="el" href="class_event_code.html#a42a2b4374afaaf0c95694abdd93e7fe9">EventCode</a></li>
<li>OnTriggerRelease&#160;:&#160;<a class="el" href="class_event_code.html#a62441710d089523d4630ac962020a6b5">EventCode</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

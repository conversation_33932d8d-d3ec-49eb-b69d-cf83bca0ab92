<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Class Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('functions_t.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_t" name="index_t"></a>- t -</h3><ul>
<li>Tan()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#aefc20a4773c6e7ce2f493974dbdf27c1">Lua.Mathf</a></li>
<li>Text&#160;:&#160;<a class="el" href="class_lua_1_1_game_1_1_version.html#ae0129c5ed39c108d4d6f9a5935c34517">Lua.Game.Version</a></li>
<li>time&#160;:&#160;<a class="el" href="class_lua_1_1_time.html#a6a7753473015073c35d5ae5bc4edfdf3">Lua.Time</a></li>
<li>timeSinceLevelLoad&#160;:&#160;<a class="el" href="class_lua_1_1_time.html#af1a087ea59af5ee339aa26ae49c13370">Lua.Time</a></li>
<li>ToString()&#160;:&#160;<a class="el" href="class_lua_1_1_quaternion.html#a72a4b219c3442e088c7ee963feb1b372">Lua.Quaternion</a>, <a class="el" href="class_lua_1_1_vector3.html#a63129be99b82f76bb94c8267b0dcd692">Lua.Vector3</a></li>
<li>transform&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a51edf8c42bd2acb730ae73d045341320">Lua.Entity</a></li>
<li>TransformDirection()&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#a14270ac6dbade453decf26513f533b66">Lua.Transform</a></li>
<li>TransformPoint()&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#a77c8ed5338803453798fbfe848ed02e5">Lua.Transform</a></li>
<li>TransformVector()&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#a8a4bb1f1feb42a0d3be3577e4463f5f4">Lua.Transform</a></li>
<li>transitionDuration&#160;:&#160;<a class="el" href="class_lua_1_1_animation.html#ad1f57da869e710f55a79e82ceb579cc0">Lua.Animation</a></li>
<li>Translate()&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#a1a2933390110217890785619c897df7f">Lua.Transform</a></li>
<li>Turn()&#160;:&#160;<a class="el" href="class_lua_1_1_movement.html#a26e7ee9591b48c6ddf74becf73ebe078">Lua.Movement</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

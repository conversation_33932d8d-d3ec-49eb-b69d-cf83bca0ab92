---
description: Repository Information Overview
alwaysApply: true
---

# Sizebox Information

## Summary
Sizebox is a Unity-based 3D application/game focused on size-changing mechanics. It allows users to control characters (primarily "giantesses") in a 3D environment with various size manipulation features. The application includes Lua scripting support for custom behaviors and interactions.

## Structure
- **Sizebox_Data/**: Contains the main Unity game data including managed assemblies and resources
- **Sizebox_Data/StreamingAssets/**: Contains Lua scripts for custom behaviors and game functionality
- **Models/**: Contains character models and assets organized by type (Giantess, Character, Objects, Scenes)
- **MonoBleedingEdge/**: Contains Mono runtime for .NET support
- **Saves/**: Directory for saved game data and configurations
- **Sounds/**: Contains audio assets for the application

## Language & Runtime
**Engine**: Unity (3D Game Engine)
**Scripting**: Lua for custom behaviors, C# (Unity Mono) for core functionality
**Runtime**: Mono (.NET runtime for Unity)
**Main Executable**: Sizebox.exe

## Dependencies
**Main Dependencies**:
- Unity Engine
- Mono Runtime
- Lua Scripting Engine

## Key Features
**Size Manipulation**:
- Various growth modes (Constant, Linear, Quadric, Cubic)
- Scale-based interactions between characters
- Custom animation support

**Scripting System**:
- Lua-based behavior scripting
- Event-driven architecture
- Custom behavior templates
- AI-controlled character actions

**Asset Management**:
- Support for custom character models (.gts format)
- Animation packs
- Scene management

## Usage & Operations
**Main Application**:
```
Sizebox.exe
```

**Custom Behaviors**:
The application supports custom Lua scripts placed in the StreamingAssets/lua directory. These scripts can define new behaviors, animations, and interactions for characters in the game.

## Key Resources
**Main Files**:
- **Sizebox.exe**: Main application executable
- **BehaviorTemplate.lua**: Template for creating custom behaviors
- **StreamingAssets/lua/**: Directory containing Lua scripts for game behaviors

**Model Files**:
- **.gts files**: Custom model format for characters with animations and properties
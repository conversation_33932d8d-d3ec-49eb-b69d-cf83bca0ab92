--v1.4 13/04/21
Follow = RegisterBehavior("Follow (d)")
Follow.data = {
    menuEntry = "Walk/Follow (d)",
    agent = {
        type = { "humanoid"}
    },
    target = {
        type = {"humanoid"}, 
		exclude = {"oneself"}
    },
    settings = {
		{"ExpAnims", "More Expressive Anims", "bool", true},
    	{"arrivalRadius", "Arrival Radius", "string", "0.6"},
    	{"arrivalRadius2", "AR (Smaller GTS)", "string", "1"},
    	{"scaleRatio", "Scale Ratio Limit", "string", "5"},
    	{"walkRadius1", "Radius 1", "string", "2.4"},
    	{"walkRadius2", "Radius 2", "string", "4"},
    	{"walkRadius3", "Radius 3", "string", "5"},
    	{"useRunRadius", "Use Different Radii for Run Anims", "bool", true},
    	{"runRadius1", "Run Radius 1", "string", "3.8"},
    	{"runRadius2", "Run Radius 2", "string", "8"},
    	{"runRadius3", "Run Radius 3", "string", "10"}
    }
}

function Follow:SetAnim(anim,speed,td)
	if not speed then speed = 1 end
	if not td then td = 1 end
	self.agent.animation.SetSpeed(speed)
	self.agent.animation.transitionDuration = self.transD * td
	--if self.agent.isPlayer() then
		--self.agent.animation.SetAndWait(anim)
	--else
		self.agent.animation.Set(anim)
	--end
end

function Follow:resetValues()
	self.started = false
	self.radius = 0
	self.RIdle = 1
	self.animating = false
	self.scratchy1 = false
	self.scratchy2 = false
	self.sitting = false
	self.moving1 = false
	self.moving2 = false
	self.moving3 = false
	self.moving4 = false
	self.moving5 = false
	self.moving6 = false
	self.lookDown = false
	self.caughtUp = false
	self.greeted = false
	self.facing = false
	self.look = false
	self.animTimer = Time.time
	self.rate = 0.1
	self.mrate = 0
	self.agent.animation.SetSpeed(1)
	self.animProgressComplete = 1
	self.agent.ai.StopAction()
end

function Follow:animLog()
	--log("anim = "..self.agent.animation.Get())
	----log("time = "..self.agent.animation.GetTime())
	----log("length = "..self.agent.animation.GetLength())
	--log("progress = "..self.agent.animation.GetProgress())
	--log("speed = "..self.agent.animation.GetSpeed())
	--log("trandur = "..self.agent.animation.transitionDuration)
	--log("trans? = "..tostring(self.agent.animation.IsInTransition()))
	--log(";;;")
end

function Follow:caughtOn()
	if not self.look then self.target.LookAt(self.agent) self.look = true end
	self.agent.ai.StopAction()
	local a = "Greet"
	local s = 0.4
	if self.agent.scale < self.target.scale * 3 then a = "Greet 2" s = 0.8 end
	self.agent.animation.SetAndWait(a)
	self.agent.animation.SetSpeed(s)
	self.radius = 1
	self.caughtUp = true
end

function Follow:radiusCheck()
	local r = self.radius
	
	-- R1
	if self.radius == 1 then
		-- R1 >> R2
		if (self.agent.DistanceTo(self.target) > self.agent.scale * self.chaseRadius1 * 1.1) and (self.agent.DistanceTo(self.target)) <= self.agent.scale * self.chaseRadius2 then
			if self.look then self.target.LookAt(nil) self.look = false end
			self.radius = 2
			--log("2 (from 1)")
		end
		
	-- R2
	elseif self.radius == 2 then
		-- R2 >> R1
		if self.agent.DistanceTo(self.target) <= self.agent.scale * self.chaseRadius1 then
			if not self.look then self.target.LookAt(self.agent) self.look = true end
			self.radius = 1
			--log("1")
		-- R2 >> R3
		elseif self.agent.DistanceTo(self.target) > self.agent.scale * self.chaseRadius2 * 1.1 then
			self.radius = 3
			--log("3")
		end
	
	-- R3
	elseif self.radius == 3 then
		-- R3 >> R2
		if self.agent.DistanceTo(self.target) <= self.agent.scale * self.chaseRadius2 then
			self.radius = 2
			--log("2 (from 3)")
		elseif self.agent.DistanceTo(self.target) > self.agent.scale * self.chaseRadius3 then
			self.radius = 4
			self.moving1 = false
			self.moving2 = false
			self.moving3 = false
			self.moving4 = false
			self.moving5 = false
			self.moving6 = false
			self.scratchy1 = false
			if self.sitting == "liedown2" or self.sitting == "liedown3" or self.sitting == "liedown3b" or self.sitting == "sit" then 
				self.animTimer = 1 + Time.time
				if self.sitting == "liedown2" then
					self.scratchy1 = true
				end
			end
			--log("4")
		end
	end
	
	--if r ~= self.radius and self.radius ~= 4 then
	--	self.scratchy1 = true
	--end
end

function Follow:Start()
	BasicIdles = {"Idle", "Idle 3", "Idle 4", "Breathing Idle", "Neutral Idle", "Look Down", "Thinking"}
	SitIdles = {"Falling Down", "Sit 3", "Sit 4", "Sit 6", "Sitting Idle", "Lie Down 3"}
	R1Idles = {"Bashful", "Crouch Idle"}
	R1IdlesSmolG = {"Happy", "Breathing Idle", "Bored", "Thinking 2"}
	R2Idles = {"Dismissing Gesture 2", "Happy Hand Gesture", "Pointing", "Refuse", "Rejected", "Relieved Sigh",
			   "Waving", "Whatever Gesture", "Standing Greeting"}
	R3Idles = {"Idle", "Idle 3", "Idle 4", "Idle 5", "Happy", "Breathing Idle", "Bored", "Roar",
			   "Bashful", "Crossarms", "Wait Torso Twist", "Look Down", "Salute 2", "Whatever Gesture", "Refuse",
			   "Waving", "Waving 2", "Salute", "Greet 2", "Look Away Gesture", "Loser", "No", "Pointing Forward",
			   "Shake Fist", "Stomping", "Surprised", "Taunt 3", "Thinking", "Thinking 2", "Victory 2", "Wait Gesture", "Yelling"}
	R1IdlesExp = {"Crouch Idle", "Bashful", "Bashful", "Stomping", "Crossarms", "Crossarms", "Crouch Idle", "Whatever Gesture",
				  "Breathing Idle", "Look Down", "Thinking 2", "Idle 2", "Idle 3", "Idle 4", "Idle 5", "Crouch Idle"}
	R3IdlesExp = {"Bashful", "Bashful", "Stomping", "Crossarms", "Crossarms", "Whatever Gesture", "Whatever Gesture", "Wait Gesture", "Loser"}
	CrouchJumps = {"Jump", "Jump 2", "Jump 3", "Jump 4", "Jump Up", "Jump Low", "Standing Jump"}
	R3Jumps = {"Standing Jump", "Jump 3", "Jump 4"}
	SmolGTSIdles = {"Hands Forward", "Surprised"}
	
	self:resetValues()
	globals[(self.agent.id.."stomp stop")] = true
	self.x10 = false
	self.d10 = false
	self.transD = self.agent.animation.transitionDuration
	self.agent.animation.Set("Idle")
	--self.player = Game.GetLocalPlayer()
	
	if not self.useRunRadius then
		self.runRadius1 = self.walkRadius1
		self.runRadius2 = self.walkRadius2
		self.runRadius3 = self.walkRadius3
	end
	self.chaseRadius1 = self.walkRadius1
	self.chaseRadius2 = self.walkRadius2
	self.chaseRadius3 = self.walkRadius3
	--if self.agent.dict.wSpeed == nil then
	--	self.agent.dict.wSpeed = 0.7
	--end
	if self.agent.dict.wAnim == nil then
		self.agent.dict.wAnim = "Walk"
	end

	--Only do randomseed once per scene
    if not globals["followDRand"] then math.randomseed(tonumber(os.time()) + self.agent.id) globals["followDRand"] = true end
end

function Follow:Update()
	if not self.target or (self.target and self.target.IsDead()) then self.agent.ai.StopBehavior() end
----log(self.walkRadius3 .. " - " .. self.runRadius3)
	--self:animLog()
	----log(self.agent.animation.transitionDuration)
	self:radiusCheck()
	----log(globals["run"])
	----log(self.agent.DistanceTo(self.target)) --Distance from the giantess, relative to her size
	
	----log(self.agent.scale * 1000)
	----log(player.scale)
	----log(self.agent.animation.Get())
	if globals["run"] then
		if self.agent.DistanceTo(self.target) < self.agent.scale * self.arrivalRadius * 1.25 and not self.caughtUp then
			--log("1a")
			self:caughtOn()
		end
	else
		local arrivalRadius = self.arrivalRadius
		if self.target and self.target.scale * self.scaleRatio > self.agent.scale then arrivalRadius = self.arrivalRadius2 end
		if self.agent.DistanceTo(self.target) < self.agent.scale * arrivalRadius and not self.caughtUp then
			--log("1b")
			self:caughtOn()
		end
	end 
	if self.caughtUp and self.greeted then
		if globals["run"] then
			self.chaseRadius1 = self.runRadius1
			self.chaseRadius2 = self.runRadius2
			self.chaseRadius3 = self.runRadius3
		else
			self.chaseRadius1 = self.walkRadius1
			self.chaseRadius2 = self.walkRadius2
			self.chaseRadius3 = self.walkRadius3
		end
			----log(self.agent.animation.IsCompleted())
			if (Time.time > self.animTimer and not self.agent.animation.IsInTransition() and self.agent.animation.GetProgress() >= self.animProgressComplete) or self.scratchy1 then
				if self.agent.scale > self.target.scale * 10 then -- if larger than 10x player size
					if not self.x10 then self.x10 = true end
					if self.d10 then
						self.d10 = false
						self.moving1 = false
						self.moving2 = false
						self.moving3 = false
						self.moving4 = false
						self.moving5 = false
						self.moving6 = false
						self.scratchy1 = false
					end
					if self.radius == 1 then
						if not self.animating then
						
							--Greet 2
							if not self.moving1 then
								--log("3b")
								self:SetAnim("Greet 2",1.1)
								self.moving1 = true
								self.animTimer = 10 * Time.deltaTime + Time.time
								
							--Greet 3
							elseif self.moving1 and not self.moving2 then
								--log("3.5b")
								self:SetAnim("Greet",0.6)
								self.animTimer = 10 * Time.deltaTime + Time.time
								
								--Roll for Random Idles ahead of time, because if Crouch, cut from Greet mid-bow
								local i = R1Idles
								if self.ExpAnims then
									local n = math.random(2)
									if n > 1.5 then i = R1IdlesExp end
								end
								i = i[math.random(#i)]
								if i == "Crouch Idle" then self.animProgressComplete = 0.3 end
								self.moving2 = i

							--Bash/Crouch or Alt
							elseif self.moving2 and not self.moving3 then
								local i = self.moving2
								local s = 0.8
								local td = false
								self.moving2 = true
								if i == "Crouch Idle" then
									s = 0.2
									td = 5
									self.animProgressComplete = 1
								end
								self:SetAnim(i,s,td)
								self.agent.animation.transitionDuration = self.transD
								self.moving3 = true
								self.animTimer = 20 * Time.deltaTime + Time.time
								--log("4b - "..i)

							--Look Down if not Crouch
							elseif self.moving3 and not self.moving4 then
								self.agent.animation.SetSpeed(1)
								if self.agent.animation.Get() ~= "Crouch Idle" then
									self:SetAnim("Look Down",1)
									--log("5b")
								else
									--log("5b crouching")
								end
								self.moving4 = true
								self.animTimer = 20 * Time.deltaTime + Time.time
							elseif self.moving5 then
								
							end
						else
							if self.moving5 and not self.moving6 then
								self.scratchy1 = false
								--if Sitting and fell down OR (Sitting without falling down BUT very close)
								if self.sitting == "final" or (self.sitting and self.agent.DistanceTo(self.target) <= self.agent.scale * self.chaseRadius1 * 0.45) then
									if self.sitting == "liedown3" then
										self.sitting = "liedown3b"
										self.moving6 = true
										--log("liedown3b")
									else
										--if didn't fall down but very close, every 10 seconds, roll a chance (1/20) to run Lie Down 2 anim
										--log("self.sitting: "..tostring(self.sitting))
										if self.sitting ~= "final" and math.random(20) > 1 then
											self.animTimer = 5 + Time.time
											--log("didn't feel like lying down")

										--transition into Lie Down 2; immediately if had fell down
										else
											if self.sitting ~= "final" then --log("laid down!!!!") 
											end
											self:SetAnim("Lie Down 2",0.8,1.4)
											self.animTimer = 20 * Time.deltaTime + Time.time
											self.sitting = "liedown2"
											self.moving6 = true
											--log("lie down 2!")
										end
									end
								end
							elseif self.sitting == "gettingUp" then
								self.sitting = self.gettingUp
								self.gettingUp = nil
							elseif self.moving5 then
								--log(self.agent.animation.Get())
								self.moving5 = false
							elseif self.moving6 and self.scratchy2 == "sad" then
								if self.facing == "sad" then
									log("You came back?~")
									self.agent.Face(self.target)
									self.facing = true
									self.scratchy1 = false
									self.moving6 = "facing"
									
								elseif not self.agent.ai.IsActionActive() and self.moving6 == "facing" then
									--log("exci")
									self.animProgressComplete = 0.7
									self:SetAnim("Excited",0.8,0.2)
									self.animTimer = 20 * Time.deltaTime + Time.time
									self.moving6 = "not sad"
								elseif self.moving6 == "not sad" then
									--log("happy")
									self:SetAnim("Happy",0.8)
									self.animTimer = 20 * Time.deltaTime + Time.time
									self.moving6 = "happy"
								elseif self.moving6 == "happy" then
									--log("breathing idle")
									self:SetAnim("Breathing Idle")
									self.animTimer = 20 * Time.deltaTime + Time.time
									self.scratchy2 = "happy"
								end
							end
						end

					elseif self.radius == 2 and self.scratchy2 ~= "sad" then --if still in 2nd radius
					--if lying down from r1, wait 30 seconds
					if self.sitting == "liedown2" or self.sitting == "liedown3b" then
						--log("gonna get up in 30s...")
						self.animTimer = 30 + Time.time
						self.gettingUp = self.sitting
						self.sitting = "gettingUp"
						
					elseif self.sitting == "gettingUp" then
						--log("getting up")
						self.radius = 4
						self.sitting = "not"
						self.animTimer = 20 * Time.deltaTime + Time.time
					elseif self.sitting ~= "sit" then
						----log("6b")
						if self.scratchy2 == "happy" then
							self.scratchy2 = false
							self.moving1 = true
							self.moving2 = true
							self.moving3 = false
							self.moving4 = false
							self.moving5 = false
							self.moving6 = false
							self.sitting = false
							self.RIdle = 1
							self.animProgressComplete = 1
							self.animating = false
						end
						local i
						local s = 1
						local td = false
						if self.facing then self.facing = false end
						--after exitting radius for the 1st time, play anim according to how early r1 was left/what anim was playing
						if self.agent.animation.Get() ~= "Crouch Idle" and not self.scratchy2 then
							--log("7b1")
							if self.moving4 then
								i = "Breathing Idle"
								--log("7b1-m4")
							elseif self.moving3 then
								i = "Scratch Head"
								--log("7b1-m3")
							elseif self.moving2 then
								i = R2Idles[math.random(#R2Idles)]
								--log("7b1-m2 - "..i)
							else
								i = "Stomping"
								self.animProgressComplete = 0.7
								self.stompTD = true
								--log("7b1-m1")
							end
							self.scratchy2 = true
							
						--after 1st anim in radius 2
						elseif self.scratchy2 and not self.sitting then
							--log("7b2")

							--run BasicIdles anim 5 times before running a Sitting anim
							self.RIdle = self.RIdle + 1
							if self.RIdle > 6 then
								i = SitIdles
								td = 0.5
								self.RIdle = 1
								self.animProgressComplete = 1
								self.sitting = true
								--log("7b22-SitIdles")
							else
								if self.stompTD then
									td = 4
									self.stompTD = nil
								end
								i = BasicIdles
								--log("7b21-BasicIdles")
							end

							i = i[math.random(#i)]
							--log("7b2 - "..i)

							--if Sitting anim is "Falling Down", prep for early transition into a proper sitting idle
							if self.sitting then
								if i == SitIdles[1] then
									s = 0.9
									self.animProgressComplete = 0.3
									self.sitting = "falling"
									--log("7b23-FallingDown")
								elseif i == SitIdles[6] then
									i = "Pick Up"
									self.animProgressComplete = 0.26
									self.sitting = "pickup"
									--log("7b23-LieDown3")
								else
									self.sitting = "sit"
								end
							end
						
						--after 2nd round of anims in radius 2, if "Falling Down", transition into final sitting idle
						elseif self.sitting == "falling" then
							i = SitIdles[math.random(2,5)]
							s = 1
							td = 6.5
							self.animProgressComplete = 1
							self.sitting = "final"
							--log("7b3-finalSit - "..i)
						elseif self.sitting == "pickup" then
							i = "Lie Down 3"
							s = 1
							td = 6
							self.animProgressComplete = 1
							self.sitting = "liedown3"
							--log("7b3-finalSit - "..i)
						end
						
						--if last animation (final Sit), don't run this again
						if not self.animating and self.agent.animation.Get() ~= "Crouch Idle" then
							self:SetAnim(i,s,td)
							if self.sitting and self.sitting ~= "falling" and self.sitting ~= "pickup" then self.animating = true end
						end
						self.animTimer = 20 * Time.deltaTime + Time.time
						if not self.moving5 then self.moving5 = true self.moving6 = false self.scratchy1 = false end
					end	
					elseif self.radius == 3 and self.sitting ~= "liedown2" and self.sitting ~= "liedown3" and self.sitting ~= "sit" then -- if still in 3rd radius
						--log("8b")
						self.animating = true
						self.scratchy1 = false
						--If 5b anim == Crouching
						if self.agent.animation.Get() == "Crouch Idle" then
							local j = CrouchJumps
							j = j[math.random(#j)]
							self:SetAnim(j,0.8,0.7)
							--log("crouch jump - "..j)
							log("Hey!")
							self.animating = "Crouch"
							self.animTimer = 20 * Time.deltaTime + Time.time
						else
							--If Crouched then Jumped, play Sad Idle
							if not self.scratchy2 then
								log("Aww...")
								self.moving5 = false
								self.moving6 = true
								self.facing = "sad"
								self.scratchy2 = "sad"
								self:SetAnim("Sad Idle",1)

							--If Scratching or Jumped out of crouch (Sad Idle)
							else
								--Face target (trigger action only once)
								if not self.facing then
									--log("9b")
									log("Oh come back here~")
									self.agent.Face(self.target)
									self.facing = true 
								end

								--Shuffle through some anims for last radius
								if not self.agent.ai.IsActionActive() and self.scratchy2 ~= "sad" then
									local i = R3Idles
									if self.RIdle == 0 then
										i = "Angry"
										self.RIdle = 1
									else
										self.RIdle = self.RIdle + 1
										if self.RIdle > 5 then
											local n = math.random(8)
											if n == 8 then 
												i = R3Jumps
												self.RIdle = 0
											elseif self.ExpAnims and n < 3 then
												i = R3IdlesExp
												self.RIdle = 1
											end
										end
										i = i[math.random(#i)]
									end
									self.agent.animation.SetAndWait(i)
									self.agent.animation.SetSpeed(1)
									self.animTimer = 20 * Time.deltaTime + Time.time
									--log("10b - "..i)
									self.moving5 = false
									self.moving6 = true
								end
							end
						end
					elseif self.radius == 4 then
						self.animating = true
						self.scratchy1 = false
						if self.sitting then
							if not self.moving1 then
								--log("11b")
								local td = 1.5
								if self.gettingUp then self.sitting = self.gettingUp self.gettingUp = nil end
								if self.sitting == "liedown2" then
									td = 0.65
								elseif self.sitting == "liedown3" or self.sitting == "liedown3b" then
									td = 3
								end
								self.animProgressComplete = 0.1
								self:SetAnim("Kneeling Idle",1,td)
								self.moving1 = true
								self.animTimer = 0.3 + Time.time
								self.sitting = "up"
							elseif self.moving1 and not self.moving2 then
								--log("12b")
								self.animProgressComplete = 0.55
								self:SetAnim("Kneeling Recover",1)
								self.moving2 = true
								self.animTimer = 0.5 + Time.time
							elseif self.moving2 then
								--log("13b - raised and ready to walk")
								self.sitting = false
								self.animating = "ready"
							end
						end
					end
				else --If GTS not bigger than 10x target's size
					if not self.d10 then self.d10 = true end
					if self.x10 then
						self.x10 = false
						self.moving1 = false
						self.moving2 = false
						self.moving3 = false
						self.moving4 = false
						self.moving5 = false
						self.moving6 = false
						self.scratchy1 = false
					end
					if not self.moving1 then					
						--log("3bb")
						local i = "Bashful"
						--If GTS not bigger than 3.333x target's size (amazon/miniGTS)
						if self.agent.scale * 0.3 < self.target.scale then
							i = R1IdlesSmolG
							i = i[math.random(#i)]
						end
						self:SetAnim(i,1)
						self.moving1 = true
						self.animTimer = 10 * Time.deltaTime + Time.time
					elseif not self.moving2 then
						--log("4bb")
						self:SetAnim("Look Down",1)
						self.moving2 = true
						self.animTimer = 10 * Time.deltaTime + Time.time
					end
				end
			end
			if (self.agent.DistanceTo(self.target) > self.agent.scale * self.chaseRadius3 and self.animating ~= "Crouch" and not self.sitting) or self.animating == "ready" then -- Evade limit
				--log("5bb - reset")
				self:resetValues()
			end
	else
		if not self.agent.ai.IsActionActive() then -- chasing block
			if self.caughtUp and not self.greeted then -- conditionnals workaround of mine...
				self.greeted = true
				if globals["run"] then --log("2a") else --log("2b")
				end
				----log("self.greeted true")
			else
				if not self.started then -- don't take time to look at player everytime we change walks
					self.agent.LookAt(self.target)
					log("The chase is on.")
				end				
				local td = 1
				if self.raiseUp then
					td = self.raiseUp
					self.raiseUp = nil
				end
				self:SetAnim(self.agent.dict.wAnim,false,td)
				self.agent.MoveTo(self.target)
			end
		end
	end
	if self.agent.GetSelectedEntity() == self.agent then
		if Input.GetKeyDown("right alt") or Input.GetKeyDown("right ctrl") or Input.GetKeyDown("left") and not self.caughtUp then
			self.started = true
			--self.agent.dict.wSpeed = globals["walkspeed"]
			self.agent.dict.wAnim = globals["walk"]
			self.agent.ai.StopAction()
		end
	end
end

function Follow:Exit()
	--self.agent.movement.speed = 0.8
	self.agent.LookAt(nil)
	self.target.LookAt(nil)
    self:SetAnim("Idle 2",1)
	log("The chase is over.")
end
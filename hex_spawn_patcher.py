#!/usr/bin/env python3
"""
Hex-based Sizebox Spawn Key Patcher
Looks for specific byte patterns that represent spawn key bindings
"""

import os
import shutil
import hashlib

def backup_file(source, backup_dir):
    """Create a backup of the original file"""
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    
    backup_path = os.path.join(backup_dir, os.path.basename(source) + '.hex_backup')
    shutil.copy2(source, backup_path)
    print(f"Backup created: {backup_path}")
    return backup_path

def find_spawn_key_sequences(dll_data):
    """Find specific byte sequences that likely represent spawn keys"""
    
    print("Searching for spawn key byte sequences...")
    
    # Unity KeyCode enum values
    keycodes = {
        'P': 0x50,  # KeyCode.P = 80
        'O': 0x4F,  # KeyCode.O = 79
        'F9': 0x78,  # KeyCode.F9 = 120
        'F10': 0x79, # KeyCode.F10 = 121
    }
    
    # Look for specific patterns that suggest input handling
    # These are common Unity input patterns
    spawn_sequences = [
        # Pattern 1: KeyCode followed by method call pattern
        bytes([0x50, 0x00, 0x00, 0x00]),  # P key as 32-bit int
        bytes([0x4F, 0x00, 0x00, 0x00]),  # O key as 32-bit int
        
        # Pattern 2: KeyCode in switch statement
        bytes([0x50, 0x00]),  # P key as 16-bit
        bytes([0x4F, 0x00]),  # O key as 16-bit
        
        # Pattern 3: Direct byte value
        bytes([0x50]),  # P key as single byte
        bytes([0x4F]),  # O key as single byte
    ]
    
    found_sequences = []
    
    for i, sequence in enumerate(spawn_sequences):
        key_char = 'P' if sequence[0] == 0x50 else 'O'
        pattern_type = f"Pattern {i+1}"
        
        pos = 0
        while True:
            pos = dll_data.find(sequence, pos)
            if pos == -1:
                break
            
            # Check context around this position
            context_start = max(0, pos - 50)
            context_end = min(len(dll_data), pos + 50)
            context = dll_data[context_start:context_end]
            
            # Look for spawn-related indicators in the context
            spawn_indicators = [
                b'spawn', b'Spawn', b'SPAWN',
                b'micro', b'Micro', b'MICRO',
                b'character', b'Character',
                b'instantiate', b'Instantiate',
                b'create', b'Create',
                b'input', b'Input',
                b'key', b'Key'
            ]
            
            indicator_count = sum(1 for indicator in spawn_indicators if indicator in context)
            
            # Only consider this if we find spawn-related context
            if indicator_count > 0:
                found_sequences.append({
                    'key': key_char,
                    'position': pos,
                    'sequence': sequence,
                    'pattern_type': pattern_type,
                    'context_score': indicator_count,
                    'context': context
                })
                
                print(f"Found {key_char} key {pattern_type} at 0x{pos:08X} (score: {indicator_count})")
            
            pos += 1
    
    # Sort by context score (higher is better)
    found_sequences.sort(key=lambda x: x['context_score'], reverse=True)
    
    # Only keep the highest scoring matches to avoid false positives
    if found_sequences:
        max_score = found_sequences[0]['context_score']
        high_confidence = [seq for seq in found_sequences if seq['context_score'] >= max_score * 0.7]
        
        print(f"\nHigh confidence matches: {len(high_confidence)}")
        for seq in high_confidence[:10]:  # Show top 10
            print(f"  {seq['key']} at 0x{seq['position']:08X} - {seq['pattern_type']} (score: {seq['context_score']})")
        
        return high_confidence, keycodes
    
    return [], keycodes

def patch_hex_spawn_keys():
    """Main hex patching function"""
    dll_path = r"Sizebox_Data\Managed\Assembly-CSharp.dll"
    backup_dir = r"Sizebox_Backup_Hex"
    
    print("Hex-based Sizebox Spawn Key Patcher")
    print("===================================")
    print("This will find spawn keys using byte pattern analysis")
    print()
    
    # Verify file exists
    if not os.path.exists(dll_path):
        print(f"ERROR: {dll_path} not found!")
        return False
    
    # Create backup
    backup_path = backup_file(dll_path, backup_dir)
    
    # Load the DLL
    with open(dll_path, 'rb') as f:
        dll_data = bytearray(f.read())
    
    print(f"Loaded DLL: {len(dll_data)} bytes")
    
    # Find hex spawn patterns
    spawn_sequences, keycodes = find_spawn_key_sequences(dll_data)
    
    if not spawn_sequences:
        print("No spawn key sequences found!")
        print("The spawn keys might be stored in a different format.")
        return False
    
    print(f"\nFound {len(spawn_sequences)} potential spawn key sequences")
    
    # Group by key type
    p_sequences = [seq for seq in spawn_sequences if seq['key'] == 'P']
    o_sequences = [seq for seq in spawn_sequences if seq['key'] == 'O']
    
    print(f"• {len(p_sequences)} P key sequences -> F9")
    print(f"• {len(o_sequences)} O key sequences -> F10")
    
    # Show details of what we'll patch
    print("\nSequences to patch:")
    for seq in spawn_sequences[:10]:  # Show top 10
        print(f"  {seq['key']} at 0x{seq['position']:08X} - {seq['pattern_type']}")
        print(f"    Sequence: {seq['sequence'].hex()}")
        print(f"    Score: {seq['context_score']}")
    
    if len(spawn_sequences) > 10:
        print(f"  ... and {len(spawn_sequences) - 10} more")
    
    proceed = input(f"\nPatch {len(spawn_sequences)} sequences? (y/N): ").lower().strip()
    if proceed != 'y':
        print("Aborted.")
        return False
    
    # Apply hex patches
    patches_applied = 0
    
    for seq in spawn_sequences:
        pos = seq['position']
        key = seq['key']
        sequence = seq['sequence']
        
        # Replace the key byte in the sequence
        if key == 'P':
            # Replace P (0x50) with F9 (0x78)
            new_sequence = sequence.replace(bytes([0x50]), bytes([0x78]))
            dll_data[pos:pos+len(sequence)] = new_sequence
            patches_applied += 1
            print(f"Patched P -> F9 at 0x{pos:08X}")
            
        elif key == 'O':
            # Replace O (0x4F) with F10 (0x79)
            new_sequence = sequence.replace(bytes([0x4F]), bytes([0x79]))
            dll_data[pos:pos+len(sequence)] = new_sequence
            patches_applied += 1
            print(f"Patched O -> F10 at 0x{pos:08X}")
    
    if patches_applied == 0:
        print("No patches applied!")
        return False
    
    # Write the patched DLL
    patched_path = dll_path + ".hex_patched"
    with open(patched_path, 'wb') as f:
        f.write(dll_data)
    
    print(f"\nHex patch ready!")
    print(f"Applied {patches_applied} hex patches")
    
    apply = input("\nApply hex patch to game? (y/N): ").lower().strip()
    if apply == 'y':
        # Backup original and apply patch
        if not os.path.exists(dll_path + ".original"):
            shutil.copy2(dll_path, dll_path + ".original")
        shutil.copy2(patched_path, dll_path)
        print("✓ Hex patch applied!")
        print("✓ Spawn keys changed: P -> F9, O -> F10 (hex level)")
        print("✓ Restart Sizebox to test!")
        return True
    else:
        print("Patch not applied.")
        return True

def restore_original():
    """Restore the original DLL"""
    dll_path = r"Sizebox_Data\Managed\Assembly-CSharp.dll"
    original_path = dll_path + ".original"
    
    if os.path.exists(original_path):
        shutil.copy2(original_path, dll_path)
        print("✓ Original DLL restored")
        return True
    else:
        print("✗ Original backup not found")
        return False

if __name__ == "__main__":
    try:
        print("Hex-based Sizebox Spawn Key Patcher")
        print("===================================")
        print("1. Apply hex spawn key patch (P->F9, O->F10)")
        print("2. Restore original keys")
        print()
        
        choice = input("Enter choice (1 or 2): ").strip()
        
        if choice == "1":
            success = patch_hex_spawn_keys()
        elif choice == "2":
            success = restore_original()
        else:
            print("Invalid choice!")
            success = False
            
        if success:
            print("\nOperation completed!")
        else:
            print("\nOperation failed!")
            
    except Exception as e:
        print(f"Error: {e}")
    
    input("Press Enter to exit...")

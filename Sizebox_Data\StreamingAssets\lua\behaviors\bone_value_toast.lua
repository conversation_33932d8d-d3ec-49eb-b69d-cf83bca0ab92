local BoneValueToast = Register<PERSON><PERSON><PERSON>or("Pose Recorder")
BoneValueToast.data = {
    menuEntry = "Utility/Pose Recorder",
    agent = { type = { "giantess", "humanoid", "micro", "player" } },
    secondary = true
}

local function FindBone(entity, boneName)
    if entity.bones and entity.bones.GetBonesByName then
        local bones = entity.bones.GetBonesByName(boneName, true)
        if bones and bones[1] then
            return bones[1]
        end
    end
    return nil
end

function BoneValueToast:Update()
    if Input and Input.GetKeyDown and Input.GetKeyDown("l") then
        local agent = self.agent
        if not agent then
            Game.Toast.New().Print("Pose Recorder: Agent not found!")
            return
        end

        local bones = {
            right_shoulder = FindBone(agent, "51.JOINT_RIGHTARM"),
            right_elbow    = FindBone(agent, "56.JOINT_RIGHTELBOW"),
            right_wrist    = FindBone(agent, "61.JOINT_RIGHTWRIST"),
            left_shoulder  = FindB<PERSON>(agent, "87.JOINT_LEFTARM"),
            left_elbow     = FindBone(agent, "92.JOINT_LEFTELBOW"),
            left_wrist     = FindBone(agent, "97.JOINT_LEFTWRIST"),
        }

        local poseData = "local pose = {\n"
        local allBonesFound = true
        for name, bone in pairs(bones) do
            if bone then
                local rot = bone.rotation -- Get WORLD rotation
                poseData = poseData .. string.format("    %s = {x = %.4f, y = %.4f, z = %.4f, w = %.4f},\n", name, rot.x, rot.y, rot.z, rot.w)
            else
                Game.Toast.New().Print(string.format("Warning: Bone '%s' not found.", name))
                allBonesFound = false
            end
        end
        poseData = poseData .. "}"

        if allBonesFound then
            Game.Toast.New().Print("Pose data generated! Paste this into the other script:")
            -- Split data for toasts
            local chunks = {}
            for i = 1, #poseData, 100 do -- Split into chunks of 100 chars
                table.insert(chunks, string.sub(poseData, i, i + 99))
            end
            for _, chunk in ipairs(chunks) do
                Game.Toast.New().Print(chunk)
            end
        else
            Game.Toast.New().Print("Could not generate complete pose data.")
        end
    end
end
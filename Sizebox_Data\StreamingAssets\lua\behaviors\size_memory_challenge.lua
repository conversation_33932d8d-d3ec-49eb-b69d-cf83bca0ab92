local Memory = RegisterBehavior("Size Memory Challenge")
Memory.data = {
    menuEntry = "Size Memory Challenge",
    agent = { type = { "giantess" } },
    secondary = true
}

local sequence = {}
local userInput = {}
local round = 1
local showing = false
local waiting = false
local timer = 0
local interval = 0.7
local baseGrow = 10
local growAmount = baseGrow
local growMultiplier = 2.0 -- Doubles each round!
local shrinkPenalty = 200   -- Much bigger penalty
local toast = nil
local audioSource
local soundList = {
    "GaspMoan001_Mmm.ogg",
    "GaspMoan002_Augh_MidLong.ogg",
    "GaspMoan003_Ahh_MidLong.ogg"
}

local nextRoundDelay = 0
local waitingForNextRound = false

local function randomStep()
    return math.random() > 0.5 and "up" or "down"
end

local function sequenceToArrows(seq)
    local out = {}
    for i, step in ipairs(seq) do
        table.insert(out, step == "up" and "↑" or "↓")
    end
    return table.concat(out, " ")
end

local function showSequence()
    showing = true
    waiting = false
    userInput = {}
    timer = 0
    if toast then toast.Print("") end -- clear previous toast
    toast.Print("Round " .. round .. "\nPattern: " .. sequenceToArrows(sequence))
end

local function startRound()
    table.insert(sequence, randomStep())
    showSequence()
end

function Memory:Start()
    if not toast then
        toast = Game.Toast.New()
    else
        toast.Print("") -- clear any old toast
    end
    audioSource = AudioSource:new(self.agent.bones.spine)
    audioSource.spatialBlend = 1
    audioSource.loop = false
    audioSource.volume = 1
    sequence = {}
    userInput = {}
    round = 1
    growAmount = baseGrow
    showing = false
    waiting = false
    timer = 0
    toast.Print("Press M to start Size Memory Challenge!")
end

function Memory:Update()
    if Input.GetKeyDown("m") and not showing and not waiting then
        sequence = {}
        userInput = {}
        round = 1
        growAmount = baseGrow
        startRound()
        return
    end

    if showing then
        timer = timer + Time.deltaTime
        local idx = math.floor(timer / interval) + 1
        if idx <= #sequence then
            toast.Print("Round " .. round .. "\nPattern: " .. sequenceToArrows(sequence) ..
                "\nStep " .. idx .. "/" .. #sequence .. ": " .. (sequence[idx] == "up" and "↑" or "↓"))
        else
            showing = false
            waiting = true
            timer = 0
            toast.Print("Repeat the pattern!\nUse Up/Down arrows (" .. #sequence .. " steps).")
        end
        return
    end

    if waiting then
        if Input.GetKeyDown("up") then
            table.insert(userInput, "up")
            toast.Print("You pressed: ↑ (" .. #userInput .. "/" .. #sequence .. ")")
        elseif Input.GetKeyDown("down") then
            table.insert(userInput, "down")
            toast.Print("You pressed: ↓ (" .. #userInput .. "/" .. #sequence .. ")")
        end

        if #userInput == #sequence and #sequence > 0 then
            waiting = false
            local correct = true
            for i = 1, #sequence do
                if sequence[i] ~= userInput[i] then
                    correct = false
                    break
                end
            end
            if correct then
                -- Success: grow, growth increases each round
                local scale = self.agent.scale or self.agent.localScale or 1.0
                scale = scale + growAmount
                if self.agent.scale ~= nil then
                    self.agent.scale = scale
                elseif self.agent.localScale ~= nil then
                    self.agent.localScale = scale
                end
                audioSource.clip = soundList[math.random(1, #soundList)]
                audioSource:Play()
                toast.Print("Correct! GROW +" .. string.format("%.2f", growAmount) ..
                    "\nMultiplier: x" .. string.format("%.2f", growAmount / baseGrow) ..
                    "\nNext round: " .. (round + 1))
                round = round + 1
                growAmount = growAmount * growMultiplier
                nextRoundDelay = 1.2
                waitingForNextRound = true
            else
                -- Fail: shrink as penalty, reset growth
                local scale = self.agent.scale or self.agent.localScale or 1.0
                scale = math.max(0.1, scale * 0.5)
                if self.agent.scale ~= nil then
                    self.agent.scale = scale
                elseif self.agent.localScale ~= nil then
                    self.agent.localScale = scale
                end
                toast.Print("Wrong! SHRINK to 50%\nPress M to try again.")
                sequence = {}
                userInput = {}
                round = 1
                growAmount = baseGrow
            end
        end
    end

    if waitingForNextRound then
        nextRoundDelay = nextRoundDelay - Time.deltaTime
        if nextRoundDelay <= 0 then
            waitingForNextRound = false
            startRound()
        end
        return
    end
end
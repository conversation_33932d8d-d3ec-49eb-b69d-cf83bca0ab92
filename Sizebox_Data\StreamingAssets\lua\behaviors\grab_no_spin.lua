-- Simple Grab & Release Behavior: Press H to grab, J to release

SimpleGrabRelease = RegisterBehavior("Simple Grab & Release")
SimpleGrabRelease.data = {
    menuEntry = "Simple Grab & Release",
    agent = { type = { "giantess" } },
    secondary = true
}

local micro = nil
local isGrabbed = false

local function FindClosestMicro(self)
    if self and self.agent and self.agent.findClosestMicro then
        return self.agent.findClosestMicro()
    end
    return nil
end

function SimpleGrabRelease:Start()
    micro = FindClosestMicro(self)
    isGrabbed = false
    Log("Simple Grab & Release: Press H to grab, J to release")
end

function SimpleGrabRelease:Update()
    -- Refresh micro if not grabbed
    if not isGrabbed then
        micro = FindClosestMicro(self)
    end

    -- Debug: show grab state
    -- Log("isGrabbed: " .. tostring(isGrabbed))

    -- Grab on H
    if Input.GetKeyDown("h") and not isGrabbed and micro then
        if self.agent.bones and self.agent.bones.rightHand then
            micro.transform:SetParent(self.agent.bones.rightHand)
            micro.transform.localPosition = Vector3.new(0, 0, 0)
        else
            micro.transform:SetParent(self.agent.transform)
            micro.transform.localPosition = Vector3.new(0, 1, 0)
        end
        if micro.rigidbody then
            micro.rigidbody.isKinematic = true
            micro.rigidbody.useGravity = false
            micro.rigidbody.detectCollisions = false
        end
        if micro.collider then
            micro.collider.enabled = false
        end
        isGrabbed = true
        Log("Micro grabbed!")
    end

    -- Release on J
    if Input.GetKeyDown("j") then
        Log("J pressed!")
    end

    if Input.GetKeyDown("j") and isGrabbed and micro then
        Log("Attempting release...")
        micro.transform:SetParent(nil)
        if micro.rigidbody then
            micro.rigidbody.isKinematic = false
            micro.rigidbody.useGravity = true
            micro.rigidbody.detectCollisions = true
            micro.rigidbody:AddForce(Vector3.new(0, -30, 0), ForceMode.Impulse)
        end
        if micro.collider then
            micro.collider.enabled = true
        end
        if micro.movement then
            micro.movement.enabled = true
            micro.movement.isGrounded = false
        end
        if micro.isPlayer and micro.player then
            micro.player.isGrabbed = false
            micro.player.isFlying = true
            Event.AddTimeout(0.2, function()
                pcall(function() micro.player.isFlying = false end)
            end)
            micro.player.isCrouching = true
            Event.AddTimeout(0.4, function()
                pcall(function() micro.player.isCrouching = false end)
            end)
        end
        Event.AddTimeout(0.7, function()
            if micro.transform.position.y > 1 then
                micro.transform.position = Vector3.new(
                    micro.transform.position.x,
                    0.5,
                    micro.transform.position.z
                )
            end
        end)
        isGrabbed = false
        Log("Micro released!")
    end
end

function SimpleGrabRelease:Exit()








































end    Log("Simple Grab & Release behavior ended")    end        isGrabbed = false        end)            end                )                    micro.transform.position.z                    0.5,                    micro.transform.position.x,                micro.transform.position = Vector3.new(            if micro.transform.position.y > 1 then        Event.AddTimeout(0.7, function()        end            end)                pcall(function() micro.player.isCrouching = false end)            Event.AddTimeout(0.4, function()            micro.player.isCrouching = true            end)                pcall(function() micro.player.isFlying = false end)            Event.AddTimeout(0.2, function()            micro.player.isFlying = true            micro.player.isGrabbed = false        if micro.isPlayer and micro.player then        end            micro.movement.isGrounded = false            micro.movement.enabled = true        if micro.movement then        end            micro.collider.enabled = true        if micro.collider then        end            micro.rigidbody:AddForce(Vector3.new(0, -30, 0), ForceMode.Impulse)            micro.rigidbody.detectCollisions = true            micro.rigidbody.useGravity = true            micro.rigidbody.isKinematic = false        if micro.rigidbody then        micro.transform:SetParent(nil)    if isGrabbed and micro then    -- Always release if still grabbed
if micro then
    Log("Micro exists: " .. tostring(micro))
else
    Log("Micro is nil!")
endm
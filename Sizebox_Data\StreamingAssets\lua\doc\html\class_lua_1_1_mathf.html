<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.Mathf Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_mathf.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="class_lua_1_1_mathf-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.Mathf Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>A collection of common Unity math functions. Largely overlaps with built-in <a class="el" href="namespace_lua.html">Lua</a> math library (<a href="https://www.lua.org/manual/5.3/manual.html#6.7">https://www.lua.org/manual/5.3/manual.html#6.7</a>)  
 <a href="class_lua_1_1_mathf.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-static-methods" name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:a321380853cd01074ad91e673a7071c99"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#a321380853cd01074ad91e673a7071c99">Abs</a> (float f)</td></tr>
<tr class="memdesc:a321380853cd01074ad91e673a7071c99"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the absolute value of f.  <a href="class_lua_1_1_mathf.html#a321380853cd01074ad91e673a7071c99">More...</a><br /></td></tr>
<tr class="separator:a321380853cd01074ad91e673a7071c99"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a33b558ad9dabaee4792399525a89e4ce"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#a33b558ad9dabaee4792399525a89e4ce">Acos</a> (float f)</td></tr>
<tr class="memdesc:a33b558ad9dabaee4792399525a89e4ce"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the arc-cosine of f - the angle in radians whose cosine is f.  <a href="class_lua_1_1_mathf.html#a33b558ad9dabaee4792399525a89e4ce">More...</a><br /></td></tr>
<tr class="separator:a33b558ad9dabaee4792399525a89e4ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2a53d9e05a8d05eaa05d1fced26906e6"><td class="memItemLeft" align="right" valign="top">static bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#a2a53d9e05a8d05eaa05d1fced26906e6">Approximately</a> (float a, float b)</td></tr>
<tr class="memdesc:a2a53d9e05a8d05eaa05d1fced26906e6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compares two floating point values and returns true if they are similar.  <a href="class_lua_1_1_mathf.html#a2a53d9e05a8d05eaa05d1fced26906e6">More...</a><br /></td></tr>
<tr class="separator:a2a53d9e05a8d05eaa05d1fced26906e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a85731b3f55f246a46df2a2f40bc87ae7"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#a85731b3f55f246a46df2a2f40bc87ae7">Asin</a> (float f)</td></tr>
<tr class="memdesc:a85731b3f55f246a46df2a2f40bc87ae7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the arc-sine of f - the angle in radians whose sine is f.  <a href="class_lua_1_1_mathf.html#a85731b3f55f246a46df2a2f40bc87ae7">More...</a><br /></td></tr>
<tr class="separator:a85731b3f55f246a46df2a2f40bc87ae7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a03740fc6f71760901fbe5f8a6295edeb"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#a03740fc6f71760901fbe5f8a6295edeb">Atan</a> (float f)</td></tr>
<tr class="memdesc:a03740fc6f71760901fbe5f8a6295edeb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the arc-tangent of f - the angle in radians whose tangent is f.  <a href="class_lua_1_1_mathf.html#a03740fc6f71760901fbe5f8a6295edeb">More...</a><br /></td></tr>
<tr class="separator:a03740fc6f71760901fbe5f8a6295edeb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab7f3034dcb7244d1cfb06ae17f014278"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#ab7f3034dcb7244d1cfb06ae17f014278">Atan2</a> (float y, float x)</td></tr>
<tr class="memdesc:ab7f3034dcb7244d1cfb06ae17f014278"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the angle in radians whose Tan is y/x.  <a href="class_lua_1_1_mathf.html#ab7f3034dcb7244d1cfb06ae17f014278">More...</a><br /></td></tr>
<tr class="separator:ab7f3034dcb7244d1cfb06ae17f014278"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa17eeb105797860b39d5765c5f4a8929"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#aa17eeb105797860b39d5765c5f4a8929">Ceil</a> (float f)</td></tr>
<tr class="memdesc:aa17eeb105797860b39d5765c5f4a8929"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the smallest integer greater to or equal to f.  <a href="class_lua_1_1_mathf.html#aa17eeb105797860b39d5765c5f4a8929">More...</a><br /></td></tr>
<tr class="separator:aa17eeb105797860b39d5765c5f4a8929"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad734e258b7adf07ed4a34557d80f0122"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#ad734e258b7adf07ed4a34557d80f0122">Clamp</a> (float value, float min, float max)</td></tr>
<tr class="memdesc:ad734e258b7adf07ed4a34557d80f0122"><td class="mdescLeft">&#160;</td><td class="mdescRight">Clamps a value between a minimum float and maximum float value.  <a href="class_lua_1_1_mathf.html#ad734e258b7adf07ed4a34557d80f0122">More...</a><br /></td></tr>
<tr class="separator:ad734e258b7adf07ed4a34557d80f0122"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8302fdbff60f945480e559d3f97474d5"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#a8302fdbff60f945480e559d3f97474d5">Clamp01</a> (float value)</td></tr>
<tr class="memdesc:a8302fdbff60f945480e559d3f97474d5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Clamps value between 0 and 1 and returns value.  <a href="class_lua_1_1_mathf.html#a8302fdbff60f945480e559d3f97474d5">More...</a><br /></td></tr>
<tr class="separator:a8302fdbff60f945480e559d3f97474d5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae78395f9919d38bd29ae567f6f1aac3e"><td class="memItemLeft" align="right" valign="top">static int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#ae78395f9919d38bd29ae567f6f1aac3e">ClosestPowerOfTwo</a> (int value)</td></tr>
<tr class="memdesc:ae78395f9919d38bd29ae567f6f1aac3e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the closest power of two value.  <a href="class_lua_1_1_mathf.html#ae78395f9919d38bd29ae567f6f1aac3e">More...</a><br /></td></tr>
<tr class="separator:ae78395f9919d38bd29ae567f6f1aac3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9bb7f6cc54371f64edaf224c4a5365b6"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#a9bb7f6cc54371f64edaf224c4a5365b6">Cos</a> (float f)</td></tr>
<tr class="memdesc:a9bb7f6cc54371f64edaf224c4a5365b6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the cosine of angle f in radians.  <a href="class_lua_1_1_mathf.html#a9bb7f6cc54371f64edaf224c4a5365b6">More...</a><br /></td></tr>
<tr class="separator:a9bb7f6cc54371f64edaf224c4a5365b6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aad5844ff29ce4955f86e72b701cf871b"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#aad5844ff29ce4955f86e72b701cf871b">DeltaAngle</a> (float current, float target)</td></tr>
<tr class="memdesc:aad5844ff29ce4955f86e72b701cf871b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculates the shortest difference between two given angles given in degrees.  <a href="class_lua_1_1_mathf.html#aad5844ff29ce4955f86e72b701cf871b">More...</a><br /></td></tr>
<tr class="separator:aad5844ff29ce4955f86e72b701cf871b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2bde76bb17351b51bc55e2d65f8e9263"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#a2bde76bb17351b51bc55e2d65f8e9263">Exp</a> (float power)</td></tr>
<tr class="memdesc:a2bde76bb17351b51bc55e2d65f8e9263"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns e raised to the specified power.  <a href="class_lua_1_1_mathf.html#a2bde76bb17351b51bc55e2d65f8e9263">More...</a><br /></td></tr>
<tr class="separator:a2bde76bb17351b51bc55e2d65f8e9263"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a72fc411403ab2b7e87ffd6e3989bc9e4"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#a72fc411403ab2b7e87ffd6e3989bc9e4">Floor</a> (float f)</td></tr>
<tr class="memdesc:a72fc411403ab2b7e87ffd6e3989bc9e4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the largest integer smaller to or equal to f.  <a href="class_lua_1_1_mathf.html#a72fc411403ab2b7e87ffd6e3989bc9e4">More...</a><br /></td></tr>
<tr class="separator:a72fc411403ab2b7e87ffd6e3989bc9e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4344694ab95eb4dc13046b0798a88ff3"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#a4344694ab95eb4dc13046b0798a88ff3">InverseLerp</a> (float a, float b, float value)</td></tr>
<tr class="memdesc:a4344694ab95eb4dc13046b0798a88ff3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculates the linear parameter t that produces the interpolant value within the range [a, b].  <a href="class_lua_1_1_mathf.html#a4344694ab95eb4dc13046b0798a88ff3">More...</a><br /></td></tr>
<tr class="separator:a4344694ab95eb4dc13046b0798a88ff3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a44aca0d32ffbf1de0925f05b65d94032"><td class="memItemLeft" align="right" valign="top">static bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#a44aca0d32ffbf1de0925f05b65d94032">IsPowerOfTwo</a> (int value)</td></tr>
<tr class="memdesc:a44aca0d32ffbf1de0925f05b65d94032"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if the value is power of two.  <a href="class_lua_1_1_mathf.html#a44aca0d32ffbf1de0925f05b65d94032">More...</a><br /></td></tr>
<tr class="separator:a44aca0d32ffbf1de0925f05b65d94032"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a659f0bf0690e5056165eb8bd958d6751"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#a659f0bf0690e5056165eb8bd958d6751">Lerp</a> (float a, float b, float t)</td></tr>
<tr class="memdesc:a659f0bf0690e5056165eb8bd958d6751"><td class="mdescLeft">&#160;</td><td class="mdescRight">Linearly interpolates between a and b by t.  <a href="class_lua_1_1_mathf.html#a659f0bf0690e5056165eb8bd958d6751">More...</a><br /></td></tr>
<tr class="separator:a659f0bf0690e5056165eb8bd958d6751"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2363a79cc48061f10c4e7e1b47df2538"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#a2363a79cc48061f10c4e7e1b47df2538">LerpAngle</a> (float a, float b, float t)</td></tr>
<tr class="memdesc:a2363a79cc48061f10c4e7e1b47df2538"><td class="mdescLeft">&#160;</td><td class="mdescRight">Same as Lerp but makes sure the values interpolate correctly when they wrap around 360 degrees.  <a href="class_lua_1_1_mathf.html#a2363a79cc48061f10c4e7e1b47df2538">More...</a><br /></td></tr>
<tr class="separator:a2363a79cc48061f10c4e7e1b47df2538"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2707664a0c93b38cece4445ee6750709"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#a2707664a0c93b38cece4445ee6750709">LerpUnclamped</a> (float a, float b, float t)</td></tr>
<tr class="memdesc:a2707664a0c93b38cece4445ee6750709"><td class="mdescLeft">&#160;</td><td class="mdescRight">Linearly interpolates between a and b by t with no limit to t.  <a href="class_lua_1_1_mathf.html#a2707664a0c93b38cece4445ee6750709">More...</a><br /></td></tr>
<tr class="separator:a2707664a0c93b38cece4445ee6750709"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9d1e276c7cfc8fe9f902ebda005e04e1"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#a9d1e276c7cfc8fe9f902ebda005e04e1">Log</a> (float f, float p)</td></tr>
<tr class="memdesc:a9d1e276c7cfc8fe9f902ebda005e04e1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the logarithm of a specified number in a specified base.  <a href="class_lua_1_1_mathf.html#a9d1e276c7cfc8fe9f902ebda005e04e1">More...</a><br /></td></tr>
<tr class="separator:a9d1e276c7cfc8fe9f902ebda005e04e1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afcdb61fd1acfbe37c5e7a675421f3dc9"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#afcdb61fd1acfbe37c5e7a675421f3dc9">Log10</a> (float f)</td></tr>
<tr class="memdesc:afcdb61fd1acfbe37c5e7a675421f3dc9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the base 10 logarithm of a specified number.  <a href="class_lua_1_1_mathf.html#afcdb61fd1acfbe37c5e7a675421f3dc9">More...</a><br /></td></tr>
<tr class="separator:afcdb61fd1acfbe37c5e7a675421f3dc9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae0c3619a26a60fdc6442011911e0e412"><td class="memItemLeft" align="right" valign="top"><a id="ae0c3619a26a60fdc6442011911e0e412" name="ae0c3619a26a60fdc6442011911e0e412"></a>
static float&#160;</td><td class="memItemRight" valign="bottom"><b>Max</b> (float a, float b)</td></tr>
<tr class="memdesc:ae0c3619a26a60fdc6442011911e0e412"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns largest of two or more values. <br /></td></tr>
<tr class="separator:ae0c3619a26a60fdc6442011911e0e412"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab683c60c60e63553655e8727a55f3e61"><td class="memItemLeft" align="right" valign="top"><a id="ab683c60c60e63553655e8727a55f3e61" name="ab683c60c60e63553655e8727a55f3e61"></a>
static float&#160;</td><td class="memItemRight" valign="bottom"><b>Max</b> (params float[] values)</td></tr>
<tr class="memdesc:ab683c60c60e63553655e8727a55f3e61"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns largest of two or more values. <br /></td></tr>
<tr class="separator:ab683c60c60e63553655e8727a55f3e61"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acc164cac8453f2551303265e346f4969"><td class="memItemLeft" align="right" valign="top"><a id="acc164cac8453f2551303265e346f4969" name="acc164cac8453f2551303265e346f4969"></a>
static float&#160;</td><td class="memItemRight" valign="bottom"><b>Min</b> (float a, float b)</td></tr>
<tr class="memdesc:acc164cac8453f2551303265e346f4969"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the smallest of two or more values. <br /></td></tr>
<tr class="separator:acc164cac8453f2551303265e346f4969"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1d580ef078dacb928fb6df039df2281b"><td class="memItemLeft" align="right" valign="top"><a id="a1d580ef078dacb928fb6df039df2281b" name="a1d580ef078dacb928fb6df039df2281b"></a>
static float&#160;</td><td class="memItemRight" valign="bottom"><b>Min</b> (params float[] values)</td></tr>
<tr class="memdesc:a1d580ef078dacb928fb6df039df2281b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the smallest of two or more values. <br /></td></tr>
<tr class="separator:a1d580ef078dacb928fb6df039df2281b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0b9df3fa414f0b12c2fcfd1eee83570c"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#a0b9df3fa414f0b12c2fcfd1eee83570c">MoveTowards</a> (float current, float target, float maxDelta)</td></tr>
<tr class="memdesc:a0b9df3fa414f0b12c2fcfd1eee83570c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Moves a value current towards target.  <a href="class_lua_1_1_mathf.html#a0b9df3fa414f0b12c2fcfd1eee83570c">More...</a><br /></td></tr>
<tr class="separator:a0b9df3fa414f0b12c2fcfd1eee83570c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abc78356d294242d7d4f10dc5e3e81a81"><td class="memItemLeft" align="right" valign="top">static int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#abc78356d294242d7d4f10dc5e3e81a81">NextPowerOfTwo</a> (int value)</td></tr>
<tr class="memdesc:abc78356d294242d7d4f10dc5e3e81a81"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the next power of two value.  <a href="class_lua_1_1_mathf.html#abc78356d294242d7d4f10dc5e3e81a81">More...</a><br /></td></tr>
<tr class="separator:abc78356d294242d7d4f10dc5e3e81a81"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3044ff5b1dd835169520fd054c713d63"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#a3044ff5b1dd835169520fd054c713d63">PerlinNoise</a> (float x, float y)</td></tr>
<tr class="memdesc:a3044ff5b1dd835169520fd054c713d63"><td class="mdescLeft">&#160;</td><td class="mdescRight">Generate 2D Perlin noise.  <a href="class_lua_1_1_mathf.html#a3044ff5b1dd835169520fd054c713d63">More...</a><br /></td></tr>
<tr class="separator:a3044ff5b1dd835169520fd054c713d63"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8eed89df943f9dc0df1398e541e023ad"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#a8eed89df943f9dc0df1398e541e023ad">PingPong</a> (float t, float length)</td></tr>
<tr class="memdesc:a8eed89df943f9dc0df1398e541e023ad"><td class="mdescLeft">&#160;</td><td class="mdescRight">PingPongs the value t, so that it is never larger than length and never smaller than 0.  <a href="class_lua_1_1_mathf.html#a8eed89df943f9dc0df1398e541e023ad">More...</a><br /></td></tr>
<tr class="separator:a8eed89df943f9dc0df1398e541e023ad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ace6b91fa037354fa541a5de450ba6e23"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#ace6b91fa037354fa541a5de450ba6e23">Pow</a> (float f, float p)</td></tr>
<tr class="memdesc:ace6b91fa037354fa541a5de450ba6e23"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns f raised to power p.  <a href="class_lua_1_1_mathf.html#ace6b91fa037354fa541a5de450ba6e23">More...</a><br /></td></tr>
<tr class="separator:ace6b91fa037354fa541a5de450ba6e23"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abf1f882dcf08b3749a27a28f6f7f3630"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#abf1f882dcf08b3749a27a28f6f7f3630">Repeat</a> (float t, float length)</td></tr>
<tr class="memdesc:abf1f882dcf08b3749a27a28f6f7f3630"><td class="mdescLeft">&#160;</td><td class="mdescRight">Loops the value t, so that it is never larger than length and never smaller than 0.  <a href="class_lua_1_1_mathf.html#abf1f882dcf08b3749a27a28f6f7f3630">More...</a><br /></td></tr>
<tr class="separator:abf1f882dcf08b3749a27a28f6f7f3630"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9f6511ccc1da8fd5228959f67b995d91"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#a9f6511ccc1da8fd5228959f67b995d91">Round</a> (float f)</td></tr>
<tr class="memdesc:a9f6511ccc1da8fd5228959f67b995d91"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns f rounded to the nearest integer.  <a href="class_lua_1_1_mathf.html#a9f6511ccc1da8fd5228959f67b995d91">More...</a><br /></td></tr>
<tr class="separator:a9f6511ccc1da8fd5228959f67b995d91"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae5d3654f321079c30baaf42bdf226d89"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#ae5d3654f321079c30baaf42bdf226d89">Sign</a> (float f)</td></tr>
<tr class="memdesc:ae5d3654f321079c30baaf42bdf226d89"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the sign of f.  <a href="class_lua_1_1_mathf.html#ae5d3654f321079c30baaf42bdf226d89">More...</a><br /></td></tr>
<tr class="separator:ae5d3654f321079c30baaf42bdf226d89"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a23fb3f1fdbc09b29120c653bb184fb96"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#a23fb3f1fdbc09b29120c653bb184fb96">Sin</a> (float f)</td></tr>
<tr class="memdesc:a23fb3f1fdbc09b29120c653bb184fb96"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the sine of angle f in radians.  <a href="class_lua_1_1_mathf.html#a23fb3f1fdbc09b29120c653bb184fb96">More...</a><br /></td></tr>
<tr class="separator:a23fb3f1fdbc09b29120c653bb184fb96"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa20bfd858dc30b96f3fb7a5033c4b6bf"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#aa20bfd858dc30b96f3fb7a5033c4b6bf">Sqrt</a> (float f)</td></tr>
<tr class="memdesc:aa20bfd858dc30b96f3fb7a5033c4b6bf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns square root of f.  <a href="class_lua_1_1_mathf.html#aa20bfd858dc30b96f3fb7a5033c4b6bf">More...</a><br /></td></tr>
<tr class="separator:aa20bfd858dc30b96f3fb7a5033c4b6bf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afee5fd526c03b9d4f8f5e6fc978e04b8"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#afee5fd526c03b9d4f8f5e6fc978e04b8">SmoothStep</a> (float from, float to, float t)</td></tr>
<tr class="memdesc:afee5fd526c03b9d4f8f5e6fc978e04b8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Interpolates between min and max with smoothing at the limits.  <a href="class_lua_1_1_mathf.html#afee5fd526c03b9d4f8f5e6fc978e04b8">More...</a><br /></td></tr>
<tr class="separator:afee5fd526c03b9d4f8f5e6fc978e04b8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aefc20a4773c6e7ce2f493974dbdf27c1"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#aefc20a4773c6e7ce2f493974dbdf27c1">Tan</a> (float f)</td></tr>
<tr class="memdesc:aefc20a4773c6e7ce2f493974dbdf27c1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the tangent of angle f in radians.  <a href="class_lua_1_1_mathf.html#aefc20a4773c6e7ce2f493974dbdf27c1">More...</a><br /></td></tr>
<tr class="separator:aefc20a4773c6e7ce2f493974dbdf27c1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab274b83f44ab3e9457e67f949097bc27"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#ab274b83f44ab3e9457e67f949097bc27">ConvertToMeter</a> (string measurement)</td></tr>
<tr class="memdesc:ab274b83f44ab3e9457e67f949097bc27"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts a strings length into meters that can be used to scale entities height.  <a href="class_lua_1_1_mathf.html#ab274b83f44ab3e9457e67f949097bc27">More...</a><br /></td></tr>
<tr class="separator:ab274b83f44ab3e9457e67f949097bc27"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a45b9fc3bad2de4240be1352cc4fa7bca"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#a45b9fc3bad2de4240be1352cc4fa7bca">ConvertToMeter</a> (float measurement, string unit)</td></tr>
<tr class="memdesc:a45b9fc3bad2de4240be1352cc4fa7bca"><td class="mdescLeft">&#160;</td><td class="mdescRight">A variant of ConvertToMeter that takes a float parameter for length.  <a href="class_lua_1_1_mathf.html#a45b9fc3bad2de4240be1352cc4fa7bca">More...</a><br /></td></tr>
<tr class="separator:a45b9fc3bad2de4240be1352cc4fa7bca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf551e91a5f4ff23ff24de2b64cdad15"><td class="memItemLeft" align="right" valign="top">static string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#adf551e91a5f4ff23ff24de2b64cdad15">DistanceToString</a> (float distance)</td></tr>
<tr class="memdesc:adf551e91a5f4ff23ff24de2b64cdad15"><td class="mdescLeft">&#160;</td><td class="mdescRight">Takes this scale and converts it into a string the user can understand  <a href="class_lua_1_1_mathf.html#adf551e91a5f4ff23ff24de2b64cdad15">More...</a><br /></td></tr>
<tr class="separator:adf551e91a5f4ff23ff24de2b64cdad15"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="properties" name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a5f003a3aab6299095b301066d0af6eab"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#a5f003a3aab6299095b301066d0af6eab">Deg2Rad</a><code> [get]</code></td></tr>
<tr class="memdesc:a5f003a3aab6299095b301066d0af6eab"><td class="mdescLeft">&#160;</td><td class="mdescRight">Degrees-to-radians conversion constant (Read Only).  <a href="class_lua_1_1_mathf.html#a5f003a3aab6299095b301066d0af6eab">More...</a><br /></td></tr>
<tr class="separator:a5f003a3aab6299095b301066d0af6eab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a121e67b35c4d96893e79a5be089ebc8a"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#a121e67b35c4d96893e79a5be089ebc8a">Epsilon</a><code> [get]</code></td></tr>
<tr class="memdesc:a121e67b35c4d96893e79a5be089ebc8a"><td class="mdescLeft">&#160;</td><td class="mdescRight">A tiny floating point value (Read Only).  <a href="class_lua_1_1_mathf.html#a121e67b35c4d96893e79a5be089ebc8a">More...</a><br /></td></tr>
<tr class="separator:a121e67b35c4d96893e79a5be089ebc8a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac3686fdccff0df0a6c797af8ba4722b1"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#ac3686fdccff0df0a6c797af8ba4722b1">Infinity</a><code> [get]</code></td></tr>
<tr class="memdesc:ac3686fdccff0df0a6c797af8ba4722b1"><td class="mdescLeft">&#160;</td><td class="mdescRight">A representation of positive infinity (Read Only).  <a href="class_lua_1_1_mathf.html#ac3686fdccff0df0a6c797af8ba4722b1">More...</a><br /></td></tr>
<tr class="separator:ac3686fdccff0df0a6c797af8ba4722b1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0f4a13d448b3d1a96f3aaff159d6636b"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#a0f4a13d448b3d1a96f3aaff159d6636b">NegativeInfinity</a><code> [get]</code></td></tr>
<tr class="memdesc:a0f4a13d448b3d1a96f3aaff159d6636b"><td class="mdescLeft">&#160;</td><td class="mdescRight">A representation of negative infinity (Read Only).  <a href="class_lua_1_1_mathf.html#a0f4a13d448b3d1a96f3aaff159d6636b">More...</a><br /></td></tr>
<tr class="separator:a0f4a13d448b3d1a96f3aaff159d6636b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a408b4fa7c06dd48e2aa0d6fcde7adedc"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#a408b4fa7c06dd48e2aa0d6fcde7adedc">PI</a><code> [get]</code></td></tr>
<tr class="memdesc:a408b4fa7c06dd48e2aa0d6fcde7adedc"><td class="mdescLeft">&#160;</td><td class="mdescRight">The infamous 3.14159265358979... value (Read Only).  <a href="class_lua_1_1_mathf.html#a408b4fa7c06dd48e2aa0d6fcde7adedc">More...</a><br /></td></tr>
<tr class="separator:a408b4fa7c06dd48e2aa0d6fcde7adedc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aed19ee907a834cbea518af347c4f39d7"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_mathf.html#aed19ee907a834cbea518af347c4f39d7">Rad2Deg</a><code> [get]</code></td></tr>
<tr class="memdesc:aed19ee907a834cbea518af347c4f39d7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Radians-to-degrees conversion constant (Read Only).  <a href="class_lua_1_1_mathf.html#aed19ee907a834cbea518af347c4f39d7">More...</a><br /></td></tr>
<tr class="separator:aed19ee907a834cbea518af347c4f39d7"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >A collection of common Unity math functions. Largely overlaps with built-in <a class="el" href="namespace_lua.html">Lua</a> math library (<a href="https://www.lua.org/manual/5.3/manual.html#6.7">https://www.lua.org/manual/5.3/manual.html#6.7</a>) </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a321380853cd01074ad91e673a7071c99" name="a321380853cd01074ad91e673a7071c99"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a321380853cd01074ad91e673a7071c99">&#9670;&nbsp;</a></span>Abs()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.Abs </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>f</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns the absolute value of f. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">f</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a33b558ad9dabaee4792399525a89e4ce" name="a33b558ad9dabaee4792399525a89e4ce"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a33b558ad9dabaee4792399525a89e4ce">&#9670;&nbsp;</a></span>Acos()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.Acos </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>f</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns the arc-cosine of f - the angle in radians whose cosine is f. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">f</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a2a53d9e05a8d05eaa05d1fced26906e6" name="a2a53d9e05a8d05eaa05d1fced26906e6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2a53d9e05a8d05eaa05d1fced26906e6">&#9670;&nbsp;</a></span>Approximately()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static bool Lua.Mathf.Approximately </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>b</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Compares two floating point values and returns true if they are similar. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">a</td><td></td></tr>
    <tr><td class="paramname">b</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a85731b3f55f246a46df2a2f40bc87ae7" name="a85731b3f55f246a46df2a2f40bc87ae7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a85731b3f55f246a46df2a2f40bc87ae7">&#9670;&nbsp;</a></span>Asin()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.Asin </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>f</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns the arc-sine of f - the angle in radians whose sine is f. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">f</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a03740fc6f71760901fbe5f8a6295edeb" name="a03740fc6f71760901fbe5f8a6295edeb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a03740fc6f71760901fbe5f8a6295edeb">&#9670;&nbsp;</a></span>Atan()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.Atan </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>f</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns the arc-tangent of f - the angle in radians whose tangent is f. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">f</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ab7f3034dcb7244d1cfb06ae17f014278" name="ab7f3034dcb7244d1cfb06ae17f014278"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab7f3034dcb7244d1cfb06ae17f014278">&#9670;&nbsp;</a></span>Atan2()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.Atan2 </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>x</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns the angle in radians whose Tan is y/x. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">y</td><td></td></tr>
    <tr><td class="paramname">x</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="aa17eeb105797860b39d5765c5f4a8929" name="aa17eeb105797860b39d5765c5f4a8929"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa17eeb105797860b39d5765c5f4a8929">&#9670;&nbsp;</a></span>Ceil()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.Ceil </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>f</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns the smallest integer greater to or equal to f. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">f</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ad734e258b7adf07ed4a34557d80f0122" name="ad734e258b7adf07ed4a34557d80f0122"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad734e258b7adf07ed4a34557d80f0122">&#9670;&nbsp;</a></span>Clamp()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.Clamp </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>value</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>min</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>max</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Clamps a value between a minimum float and maximum float value. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">value</td><td></td></tr>
    <tr><td class="paramname">min</td><td></td></tr>
    <tr><td class="paramname">max</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a8302fdbff60f945480e559d3f97474d5" name="a8302fdbff60f945480e559d3f97474d5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8302fdbff60f945480e559d3f97474d5">&#9670;&nbsp;</a></span>Clamp01()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.Clamp01 </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>value</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Clamps value between 0 and 1 and returns value. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">value</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ae78395f9919d38bd29ae567f6f1aac3e" name="ae78395f9919d38bd29ae567f6f1aac3e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae78395f9919d38bd29ae567f6f1aac3e">&#9670;&nbsp;</a></span>ClosestPowerOfTwo()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static int Lua.Mathf.ClosestPowerOfTwo </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>value</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns the closest power of two value. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">value</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a45b9fc3bad2de4240be1352cc4fa7bca" name="a45b9fc3bad2de4240be1352cc4fa7bca"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a45b9fc3bad2de4240be1352cc4fa7bca">&#9670;&nbsp;</a></span>ConvertToMeter() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.ConvertToMeter </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>measurement</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>unit</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>A variant of ConvertToMeter that takes a float parameter for length. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">measurement</td><td>A float representing a length of distance. </td></tr>
    <tr><td class="paramname">unit</td><td>A acronym. Valid acronyms are km, m, cm, mm, μm (um for convenience), mi, ft, in and th. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>A float value representing the quantity of meters from the conversion or negative infinity upon failure</dd></dl>
<p >Set a character to be precisely 50ft tall </p><div class="fragment"><div class="line"><a class="code hl_function" href="class_lua_1_1_mathf.html#a9d1e276c7cfc8fe9f902ebda005e04e1">Log</a>(<span class="stringliteral">&quot;Attack of the 50ft woman&quot;</span>)</div>
<div class="line">scale = <a class="code hl_class" href="class_lua_1_1_mathf.html">Mathf</a>.<a class="code hl_function" href="class_lua_1_1_mathf.html#ab274b83f44ab3e9457e67f949097bc27">ConvertToMeter</a>(50, &quot;ft&quot;)</div>
<div class="line">self.entity.metricHeight = scale</div>
<div class="ttc" id="aclass_lua_1_1_mathf_html"><div class="ttname"><a href="class_lua_1_1_mathf.html">Lua.Mathf</a></div><div class="ttdoc">A collection of common Unity math functions. Largely overlaps with built-in Lua math library (https:/...</div><div class="ttdef"><b>Definition:</b> LuaMathf.cs:11</div></div>
<div class="ttc" id="aclass_lua_1_1_mathf_html_a9d1e276c7cfc8fe9f902ebda005e04e1"><div class="ttname"><a href="class_lua_1_1_mathf.html#a9d1e276c7cfc8fe9f902ebda005e04e1">Lua.Mathf.Log</a></div><div class="ttdeci">static float Log(float f, float p)</div><div class="ttdoc">Returns the logarithm of a specified number in a specified base.</div><div class="ttdef"><b>Definition:</b> LuaMathf.cs:275</div></div>
<div class="ttc" id="aclass_lua_1_1_mathf_html_ab274b83f44ab3e9457e67f949097bc27"><div class="ttname"><a href="class_lua_1_1_mathf.html#ab274b83f44ab3e9457e67f949097bc27">Lua.Mathf.ConvertToMeter</a></div><div class="ttdeci">static float ConvertToMeter(string measurement)</div><div class="ttdoc">Converts a strings length into meters that can be used to scale entities height.</div><div class="ttdef"><b>Definition:</b> LuaMathf.cs:485</div></div>
</div><!-- fragment --> 
</div>
</div>
<a id="ab274b83f44ab3e9457e67f949097bc27" name="ab274b83f44ab3e9457e67f949097bc27"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab274b83f44ab3e9457e67f949097bc27">&#9670;&nbsp;</a></span>ConvertToMeter() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.ConvertToMeter </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>measurement</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Converts a strings length into meters that can be used to scale entities height. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">measurement</td><td>A string representing a length of distance and unit acronym. Valid acronyms are km, m, cm, mm, μm (um for convenience), mi, ft, in and th. If no acronym is specified meters is assumed. If the string contains a quote (') character then feet'inch is assumed and no acronym is needed. For portability reasons the decimal separator is always the dot (.) character </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>A float value representing the quantity of meters from the conversion or negative infinity upon failure</dd></dl>
<p >Set a character to be precisely 50ft tall </p><div class="fragment"><div class="line"><a class="code hl_function" href="class_lua_1_1_mathf.html#a9d1e276c7cfc8fe9f902ebda005e04e1">Log</a>(<span class="stringliteral">&quot;Attack of the 50ft woman&quot;</span>)</div>
<div class="line">scale = <a class="code hl_class" href="class_lua_1_1_mathf.html">Mathf</a>.<a class="code hl_function" href="class_lua_1_1_mathf.html#ab274b83f44ab3e9457e67f949097bc27">ConvertToMeter</a>(&quot;50ft&quot;)</div>
<div class="line">self.entity.metricHeight = scale</div>
</div><!-- fragment --> <p >Set a character in feet and inches </p><div class="fragment"><div class="line"><a class="code hl_function" href="class_lua_1_1_mathf.html#a9d1e276c7cfc8fe9f902ebda005e04e1">Log</a>(<span class="stringliteral">&quot;You are no longer a manlet!&quot;</span>)</div>
<div class="line">scale = <a class="code hl_class" href="class_lua_1_1_mathf.html">Mathf</a>.<a class="code hl_function" href="class_lua_1_1_mathf.html#ab274b83f44ab3e9457e67f949097bc27">ConvertToMeter</a>(&quot;6&#39;8&quot;)</div>
<div class="line">self.entity.metricHeight = scale</div>
</div><!-- fragment --> <p >For portability reasons dot (.) is always the decimal separator and comma (,) is always the thousands separator on this function </p><div class="fragment"><div class="line"><a class="code hl_function" href="class_lua_1_1_mathf.html#a9d1e276c7cfc8fe9f902ebda005e04e1">Log</a>(<span class="stringliteral">&quot;InvariantCulture format only. Use 1.5km or 1,500m not 1,5km nor 1.500m&quot;</span>)</div>
<div class="line">scale = <a class="code hl_class" href="class_lua_1_1_mathf.html">Mathf</a>.<a class="code hl_function" href="class_lua_1_1_mathf.html#ab274b83f44ab3e9457e67f949097bc27">ConvertToMeter</a>(&quot;1.5km&quot;)</div>
<div class="line">self.entity.metricHeight = scale</div>
</div><!-- fragment --> 
</div>
</div>
<a id="a9bb7f6cc54371f64edaf224c4a5365b6" name="a9bb7f6cc54371f64edaf224c4a5365b6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9bb7f6cc54371f64edaf224c4a5365b6">&#9670;&nbsp;</a></span>Cos()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.Cos </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>f</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns the cosine of angle f in radians. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">f</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="aad5844ff29ce4955f86e72b701cf871b" name="aad5844ff29ce4955f86e72b701cf871b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aad5844ff29ce4955f86e72b701cf871b">&#9670;&nbsp;</a></span>DeltaAngle()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.DeltaAngle </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>current</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>target</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Calculates the shortest difference between two given angles given in degrees. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">current</td><td></td></tr>
    <tr><td class="paramname">target</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="adf551e91a5f4ff23ff24de2b64cdad15" name="adf551e91a5f4ff23ff24de2b64cdad15"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adf551e91a5f4ff23ff24de2b64cdad15">&#9670;&nbsp;</a></span>DistanceToString()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static string Lua.Mathf.DistanceToString </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>distance</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Takes this scale and converts it into a string the user can understand </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">distance</td><td>The distance in Unity units to be converted</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>A string of the distance in a metric the user prefers</dd></dl>
<p >Get the distance between the agent and target </p><div class="fragment"><div class="line">local distance = <span class="keyword">self</span>.agent.DistanceTo(<span class="keyword">self</span>.target)</div>
<div class="line">local str = <a class="code hl_class" href="class_lua_1_1_mathf.html">Mathf</a>.<a class="code hl_function" href="class_lua_1_1_mathf.html#adf551e91a5f4ff23ff24de2b64cdad15">DistanceToString</a>(distance)</div>
<div class="line">log(<span class="stringliteral">&quot;Distance: &quot;</span> .. str)</div>
<div class="ttc" id="aclass_lua_1_1_mathf_html_adf551e91a5f4ff23ff24de2b64cdad15"><div class="ttname"><a href="class_lua_1_1_mathf.html#adf551e91a5f4ff23ff24de2b64cdad15">Lua.Mathf.DistanceToString</a></div><div class="ttdeci">static string DistanceToString(float distance)</div><div class="ttdoc">Takes this scale and converts it into a string the user can understand</div><div class="ttdef"><b>Definition:</b> LuaMathf.cs:524</div></div>
</div><!-- fragment --> <p >Although it's inefficient to do so the return value is suitable to be used as a parameter for <a class="el" href="class_lua_1_1_mathf.html#ab274b83f44ab3e9457e67f949097bc27" title="Converts a strings length into meters that can be used to scale entities height.">Mathf.ConvertToMeter()</a></p>

</div>
</div>
<a id="a2bde76bb17351b51bc55e2d65f8e9263" name="a2bde76bb17351b51bc55e2d65f8e9263"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2bde76bb17351b51bc55e2d65f8e9263">&#9670;&nbsp;</a></span>Exp()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.Exp </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>power</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns e raised to the specified power. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">power</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a72fc411403ab2b7e87ffd6e3989bc9e4" name="a72fc411403ab2b7e87ffd6e3989bc9e4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a72fc411403ab2b7e87ffd6e3989bc9e4">&#9670;&nbsp;</a></span>Floor()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.Floor </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>f</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns the largest integer smaller to or equal to f. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">f</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a4344694ab95eb4dc13046b0798a88ff3" name="a4344694ab95eb4dc13046b0798a88ff3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4344694ab95eb4dc13046b0798a88ff3">&#9670;&nbsp;</a></span>InverseLerp()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.InverseLerp </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Calculates the linear parameter t that produces the interpolant value within the range [a, b]. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">a</td><td></td></tr>
    <tr><td class="paramname">b</td><td></td></tr>
    <tr><td class="paramname">value</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a44aca0d32ffbf1de0925f05b65d94032" name="a44aca0d32ffbf1de0925f05b65d94032"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a44aca0d32ffbf1de0925f05b65d94032">&#9670;&nbsp;</a></span>IsPowerOfTwo()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static bool Lua.Mathf.IsPowerOfTwo </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>value</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns true if the value is power of two. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">value</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a659f0bf0690e5056165eb8bd958d6751" name="a659f0bf0690e5056165eb8bd958d6751"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a659f0bf0690e5056165eb8bd958d6751">&#9670;&nbsp;</a></span>Lerp()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.Lerp </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>t</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Linearly interpolates between a and b by t. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">a</td><td></td></tr>
    <tr><td class="paramname">b</td><td></td></tr>
    <tr><td class="paramname">t</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a2363a79cc48061f10c4e7e1b47df2538" name="a2363a79cc48061f10c4e7e1b47df2538"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2363a79cc48061f10c4e7e1b47df2538">&#9670;&nbsp;</a></span>LerpAngle()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.LerpAngle </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>t</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Same as Lerp but makes sure the values interpolate correctly when they wrap around 360 degrees. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">a</td><td></td></tr>
    <tr><td class="paramname">b</td><td></td></tr>
    <tr><td class="paramname">t</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a2707664a0c93b38cece4445ee6750709" name="a2707664a0c93b38cece4445ee6750709"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2707664a0c93b38cece4445ee6750709">&#9670;&nbsp;</a></span>LerpUnclamped()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.LerpUnclamped </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>t</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Linearly interpolates between a and b by t with no limit to t. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">a</td><td></td></tr>
    <tr><td class="paramname">b</td><td></td></tr>
    <tr><td class="paramname">t</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a9d1e276c7cfc8fe9f902ebda005e04e1" name="a9d1e276c7cfc8fe9f902ebda005e04e1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9d1e276c7cfc8fe9f902ebda005e04e1">&#9670;&nbsp;</a></span>Log()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.Log </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>f</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>p</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns the logarithm of a specified number in a specified base. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">f</td><td></td></tr>
    <tr><td class="paramname">p</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="afcdb61fd1acfbe37c5e7a675421f3dc9" name="afcdb61fd1acfbe37c5e7a675421f3dc9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afcdb61fd1acfbe37c5e7a675421f3dc9">&#9670;&nbsp;</a></span>Log10()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.Log10 </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>f</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns the base 10 logarithm of a specified number. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">f</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a0b9df3fa414f0b12c2fcfd1eee83570c" name="a0b9df3fa414f0b12c2fcfd1eee83570c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0b9df3fa414f0b12c2fcfd1eee83570c">&#9670;&nbsp;</a></span>MoveTowards()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.MoveTowards </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>current</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>target</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>maxDelta</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Moves a value current towards target. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">current</td><td></td></tr>
    <tr><td class="paramname">target</td><td></td></tr>
    <tr><td class="paramname">maxDelta</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="abc78356d294242d7d4f10dc5e3e81a81" name="abc78356d294242d7d4f10dc5e3e81a81"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abc78356d294242d7d4f10dc5e3e81a81">&#9670;&nbsp;</a></span>NextPowerOfTwo()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static int Lua.Mathf.NextPowerOfTwo </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>value</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns the next power of two value. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">value</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a3044ff5b1dd835169520fd054c713d63" name="a3044ff5b1dd835169520fd054c713d63"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3044ff5b1dd835169520fd054c713d63">&#9670;&nbsp;</a></span>PerlinNoise()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.PerlinNoise </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Generate 2D Perlin noise. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">x</td><td></td></tr>
    <tr><td class="paramname">y</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>
<p>Perlin noise is a pseudo-random pattern of float values generated across a 2D plane (although the technique does generalise to three or more dimensions, this is not implemented in Unity). The noise does not contain a completely random value at each point but rather consists of "waves" whose values gradually increase and decrease across the pattern. The noise can be used as the basis for texture effects but also for animation, generating terrain heightmaps and many other things. </p>

</div>
</div>
<a id="a8eed89df943f9dc0df1398e541e023ad" name="a8eed89df943f9dc0df1398e541e023ad"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8eed89df943f9dc0df1398e541e023ad">&#9670;&nbsp;</a></span>PingPong()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.PingPong </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>t</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>length</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>PingPongs the value t, so that it is never larger than length and never smaller than 0. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">t</td><td></td></tr>
    <tr><td class="paramname">length</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ace6b91fa037354fa541a5de450ba6e23" name="ace6b91fa037354fa541a5de450ba6e23"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ace6b91fa037354fa541a5de450ba6e23">&#9670;&nbsp;</a></span>Pow()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.Pow </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>f</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>p</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns f raised to power p. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">f</td><td></td></tr>
    <tr><td class="paramname">p</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="abf1f882dcf08b3749a27a28f6f7f3630" name="abf1f882dcf08b3749a27a28f6f7f3630"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abf1f882dcf08b3749a27a28f6f7f3630">&#9670;&nbsp;</a></span>Repeat()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.Repeat </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>t</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>length</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Loops the value t, so that it is never larger than length and never smaller than 0. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">t</td><td></td></tr>
    <tr><td class="paramname">length</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a9f6511ccc1da8fd5228959f67b995d91" name="a9f6511ccc1da8fd5228959f67b995d91"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9f6511ccc1da8fd5228959f67b995d91">&#9670;&nbsp;</a></span>Round()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.Round </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>f</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns f rounded to the nearest integer. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">f</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ae5d3654f321079c30baaf42bdf226d89" name="ae5d3654f321079c30baaf42bdf226d89"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae5d3654f321079c30baaf42bdf226d89">&#9670;&nbsp;</a></span>Sign()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.Sign </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>f</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns the sign of f. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">f</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a23fb3f1fdbc09b29120c653bb184fb96" name="a23fb3f1fdbc09b29120c653bb184fb96"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a23fb3f1fdbc09b29120c653bb184fb96">&#9670;&nbsp;</a></span>Sin()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.Sin </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>f</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns the sine of angle f in radians. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">f</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="afee5fd526c03b9d4f8f5e6fc978e04b8" name="afee5fd526c03b9d4f8f5e6fc978e04b8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afee5fd526c03b9d4f8f5e6fc978e04b8">&#9670;&nbsp;</a></span>SmoothStep()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.SmoothStep </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>from</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>to</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>t</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Interpolates between min and max with smoothing at the limits. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">from</td><td></td></tr>
    <tr><td class="paramname">to</td><td></td></tr>
    <tr><td class="paramname">t</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="aa20bfd858dc30b96f3fb7a5033c4b6bf" name="aa20bfd858dc30b96f3fb7a5033c4b6bf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa20bfd858dc30b96f3fb7a5033c4b6bf">&#9670;&nbsp;</a></span>Sqrt()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.Sqrt </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>f</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns square root of f. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">f</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="aefc20a4773c6e7ce2f493974dbdf27c1" name="aefc20a4773c6e7ce2f493974dbdf27c1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aefc20a4773c6e7ce2f493974dbdf27c1">&#9670;&nbsp;</a></span>Tan()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.Mathf.Tan </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>f</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns the tangent of angle f in radians. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">f</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="a5f003a3aab6299095b301066d0af6eab" name="a5f003a3aab6299095b301066d0af6eab"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5f003a3aab6299095b301066d0af6eab">&#9670;&nbsp;</a></span>Deg2Rad</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Mathf.Deg2Rad</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Degrees-to-radians conversion constant (Read Only). </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a121e67b35c4d96893e79a5be089ebc8a" name="a121e67b35c4d96893e79a5be089ebc8a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a121e67b35c4d96893e79a5be089ebc8a">&#9670;&nbsp;</a></span>Epsilon</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Mathf.Epsilon</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>A tiny floating point value (Read Only). </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ac3686fdccff0df0a6c797af8ba4722b1" name="ac3686fdccff0df0a6c797af8ba4722b1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac3686fdccff0df0a6c797af8ba4722b1">&#9670;&nbsp;</a></span>Infinity</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Mathf.Infinity</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>A representation of positive infinity (Read Only). </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a0f4a13d448b3d1a96f3aaff159d6636b" name="a0f4a13d448b3d1a96f3aaff159d6636b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0f4a13d448b3d1a96f3aaff159d6636b">&#9670;&nbsp;</a></span>NegativeInfinity</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Mathf.NegativeInfinity</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>A representation of negative infinity (Read Only). </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a408b4fa7c06dd48e2aa0d6fcde7adedc" name="a408b4fa7c06dd48e2aa0d6fcde7adedc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a408b4fa7c06dd48e2aa0d6fcde7adedc">&#9670;&nbsp;</a></span>PI</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Mathf.PI</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The infamous 3.14159265358979... value (Read Only). </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="aed19ee907a834cbea518af347c4f39d7" name="aed19ee907a834cbea518af347c4f39d7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aed19ee907a834cbea518af347c4f39d7">&#9670;&nbsp;</a></span>Rad2Deg</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Mathf.Rad2Deg</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Radians-to-degrees conversion constant (Read Only). </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaMathf.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_mathf.html">Mathf</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.15"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.PlayerEntity Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(initResizable);
/* @license-end */</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(document).ready(function(){initNavTree('class_lua_1_1_player_entity.html','');});
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="class_lua_1_1_player_entity-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">Lua.PlayerEntity Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>A <a class="el" href="class_lua_1_1_player_entity.html" title="A PlayerEntity represents player-controlled character.">PlayerEntity</a> represents player-controlled character.  
 <a href="class_lua_1_1_player_entity.html#details">More...</a></p>
<div class="dynheader">
Inheritance diagram for Lua.PlayerEntity:</div>
<div class="dyncontent">
 <div class="center">
  <img src="class_lua_1_1_player_entity.png" usemap="#Lua.PlayerEntity_map" alt=""/>
  <map id="Lua.PlayerEntity_map" name="Lua.PlayerEntity_map">
<area href="class_lua_1_1_entity.html" title="A Entity represents Characters and Objects" alt="Lua.Entity" shape="rect" coords="0,0,105,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a9d1469da41be5408b4d6702dc04f3351"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_player_entity.html#a9d1469da41be5408b4d6702dc04f3351">Crush</a> ()</td></tr>
<tr class="memdesc:a9d1469da41be5408b4d6702dc04f3351"><td class="mdescLeft">&#160;</td><td class="mdescRight">Crushes the player.  <a href="#a9d1469da41be5408b4d6702dc04f3351">More...</a><br /></td></tr>
<tr class="separator:a9d1469da41be5408b4d6702dc04f3351"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_class_lua_1_1_entity"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_class_lua_1_1_entity')"><img src="closed.png" alt="-"/>&#160;Public Member Functions inherited from <a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td></tr>
<tr class="memitem:a422af2c756caecc01bad49a14ba5da7f inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a422af2c756caecc01bad49a14ba5da7f">Delete</a> ()</td></tr>
<tr class="memdesc:a422af2c756caecc01bad49a14ba5da7f inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Safely deletes the entity (don't deletes the player).  <a href="class_lua_1_1_entity.html#a422af2c756caecc01bad49a14ba5da7f">More...</a><br /></td></tr>
<tr class="separator:a422af2c756caecc01bad49a14ba5da7f inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4ec9fe7962ad6a301c33253c735aedae inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a4ec9fe7962ad6a301c33253c735aedae">DistanceTo</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> target)</td></tr>
<tr class="memdesc:a4ec9fe7962ad6a301c33253c735aedae inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate the relative distance to a target.  <a href="class_lua_1_1_entity.html#a4ec9fe7962ad6a301c33253c735aedae">More...</a><br /></td></tr>
<tr class="separator:a4ec9fe7962ad6a301c33253c735aedae inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad341a3f8e110857d06111b8b6f29d646 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#ad341a3f8e110857d06111b8b6f29d646">DistanceTo</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> point)</td></tr>
<tr class="memdesc:ad341a3f8e110857d06111b8b6f29d646 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculate the relative distance to a point.  <a href="class_lua_1_1_entity.html#ad341a3f8e110857d06111b8b6f29d646">More...</a><br /></td></tr>
<tr class="separator:ad341a3f8e110857d06111b8b6f29d646 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2e06bf904c49f705ac361e1538365e2e inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a2e06bf904c49f705ac361e1538365e2e">FindClosestMicro</a> ()</td></tr>
<tr class="memdesc:a2e06bf904c49f705ac361e1538365e2e inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the closes Micro <a class="el" href="class_lua_1_1_entity.html" title="A Entity represents Characters and Objects">Entity</a>. It can also return the player.  <a href="class_lua_1_1_entity.html#a2e06bf904c49f705ac361e1538365e2e">More...</a><br /></td></tr>
<tr class="separator:a2e06bf904c49f705ac361e1538365e2e inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7eac4ebcb1fe784ab5c06eed7885cf7 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#ac7eac4ebcb1fe784ab5c06eed7885cf7">FindClosestGiantess</a> ()</td></tr>
<tr class="memdesc:ac7eac4ebcb1fe784ab5c06eed7885cf7 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the closes Giantess <a class="el" href="class_lua_1_1_entity.html" title="A Entity represents Characters and Objects">Entity</a>. It can also return the player.  <a href="class_lua_1_1_entity.html#ac7eac4ebcb1fe784ab5c06eed7885cf7">More...</a><br /></td></tr>
<tr class="separator:ac7eac4ebcb1fe784ab5c06eed7885cf7 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27a8d5461e9d6890e085b06e2d00f6bd inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a27a8d5461e9d6890e085b06e2d00f6bd">isHumanoid</a> ()</td></tr>
<tr class="memdesc:a27a8d5461e9d6890e085b06e2d00f6bd inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if the entity is a humanoid character.  <a href="class_lua_1_1_entity.html#a27a8d5461e9d6890e085b06e2d00f6bd">More...</a><br /></td></tr>
<tr class="separator:a27a8d5461e9d6890e085b06e2d00f6bd inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af72e042e1fd05c66abceebb49ec2caf4 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#af72e042e1fd05c66abceebb49ec2caf4">isGiantess</a> ()</td></tr>
<tr class="memdesc:af72e042e1fd05c66abceebb49ec2caf4 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if the entity is a giantess (.gts).  <a href="class_lua_1_1_entity.html#af72e042e1fd05c66abceebb49ec2caf4">More...</a><br /></td></tr>
<tr class="separator:af72e042e1fd05c66abceebb49ec2caf4 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af7b9099c16b719f42e4fdfd82661d259 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#af7b9099c16b719f42e4fdfd82661d259">isPlayer</a> ()</td></tr>
<tr class="memdesc:af7b9099c16b719f42e4fdfd82661d259 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if this entity is player controlled (micro or giantess).  <a href="class_lua_1_1_entity.html#af7b9099c16b719f42e4fdfd82661d259">More...</a><br /></td></tr>
<tr class="separator:af7b9099c16b719f42e4fdfd82661d259 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5eaa128b6b8cf4aeb7f219edd030d61e inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a5eaa128b6b8cf4aeb7f219edd030d61e">isMicro</a> ()</td></tr>
<tr class="memdesc:a5eaa128b6b8cf4aeb7f219edd030d61e inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if the entity is a micro (.micro)  <a href="class_lua_1_1_entity.html#a5eaa128b6b8cf4aeb7f219edd030d61e">More...</a><br /></td></tr>
<tr class="separator:a5eaa128b6b8cf4aeb7f219edd030d61e inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1bf50f8db78480e10f40a71e44480e21 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a1bf50f8db78480e10f40a71e44480e21">BE</a> (float speed)</td></tr>
<tr class="memdesc:a1bf50f8db78480e10f40a71e44480e21 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Adds a Breast Expansion action at the end of the action queue.  <a href="class_lua_1_1_entity.html#a1bf50f8db78480e10f40a71e44480e21">More...</a><br /></td></tr>
<tr class="separator:a1bf50f8db78480e10f40a71e44480e21 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac8dfc303d378e4e805c5ba3f38f49c59 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#ac8dfc303d378e4e805c5ba3f38f49c59">BE</a> (float speed, float time)</td></tr>
<tr class="memdesc:ac8dfc303d378e4e805c5ba3f38f49c59 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Adds a Breast Expansion action at the end of the action queue.  <a href="class_lua_1_1_entity.html#ac8dfc303d378e4e805c5ba3f38f49c59">More...</a><br /></td></tr>
<tr class="separator:ac8dfc303d378e4e805c5ba3f38f49c59 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a92feb21c4219c60a7e5935733302083f inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a92feb21c4219c60a7e5935733302083f">Grow</a> (float factor)</td></tr>
<tr class="memdesc:a92feb21c4219c60a7e5935733302083f inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Adds a <b>priority</b> Grow action at the <b>beginning</b> of the action queue.  <a href="class_lua_1_1_entity.html#a92feb21c4219c60a7e5935733302083f">More...</a><br /></td></tr>
<tr class="separator:a92feb21c4219c60a7e5935733302083f inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac574fce9abb6f90e7f7675be74838964 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#ac574fce9abb6f90e7f7675be74838964">Grow</a> (float factor, float time)</td></tr>
<tr class="memdesc:ac574fce9abb6f90e7f7675be74838964 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Adds a <b>priority</b> Grow action at the <b>beginning</b> of the action queue.  <a href="class_lua_1_1_entity.html#ac574fce9abb6f90e7f7675be74838964">More...</a><br /></td></tr>
<tr class="separator:ac574fce9abb6f90e7f7675be74838964 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0007133219ff5ec24e9eecf6a9d2dd50 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a0007133219ff5ec24e9eecf6a9d2dd50">GrowAndWait</a> (float factor, float time)</td></tr>
<tr class="memdesc:a0007133219ff5ec24e9eecf6a9d2dd50 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Adds a regular Grow action at the end of the action queue.  <a href="class_lua_1_1_entity.html#a0007133219ff5ec24e9eecf6a9d2dd50">More...</a><br /></td></tr>
<tr class="separator:a0007133219ff5ec24e9eecf6a9d2dd50 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5d6cfb68967adf948db2b6c09d7dfd38 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a5d6cfb68967adf948db2b6c09d7dfd38">MoveTo</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> destination)</td></tr>
<tr class="memdesc:a5d6cfb68967adf948db2b6c09d7dfd38 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">The entity will walk to a designed point.  <a href="class_lua_1_1_entity.html#a5d6cfb68967adf948db2b6c09d7dfd38">More...</a><br /></td></tr>
<tr class="separator:a5d6cfb68967adf948db2b6c09d7dfd38 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a307f39e2316f0c4246604ba5ce5b749e inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a307f39e2316f0c4246604ba5ce5b749e">MoveTo</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> targetEntity)</td></tr>
<tr class="memdesc:a307f39e2316f0c4246604ba5ce5b749e inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">The entity will walk towards another target entity, and stop when it reaches it.  <a href="class_lua_1_1_entity.html#a307f39e2316f0c4246604ba5ce5b749e">More...</a><br /></td></tr>
<tr class="separator:a307f39e2316f0c4246604ba5ce5b749e inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab4f0e1c31b16110cdadb7479ba008423 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#ab4f0e1c31b16110cdadb7479ba008423">Chase</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> target)</td></tr>
<tr class="memdesc:ab4f0e1c31b16110cdadb7479ba008423 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Chase the target entity while evading objects.  <a href="class_lua_1_1_entity.html#ab4f0e1c31b16110cdadb7479ba008423">More...</a><br /></td></tr>
<tr class="separator:ab4f0e1c31b16110cdadb7479ba008423 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a442bfc9dcbb33b4fa3922a59357a8723 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a442bfc9dcbb33b4fa3922a59357a8723">Face</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> target)</td></tr>
<tr class="memdesc:a442bfc9dcbb33b4fa3922a59357a8723 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Face the target.  <a href="class_lua_1_1_entity.html#a442bfc9dcbb33b4fa3922a59357a8723">More...</a><br /></td></tr>
<tr class="separator:a442bfc9dcbb33b4fa3922a59357a8723 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32545d25ff6935a006bdb6b5ad45ad9a inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a32545d25ff6935a006bdb6b5ad45ad9a">Seek</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> target, float duration=0, float separation=-1)</td></tr>
<tr class="memdesc:a32545d25ff6935a006bdb6b5ad45ad9a inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">The entity will seek toward another target until the time runs out or it is close enough. Used in the "Follow" command.  <a href="class_lua_1_1_entity.html#a32545d25ff6935a006bdb6b5ad45ad9a">More...</a><br /></td></tr>
<tr class="separator:a32545d25ff6935a006bdb6b5ad45ad9a inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1a0205dc1d6ab6ac54679970885490f9 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a1a0205dc1d6ab6ac54679970885490f9">Seek</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> <a class="el" href="class_lua_1_1_entity.html#ad8bd97d98fddc9b89f8410512b502c3f">position</a>, float duration=0, float separation=-1)</td></tr>
<tr class="memdesc:a1a0205dc1d6ab6ac54679970885490f9 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">The entity will seek toward a position for the specified amount of time (in seconds).  <a href="class_lua_1_1_entity.html#a1a0205dc1d6ab6ac54679970885490f9">More...</a><br /></td></tr>
<tr class="separator:a1a0205dc1d6ab6ac54679970885490f9 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa4d1d044edd1f9b9dfb4b310c49b8761 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#aa4d1d044edd1f9b9dfb4b310c49b8761">Wander</a> ()</td></tr>
<tr class="memdesc:aa4d1d044edd1f9b9dfb4b310c49b8761 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">The entity will wander without stopping.  <a href="class_lua_1_1_entity.html#aa4d1d044edd1f9b9dfb4b310c49b8761">More...</a><br /></td></tr>
<tr class="separator:aa4d1d044edd1f9b9dfb4b310c49b8761 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9858a940de17a0405da24bff1d834970 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a9858a940de17a0405da24bff1d834970">Wander</a> (float time)</td></tr>
<tr class="memdesc:a9858a940de17a0405da24bff1d834970 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">The entity will wander during the specified amount of time.  <a href="class_lua_1_1_entity.html#a9858a940de17a0405da24bff1d834970">More...</a><br /></td></tr>
<tr class="separator:a9858a940de17a0405da24bff1d834970 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a58a5bb200f7182dea5e622f036f05bf3 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a58a5bb200f7182dea5e622f036f05bf3">Wait</a> (float time)</td></tr>
<tr class="memdesc:a58a5bb200f7182dea5e622f036f05bf3 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">The entity will wait for the specified amount of time.  <a href="class_lua_1_1_entity.html#a58a5bb200f7182dea5e622f036f05bf3">More...</a><br /></td></tr>
<tr class="separator:a58a5bb200f7182dea5e622f036f05bf3 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af1290dfaf8e3da8c2adb7279359bf036 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#af1290dfaf8e3da8c2adb7279359bf036">Flee</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> target, float time)</td></tr>
<tr class="memdesc:af1290dfaf8e3da8c2adb7279359bf036 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">The entity will flee from the target during the specified amount of time. A time of 0 will make it unlimited.  <a href="class_lua_1_1_entity.html#af1290dfaf8e3da8c2adb7279359bf036">More...</a><br /></td></tr>
<tr class="separator:af1290dfaf8e3da8c2adb7279359bf036 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a334e63b56e1c78e6dbebd4dd9c48a69e inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a334e63b56e1c78e6dbebd4dd9c48a69e">Flee</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> <a class="el" href="class_lua_1_1_entity.html#ad8bd97d98fddc9b89f8410512b502c3f">position</a>, float time)</td></tr>
<tr class="memdesc:a334e63b56e1c78e6dbebd4dd9c48a69e inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">The entity will flee from the position during the specified amount of time. A time of 0 will make it unlimited.  <a href="class_lua_1_1_entity.html#a334e63b56e1c78e6dbebd4dd9c48a69e">More...</a><br /></td></tr>
<tr class="separator:a334e63b56e1c78e6dbebd4dd9c48a69e inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a72e0a626a062ba116cf62cfeb77f87f8 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a72e0a626a062ba116cf62cfeb77f87f8">FindRandomBuilding</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> self)</td></tr>
<tr class="memdesc:a72e0a626a062ba116cf62cfeb77f87f8 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">This function a random location of a structure close to the gts  <a href="class_lua_1_1_entity.html#a72e0a626a062ba116cf62cfeb77f87f8">More...</a><br /></td></tr>
<tr class="separator:a72e0a626a062ba116cf62cfeb77f87f8 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5690c11c287139f6d5b5171f5d4189e9 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a5690c11c287139f6d5b5171f5d4189e9">Wreck</a> ()</td></tr>
<tr class="memdesc:a5690c11c287139f6d5b5171f5d4189e9 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">The enitity will aggressively stomp in an attempt to wreck everything  <a href="class_lua_1_1_entity.html#a5690c11c287139f6d5b5171f5d4189e9">More...</a><br /></td></tr>
<tr class="separator:a5690c11c287139f6d5b5171f5d4189e9 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af93c20b6cca387d60a5368c079ae65ef inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#af93c20b6cca387d60a5368c079ae65ef">StandUp</a> ()</td></tr>
<tr class="memdesc:af93c20b6cca387d60a5368c079ae65ef inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Orders a crushed micro to stand up. Note that entities stop all behaviors when crushed. So this method is only useful when called from other entity's behavior or a global script.  <a href="class_lua_1_1_entity.html#af93c20b6cca387d60a5368c079ae65ef">More...</a><br /></td></tr>
<tr class="separator:af93c20b6cca387d60a5368c079ae65ef inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0a8058c8b504215e492471b4e7d557d4 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a0a8058c8b504215e492471b4e7d557d4">Stomp</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> target)</td></tr>
<tr class="memdesc:a0a8058c8b504215e492471b4e7d557d4 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">If the entity is a giantess, she will stomp a target entity.  <a href="class_lua_1_1_entity.html#a0a8058c8b504215e492471b4e7d557d4">More...</a><br /></td></tr>
<tr class="separator:a0a8058c8b504215e492471b4e7d557d4 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad6c8d1dc736aeea69e734d3c1e884343 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#ad6c8d1dc736aeea69e734d3c1e884343">Stomp</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> target)</td></tr>
<tr class="memdesc:ad6c8d1dc736aeea69e734d3c1e884343 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">If the entity is a giantess, she will stomp a target point.  <a href="class_lua_1_1_entity.html#ad6c8d1dc736aeea69e734d3c1e884343">More...</a><br /></td></tr>
<tr class="separator:ad6c8d1dc736aeea69e734d3c1e884343 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6ddff7e7a95c85ba34bee6b8b5c4ee93 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a6ddff7e7a95c85ba34bee6b8b5c4ee93">Grab</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> target)</td></tr>
<tr class="memdesc:a6ddff7e7a95c85ba34bee6b8b5c4ee93 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">If the entity is a giantess will grab a target.  <a href="class_lua_1_1_entity.html#a6ddff7e7a95c85ba34bee6b8b5c4ee93">More...</a><br /></td></tr>
<tr class="separator:a6ddff7e7a95c85ba34bee6b8b5c4ee93 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a29cdb052c5422873a708c8080039cb4b inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a29cdb052c5422873a708c8080039cb4b">LookAt</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> target)</td></tr>
<tr class="memdesc:a29cdb052c5422873a708c8080039cb4b inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Not to be confused with <a class="el" href="class_lua_1_1_transform.html#a1e722de9c3eacff82477ab7684a67553" title="Not to be confused with Entity.LookAt Rotates the transform so the forward vector points at /target/&#39;...">Transform.LookAt</a> If the entity is a giantess it will look towards a target. If the target is nil the giantess will return to default looking behaviour  <a href="class_lua_1_1_entity.html#a29cdb052c5422873a708c8080039cb4b">More...</a><br /></td></tr>
<tr class="separator:a29cdb052c5422873a708c8080039cb4b inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a23180e62c487b6c3923e39b3be84b291 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a23180e62c487b6c3923e39b3be84b291">EquipRaygun</a> ()</td></tr>
<tr class="memdesc:a23180e62c487b6c3923e39b3be84b291 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">If the entity is an NPC micro, will equip raygun if it is currently unequipped.  <a href="class_lua_1_1_entity.html#a23180e62c487b6c3923e39b3be84b291">More...</a><br /></td></tr>
<tr class="separator:a23180e62c487b6c3923e39b3be84b291 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaa24b31446f5c0087faa4030f91e80ac inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#aaa24b31446f5c0087faa4030f91e80ac">EquipSMG</a> ()</td></tr>
<tr class="memdesc:aaa24b31446f5c0087faa4030f91e80ac inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">If the entity is an NPC micro, will equip SMG if it is currently unequipped.  <a href="class_lua_1_1_entity.html#aaa24b31446f5c0087faa4030f91e80ac">More...</a><br /></td></tr>
<tr class="separator:aaa24b31446f5c0087faa4030f91e80ac inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1a4cc3d2425ef3527ab0692f4c2a9ca3 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a1a4cc3d2425ef3527ab0692f4c2a9ca3">UnequipGun</a> ()</td></tr>
<tr class="memdesc:a1a4cc3d2425ef3527ab0692f4c2a9ca3 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">If the entity is an NPC micro, will unequip gun if it currently has one equipped.  <a href="class_lua_1_1_entity.html#a1a4cc3d2425ef3527ab0692f4c2a9ca3">More...</a><br /></td></tr>
<tr class="separator:a1a4cc3d2425ef3527ab0692f4c2a9ca3 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac47475b1b0342f1e6ce52aca2eec7f38 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#ac47475b1b0342f1e6ce52aca2eec7f38">Aim</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> target)</td></tr>
<tr class="memdesc:ac47475b1b0342f1e6ce52aca2eec7f38 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">If the entity is an NPC micro, will aim at target. If target is null, will stop aiming  <a href="class_lua_1_1_entity.html#ac47475b1b0342f1e6ce52aca2eec7f38">More...</a><br /></td></tr>
<tr class="separator:ac47475b1b0342f1e6ce52aca2eec7f38 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aadc969ba1387cf03d80b8432705f0750 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#aadc969ba1387cf03d80b8432705f0750">StopAiming</a> ()</td></tr>
<tr class="memdesc:aadc969ba1387cf03d80b8432705f0750 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">If the entity is an NPC micro, will stop aiming  <a href="class_lua_1_1_entity.html#aadc969ba1387cf03d80b8432705f0750">More...</a><br /></td></tr>
<tr class="separator:aadc969ba1387cf03d80b8432705f0750 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3a5c197d31f8834f8ed67f71c6157719 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a3a5c197d31f8834f8ed67f71c6157719">StartFiring</a> ()</td></tr>
<tr class="memdesc:a3a5c197d31f8834f8ed67f71c6157719 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">If the entity is an NPC micro will start firing, and aiming if not doing so already, if there it has an aim target.  <a href="class_lua_1_1_entity.html#a3a5c197d31f8834f8ed67f71c6157719">More...</a><br /></td></tr>
<tr class="separator:a3a5c197d31f8834f8ed67f71c6157719 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4d7809fc03b618624a6d7640674fe646 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a4d7809fc03b618624a6d7640674fe646">StopFiring</a> ()</td></tr>
<tr class="memdesc:a4d7809fc03b618624a6d7640674fe646 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">If the entity is an NPC micro will stop firing  <a href="class_lua_1_1_entity.html#a4d7809fc03b618624a6d7640674fe646">More...</a><br /></td></tr>
<tr class="separator:a4d7809fc03b618624a6d7640674fe646 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af23f0b36c1b4c778fc7f328490223852 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#af23f0b36c1b4c778fc7f328490223852">Engage</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> target=null)</td></tr>
<tr class="memdesc:af23f0b36c1b4c778fc7f328490223852 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">If the entity is an NPC micro will start chasing and firing (and aiming) at designated target if not doing so already. If no target is provided, will attempt to engage the currently assigned aim target. Sort of an all-in-one command where the entity will reposition themselves if too close to or too far from the target.  <a href="class_lua_1_1_entity.html#af23f0b36c1b4c778fc7f328490223852">More...</a><br /></td></tr>
<tr class="separator:af23f0b36c1b4c778fc7f328490223852 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9ead5c7d5e821fa285ab065b9cc3185f inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a9ead5c7d5e821fa285ab065b9cc3185f">StopEngaging</a> ()</td></tr>
<tr class="memdesc:a9ead5c7d5e821fa285ab065b9cc3185f inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">If the entity is an NPC micro will stop chasing and firing  <a href="class_lua_1_1_entity.html#a9ead5c7d5e821fa285ab065b9cc3185f">More...</a><br /></td></tr>
<tr class="separator:a9ead5c7d5e821fa285ab065b9cc3185f inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a17d117771166d30924da118c1fed9968 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a17d117771166d30924da118c1fed9968">FireOnce</a> ()</td></tr>
<tr class="memdesc:a17d117771166d30924da118c1fed9968 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">If the entity is an NPC micro will fire once, and aiming if not doing so already  <a href="class_lua_1_1_entity.html#a17d117771166d30924da118c1fed9968">More...</a><br /></td></tr>
<tr class="separator:a17d117771166d30924da118c1fed9968 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6357c0a54e9aa79beb928d079b42aa76 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a6357c0a54e9aa79beb928d079b42aa76">IsDead</a> ()</td></tr>
<tr class="memdesc:a6357c0a54e9aa79beb928d079b42aa76 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Checks if the entity is dead.  <a href="class_lua_1_1_entity.html#a6357c0a54e9aa79beb928d079b42aa76">More...</a><br /></td></tr>
<tr class="separator:a6357c0a54e9aa79beb928d079b42aa76 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab25d357d357ab0cf7ff9a2e81aa9cb08 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#ab25d357d357ab0cf7ff9a2e81aa9cb08">IsStuck</a> ()</td></tr>
<tr class="memdesc:ab25d357d357ab0cf7ff9a2e81aa9cb08 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Checks if the entity is stuck to giantess. Only applicable to micros.  <a href="class_lua_1_1_entity.html#ab25d357d357ab0cf7ff9a2e81aa9cb08">More...</a><br /></td></tr>
<tr class="separator:ab25d357d357ab0cf7ff9a2e81aa9cb08 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa8688dacb32db168b780597d8f11622b inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#aa8688dacb32db168b780597d8f11622b">IsTargettable</a> ()</td></tr>
<tr class="memdesc:aa8688dacb32db168b780597d8f11622b inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Checks if the entity be targeted for crushing. To be targettable it must be a living micro that is not stuck.  <a href="class_lua_1_1_entity.html#aa8688dacb32db168b780597d8f11622b">More...</a><br /></td></tr>
<tr class="separator:aa8688dacb32db168b780597d8f11622b inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4b4bb9870796b854c0d5666c9cba9375 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a4b4bb9870796b854c0d5666c9cba9375">IsCrushed</a> ()</td></tr>
<tr class="memdesc:a4b4bb9870796b854c0d5666c9cba9375 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Checks if the entity is crushed. Only applicable to micros. It doesn't matter whether it survived the crush or not. Returns false for deleted entities, even if they were crushed prior to deleting.  <a href="class_lua_1_1_entity.html#a4b4bb9870796b854c0d5666c9cba9375">More...</a><br /></td></tr>
<tr class="separator:a4b4bb9870796b854c0d5666c9cba9375 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6be24874965a4015e7fd8244fa345220 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a6be24874965a4015e7fd8244fa345220">UpdateMeshCollider</a> ()</td></tr>
<tr class="memdesc:a6be24874965a4015e7fd8244fa345220 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">[Slow operation] Forces the update of the collider. This is a slow operacion, don't update the mesh every frame, don't use inside a loop.  <a href="class_lua_1_1_entity.html#a6be24874965a4015e7fd8244fa345220">More...</a><br /></td></tr>
<tr class="separator:a6be24874965a4015e7fd8244fa345220 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac2bbfd97ccf17d7cd96fb1cf3b3c51e4 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#ac2bbfd97ccf17d7cd96fb1cf3b3c51e4">ShowBreastPhysicsOptions</a> ()</td></tr>
<tr class="memdesc:ac2bbfd97ccf17d7cd96fb1cf3b3c51e4 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Shows a UI menu to the user that can be used to alter the breasts physics  <a href="class_lua_1_1_entity.html#ac2bbfd97ccf17d7cd96fb1cf3b3c51e4">More...</a><br /></td></tr>
<tr class="separator:ac2bbfd97ccf17d7cd96fb1cf3b3c51e4 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa3ec8ca2693205f43b8e1e620b209018 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#aa3ec8ca2693205f43b8e1e620b209018">PlayAs</a> ()</td></tr>
<tr class="memdesc:aa3ec8ca2693205f43b8e1e620b209018 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Transfers player control to this entity, only if it's a playable character.  <a href="class_lua_1_1_entity.html#aa3ec8ca2693205f43b8e1e620b209018">More...</a><br /></td></tr>
<tr class="separator:aa3ec8ca2693205f43b8e1e620b209018 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aad0651348795eb39acee39055e0b7638 inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#aad0651348795eb39acee39055e0b7638">GetRandomMicro</a> ()</td></tr>
<tr class="memdesc:aad0651348795eb39acee39055e0b7638 inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a random micro in the scene. Can return null if there are no micros in the scene.  <a href="class_lua_1_1_entity.html#aad0651348795eb39acee39055e0b7638">More...</a><br /></td></tr>
<tr class="separator:aad0651348795eb39acee39055e0b7638 inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa9df9f39762cb24b89449e8b61aab43c inherit pub_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#aa9df9f39762cb24b89449e8b61aab43c">GetRandomGiantess</a> ()</td></tr>
<tr class="memdesc:aa9df9f39762cb24b89449e8b61aab43c inherit pub_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a random giantess in the scene. Can return null if there are no giantesses in the scene.  <a href="class_lua_1_1_entity.html#aa9df9f39762cb24b89449e8b61aab43c">More...</a><br /></td></tr>
<tr class="separator:aa9df9f39762cb24b89449e8b61aab43c inherit pub_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:ae050a6ac5d73748b2e989b3b3fbfd61d"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_player_entity.html#ae050a6ac5d73748b2e989b3b3fbfd61d">climbing</a><code> [get]</code></td></tr>
<tr class="memdesc:ae050a6ac5d73748b2e989b3b3fbfd61d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Player is climbing.  <a href="#ae050a6ac5d73748b2e989b3b3fbfd61d">More...</a><br /></td></tr>
<tr class="separator:ae050a6ac5d73748b2e989b3b3fbfd61d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae805d9b6fbe0ecb483f726fee471434f"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_player_entity.html#ae805d9b6fbe0ecb483f726fee471434f">isAiming</a><code> [get]</code></td></tr>
<tr class="memdesc:ae805d9b6fbe0ecb483f726fee471434f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Player is aiming.  <a href="#ae805d9b6fbe0ecb483f726fee471434f">More...</a><br /></td></tr>
<tr class="separator:ae805d9b6fbe0ecb483f726fee471434f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aabbfb4d83476aaa301ee4f526dd6ecf8"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_player_entity.html#aabbfb4d83476aaa301ee4f526dd6ecf8">walkSpeed</a><code> [get, set]</code></td></tr>
<tr class="memdesc:aabbfb4d83476aaa301ee4f526dd6ecf8"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="class_lua_1_1_movement.html" title="Use this component to control the movement of agents.">Movement</a> speed when walking (holding <code>Alt</code> by default).  <a href="#aabbfb4d83476aaa301ee4f526dd6ecf8">More...</a><br /></td></tr>
<tr class="separator:aabbfb4d83476aaa301ee4f526dd6ecf8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3b78a546178fca04a5bfd04159bebe51"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_player_entity.html#a3b78a546178fca04a5bfd04159bebe51">runSpeed</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a3b78a546178fca04a5bfd04159bebe51"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="class_lua_1_1_movement.html" title="Use this component to control the movement of agents.">Movement</a> speed when running. This is the default movement mode.  <a href="#a3b78a546178fca04a5bfd04159bebe51">More...</a><br /></td></tr>
<tr class="separator:a3b78a546178fca04a5bfd04159bebe51"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acd890123e850bbd3c1e4e459b65ea5c1"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_player_entity.html#acd890123e850bbd3c1e4e459b65ea5c1">sprintSpeed</a><code> [get, set]</code></td></tr>
<tr class="memdesc:acd890123e850bbd3c1e4e459b65ea5c1"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="class_lua_1_1_movement.html" title="Use this component to control the movement of agents.">Movement</a> speed when sprinting (holding <code>Shift</code> by default).  <a href="#acd890123e850bbd3c1e4e459b65ea5c1">More...</a><br /></td></tr>
<tr class="separator:acd890123e850bbd3c1e4e459b65ea5c1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a07be3c99f61960cadd5333939bc9ee88"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_player_entity.html#a07be3c99f61960cadd5333939bc9ee88">flySpeed</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a07be3c99f61960cadd5333939bc9ee88"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="class_lua_1_1_movement.html" title="Use this component to control the movement of agents.">Movement</a> speed when flying (activated by pressing <code>E</code> by default).  <a href="#a07be3c99f61960cadd5333939bc9ee88">More...</a><br /></td></tr>
<tr class="separator:a07be3c99f61960cadd5333939bc9ee88"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aee7c81573cc1de31d9f64fd13b534ae2"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_player_entity.html#aee7c81573cc1de31d9f64fd13b534ae2">superFlySpeed</a><code> [get, set]</code></td></tr>
<tr class="memdesc:aee7c81573cc1de31d9f64fd13b534ae2"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="class_lua_1_1_movement.html" title="Use this component to control the movement of agents.">Movement</a> speed when super flying (holding <code>Shift</code> by default while flying).  <a href="#aee7c81573cc1de31d9f64fd13b534ae2">More...</a><br /></td></tr>
<tr class="separator:aee7c81573cc1de31d9f64fd13b534ae2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6917cc802f4f2de29b01e7c968b6336e"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_player_entity.html#a6917cc802f4f2de29b01e7c968b6336e">climbSpeed</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a6917cc802f4f2de29b01e7c968b6336e"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="class_lua_1_1_movement.html" title="Use this component to control the movement of agents.">Movement</a> speed when climbing (activated by pressing <code>C</code> by default).  <a href="#a6917cc802f4f2de29b01e7c968b6336e">More...</a><br /></td></tr>
<tr class="separator:a6917cc802f4f2de29b01e7c968b6336e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aac79f44819e1adfc1b5a2f6b1bbe61e6"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_player_entity.html#aac79f44819e1adfc1b5a2f6b1bbe61e6">jumpPower</a><code> [get, set]</code></td></tr>
<tr class="memdesc:aac79f44819e1adfc1b5a2f6b1bbe61e6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Jumping height (activated by pressing <code>Space</code> by default).  <a href="#aac79f44819e1adfc1b5a2f6b1bbe61e6">More...</a><br /></td></tr>
<tr class="separator:aac79f44819e1adfc1b5a2f6b1bbe61e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1137d463c4ca74abf6fcb032e5311755"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_player_entity.html#a1137d463c4ca74abf6fcb032e5311755">autowalk</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a1137d463c4ca74abf6fcb032e5311755"><td class="mdescLeft">&#160;</td><td class="mdescRight">Is autowalk enabled? Toggled by pressing <code>Right Shift</code> by default.  <a href="#a1137d463c4ca74abf6fcb032e5311755">More...</a><br /></td></tr>
<tr class="separator:a1137d463c4ca74abf6fcb032e5311755"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3406666c706b2b01b854bada1a04da5b"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_player_entity.html#a3406666c706b2b01b854bada1a04da5b">sizeChangeSpeed</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a3406666c706b2b01b854bada1a04da5b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Speed of size change when holding <code>SizeUp</code>/<code>SizeUp</code> keys (<code>Z</code>/<code>X</code> by default).  <a href="#a3406666c706b2b01b854bada1a04da5b">More...</a><br /></td></tr>
<tr class="separator:a3406666c706b2b01b854bada1a04da5b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af2e34e02994fcfdb032932e9da17e49d"><td class="memItemLeft" align="right" valign="top">override float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_player_entity.html#af2e34e02994fcfdb032932e9da17e49d">minSize</a><code> [get, set]</code></td></tr>
<tr class="memdesc:af2e34e02994fcfdb032932e9da17e49d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Minimum player size.  <a href="#af2e34e02994fcfdb032932e9da17e49d">More...</a><br /></td></tr>
<tr class="separator:af2e34e02994fcfdb032932e9da17e49d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7ac787ce4c39f28cc36cb8b08c9934b7"><td class="memItemLeft" align="right" valign="top">override float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_player_entity.html#a7ac787ce4c39f28cc36cb8b08c9934b7">maxSize</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a7ac787ce4c39f28cc36cb8b08c9934b7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maximum player size.  <a href="#a7ac787ce4c39f28cc36cb8b08c9934b7">More...</a><br /></td></tr>
<tr class="separator:a7ac787ce4c39f28cc36cb8b08c9934b7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a853ddbac1d9d5d71d593d8e3aa9ceb0c"><td class="memItemLeft" align="right" valign="top">override float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_player_entity.html#a853ddbac1d9d5d71d593d8e3aa9ceb0c">scale</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a853ddbac1d9d5d71d593d8e3aa9ceb0c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Player scale.  <a href="#a853ddbac1d9d5d71d593d8e3aa9ceb0c">More...</a><br /></td></tr>
<tr class="separator:a853ddbac1d9d5d71d593d8e3aa9ceb0c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a47bfb3f0f7b06c235a1b516967d52408"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_lua_player_raygun.html">LuaPlayerRaygun</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_player_entity.html#a47bfb3f0f7b06c235a1b516967d52408">raygun</a><code> [get]</code></td></tr>
<tr class="memdesc:a47bfb3f0f7b06c235a1b516967d52408"><td class="mdescLeft">&#160;</td><td class="mdescRight">Player scale.  <a href="#a47bfb3f0f7b06c235a1b516967d52408">More...</a><br /></td></tr>
<tr class="separator:a47bfb3f0f7b06c235a1b516967d52408"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header properties_class_lua_1_1_entity"><td colspan="2" onclick="javascript:toggleInherit('properties_class_lua_1_1_entity')"><img src="closed.png" alt="-"/>&#160;Properties inherited from <a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td></tr>
<tr class="memitem:ae0c707512eed832f2211ace61d3be75d inherit properties_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">Table&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#ae0c707512eed832f2211ace61d3be75d">dict</a><code> [get]</code></td></tr>
<tr class="memdesc:ae0c707512eed832f2211ace61d3be75d inherit properties_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">A dictionary associated to this entity. It can be used to store and exchange arbitrary data between scripts.  <a href="class_lua_1_1_entity.html#ae0c707512eed832f2211ace61d3be75d">More...</a><br /></td></tr>
<tr class="separator:ae0c707512eed832f2211ace61d3be75d inherit properties_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a51edf8c42bd2acb730ae73d045341320 inherit properties_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_transform.html">Transform</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a51edf8c42bd2acb730ae73d045341320">transform</a><code> [get]</code></td></tr>
<tr class="memdesc:a51edf8c42bd2acb730ae73d045341320 inherit properties_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">The transform component associated to this entity. It contains data about the position, rotation and scale used by the Unity Engine.  <a href="class_lua_1_1_entity.html#a51edf8c42bd2acb730ae73d045341320">More...</a><br /></td></tr>
<tr class="separator:a51edf8c42bd2acb730ae73d045341320 inherit properties_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a56d48f666679b251eefa10e6f65bbb60 inherit properties_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_rigidbody.html">Rigidbody</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a56d48f666679b251eefa10e6f65bbb60">rigidbody</a><code> [get]</code></td></tr>
<tr class="memdesc:a56d48f666679b251eefa10e6f65bbb60 inherit properties_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Control the physics of the <a class="el" href="class_lua_1_1_entity.html" title="A Entity represents Characters and Objects">Entity</a>. Normal objects don't come with physics by default, but player and npc they have it for movement.  <a href="class_lua_1_1_entity.html#a56d48f666679b251eefa10e6f65bbb60">More...</a><br /></td></tr>
<tr class="separator:a56d48f666679b251eefa10e6f65bbb60 inherit properties_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a13c1350817e444010fcbaff8d224039e inherit properties_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_a_i.html">AI</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a13c1350817e444010fcbaff8d224039e">ai</a><code> [get]</code></td></tr>
<tr class="memdesc:a13c1350817e444010fcbaff8d224039e inherit properties_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">The ai component controls the ai behaviors of the entity.  <a href="class_lua_1_1_entity.html#a13c1350817e444010fcbaff8d224039e">More...</a><br /></td></tr>
<tr class="separator:a13c1350817e444010fcbaff8d224039e inherit properties_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acf080bfbeeb3a6308c2fdd4cc4993e81 inherit properties_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_animation.html">Animation</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#acf080bfbeeb3a6308c2fdd4cc4993e81">animation</a><code> [get]</code></td></tr>
<tr class="memdesc:acf080bfbeeb3a6308c2fdd4cc4993e81 inherit properties_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Component for controling the animation of humanoid entities.  <a href="class_lua_1_1_entity.html#acf080bfbeeb3a6308c2fdd4cc4993e81">More...</a><br /></td></tr>
<tr class="separator:acf080bfbeeb3a6308c2fdd4cc4993e81 inherit properties_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8d4bdecb96c327395e5ddbde88608cf4 inherit properties_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_bones.html">Bones</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a8d4bdecb96c327395e5ddbde88608cf4">bones</a><code> [get]</code></td></tr>
<tr class="memdesc:a8d4bdecb96c327395e5ddbde88608cf4 inherit properties_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Access the bone transforms of the model (head, hands, feet, etc).  <a href="class_lua_1_1_entity.html#a8d4bdecb96c327395e5ddbde88608cf4">More...</a><br /></td></tr>
<tr class="separator:a8d4bdecb96c327395e5ddbde88608cf4 inherit properties_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a42cd1e5e507a1e79eb1161799564da88 inherit properties_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_i_k.html">IK</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a42cd1e5e507a1e79eb1161799564da88">ik</a><code> [get]</code></td></tr>
<tr class="memdesc:a42cd1e5e507a1e79eb1161799564da88 inherit properties_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Inverse Kinematics for the model  <a href="class_lua_1_1_entity.html#a42cd1e5e507a1e79eb1161799564da88">More...</a><br /></td></tr>
<tr class="separator:a42cd1e5e507a1e79eb1161799564da88 inherit properties_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9a5a67b2da9b9d95e31c766aa68760ee inherit properties_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_senses.html">Senses</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a9a5a67b2da9b9d95e31c766aa68760ee">senses</a><code> [get]</code></td></tr>
<tr class="memdesc:a9a5a67b2da9b9d95e31c766aa68760ee inherit properties_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Manages the senses of the entity such as the vision.  <a href="class_lua_1_1_entity.html#a9a5a67b2da9b9d95e31c766aa68760ee">More...</a><br /></td></tr>
<tr class="separator:a9a5a67b2da9b9d95e31c766aa68760ee inherit properties_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa9723210eb2494461ff12c55b52e8844 inherit properties_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_morphs.html">Morphs</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#aa9723210eb2494461ff12c55b52e8844">morphs</a><code> [get]</code></td></tr>
<tr class="memdesc:aa9723210eb2494461ff12c55b52e8844 inherit properties_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Control the morphs of the entity.  <a href="class_lua_1_1_entity.html#aa9723210eb2494461ff12c55b52e8844">More...</a><br /></td></tr>
<tr class="separator:aa9723210eb2494461ff12c55b52e8844 inherit properties_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2845d63d6164b33ee49f760211fa4116 inherit properties_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_movement.html">Movement</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a2845d63d6164b33ee49f760211fa4116">movement</a><code> [get]</code></td></tr>
<tr class="memdesc:a2845d63d6164b33ee49f760211fa4116 inherit properties_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Manages the movement of the entity.  <a href="class_lua_1_1_entity.html#a2845d63d6164b33ee49f760211fa4116">More...</a><br /></td></tr>
<tr class="separator:a2845d63d6164b33ee49f760211fa4116 inherit properties_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af00214fc6ff19d22818d8d0810630cea inherit properties_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_shooting.html">Shooting</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#af00214fc6ff19d22818d8d0810630cea">shooting</a><code> [get]</code></td></tr>
<tr class="memdesc:af00214fc6ff19d22818d8d0810630cea inherit properties_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Manages the shooting of the entity.  <a href="class_lua_1_1_entity.html#af00214fc6ff19d22818d8d0810630cea">More...</a><br /></td></tr>
<tr class="separator:af00214fc6ff19d22818d8d0810630cea inherit properties_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad8bd97d98fddc9b89f8410512b502c3f inherit properties_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#ad8bd97d98fddc9b89f8410512b502c3f">position</a><code> [get, set]</code></td></tr>
<tr class="memdesc:ad8bd97d98fddc9b89f8410512b502c3f inherit properties_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the current position on world space of this entity.  <a href="class_lua_1_1_entity.html#ad8bd97d98fddc9b89f8410512b502c3f">More...</a><br /></td></tr>
<tr class="separator:ad8bd97d98fddc9b89f8410512b502c3f inherit properties_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7dfc341caa3b11cc42ef45226689741c inherit properties_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a7dfc341caa3b11cc42ef45226689741c">CanLookAtPlayer</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a7dfc341caa3b11cc42ef45226689741c inherit properties_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Whether or not a Giantess entity can look at the player.  <a href="class_lua_1_1_entity.html#a7dfc341caa3b11cc42ef45226689741c">More...</a><br /></td></tr>
<tr class="separator:a7dfc341caa3b11cc42ef45226689741c inherit properties_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7776e8422e86d2ab5670ca314a65aab5 inherit properties_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a7776e8422e86d2ab5670ca314a65aab5">id</a><code> [get]</code></td></tr>
<tr class="memdesc:a7776e8422e86d2ab5670ca314a65aab5 inherit properties_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the id associated to this entity.  <a href="class_lua_1_1_entity.html#a7776e8422e86d2ab5670ca314a65aab5">More...</a><br /></td></tr>
<tr class="separator:a7776e8422e86d2ab5670ca314a65aab5 inherit properties_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8155b6c6ef0f0630ec7e818dd4cdaec4 inherit properties_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a8155b6c6ef0f0630ec7e818dd4cdaec4">name</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a8155b6c6ef0f0630ec7e818dd4cdaec4 inherit properties_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">The name of this entity.  <a href="class_lua_1_1_entity.html#a8155b6c6ef0f0630ec7e818dd4cdaec4">More...</a><br /></td></tr>
<tr class="separator:a8155b6c6ef0f0630ec7e818dd4cdaec4 inherit properties_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acd67b39a7c95e3cb87171073c2877de1 inherit properties_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#acd67b39a7c95e3cb87171073c2877de1">modelName</a><code> [get]</code></td></tr>
<tr class="memdesc:acd67b39a7c95e3cb87171073c2877de1 inherit properties_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Name the model of this entity.  <a href="class_lua_1_1_entity.html#acd67b39a7c95e3cb87171073c2877de1">More...</a><br /></td></tr>
<tr class="separator:acd67b39a7c95e3cb87171073c2877de1 inherit properties_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a784673c0e6fbf29381a309a5df0ee10e inherit properties_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">virtual float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a784673c0e6fbf29381a309a5df0ee10e">scale</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a784673c0e6fbf29381a309a5df0ee10e inherit properties_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">The scale of this entity. Use this instead of the transform when possible.  <a href="class_lua_1_1_entity.html#a784673c0e6fbf29381a309a5df0ee10e">More...</a><br /></td></tr>
<tr class="separator:a784673c0e6fbf29381a309a5df0ee10e inherit properties_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1873494f26c8f90c79254b43d25d47f7 inherit properties_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">virtual float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a1873494f26c8f90c79254b43d25d47f7">baseHeight</a><code> [get]</code></td></tr>
<tr class="memdesc:a1873494f26c8f90c79254b43d25d47f7 inherit properties_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Base model height of this entity.  <a href="class_lua_1_1_entity.html#a1873494f26c8f90c79254b43d25d47f7">More...</a><br /></td></tr>
<tr class="separator:a1873494f26c8f90c79254b43d25d47f7 inherit properties_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6b6baf8292fe2447ad0620722bc24526 inherit properties_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">virtual float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a6b6baf8292fe2447ad0620722bc24526">height</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a6b6baf8292fe2447ad0620722bc24526 inherit properties_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">The height of this entity.  <a href="class_lua_1_1_entity.html#a6b6baf8292fe2447ad0620722bc24526">More...</a><br /></td></tr>
<tr class="separator:a6b6baf8292fe2447ad0620722bc24526 inherit properties_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a133133afe701b7ca4f0e2d6632beae33 inherit properties_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">virtual float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a133133afe701b7ca4f0e2d6632beae33">maxSize</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a133133afe701b7ca4f0e2d6632beae33 inherit properties_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">The max scale for this entity.  <a href="class_lua_1_1_entity.html#a133133afe701b7ca4f0e2d6632beae33">More...</a><br /></td></tr>
<tr class="separator:a133133afe701b7ca4f0e2d6632beae33 inherit properties_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a642603e2952e4fcea70979837049f813 inherit properties_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">virtual float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a642603e2952e4fcea70979837049f813">minSize</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a642603e2952e4fcea70979837049f813 inherit properties_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">The min scale for this entity.  <a href="class_lua_1_1_entity.html#a642603e2952e4fcea70979837049f813">More...</a><br /></td></tr>
<tr class="separator:a642603e2952e4fcea70979837049f813 inherit properties_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header pub_static_methods_class_lua_1_1_entity"><td colspan="2" onclick="javascript:toggleInherit('pub_static_methods_class_lua_1_1_entity')"><img src="closed.png" alt="-"/>&#160;Static Public Member Functions inherited from <a class="el" href="class_lua_1_1_entity.html">Lua.Entity</a></td></tr>
<tr class="memitem:af3f7aae31ecb691380a6f18f053fb907 inherit pub_static_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#af3f7aae31ecb691380a6f18f053fb907">GetSelectedEntity</a> ()</td></tr>
<tr class="memdesc:af3f7aae31ecb691380a6f18f053fb907 inherit pub_static_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the currently selected entity in the Editor.  <a href="class_lua_1_1_entity.html#af3f7aae31ecb691380a6f18f053fb907">More...</a><br /></td></tr>
<tr class="separator:af3f7aae31ecb691380a6f18f053fb907 inherit pub_static_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abf1acc0c0ad6baa7224c8f3e088caf0f inherit pub_static_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">static IList&lt; string &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#abf1acc0c0ad6baa7224c8f3e088caf0f">GetPlayerModelList</a> ()</td></tr>
<tr class="memdesc:abf1acc0c0ad6baa7224c8f3e088caf0f inherit pub_static_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">A list of all available player models.  <a href="class_lua_1_1_entity.html#abf1acc0c0ad6baa7224c8f3e088caf0f">More...</a><br /></td></tr>
<tr class="separator:abf1acc0c0ad6baa7224c8f3e088caf0f inherit pub_static_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8abff3f32d1cbaa6355d4d217ab558e1 inherit pub_static_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">static IList&lt; string &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a8abff3f32d1cbaa6355d4d217ab558e1">GetGtsModelList</a> ()</td></tr>
<tr class="memdesc:a8abff3f32d1cbaa6355d4d217ab558e1 inherit pub_static_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">A list of all available giantess models.  <a href="class_lua_1_1_entity.html#a8abff3f32d1cbaa6355d4d217ab558e1">More...</a><br /></td></tr>
<tr class="separator:a8abff3f32d1cbaa6355d4d217ab558e1 inherit pub_static_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a42f84cbad068689f4308f5e4b1c7a981 inherit pub_static_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">static IList&lt; string &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a42f84cbad068689f4308f5e4b1c7a981">GetFemaleMicroList</a> ()</td></tr>
<tr class="memdesc:a42f84cbad068689f4308f5e4b1c7a981 inherit pub_static_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">A list of all available female micro models.  <a href="class_lua_1_1_entity.html#a42f84cbad068689f4308f5e4b1c7a981">More...</a><br /></td></tr>
<tr class="separator:a42f84cbad068689f4308f5e4b1c7a981 inherit pub_static_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7d49be266385d4155691a2349d964b81 inherit pub_static_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">static IList&lt; string &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a7d49be266385d4155691a2349d964b81">GetMaleMicroList</a> ()</td></tr>
<tr class="memdesc:a7d49be266385d4155691a2349d964b81 inherit pub_static_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">A list of all available male micro models.  <a href="class_lua_1_1_entity.html#a7d49be266385d4155691a2349d964b81">More...</a><br /></td></tr>
<tr class="separator:a7d49be266385d4155691a2349d964b81 inherit pub_static_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aacd0f73c8377462b354deb3bc73c6b40 inherit pub_static_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">static IList&lt; string &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#aacd0f73c8377462b354deb3bc73c6b40">GetObjectList</a> ()</td></tr>
<tr class="memdesc:aacd0f73c8377462b354deb3bc73c6b40 inherit pub_static_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">A list of all available object models.  <a href="class_lua_1_1_entity.html#aacd0f73c8377462b354deb3bc73c6b40">More...</a><br /></td></tr>
<tr class="separator:aacd0f73c8377462b354deb3bc73c6b40 inherit pub_static_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a582caed51f918e3d7642a7ec8a227fd1 inherit pub_static_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a582caed51f918e3d7642a7ec8a227fd1">SpawnGiantess</a> (string <a class="el" href="class_lua_1_1_entity.html#a8155b6c6ef0f0630ec7e818dd4cdaec4">name</a>, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> <a class="el" href="class_lua_1_1_entity.html#ad8bd97d98fddc9b89f8410512b502c3f">position</a>, <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> rotation, float <a class="el" href="class_lua_1_1_entity.html#a784673c0e6fbf29381a309a5df0ee10e">scale</a>)</td></tr>
<tr class="memdesc:a582caed51f918e3d7642a7ec8a227fd1 inherit pub_static_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Spawns a giantess from a given model name.  <a href="class_lua_1_1_entity.html#a582caed51f918e3d7642a7ec8a227fd1">More...</a><br /></td></tr>
<tr class="separator:a582caed51f918e3d7642a7ec8a227fd1 inherit pub_static_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a347a27b3fea83b83461600b3b80ce5d8 inherit pub_static_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a347a27b3fea83b83461600b3b80ce5d8">SpawnFemaleMicro</a> (string <a class="el" href="class_lua_1_1_entity.html#a8155b6c6ef0f0630ec7e818dd4cdaec4">name</a>, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> <a class="el" href="class_lua_1_1_entity.html#ad8bd97d98fddc9b89f8410512b502c3f">position</a>, <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> rotation, float <a class="el" href="class_lua_1_1_entity.html#a784673c0e6fbf29381a309a5df0ee10e">scale</a>)</td></tr>
<tr class="memdesc:a347a27b3fea83b83461600b3b80ce5d8 inherit pub_static_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Spawns a female micro from a given model name.  <a href="class_lua_1_1_entity.html#a347a27b3fea83b83461600b3b80ce5d8">More...</a><br /></td></tr>
<tr class="separator:a347a27b3fea83b83461600b3b80ce5d8 inherit pub_static_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad8f4dd9eec83d4df5d28efa120210ef6 inherit pub_static_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#ad8f4dd9eec83d4df5d28efa120210ef6">SpawnMaleMicro</a> (string <a class="el" href="class_lua_1_1_entity.html#a8155b6c6ef0f0630ec7e818dd4cdaec4">name</a>, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> <a class="el" href="class_lua_1_1_entity.html#ad8bd97d98fddc9b89f8410512b502c3f">position</a>, <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> rotation, float <a class="el" href="class_lua_1_1_entity.html#a784673c0e6fbf29381a309a5df0ee10e">scale</a>)</td></tr>
<tr class="memdesc:ad8f4dd9eec83d4df5d28efa120210ef6 inherit pub_static_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Spawns a male micro from a given model name.  <a href="class_lua_1_1_entity.html#ad8f4dd9eec83d4df5d28efa120210ef6">More...</a><br /></td></tr>
<tr class="separator:ad8f4dd9eec83d4df5d28efa120210ef6 inherit pub_static_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5bb33d1afcfac1b1452d362cb5d8bb94 inherit pub_static_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="class_lua_1_1_entity.html">Entity</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#a5bb33d1afcfac1b1452d362cb5d8bb94">SpawnObject</a> (string <a class="el" href="class_lua_1_1_entity.html#a8155b6c6ef0f0630ec7e818dd4cdaec4">name</a>, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> <a class="el" href="class_lua_1_1_entity.html#ad8bd97d98fddc9b89f8410512b502c3f">position</a>, <a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> rotation, float <a class="el" href="class_lua_1_1_entity.html#a784673c0e6fbf29381a309a5df0ee10e">scale</a>)</td></tr>
<tr class="memdesc:a5bb33d1afcfac1b1452d362cb5d8bb94 inherit pub_static_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Spawns an object from a given model name.  <a href="class_lua_1_1_entity.html#a5bb33d1afcfac1b1452d362cb5d8bb94">More...</a><br /></td></tr>
<tr class="separator:a5bb33d1afcfac1b1452d362cb5d8bb94 inherit pub_static_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac55e7536a3bdc7a6b9a3fa6a759db9ee inherit pub_static_methods_class_lua_1_1_entity"><td class="memItemLeft" align="right" valign="top">static bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_entity.html#ac55e7536a3bdc7a6b9a3fa6a759db9ee">Equals</a> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> a, <a class="el" href="class_lua_1_1_entity.html">Entity</a> b)</td></tr>
<tr class="memdesc:ac55e7536a3bdc7a6b9a3fa6a759db9ee inherit pub_static_methods_class_lua_1_1_entity"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tests 2 entities for equality.  <a href="class_lua_1_1_entity.html#ac55e7536a3bdc7a6b9a3fa6a759db9ee">More...</a><br /></td></tr>
<tr class="separator:ac55e7536a3bdc7a6b9a3fa6a759db9ee inherit pub_static_methods_class_lua_1_1_entity"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>A <a class="el" href="class_lua_1_1_player_entity.html" title="A PlayerEntity represents player-controlled character.">PlayerEntity</a> represents player-controlled character. </p>
<p>Accessible through the <code>player</code> global variable. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a9d1469da41be5408b4d6702dc04f3351"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9d1469da41be5408b4d6702dc04f3351">&#9670;&nbsp;</a></span>Crush()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.PlayerEntity.Crush </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Crushes the player. </p>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="a1137d463c4ca74abf6fcb032e5311755"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1137d463c4ca74abf6fcb032e5311755">&#9670;&nbsp;</a></span>autowalk</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.PlayerEntity.autowalk</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Is autowalk enabled? Toggled by pressing <code>Right Shift</code> by default. </p>

</div>
</div>
<a id="ae050a6ac5d73748b2e989b3b3fbfd61d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae050a6ac5d73748b2e989b3b3fbfd61d">&#9670;&nbsp;</a></span>climbing</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.PlayerEntity.climbing</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Player is climbing. </p>

</div>
</div>
<a id="a6917cc802f4f2de29b01e7c968b6336e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6917cc802f4f2de29b01e7c968b6336e">&#9670;&nbsp;</a></span>climbSpeed</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.PlayerEntity.climbSpeed</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><a class="el" href="class_lua_1_1_movement.html" title="Use this component to control the movement of agents.">Movement</a> speed when climbing (activated by pressing <code>C</code> by default). </p>

</div>
</div>
<a id="a07be3c99f61960cadd5333939bc9ee88"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a07be3c99f61960cadd5333939bc9ee88">&#9670;&nbsp;</a></span>flySpeed</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.PlayerEntity.flySpeed</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><a class="el" href="class_lua_1_1_movement.html" title="Use this component to control the movement of agents.">Movement</a> speed when flying (activated by pressing <code>E</code> by default). </p>

</div>
</div>
<a id="ae805d9b6fbe0ecb483f726fee471434f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae805d9b6fbe0ecb483f726fee471434f">&#9670;&nbsp;</a></span>isAiming</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.PlayerEntity.isAiming</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Player is aiming. </p>

</div>
</div>
<a id="aac79f44819e1adfc1b5a2f6b1bbe61e6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aac79f44819e1adfc1b5a2f6b1bbe61e6">&#9670;&nbsp;</a></span>jumpPower</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.PlayerEntity.jumpPower</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Jumping height (activated by pressing <code>Space</code> by default). </p>

</div>
</div>
<a id="a7ac787ce4c39f28cc36cb8b08c9934b7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7ac787ce4c39f28cc36cb8b08c9934b7">&#9670;&nbsp;</a></span>maxSize</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">override float Lua.PlayerEntity.maxSize</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Maximum player size. </p>

</div>
</div>
<a id="af2e34e02994fcfdb032932e9da17e49d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af2e34e02994fcfdb032932e9da17e49d">&#9670;&nbsp;</a></span>minSize</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">override float Lua.PlayerEntity.minSize</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Minimum player size. </p>

</div>
</div>
<a id="a47bfb3f0f7b06c235a1b516967d52408"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a47bfb3f0f7b06c235a1b516967d52408">&#9670;&nbsp;</a></span>raygun</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_lua_player_raygun.html">LuaPlayerRaygun</a> Lua.PlayerEntity.raygun</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Player scale. </p>

</div>
</div>
<a id="a3b78a546178fca04a5bfd04159bebe51"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3b78a546178fca04a5bfd04159bebe51">&#9670;&nbsp;</a></span>runSpeed</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.PlayerEntity.runSpeed</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><a class="el" href="class_lua_1_1_movement.html" title="Use this component to control the movement of agents.">Movement</a> speed when running. This is the default movement mode. </p>

</div>
</div>
<a id="a853ddbac1d9d5d71d593d8e3aa9ceb0c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a853ddbac1d9d5d71d593d8e3aa9ceb0c">&#9670;&nbsp;</a></span>scale</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">override float Lua.PlayerEntity.scale</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Player scale. </p>

</div>
</div>
<a id="a3406666c706b2b01b854bada1a04da5b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3406666c706b2b01b854bada1a04da5b">&#9670;&nbsp;</a></span>sizeChangeSpeed</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.PlayerEntity.sizeChangeSpeed</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Speed of size change when holding <code>SizeUp</code>/<code>SizeUp</code> keys (<code>Z</code>/<code>X</code> by default). </p>

</div>
</div>
<a id="acd890123e850bbd3c1e4e459b65ea5c1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acd890123e850bbd3c1e4e459b65ea5c1">&#9670;&nbsp;</a></span>sprintSpeed</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.PlayerEntity.sprintSpeed</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><a class="el" href="class_lua_1_1_movement.html" title="Use this component to control the movement of agents.">Movement</a> speed when sprinting (holding <code>Shift</code> by default). </p>

</div>
</div>
<a id="aee7c81573cc1de31d9f64fd13b534ae2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aee7c81573cc1de31d9f64fd13b534ae2">&#9670;&nbsp;</a></span>superFlySpeed</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.PlayerEntity.superFlySpeed</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><a class="el" href="class_lua_1_1_movement.html" title="Use this component to control the movement of agents.">Movement</a> speed when super flying (holding <code>Shift</code> by default while flying). </p>

</div>
</div>
<a id="aabbfb4d83476aaa301ee4f526dd6ecf8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aabbfb4d83476aaa301ee4f526dd6ecf8">&#9670;&nbsp;</a></span>walkSpeed</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.PlayerEntity.walkSpeed</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><a class="el" href="class_lua_1_1_movement.html" title="Use this component to control the movement of agents.">Movement</a> speed when walking (holding <code>Alt</code> by default). </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaPlayer.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_player_entity.html">PlayerEntity</a></li>
    <li class="footer">Generated by
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.15 </li>
  </ul>
</div>
</body>
</html>

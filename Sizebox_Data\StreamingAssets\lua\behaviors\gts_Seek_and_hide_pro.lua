Stroll = RegisterBehavior("StrollStomp_Seek")
Stroll.scores = {
    hostile = 100,     --[[ the higher the value the more likely to choose that action ]]
    curious = -30,
}
Stroll.data = {
	menuEntry = "Seek_and_hide_Pro",
    agent = {
        type = { "giantess" }
    },
    target = {
        type = { "player" }
    }
}
    
IDLE = "Idle 2"
WALK = "Walk" 
stompAnimModule = require "stomp_anim"

function Stroll:Start()
    self.state = "idle"
    self.currentTarget = nil
    self.cooldownDistance = self.agent.scale * 2.0 
    self.stop = false -- A stop variable to end the behavior.. this is custom for this script
	self.agent.senses.baseVisibilityDistance = 30000
	self.agent.senses.fieldOfView = 180
    --self.target = self.agent.FindRandomBuilding(self.agent) --  check if theres a building to walk into
    if not self.target then
		self.stop = true    --  there are no more buildings near the gts, stop the script
		log("No building was found for the stroll script")
		return -- return early, don't bother making a seed since we're not going to run anyway
	end
	Random.InitState(Mathf.Round(Time.timeSinceLevelLoad)*self.agent.id) -- get a somewhat random seed for the animations
end

function Stroll:Update()
    local targetVisible = self.agent.senses.CanSee(self.target)
                          and not self.target.IsDead()

    local distanceToTarget = self.agent.DistanceTo(self.target)

    if targetVisible then
        if self.lastStompedTarget == self.target then
            if distanceToTarget < self.cooldownDistance then
                if self.state ~= "movingToBuilding" and not self.agent.ai.IsActionActive() then
                    local randomBuilding = self.agent.FindRandomBuilding(self.agent)
                    if randomBuilding then
                        self.agent.animation.Set(WALK)
                        self.agent.MoveTo(randomBuilding)
                        self.currentTarget = randomBuilding
                        self.state = "movingToBuilding"
                    else
                        self.agent.ai.StopBehavior()
                        self.state = "idle"
                    end
                end
                return
            else
                self.lastStompedTarget = nil 
            end
        end

        if distanceToTarget <= (self.agent.scale * 0.5) then
            if self.state ~= "stompingTarget" and not self.agent.ai.IsActionActive() then
                self.agent.ai.StopAction()
                self.agent.animation.Set(IDLE)
                self.agent.LookAt(self.target)
                self.agent.Stomp(self.target)
                self.state = "stompingTarget"
                self.lastStompedTarget = self.target
            end
            return
        else
            if self.state ~= "movingToTarget" and not self.agent.ai.IsActionActive() then
                self.agent.animation.Set(WALK)
                self.agent.MoveTo(self.target)
                self.state = "movingToTarget"
            end
            return
        end
    else
        if self.state ~= "movingToBuilding" and not self.agent.ai.IsActionActive() then
            local randomBuilding = self.agent.FindRandomBuilding(self.agent)
            if randomBuilding then
                self.agent.animation.Set(WALK)
                self.agent.MoveTo(randomBuilding)
                self.currentTarget = randomBuilding
                self.state = "movingToBuilding"
            else
                self.agent.ai.StopBehavior()
                self.state = "idle"
            end
            return
        end

        if self.state == "movingToBuilding" and not self.agent.ai.IsActionActive() then
            if self.currentTarget then
                self.agent.animation.Set(IDLE)
                self.agent.Stomp(self.currentTarget)
            end
            self.state = "idle"
            self.currentTarget = nil
            return
        end
    end
end

function Stroll:Exit()
    self.agent.animation.Set(IDLE)
    self.state = "idle"
    self.currentTarget = nil
    self.lastStompedTarget = nil
end

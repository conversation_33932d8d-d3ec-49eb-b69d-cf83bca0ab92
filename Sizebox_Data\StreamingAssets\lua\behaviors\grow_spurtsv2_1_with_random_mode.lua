Spurt = RegisterBehavior("Spurt 2.1")
Spurt.data = {
    menuEntry = "Size/Spurts 2.1",
    secondary = true,
    flags = { "grow" },
    agent = {
        type = { "humanoid" }
    },
    target = {
        type = { "oneself" }
    }
}

function round(num, numDecimalPlaces) -- Function for rounding some printed numbers at 2 decimals
  return tonumber(string.format("%." .. (numDecimalPlaces or 0) .. "f", num))
end

function Spurt:Start() -- Starting values
	
	-- Declaring arrays --
	sFx = {"Rumblev1.wav", "stretchmix.ogg" , "stretchmixbig.ogg", "customboing.wav", "customboingbig.wav"} -- Sounds used (found in ...Sizebox/Sounds)
	modes = {"Grow", "Grow Spurts", "Single Spurt", "Spurt On-Demand", "Random Spurts"}
	self.m = 1

	Log("Debolte's Spurt Script 2.1 started!")
	Log(" - Press 'B' to change Growing Modes")
	Log(" - Press 'T' to start or pause the script")
	Log(" - Press '7, 8, 9, 0, - or =' to change growth parameters (idle time, spurt length and growth rate)")
	Log(" - Press ';, H, J, K or L' to change sound effects (defaults to 'G')")
	Log(" - Press 'M' to mute sound effects")
	Log(" - Press 'N' to toggle shrinking or growing")
	Log(" - Press 'Y' to compare your size with a giantess")
	Log(" - Hold 'U' and press '6' to raise the maximum size limit; '5' to lower it")
	Log("Use the 'Stop Spurts 2.1' command under 'Size...' to completely end this script. Enjoy.")
	Log("Growing Mode: " .. modes[self.m])

	-- Starting values for the booleans --
	
    self.growing = false			-- Whether main growth is ongoing
	self.playing = false			-- Whether a sound is playing
	self.paused = true				-- Whether the script is paused
	self.boing = false 			-- Whether it is one of the 2 spurt sound effects
	self.muted = false 			-- Sound muting
	self.shM = false 				-- Shrink Mode
	self.maxed = false				-- Whether Giantess has reached maximum size allowed
	self.minned = false			-- Whether Giantess has reached minimum size allowed
	self.confusedConsole = false	-- Silly Log boolean for the size info keypress
	self.escaped = false			-- Used to manage the escape menu block
	self.escaped2 = false			-- ''
	self.terminus = false
	self.vol0 = nil	 			-- Prevents going back to fade-in block, because of fluctuating volumes when scaling a giantess
	self.vol0paused = true			-- Same as above, only as an additionnal layer just for the PAUSED block
	
	--v Used in mode 4 v--
	self.m4minned = false
	self.m4minnedEnd = false

	-- Default values --
	self.growStart = Time.time -- Declares a value to measure time
	self.rate = nil	 		-- Main full rate value (reduntant)
	self.length = nil			-- Main length value (redundant)
	self.cRate = 0				-- Changing rate
	self.mRate = 0				-- Rate modifier
	self.sRate = nil			-- Main Rate Smoothifier™ value
	self.rRate = nil			-- rate used in m4minned rebound
	self.rmRate = nil			-- mRate used in m4minned rebound
	self.rate123 = 0.08		-- Declares default speed of growth
	self.length123 = 1.5		-- Declares default length of growth in seconds
	self.sRate123 = 1130
	self.respite = 3			-- Declares default time between spurts in seconds (only used in mode 2)
	
	self.rate = self.rate123		-- Ensures that the main rate value starts with the starting mode value
	self.length = self.length123	-- Ensures that the main length value starts with the starting mode value
	self.sRate = self.sRate123		-- Ensures that the main Rate Smoothifier™ value starts with the starting mode value
	self.maxMult = 1			-- Starting value to print maxSize
	self.mute = 0				-- Makes acoustics inaudible to the human ear			
	
	--v  Used in mode 4  v--
	self.sRate4 = 490	-- Mode 4 starting Smoothifier™ value
	self.rate4 = 1.4	-- Mode 4 Full rate
	self.length4 = 0.7	-- Mode 4 length
	
	self.scalee = (self.agent.scale * 1000) 	-- Sets a variable to use for sound distance effects
    self.audio_source = AudioSource:new(self.agent.bones.spine) -- To create an audiosource, you have to pass a transform or entity as argument
    self.audio_source.spatialBlend = 1 	-- 0 makes it 2D (for example for background music), and 1 makes it 3d (the source of the sounds is in the game space)
										-- Current restriction is one audiosource per bone. If the bone already has one AudioSource, that is the one that will return.
	
	--v Configuration for Random Growth v--
	self.respiteMin = 5 -- *0.1 in seconds
	self.respiteMax = 70 -- *0.1 in seconds
	self.lengthMin = 5 -- *0.1 in seconds
	self.lengthMax = 40 -- *0.1 in seconds
	self.rateMin = 1 -- *0.01 
	self.rateMax = 15 -- *0.01 

	-- Pre-loads other sounds --
	self.audio_source.clip = sFx[5]
	self.audio_source:Play()
	self.audio_source.clip = sFx[4]
	self.audio_source:Play()
	self.audio_source.clip = sFx[3]
	self.audio_source:Play()
	self.audio_source.clip = sFx[2]
	self.audio_source:Play()

    self.audio_source.clip = sFx[1] -- Declares default audio source
    self.audio_source.pitch = ((0.8 * (1 / math.sqrt((self.scalee / 125) + 1))) + 0.8)	-- Adjusts the pitch in relation to the size of the giantess
    self.audio_source.volume = (1.7 - self.audio_source.pitch)					 		-- Defines volume with the resulting pitch value
	self.vol = self.audio_source.volume -- States a given base volume used in the fade-in block (starting volumes vary, depending on giantess' spawning size)
    self.audio_source.minDistance = (0.01 * (self.scalee * 0.25))						-- Sets a minimum rolloff distance based on the size of the giantess
    self.audio_source.loop = true														-- Sets the audio clip to play in an infinite loop
end

function Spurt:Update() -- Loops indefinitely

	-- If minimum or maximum sizes reached --
if self.agent.scale == gts.minSize and not (self.minned or self.m4minned) then -- Minimum size
	if self.m == 4 and self.cRate ~= 0 then -- If mode 4 and actually growing, start rebound
		self.m4minned = true
	else									-- Otherwise, stop the engine now
		self.paused = true
		self.minned = true
	end
elseif self.agent.scale == gts.maxSize and not (self.maxed or self.m4minned) then -- Maximum size
	if self.m == 4 and self.cRate ~= 0 then -- If mode 4 and actually growing, start rebound
		self.m4minned = true
	else									-- Otherwise, stop the engine now
		self.paused = true
		self.maxed = true
	end
elseif self.agent.scale > gts.minSize and self.minned then	-- If growing out of minimum size
	self.minned = false
elseif self.agent.scale < gts.maxSize and self.maxed then	-- or vice-versa
	self.maxed = false
end




-- vv  Stuff happens in here  vv --
if not self.paused then -- If the script isn't paused
 if self.growing or (self.m4minned and not self.boing) then -- If growth enabled
  if (self.minned and not self.shM) or (self.maxed and self.shM) or (not self.minned and not self.maxed) or self.m4minned then -- If no size limit has been reached, or is possible to return, or bouncing from size limit

	--(2)-- Stop growth
	if (Time.time >= self.growStart + self.length * self.rate / (self.rate * 0.8) and self.m ~= 1) or self.m4minned then	-- As soon as growth timer finishes OR size limit reached (and not in Grow mode)
	
		-- Fade-out looping sounds a little before deceleration finishes --
		if self.cRate < (self.rate * (27 / (((self.sRate - 10) / 40) + 15))) and not self.vol0 then -- Start fading out after around 50% of deceleration is done
			if self.playing and not (self.boing or self.vol0) then
				if self.audio_source.volume > 0 then -- Lower volume gradually as long as not 0
				  if self.m4minned or self.length < 0.3 then
					self.audio_source.volume = self.audio_source.volume - (self.vol / 12) -- Fade-out 
				  elseif self.m == 4 then
					self.audio_source.volume = self.audio_source.volume - (self.vol / 18) -- Fade-out
				  else
					self.audio_source.volume = self.audio_source.volume - (self.vol / 25) -- Fade-out
				  end
				else -- Pause current sound
					self.vol0 = true
					self.audio_source:Pause()
				end
			end
		end

		if self.cRate > 0.0001 and not self.m4minned then -- DECELERATION [1] -- If size limit reached, no deceleration
			self.cRate = self.cRate - self.mRate
			if self.m == 4 then
				if self.mRate > 0.05 then
					self.mRate = self.mRate - (self.rate / self.sRate)
				end
			else
				if self.length < 0.3 and m == 2 then
					self.mRate = self.mRate - 0.05
				elseif self.mRate > (self.rate / self.sRate * 5) then
					self.mRate = self.mRate - (self.rate / self.sRate)
				end
			end
			if self.shM then
				self.agent.scale = self.agent.scale * (1 + -self.cRate * Time.deltaTime)
			else
				self.agent.scale = self.agent.scale * (1 + self.cRate * Time.deltaTime)
			end
			
		else -- Deceleration done [1] -- When growth has completely stopped 
			if not self.terminus then -- Run once
				self.rRate = self.cRate -- Store changing rate into rebound rate in case of being in mode 4
				self.cRate = 0			-- Reset changing rate otherwise
				self.mRate = 0
				
				if self.m4minned then 		-- If going to rebound (size limit reached), make everything much faster
					self.cRate = self.rRate -- Restore changing rate from rebound rate for proper use
					self.mRate = self.cRate / 5
					self.rmRate = self.mRate / 20
					self.growing = false
					self.terminus = true
				
				else
				  if self.m == 3 then	-- [m3 END] If in Single Spurt mode,
					self.paused = true	-- 		 pause script as soon as spurt ends
					self.terminus = true
					self.vol0paused = true
					Log("Paused")
					
				  elseif self.m == 4 then	-- If in mode4, prepare for part [2]
						self.growing = false
						self.growStart = Time.time + ((self.length / 15) * (self.rate / (self.rate * 0.8))) -- Re-state growStart variable to prepare shrink length
						self.terminus = true
			
				  else	-- If in mode2
					if self.vol0 or self.boing then	-- Make sure fade-out has ended
						self.growStart = Time.time + self.respite	-- Re-state growStart variable to allow idle time (respite)
						self.growing = false
						self.terminus = true
					end --if faded-out or boing sounds
				  end --it in mode 4
				end --if m4minned
			end -- terminus
		end --if decelerating
		
		
		
	else --(1)-- IT STARTS HERE (Before time has passed)
	  if not self.escaped then -- If not in Pause Menu (INEFFECTIVE ATM)
	  
			-- Start growth -- [loops]
		if self.cRate < self.rate then -- ACCELERATION [1]
		
			if self.length < 0.3 and m == 2 then -- If lenght very small, make ease-in faster for mode 2
				self.mRate = self.mRate + 0.05
			else
				self.mRate = self.mRate + (self.rate / self.sRate)	-- Raises a value (mRate) every frame, that adds up to the changing rate (cRate),
			end														-- until it reaches the full rate (rate)
			
			self.cRate = self.cRate + self.mRate					-- Every frame something like this happens: (A + 1; B + A) ---> (A + 1 = 2; B + A = 3) ---> (3,6) ---> (4,10) ---> etc.
																	-- And then B (cRate) is used as a multiplier to the gts's current size.
		end															-- It thus raises exponentially until it reaches "rate", where it now grows steadily
		if self.shM then -- If shrink mode is enabled
			self.agent.scale = self.agent.scale * (1 + -self.cRate * Time.deltaTime)	-- Every frame, the gts's current size is multiplied by a small amount (negative (shrinking))
		else
			self.agent.scale = self.agent.scale * (1 + self.cRate * Time.deltaTime)		-- Every frame, the gts's current size is multiplied by a small amount (positive (growing))
		end


			-- Start sound -- [doesn't loop]
		if not self.playing then -- Prevents starting the clip continuously as this is in an Update loop
			self.playing = true  -- ''
			self.audio_source.volume = 0 -- Ensures the sound starts in a fresh state for fading-in
			self.audio_source:Play() -- Start the audio clip
		end

		-- Sound modifiers --
		if not self.boing then	-- If sounds used are rumble or stretching
		
			-- Fade-in loop --
			if self.audio_source.volume < self.vol and self.vol0 then -- If volume is below desired base volume
				self.audio_source.volume = self.audio_source.volume + (self.vol / (self.sRate / 45.2))	-- Fades-in volume according to the smoothifier rate.
																										-- It ensures an desired frame duration: "vol" being the base volume,
																										-- it is divided by a number, which turns out to be the number
			-- Sound effects loop -- [rumble]															-- of frames it'll take to fade-in. "45.2" is just a convenient
			else -- When desired base volume is reached													-- value for the general duration I needed.
				self.scalee = (self.agent.scale * 900)
				self.audio_source.minDistance = (0.01 * (self.scalee * 0.25))
				self.audio_source.pitch = ((0.8 * (1 / math.sqrt((self.scalee / 125) + 1))) + 0.8)
				self.audio_source.volume = (1.7 - self.audio_source.pitch) - self.mute
				self.vol = self.audio_source.volume
				self.vol0 = false -- Prevents from looping back to previous 'if'
			end
		
		-- Sound effect loop -- [boing]
		else -- If sounds used are bouncy sounds
			self.scalee = (self.agent.scale * 1000000)	 										-- The method used above raises the pitch of sounds if the giantess
			self.audio_source.minDistance = (0.01 * (self.scalee * 0.25))						-- is too small. My 'boing' sounds are not meant to have their pitch
			self.audio_source.pitch = ((0.7 * (1 / math.sqrt((self.scalee / 50) + 1))) + 0.9)	-- raised. The small edits I made here mostly fix the issues.
			self.audio_source.volume = (1.3 - self.audio_source.pitch) - self.mute
			self.vol = self.audio_source.volume
		end
	  end
	end
  end
 end
 
 
 --(3)-- PART [2] - If in mode 2 or 4
 if not self.growing then -- When part [1] (initial spurt) done
	if self.m == 4 then -- If in mode 4 and size limit reached, start a very fast bounce animation, if just mode 4: start a short rubber-band effect
		
		--(4)-- PART [2] Timer - Sets a limit to how far back the rebound goes
		if Time.time >= self.growStart and not self.m4minned or self.m4minnedEnd then -- If Rebound 1 timer done and no size limit reached, OR bounced back from size limit

			--(5)-- rebound 1 finished
			if self.m4minned then -- Reached size limit, bounced back and timer over

				--(6b)-- (minned)
				if Time.time >= self.growStart then -- If rebound 2 timer ended and at size limit

					--(8)-- Rebound 2 DECELERATION [3] - [m4 END](minned)
					if self.cRate > 0.0001 then -- as long as deceleration hasn't ended
						self.cRate = self.cRate - 3.2 * self.mRate
						self.mRate = self.mRate - self.rmRate
						
						if self.shM then
							self.agent.scale = self.agent.scale * (1 + -self.cRate * Time.deltaTime)
						else
							self.agent.scale = self.agent.scale * (1 + self.cRate * Time.deltaTime)
						end

					else -- Deceleration done [3] - When growth has completely stopped
						self.cRate = 0			-- Reset a bunch of values
						self.mRate = 0
						self.rRate = 0
						self.rmRate = 0
						self.m4minned = false
						self.m4minnedEnd = false
						self.paused = true		-- Pause script
						
						if not self.shM then	-- If was growing, lock commands accordingly
							self.maxed = true
						elseif self.shM then	-- If was shrinking, lock commands accordingly
							self.minned = true
						end
						
						if self.minned then 
							Log("Minimum size reached!")
						elseif self.maxed then
							Log("Maximum size reached!")
						end

					end
					
				else --(7)-- Rebound 2 ACCELERATION [3] -- Going on for one last rebound
					if self.cRate < self.rRate then
						self.mRate = self.mRate + self.rmRate
						self.cRate = self.cRate + 3 * self.mRate
					end
					
					if self.shM then
						self.agent.scale = self.agent.scale * (1 + -self.cRate * Time.deltaTime)
					else
						self.agent.scale = self.agent.scale * (1 + self.cRate * Time.deltaTime)
					end

				end
			
			-- If no size limit reached
			else --(6a)-- Normal Rebound DECELERATION [2a] - [m4 END]
				if self.cRate > 0.0001 then
					self.cRate = self.cRate - 0.04
					if self.shM then
						self.agent.scale = self.agent.scale * (1 + self.cRate * Time.deltaTime)
					else
						self.agent.scale = self.agent.scale * (1 + -self.cRate * Time.deltaTime)
					end
					
				else -- Deceleration done [2a] - [END] When growth has completely stopped
					self.cRate = 0
					self.mRate = 0
					self.paused = true -- Ready to spurt again
				end
			end
		
		
		-- PART [2] STARTS HERE --
		else -- Rebound - Before mode 4 timer ends
			
			--(4b)-- Goes to (6b)
			if self.m4minned then -- Rebound 1 DECELERATION [2b] - No acceleration because boucing back from limit; Keeps reverse momentum
				if self.cRate > 0.0001 then
					self.cRate = self.cRate - self.mRate
					self.mRate = self.mRate - self.rmRate

					if self.shM then
						self.agent.scale = self.agent.scale * (1 + self.cRate * Time.deltaTime)
					else
						self.agent.scale = self.agent.scale * (1 + -self.cRate * Time.deltaTime)
					end

				else -- Deceleration done [2b] - When 1st rebound has completely stopped, prepare for part [3]
					self.cRate = 0						-- Fresh values for the last go
					self.mRate = 0						-- ''
					self.growStart = Time.time + (0.06)	-- Re-state growStart variable to prepare 2nd rebound length
					self.m4minnedEnd = true 			-- Allows to reach --(5)--
					--
				end
				
			--(4a)-- Goes straight up to (6a)
			else -- Normal Rebound ACCELERATION [2a]
				if self.cRate < (self.rate / (self.length * 8)) then -- Very brief acceleration
					self.mRate = self.mRate + (14 * self.length * (self.rate / self.sRate))
					self.cRate = self.cRate + self.mRate
				end
				
				if self.shM then	-- After accel done, grows steadily until mode 4 timer ends (very quick)
					self.agent.scale = self.agent.scale * (1 + self.cRate * Time.deltaTime)
				else
					self.agent.scale = self.agent.scale * (1 + -self.cRate * Time.deltaTime)
				end
			end
		end
		
	else -- If in mode 2 [m2 END](Auto-restart)
		if Time.time >= self.growStart then	-- As soon as [respite] has passed
				self.playing = false		-- Reset some values
				self.terminus = false		-- ''
				if self.m == 5 then
					self.respite = math.random(self.respiteMin, self.respiteMax) * 0.1
					self.length = math.random(self.lengthMin, self.lengthMax) * 0.1
					self.rate = math.random(self.rateMin, self.rateMax) * 0.01
				end
				self.growing = true			-- Start a new Auto-Spurt
		end
	end
 end



else -- [4] If PAUSED, fade-out and pause looping sounds -- (loops here by default)
	
	-- Fade-out loop --
	if self.cRate < (self.rate * (27 / (((self.sRate - 10) / 40) + 15))) and not self.vol0paused then -- Only start if deceleration is around 50% done
		if self.playing and not self.boing then
			if self.audio_source.volume > 0 then -- Allow a small window of time to let the fade-out happen before cutting the sound
				if self.vol0 or self.m == 4 then
					self.audio_source.volume = self.audio_source.volume - (self.vol / 12) -- Quicker fade-out if in mode 4 or if couldn't complete fade-in
				else
					self.audio_source.volume = self.audio_source.volume - (self.vol / 25) -- Quick fade-out to prevent audio clipping
				end
			else
				self.vol0 = true
				self.audio_source:Pause()
				self.vol0paused = true	-- Prevents looping this block
			end
		elseif self.boing then
			self.vol0paused = true		-- ''
		end
	end

	if self.cRate > 0.0001 and not self.m4minned then -- DECELERATION [1] -- If bumped on min or max size, no deceleration
		
		self.cRate = self.cRate - self.mRate
		if self.mRate > (self.rate / self.sRate * 5) then
			self.mRate = self.mRate - (self.rate / self.sRate)
		end
		
		if self.shM then
			self.agent.scale = self.agent.scale * (1 + -self.cRate * Time.deltaTime)
		else
			self.agent.scale = self.agent.scale * (1 + self.cRate * Time.deltaTime)
		end
	
	else -- Deceleration over: Paused/Idle [m1 END](otherwise END everytime "t" pauses)
		if not self.terminus then
			if self.minned then
				Log("Minimum size reached!")
			elseif self.maxed then
				Log("Maximum size reached!")
			end
			self.audio_source.volume = 0
			self.cRate = 0
			self.mRate = 0
			self.terminus = true
		end
	end
end




  if self.agent.GetSelectedEntity() == self.agent then
	-- Controls:
		-- Growth control --
	if Input.GetKey("=") then -----------------------------> Press and hold "-" for faster growth/shrink rate;
		if self.rate < 4 then
			self.rate = self.rate + 0.02
		else
			self.rate = self.rate + 0.05
		end
		if self.shM then
			Log("Shrink Rate: " .. self.rate)
		else
			Log("Growth Rate: " .. self.rate)
		end
	elseif Input.GetKey("-") and self.rate > 0.03 then -------> Press and hold "-" for slower growth/shrink rate; Verifies that shrink rate will not hit 0 and above (growth)
		if self.rate < 4.02 then
			self.rate = self.rate - 0.02
		else
			self.rate = self.rate - 0.05
		end
		if self.shM then
			Log("Shrink Rate: " .. self.rate)
		else
			Log("Growth Rate: " .. self.rate)
		end
	elseif Input.GetKeyDown("0") then -----------------------------> Press "0" for longer spurts; Increments by 1/10th of a second every keypress (1/20th below 2 seconds)
		if self.length < 2 then 
			self.length = self.length + 0.05
		elseif self.length >= 2 and self.length < 5 then
			self.length = self.length + 0.2
		else
			self.length = self.length + 0.5
		end
		self.sRate = self.sRate + 40
		Log("Spurt Length: " .. self.length .. "s")
	elseif Input.GetKeyDown("9") and self.length > 0.15 then ------------> Press "9" for shorter spurts; Decreases by 1/10th of a second every keypress (1/20th below 2 seconds) 
		if self.length < 2.05 then
			self.length = self.length - 0.05
		elseif self.length >= 2.05 and self.length < 5.1 then
			self.length = self.length - 0.2
		else
			self.length = self.length - 0.5
		end
		self.sRate = self.sRate - 40
		Log("Spurt Length: " .. self.length .. "s")
	elseif Input.GetKeyDown("8") then -----------------------------> Press "8" for longer wait between spurts; Increments by 1/10th of a second every keypress
		if self.respite < 1.95 then 
			self.respite = self.respite + 0.05
		elseif self.respite >= 1.95 and self.respite < 5 then
			self.respite = self.respite + 0.2
		else
			self.respite = self.respite + 0.5
		end
		Log("Wait Time: " .. self.respite .. "s")
	elseif Input.GetKeyDown("7") and self.respite > 0 then -----------> Press "7" for shorter respite between spurts; Decreases by 1/10th of a second every keypress
		if self.respite < 2.05 then 
			self.respite = self.respite - 0.05
		elseif self.respite >= 2.05 and self.respite < 5.1 then
			self.respite = self.respite - 0.2
		else
			self.respite = self.respite - 0.5
		end
		if self.respite < 0 then
			self.respite = 0
		end
		Log("Wait Time: " .. self.respite .. "s")
	end

		-- Pause script Toggle --
	if Input.GetKeyDown("t") and not self.escaped then -------------> Pause script
		if (self.minned and self.shM) then -- If max or min size reached
			Log("This is as small as it gets!")
		elseif (self.maxed and not self.shM) then
			Log("This is as big as it gets!")
		else
			self.paused = not self.paused -- Toggles On/Off; defaults On
		end
		if self.paused then
			if self.m == 4 and not ((self.minned and self.shM) or (self.maxed and not self.shM)) then -- If max or min size NOT reached and in mode 4
				Log("Paused")
			elseif not ((self.minned and self.shM) or (self.maxed and not self.shM)) then -- If max or min size NOT reached
				Log("Paused")
			end
		else -- UNPAUSE: Reset some values to defaults
			if self.m == 4 then -- When triggering a spurt in mode4
				Log("At work~")
			else
				Log("At work~")
			end
			self.growing = true
			self.playing = false
			self.vol0 = true
			self.vol0paused = false	
			self.terminus = false
			self.growStart = Time.time
		end
	end
	
		-- Size comparison hotkey --
	if Input.GetKeyDown("y") then -- Press "y" to have a little info on your size and the selected Giantess'
		if round(((self.agent.scale * 1000) / player.scale), 2) == 1 then
			if self.confusedConsole then
				Log("You are both the same size...")
			else
				Log("This Giantess is... as small as, err, you're as big as-- uh... You're the same size!")
				self.confusedConsole =  true
			end
		else
			Log("This Giantess is... " .. round(((self.agent.scale * 1000) / player.scale), 2) .. " times your size...")
		end
	end
	
		-- Maximum Giantess Size control --
	if Input.GetKey("u") then -------------------------------------> Press and hold "u" to enable changing giantess maximum size allowed
																  -- This is because this isn't a value you want to change accidentally
		if Input.GetKeyDown("6") then -----------------------------> Press "6" to double maximum allowed giantess size 
			gts.maxSize = (gts.maxSize * 2)
			self.maxMult = (self.maxMult * 2)
			if self.maxMult >= 8 then
				Log("Maximum Size: " .. self.maxMult .. " times normal(!)")
				Log("WARNING: She might become too big to fit in Sizebox...")
			else
				Log("Maximum Size: " .. self.maxMult .. " times normal")
			end
		elseif Input.GetKeyDown("5") and self.maxMult > 0.00006103515625 then --------> Press "5" decrease maximum allowed giantess size by half
			if self.agent.scale <= (gts.maxSize / 2) then
				gts.maxSize = (gts.maxSize / 2)
				self.maxMult = (self.maxMult / 2)
				if self.maxMult >= 8 then
					Log("Maximum Size: " .. self.maxMult .. " times normal(!)")
					Log("WARNING: She might STILL become too big to fit in Sizebox...")
				else
					Log("Maximum Size: " .. self.maxMult .. " times normal")
				end
			else
				Log("She's stuck in the wires! I can't lower the ceiling if she's already through it!")
			end
		elseif Input.GetKeyDown("5") and self.maxMult == 0.00006103515625 then
			Log("That's as little gigantic as she'll get!")
		end
	end

		-- Growth Mode Switcher --
	if Input.GetKeyDown("b") then ----------------> Press "b" to switch between the 4 growth modes:
		self.m = self.m + 1										-- Grow: The basic and straightforward continuous growth
		if self.m < 6 then									-- Auto Spurts: Automated, delayed growth spurts that never end
			Log("Growing Mode: " .. modes[self.m])									-- Single Spurt: A single spurt; press "t" to grow again (or stop it, if it hasn't ended)
			if self.m == 4 then								-- Spurt On-Demand: More tonic variant of the previous mode with a slight animation effect;
				self.rate123 = self.rate
				self.length123 = self.length
				self.sRate123 = self.sRate
				self.rate = self.rate4
				self.length = self.length4
				self.sRate = self.sRate4
				self.paused = true
			end
		else
			self.m = 1
			self.rate4 = self.rate
			self.length4 = self.length
			self.sRate4 = self.sRate
			self.rate = self.rate123
			self.length = self.length123
			self.sRate = self.sRate123
			self.paused = true
			Log("Growing Mode: " .. modes[self.m])
		end
	end
	
		-- Shrink mode toggle --
	if Input.GetKeyDown("n") then ---------------> Press "n" to toggle whether the script shrinks or grows its agent.
		self.shM = not self.shM
		if self.shM then
			Log("Shrinking!")
		else
			Log("Growing!")
		end
	end

	
--[[ DISABLED UNTIL ABLE TO ACCESS PAUSE MENU STATE, OR RESUME BUTTON TRIGGER, BECAUSE POSSIBLE TO LEAVE MENU WITHOUT 'ESCAPE'
	 (Should request pause menu state access)

		-- Pause ANY sounds if pausing game --
	if Input.GetKeyDown("escape") then
		self.escaped = not self.escaped -- Toggle between ESCAPE menu on/off states
	end

	-- GAME PAUSED: Fade-out and pause sounds
	if self.escaped and self.playing then
		if  self.audio_source.volume > 0.001 then
			self.audio_source.volume = self.audio_source.volume * 0.7
		else
			self.vol0 = true -- Pause sounds after fading-out
			self.escaped2 = true -- Additionnal condition: Prevents out-of-menu state triggering 'play sound'; requires being in-menu 1st
		end

	-- GAME RESUMED: Resume sounds playing and fade-in
	elseif not self.escaped and self.escaped2 then -- If playing game, BUT exitting menu
		self.vol0 = false
		self.audio_source:Play()
		self.playing = true
		self.escaped2 = false
	end
	
]]



		-- To change sounds --
	if Input.GetKeyDown(";") then     ---------------> Press "g" for Peculiar Musician's default Rumble sound effect
		self.audio_source.clip = sFx[1]
		self.audio_source.loop = true
		self.boing = false
		if self.playing and not self.paused then
			self.audio_source:Play()
		end
		Log("Sound: Loud Rumble")
	elseif Input.GetKeyDown("h") then ---------------> Press "h" for a balloon stretching sound effect
		self.audio_source.clip = sFx[2]
		self.audio_source.loop = true
		self.boing = false
		if self.playing and not self.paused then
			self.audio_source:Play()
		end
		Log("Sound: Stretch Mix")
	elseif Input.GetKeyDown("j") then ---------------> Press "j" for the previous sound in a more echo-y variant
		self.audio_source.clip = sFx[3]
		self.audio_source.loop = true
		self.boing = false
		if self.playing and not self.paused then
			self.audio_source:Play()
		end
		Log("Sound: Stretch Mix (Big)")
	elseif Input.GetKeyDown("k") then ---------------> Press "k" for a bouncy spurt-like sound
		self.audio_source.clip = sFx[4]
		self.audio_source.loop = false
		self.boing = true
		Log("Sound: Boing Spurt")
	elseif Input.GetKeyDown("l") then ---------------> Press "l" for a louder, more intense version of the previous sound (intended for very fast and long spurts)
		self.audio_source.clip = sFx[5]
		self.audio_source.loop = false
		self.boing = true
		Log("Sound: Boing Spurt (Big)")
	
		-- Mute toggle --
	elseif Input.GetKeyDown("m") then ---------------> Press "m" to mute growth sounds, if prefered.
		self.muted = not self.muted -- Toggles On/Off; defaults Off
		if self.muted then
			self.mute = 2	  -- Value to substract to current sound volume (will never go above 1.1, so always results in '0')
			Log("Sounds muted")
		else
			self.mute = 0	  -- Means nothing will be substracted from volume value, leaving it the same
			Log("Sounds unmuted")
		end
	end
  end
end
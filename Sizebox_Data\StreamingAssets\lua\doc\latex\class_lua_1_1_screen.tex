\hypertarget{class_lua_1_1_screen}{}\section{Lua.\+Screen Class Reference}
\label{class_lua_1_1_screen}\index{Lua.Screen@{Lua.Screen}}


Access to display information.  


\subsection*{Properties}
\begin{DoxyCompactItemize}
\item 
static bool \mbox{\hyperlink{class_lua_1_1_screen_a7b08588981d36493e586b0d59a1f1b7a}{full\+Screen}}\hspace{0.3cm}{\ttfamily  \mbox{[}get\mbox{]}}
\begin{DoxyCompactList}\small\item\em Is the game running fullscreen? \end{DoxyCompactList}\item 
static int \mbox{\hyperlink{class_lua_1_1_screen_a7e3459d0ccc2641709d1bad599092fdc}{height}}\hspace{0.3cm}{\ttfamily  \mbox{[}get\mbox{]}}
\begin{DoxyCompactList}\small\item\em The current height of the screen window in pixels (Read Only). \end{DoxyCompactList}\item 
static int \mbox{\hyperlink{class_lua_1_1_screen_ae44386bf8759e8f85e04358297f3dd95}{width}}\hspace{0.3cm}{\ttfamily  \mbox{[}get\mbox{]}}
\begin{DoxyCompactList}\small\item\em The current width of the screen window in pixels (Read Only). \end{DoxyCompactList}\end{DoxyCompactItemize}


\subsection{Detailed Description}
Access to display information. 

\mbox{\hyperlink{class_lua_1_1_screen}{Screen}} class can be used to get the list of supported resolutions, switch the current resolution, hide or show the system mouse pointer. 

\subsection{Property Documentation}
\mbox{\Hypertarget{class_lua_1_1_screen_a7b08588981d36493e586b0d59a1f1b7a}\label{class_lua_1_1_screen_a7b08588981d36493e586b0d59a1f1b7a}} 
\index{Lua.Screen@{Lua.Screen}!fullScreen@{fullScreen}}
\index{fullScreen@{fullScreen}!Lua.Screen@{Lua.Screen}}
\subsubsection{\texorpdfstring{fullScreen}{fullScreen}}
{\footnotesize\ttfamily bool Lua.\+Screen.\+full\+Screen\hspace{0.3cm}{\ttfamily [static]}, {\ttfamily [get]}}



Is the game running fullscreen? 

It is possible to toggle fullscreen mode by changing this property\+: \begin{DoxyReturn}{Returns}

\end{DoxyReturn}
\mbox{\Hypertarget{class_lua_1_1_screen_a7e3459d0ccc2641709d1bad599092fdc}\label{class_lua_1_1_screen_a7e3459d0ccc2641709d1bad599092fdc}} 
\index{Lua.Screen@{Lua.Screen}!height@{height}}
\index{height@{height}!Lua.Screen@{Lua.Screen}}
\subsubsection{\texorpdfstring{height}{height}}
{\footnotesize\ttfamily int Lua.\+Screen.\+height\hspace{0.3cm}{\ttfamily [static]}, {\ttfamily [get]}}



The current height of the screen window in pixels (Read Only). 

This is the actual height of the player window (in fullscreen it is also the current resolution). \begin{DoxyReturn}{Returns}

\end{DoxyReturn}
\mbox{\Hypertarget{class_lua_1_1_screen_ae44386bf8759e8f85e04358297f3dd95}\label{class_lua_1_1_screen_ae44386bf8759e8f85e04358297f3dd95}} 
\index{Lua.Screen@{Lua.Screen}!width@{width}}
\index{width@{width}!Lua.Screen@{Lua.Screen}}
\subsubsection{\texorpdfstring{width}{width}}
{\footnotesize\ttfamily int Lua.\+Screen.\+width\hspace{0.3cm}{\ttfamily [static]}, {\ttfamily [get]}}



The current width of the screen window in pixels (Read Only). 

This is the actual width of the player window (in fullscreen it is also the current resolution). \begin{DoxyReturn}{Returns}

\end{DoxyReturn}


The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
Lua\+Input.\+cs\end{DoxyCompactItemize}

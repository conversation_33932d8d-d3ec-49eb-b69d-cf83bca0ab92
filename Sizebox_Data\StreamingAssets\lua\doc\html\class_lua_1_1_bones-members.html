<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_bones.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle"><div class="title">Lua.Bones Member List</div></div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="class_lua_1_1_bones.html">Lua.Bones</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_bones.html#a13cba4cdb3e2503912cc6eb9a9b9f187">GetBoneByName</a>(string name, bool partial=true)</td><td class="entry"><a class="el" href="class_lua_1_1_bones.html">Lua.Bones</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_bones.html#abc25d3c71e0a41ce1a65407f6ac77bca">GetBonesByName</a>(string name, bool partial=true)</td><td class="entry"><a class="el" href="class_lua_1_1_bones.html">Lua.Bones</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_bones.html#a6ee9efaf692471552da3f885987361ca">head</a></td><td class="entry"><a class="el" href="class_lua_1_1_bones.html">Lua.Bones</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_bones.html#aaf90bd7cb3fbd050f890e985d7a8a160">hips</a></td><td class="entry"><a class="el" href="class_lua_1_1_bones.html">Lua.Bones</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_bones.html#a6ee2d4c0e55ea06ca64e086680d87331">leftFoot</a></td><td class="entry"><a class="el" href="class_lua_1_1_bones.html">Lua.Bones</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_bones.html#a3242c39368adafc9965e274f49d63283">leftHand</a></td><td class="entry"><a class="el" href="class_lua_1_1_bones.html">Lua.Bones</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_bones.html#ac1c0091627a3dda698685dd84d8b6e6a">leftLowerArm</a></td><td class="entry"><a class="el" href="class_lua_1_1_bones.html">Lua.Bones</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_bones.html#acade1832236cb1887d8d4e30af54fbef">leftLowerLeg</a></td><td class="entry"><a class="el" href="class_lua_1_1_bones.html">Lua.Bones</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_bones.html#a3a134608353ae7791f13f2cce227ce7b">leftUpperArm</a></td><td class="entry"><a class="el" href="class_lua_1_1_bones.html">Lua.Bones</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_bones.html#a260c8638158d2f0580c1275351f4a1f2">leftUpperLeg</a></td><td class="entry"><a class="el" href="class_lua_1_1_bones.html">Lua.Bones</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_bones.html#ae0707d3e06ec285d2d4634b26b046bbe">rightFoot</a></td><td class="entry"><a class="el" href="class_lua_1_1_bones.html">Lua.Bones</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_bones.html#a33f155d4f1abdedd2c8f514fc9f768dd">rightHand</a></td><td class="entry"><a class="el" href="class_lua_1_1_bones.html">Lua.Bones</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_bones.html#aa765e8509188018d417bfae9b678f4b7">rightLowerArm</a></td><td class="entry"><a class="el" href="class_lua_1_1_bones.html">Lua.Bones</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_bones.html#a7322bb4e06909a72de8cb7899bbdda28">rightLowerLeg</a></td><td class="entry"><a class="el" href="class_lua_1_1_bones.html">Lua.Bones</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_bones.html#ae5b640e142fcbe64ab63218e48bc5271">rightUpperArm</a></td><td class="entry"><a class="el" href="class_lua_1_1_bones.html">Lua.Bones</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_bones.html#a2192099d2fee781ee13c3591caa8c78c">rightUpperLeg</a></td><td class="entry"><a class="el" href="class_lua_1_1_bones.html">Lua.Bones</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_bones.html#a28da7e69ef1c810bd255236404ad039a">spine</a></td><td class="entry"><a class="el" href="class_lua_1_1_bones.html">Lua.Bones</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

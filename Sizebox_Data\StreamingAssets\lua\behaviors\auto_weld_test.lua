-- AUTO WELD TEST BEHAVIOR
-- Proper Sizebox behavior for testing object spawning and welding
-- Press F9 to toggle on/off

local AutoWeldTest = {}
AutoWeldTest.__index = AutoWeldTest

-- REQUIRED: Behavior registration functions
function AutoWeldTest.GetBehaviorName()
    return "Auto Weld Test"
end

function AutoWeldTest.GetBehaviorDescription()
    return "Test automatic object spawning and welding. Press F9 to toggle on/off."
end

function AutoWeldTest.new(agent)
    local self = setmetatable({}, AutoWeldTest)

    -- Store the agent (character this behavior is attached to)
    self.agent = agent

    -- Configuration
    self.enabled = false
    self.hasSpawned = false
    self.updateInterval = 1.0  -- Check every 1 second
    self.lastUpdate = 0
    
    -- Object and bone settings
    self.objectName = "Beta5_SHAPECHANGE"
    self.targetBoneName = "37.JOINT_TORSO"  -- Start with simple torso bone
    
    -- Alternative object names to try
    self.objectNames = {
        "Beta5_SHAPECHANGE",
        "Beta5_ShapeChange", 
        "SHAP<PERSON><PERSON>NG<PERSON>",
        "<PERSON>hape<PERSON>hange"
    }
    
    -- Alternative bone names to try
    self.boneNames = {
        "37.JOINT_TORSO",
        "JOINT_TORSO",
        "Torso",
        "37.Torso",
        "Hips",
        "Spine",
        "Chest"
    }
    
    print("AutoWeldTest: Behavior created for " .. (agent and agent.name or "unknown"))
    Game.Toast.New().Print("AutoWeldTest Ready - Press F9 to toggle")
    
    return self
end

function AutoWeldTest:Update()
    -- Handle F9 toggle
    if Input and Input.GetKeyDown("f9") then
        self:Toggle()
    end
    
    -- Only run if enabled
    if not self.enabled then
        return
    end
    
    -- Throttle updates
    local currentTime = Time.time or 0
    if currentTime - self.lastUpdate < self.updateInterval then
        return
    end
    self.lastUpdate = currentTime
    
    -- Try to spawn and weld if we haven't already
    if not self.hasSpawned then
        self:AttemptSpawnAndWeld()
    end
end

function AutoWeldTest:Toggle()
    self.enabled = not self.enabled
    
    if self.enabled then
        print("AutoWeldTest: ENABLED - Will attempt to spawn and weld object")
        Game.Toast.New().Print("🟢 AutoWeld ENABLED")
        self.hasSpawned = false  -- Reset so it tries again
    else
        print("AutoWeldTest: DISABLED")
        Game.Toast.New().Print("🔴 AutoWeld DISABLED")
    end
end



function AutoWeldTest:FindBone(character, boneName)
    if not character then
        return nil
    end
    
    -- Try multiple ways to find the bone
    local bone = nil
    
    -- Method 1: Direct bone access
    if character.bones and character.bones[boneName] then
        bone = character.bones[boneName]
        print("AutoWeldTest: Found bone '" .. boneName .. "' via direct access")
        return bone
    end
    
    -- Method 2: FindBone function
    if character.FindBone then
        bone = character.FindBone(boneName)
        if bone then
            print("AutoWeldTest: Found bone '" .. boneName .. "' via FindBone")
            return bone
        end
    end
    
    -- Method 3: bones.Find
    if character.bones and character.bones.Find then
        bone = character.bones.Find(boneName)
        if bone then
            print("AutoWeldTest: Found bone '" .. boneName .. "' via bones.Find")
            return bone
        end
    end
    
    return nil
end

function AutoWeldTest:AttemptSpawnAndWeld()
    print("AutoWeldTest: Starting spawn and weld attempt...")

    -- Use the agent (character this behavior is attached to)
    local character = self.agent
    if not character then
        print("AutoWeldTest: Failed - No character found")
        Game.Toast.New().Print("❌ No character found")
        return false
    end

    print("AutoWeldTest: Using character: " .. (character.name or "unnamed"))
    
    -- Find a bone that works
    local targetBone = nil
    local foundBoneName = nil
    
    for _, boneName in ipairs(self.boneNames) do
        print("AutoWeldTest: Trying bone: " .. boneName)
        targetBone = self:FindBone(character, boneName)
        if targetBone then
            foundBoneName = boneName
            print("AutoWeldTest: SUCCESS - Found bone: " .. boneName)
            break
        end
    end
    
    if not targetBone then
        print("AutoWeldTest: Failed - No target bone found")
        Game.Toast.New().Print("❌ No target bone found")
        return false
    end
    
    -- Get spawn position from bone
    local spawnPos = targetBone.position or Vector3.zero
    print("AutoWeldTest: Spawn position: " .. tostring(spawnPos))
    
    -- Try to spawn object
    local spawnedObject = nil
    local successfulObjectName = nil
    
    for _, objectName in ipairs(self.objectNames) do
        print("AutoWeldTest: Trying to spawn: " .. objectName)
        
        local success, result = pcall(function()
            return Entity.Spawn(objectName, spawnPos, Quaternion.identity, 1.0)
        end)
        
        if success and result then
            spawnedObject = result
            successfulObjectName = objectName
            print("AutoWeldTest: SUCCESS - Spawned: " .. objectName)
            break
        else
            print("AutoWeldTest: Failed to spawn: " .. objectName)
        end
    end
    
    if not spawnedObject then
        print("AutoWeldTest: Failed - Could not spawn any object")
        Game.Toast.New().Print("❌ Could not spawn object")
        return false
    end
    
    -- Try to weld the object to the bone
    print("AutoWeldTest: Attempting to weld " .. successfulObjectName .. " to " .. foundBoneName)
    
    local weldSuccess = false
    
    -- Method 1: Try WeldTo
    if spawnedObject.WeldTo and targetBone then
        local success, result = pcall(function()
            return spawnedObject.WeldTo(targetBone)
        end)
        if success then
            weldSuccess = true
            print("AutoWeldTest: Welded via WeldTo")
        end
    end
    
    -- Method 2: Try SetParent
    if not weldSuccess and spawnedObject.transform and spawnedObject.transform.SetParent and targetBone then
        local success, result = pcall(function()
            spawnedObject.transform.SetParent(targetBone)
            return true
        end)
        if success then
            weldSuccess = true
            print("AutoWeldTest: Welded via SetParent")
        end
    end
    
    if weldSuccess then
        print("AutoWeldTest: COMPLETE SUCCESS - Object spawned and welded!")
        Game.Toast.New().Print("✅ SUCCESS: " .. successfulObjectName .. " welded to " .. foundBoneName)
        self.hasSpawned = true
        return true
    else
        print("AutoWeldTest: Spawned but failed to weld")
        Game.Toast.New().Print("⚠️ Spawned but failed to weld")
        return false
    end
end

-- REQUIRED: Return the behavior class for Sizebox registration
return AutoWeldTest

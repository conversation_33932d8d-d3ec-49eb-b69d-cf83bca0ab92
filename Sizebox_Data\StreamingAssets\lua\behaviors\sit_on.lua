SitOn = RegisterBehavior("Sit On")
SitOn.data = {
    menuEntry = "Sit On",
    secondary = false,
    agent = {
        type = { "giantess" }
    },
    target = {
        type = { "micro" }
    }
}

function SitOn:Start() 
	if self.agent.scale > self.target.scale*10 then
		log("You are small enough")
		self.animationStepThrough = 0
	end
	self.waitingTime = 0
	self.distanceBetween = 0
end

function SitOn:Update()

	if self.animationStepThrough == 0 and self.agent.animation.IsCompleted()  then
		self.agent.animation.Set("Walk")
		self.animationStepThrough = 1
	end
	
	if self.animationStepThrough == 1 and self.agent.animation.IsCompleted()  then
		
		targetDirection=(self.target.position-self.agent.position)
		targetDirection.normalize()
		targetDistance=(self.target.position-self.agent.position)	
		scale=self.agent.scale/3
		targetPoint=self.agent.position+targetDistance+(targetDirection*scale)
		self.agent.MoveTo(targetPoint)
		self.animationStepThrough = 2
		self.agent.animation.Set("Whatever Gesture")
	end
	

	if self.animationStepThrough == 2 and self.agent.animation.Get() == "Whatever Gesture"  then
		self.agent.animation.SetSpeed(.3)
		self.agent.animation.Set("Sit 4")
		self.animationStepThrough = 3
		self:WaitingTimeAfter(7)
	end
	
	if self.animationStepThrough == 3 and self.waitingTime < Time.time then
		self.agent.animation.SetSpeed(1)
		self.agent.animation.Set("Idle 2")
		self.agent.ai.StopBehavior()
	end
end

function SitOn:Exit()
	log("Ended")
end

function SitOn:WaitingTimeAfter(inTime)
	self.waitingTime = Time.time + inTime
end
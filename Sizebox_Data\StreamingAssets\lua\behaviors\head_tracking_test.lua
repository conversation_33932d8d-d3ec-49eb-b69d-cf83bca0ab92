-- HEAD TRACKING TEST SCRIPT - SIMPLE VERSION
-- Back to basics - what was working before
-- Based on shapechange_grow_fixed.lua bone manipulation methods

local HeadTracker = RegisterBehavior("Head Tracking Test")
HeadTracker.data = {
    menuEntry = "AI/Head Tracking Test",
    agent = { type = { "micro", "giantess" } }
}

function HeadTracker:Start()
    print("DEBUG: HEAD TRACKER - Script started!")
    Game.Toast.New().Print("HEAD TRACKER ACTIVE! Head will track micro automatically!")
    
    -- Determine who we're controlling
    if self.target and self.target ~= self.agent then
        -- Micro controlling giantess
        self.giantess = self.target
        self.micro = self.agent
        print("DEBUG: HEAD TRACKER - Micro controlling giantess setup")
    else
        -- Direct giantess control
        self.giantess = self.agent
        -- Find micro character
        if Entity and Entity.GetPlayerCharacter then
            self.micro = Entity.GetPlayerCharacter()
            print("DEBUG: HEAD TRACKER - Found player as micro")
        end
    end
    
    if not self.giantess then
        print("DEBUG: HEAD TRACKER - ERROR: No giantess found!")
        Game.Toast.New().Print("ERROR: No giantess character found!")
        return
    end
    
    if not self.micro then
        print("DEBUG: HEAD TRACKER - ERROR: No micro found!")
        Game.Toast.New().Print("ERROR: No micro character found!")
        return
    end
    
    print("DEBUG: HEAD TRACKER - Giantess: " .. (self.giantess.name or "Unknown"))
    print("DEBUG: HEAD TRACKER - Micro: " .. (self.micro.name or "Unknown"))
    
    -- Initialize head tracking system
    self.headTracking = {
        active = false,
        headBone = nil,
        initialRotation = nil,
        boneName = nil
    }
    
    -- Find and store head bone immediately
    self:FindHeadBone()
    
    -- Start tracking immediately
    if self.headTracking.headBone then
        self.headTracking.active = true
        Game.Toast.New().Print("HEAD TRACKING STARTED! Head should follow micro now!")
        print("DEBUG: HEAD TRACKER - Tracking activated!")
    else
        Game.Toast.New().Print("ERROR: Could not find head bone!")
        print("DEBUG: HEAD TRACKER - ERROR: Head bone not found!")
    end
end

function HeadTracker:FindHeadBone()
    print("DEBUG: HEAD TRACKER - Searching for head bone...")
    
    -- Helper function to find bone (EXACT same as shapechange script)
    local function FindBone(entity, boneName)
        print("DEBUG: HEAD TRACKER - Trying bone: " .. boneName)
        if entity and entity.bones and entity.bones.GetBonesByName then
            local bones = entity.bones.GetBonesByName(boneName, true)
            if bones and bones[1] then
                print("DEBUG: HEAD TRACKER - Found bone: " .. boneName)
                return bones[1]
            else
                print("DEBUG: HEAD TRACKER - Bone not found: " .. boneName)
            end
        else
            print("DEBUG: HEAD TRACKER - Entity has no bones system")
        end
        return nil
    end
    
    -- Try multiple head bone names (prioritize 47.JOINT_HEAD)
    local boneNames = {"47.JOINT_HEAD", "16.JOINT_HEAD", "Head", "head", "JOINT_HEAD", "Neck", "neck"}
    
    for _, boneName in ipairs(boneNames) do
        local headBone = FindBone(self.giantess, boneName)
        if headBone then
            self.headTracking.headBone = headBone
            self.headTracking.boneName = boneName
            
            -- Store initial rotation (CRITICAL - like shapechange script)
            self.headTracking.initialRotation = headBone.localRotation
            
            print("DEBUG: HEAD TRACKER - SUCCESS! Found head bone: " .. boneName)
            Game.Toast.New().Print("Found head bone: " .. boneName)
            return true
        end
    end
    
    print("DEBUG: HEAD TRACKER - ERROR: No head bone found!")
    return false
end

function HeadTracker:Update()
    -- Only track if system is active and we have all components
    if not self.headTracking.active or not self.headTracking.headBone or not self.giantess or not self.micro then
        return
    end
    
    -- Get current positions
    local giantessPos = self.giantess.transform.position
    local microPos = self.micro.transform.position
    
    if not giantessPos or not microPos then
        return
    end
    
    -- Calculate head position (approximate - 15 units up from body center)
    local headWorldPos = Vector3.new(giantessPos.x, giantessPos.y + 15, giantessPos.z)
    
    -- Calculate direction from head to micro
    local direction = microPos - headWorldPos
    
    -- Apply head rotation using proper method (like shapechange script)
    local success = pcall(function()
        if direction.magnitude > 0.1 then
            -- Method 1: Try direct LookRotation (most accurate)
            local lookRotation = Quaternion.LookRotation(direction.normalized)
            self.headTracking.headBone.localRotation = lookRotation
        else
            -- Fallback: Look down slightly
            local downRotation = Quaternion.Euler(30, 0, 0) * self.headTracking.initialRotation
            self.headTracking.headBone.localRotation = downRotation
        end
    end)
    
    if not success then
        print("DEBUG: HEAD TRACKER - Rotation failed, disabling tracking")
        self.headTracking.active = false
        Game.Toast.New().Print("Head tracking failed - disabled")
    end
end

function HeadTracker:OnDestroy()
    -- Reset head to original rotation when script is disabled
    if self.headTracking.headBone and self.headTracking.initialRotation then
        local success = pcall(function()
            self.headTracking.headBone.localRotation = self.headTracking.initialRotation
            print("DEBUG: HEAD TRACKER - Head rotation reset to original")
        end)
        if success then
            Game.Toast.New().Print("Head tracking disabled - head reset!")
        end
    end
    print("DEBUG: HEAD TRACKER - Script destroyed")
end

-- Manual toggle function (for testing)
function HeadTracker:ToggleTracking()
    if self.headTracking.active then
        self.headTracking.active = false
        -- Reset to original rotation
        if self.headTracking.headBone and self.headTracking.initialRotation then
            self.headTracking.headBone.localRotation = self.headTracking.initialRotation
        end
        Game.Toast.New().Print("Head tracking DISABLED")
        print("DEBUG: HEAD TRACKER - Tracking disabled manually")
    else
        if self.headTracking.headBone then
            self.headTracking.active = true
            Game.Toast.New().Print("Head tracking ENABLED")
            print("DEBUG: HEAD TRACKER - Tracking enabled manually")
        else
            Game.Toast.New().Print("ERROR: No head bone found!")
        end
    end
end

return HeadTracker

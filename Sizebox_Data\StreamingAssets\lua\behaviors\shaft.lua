-- Common variables
G_AGENT = { type = { "humanoid" } }
G_TARGET = { type = { "oneself" } }
G_FLAGS = { "shaft_growth" }

G_SETTINGS = {
    {"boneName", "Bone Name", "string", "433.!JOINT_SHAFT ROOT"},
    {"limit", "Limit Size", "float", "3", {"1.2", "8"}},
    {"speed", "Rate of Change", "float", "0.03", {"0.03", "0.2"}},
    {"pulseInterval", "Pulse Interval", "float", "5", {"1", "10"}},
    {"pulseDuration", "Pulse Duration", "float", "1", {"0.5", "2"}},
    {"growthDuration", "Growth Duration", "float", "2", {"1", "5"}}
}

-- Common functions
function FindBone(entity, boneName)
    local bones = entity.bones.GetBonesByName(boneName, true)
    if bones then
        return bones[1]
    else
        return nil
    end
end

-- Behaviours
ShaftGrowth_expand = RegisterBehavior("Shaft Growth Expand")
ShaftGrowth_expand.data = {
    menuEntry = "Body/Shaft Growth Expand",
    flags = G_FLAGS,
    agent = G_AGENT,
    target = G_TARGET,
    settings = G_SETTINGS
}

function ShaftGrowth_expand:Start()
    self.bone = FindBone(self.agent, self.boneName)

    if not self.bone then
        Game.Toast.New().Print("No bone found to expand in model " .. self.agent.name)
    elseif not self.initiated then
        self.initiated = true
        self.pulseTimer = 0
        self.pulseActive = false
        self.growthTimer = 0
        self.growing = false
        self.waiting = false
        self.waitTimer = 0
        self.rotationTimer = 0
        self.rotatingleft = false
        self.rotatingright = false
        self.rotationProgress = 0
        self.initialRotation = self.bone.localRotation
    end
end

function ShaftGrowth_expand:Update()
    if self.initiated then
        if not self.waiting then
            self.pulseTimer = self.pulseTimer + Time.deltaTime

            if self.pulseTimer >= self.pulseInterval then
                self.pulseTimer = 0
                self.pulseActive = true
            end

            if self.pulseActive then
                local pulseProgress = (self.pulseTimer / self.pulseDuration)

                if pulseProgress < 0.25 then
                    self:AdjustBoneScale(0.0005)
                elseif pulseProgress < 0.5 then
                    self:AdjustBoneScale(-0.001)
                elseif pulseProgress < 0.75 then
                    self:AdjustBoneScale(0.001)
                else
                    self.pulseActive = false
                    self.growing = true
                    self.growthTimer = 0
                    -- Update collision after pulse cycle
                    self.agent.UpdateMeshCollider()
                end
            end

            if self.growing then
                self.growthTimer = self.growthTimer + Time.deltaTime

                if self.growthTimer < self.growthDuration then
                    local growAmount = self.speed * Time.deltaTime
                    if self.bone.localScale.y < self.limit then
                        self:AdjustBoneScale(growAmount)
                    end
                else
                    self.growing = false
                    self.waiting = true
                    self.waitTimer = 0
                    -- Update collision after growth cycle
                    self.agent.UpdateMeshCollider()
                end
            end
        else
            self.waitTimer = self.waitTimer + Time.deltaTime

            if self.waitTimer >= 5 then
                self.waiting = false
                self.pulseTimer = 0
                self.pulseActive = true
            end
        end

        -- Rotation logic
        self.rotationTimer = self.rotationTimer + Time.deltaTime

        if self.rotationTimer >= 3 then
            self.rotationTimer = 0
            self.rotatingleft = true
            self.rotatingright = false
            self.rotationProgress = 0
        end

        if self.rotatingleft then
            self.rotationProgress = self.rotationProgress + Time.deltaTime / 0.3

            if self.rotationProgress < 1 then
                self.bone.localRotation = Quaternion.Slerp(self.initialRotation, Quaternion.Euler(-30, 0, 0), self.rotationProgress)
            else
                self.rotatingleft = false
                self.rotatingright = true
                self.rotationProgress = 0
            end
        elseif self.rotatingright then
            self.rotationProgress = self.rotationProgress + Time.deltaTime / 0.5

            if self.rotationProgress < 1 then
                self.bone.localRotation = Quaternion.Slerp(Quaternion.Euler(-30, 0, 0), self.initialRotation, self.rotationProgress)
            else
                self.rotatingright = false
            end
        end
    end
end

function ShaftGrowth_expand:AdjustBoneScale(amount)
    local newScale = self.bone.localScale * (1 + amount)
    self.bone.localScale = newScale
    local offset = self.bone.localPosition - self.bone.parent.localPosition
    self.bone.localPosition = self.bone.parent.localPosition + Vector3.new(offset.x * (1 + amount * 0), offset.y * (1 + amount * 0), offset.z)
    
    -- Update collision after significant growth changes
    if math.abs(amount) > 0.001 then
        self.agent.UpdateMeshCollider()
    end
end

ShaftGrowth_deflate = RegisterBehavior("Shaft Growth Deflate")
ShaftGrowth_deflate.data = {
    menuEntry = "Body/Shaft Growth Deflate",
    flags = G_FLAGS,
    agent = G_AGENT,
    target = G_TARGET,
    settings = G_SETTINGS
}

function ShaftGrowth_deflate:Start()
    self.bone = FindBone(self.agent, self.boneName)
    self.limit = 1 / self.limit

    if not self.bone then
        Game.Toast.New().Print("No bone found to expand in model " .. self.agent.name)
    elseif not self.initiated then
        self.initiated = true
    end
end

function ShaftGrowth_deflate:Update()
    if self.initiated then
        local growAmount = -self.speed * Time.deltaTime

        if self.bone.localScale.y > self.limit then
            self.bone.localScale = self.bone.localScale * (1 + growAmount)
            self.bone.localPosition = self.bone.localPosition / (1 + growAmount)
            -- Update collision after significant deflation
            if math.abs(growAmount) > 0.001 then
                self.agent.UpdateMeshCollider()
            end
        end
    end
end

ShaftGrowth_reset = RegisterBehavior("Shaft Growth Reset")
ShaftGrowth_reset.data = {
    menuEntry = "Body/Shaft Growth Reset",
    flags = G_FLAGS,
    agent = G_AGENT,
    target = G_TARGET,
    settings = G_SETTINGS
}

function ShaftGrowth_reset:Start()
    self.bone = FindBone(self.agent, self.boneName)

    if self.bone then
        self.bone.localScale = Vector3.one
        -- Update collision after reset
        self.agent.UpdateMeshCollider()
    end
end

ShaftGrowth_stop = RegisterBehavior("Shaft Growth Stop")
ShaftGrowth_stop.data = {
    menuEntry = "Body/Shaft Growth Stop",
    flags = G_FLAGS,
    agent = G_AGENT,
    target = G_TARGET
}

function ShaftGrowth_stop:Start()
    Game.Toast.New().Print("Stopping Shaft Growth")
end

CollisionUpdate = RegisterBehavior("force_collider_update")
CollisionUpdate.data = {
    menuEntry = "Debug/Update Colliders",
    secondary = true,
    flags = { "debug" },
    agent = {
        type = { "giantess" }
    },
    target = {
        type = { "oneself" }
    }
}

function CollisionUpdate:Start()
    Log("Updating mesh collider...")
    self.agent.UpdateMeshCollider()
end

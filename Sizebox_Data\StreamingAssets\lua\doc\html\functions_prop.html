<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Class Members - Properties</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('functions_prop.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a id="index_a" name="index_a"></a>- a -</h3><ul>
<li>accurateFiring&#160;:&#160;<a class="el" href="class_lua_1_1_shooting.html#af30c3a5b6caca44684fcd88ac7f7f4a7">Lua.Shooting</a></li>
<li>ai&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a13c1350817e444010fcbaff8d224039e">Lua.Entity</a></li>
<li>angularDrag&#160;:&#160;<a class="el" href="class_lua_1_1_rigidbody.html#a901b3213408100236b17a3e55b64e6f7">Lua.Rigidbody</a></li>
<li>angularVelocity&#160;:&#160;<a class="el" href="class_lua_1_1_rigidbody.html#ab88493ae1a778194017c0e3a87c0625d">Lua.Rigidbody</a></li>
<li>animation&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#acf080bfbeeb3a6308c2fdd4cc4993e81">Lua.Entity</a></li>
<li>anyKey&#160;:&#160;<a class="el" href="class_lua_1_1_input.html#a624c63ec2127ae1a70da0e8ac2a5742d">Lua.Input</a></li>
<li>anyKeyDown&#160;:&#160;<a class="el" href="class_lua_1_1_input.html#a2587110e95d4dff8d00d112fcade9631">Lua.Input</a></li>
<li>autowalk&#160;:&#160;<a class="el" href="class_lua_1_1_player.html#a409e63cab8bf55da64a77ae09a44eaa9">Lua.Player</a></li>
</ul>


<h3><a id="index_b" name="index_b"></a>- b -</h3><ul>
<li>back&#160;:&#160;<a class="el" href="class_lua_1_1_vector3.html#a3742b113252e0163b006a17a76cb558c">Lua.Vector3</a></li>
<li>baseHeight&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a1873494f26c8f90c79254b43d25d47f7">Lua.Entity</a></li>
<li>baseVisibilityDistance&#160;:&#160;<a class="el" href="class_lua_1_1_senses.html#aed3385cd359ba4734ac8c114b524a1b2">Lua.Senses</a></li>
<li>body&#160;:&#160;<a class="el" href="class_lua_1_1_i_k.html#aca9dea4db5e6c66d5d1e904266c626ef">Lua.IK</a></li>
<li>bones&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a8d4bdecb96c327395e5ddbde88608cf4">Lua.Entity</a></li>
<li>burstFire&#160;:&#160;<a class="el" href="class_lua_1_1_shooting.html#a6ddbbd79abebeb560511e7c00093fb1a">Lua.Shooting</a></li>
<li>burstFireInterval&#160;:&#160;<a class="el" href="class_lua_1_1_shooting.html#a51b27960d8f74262edb79dc173f75670">Lua.Shooting</a></li>
<li>burstFireRounds&#160;:&#160;<a class="el" href="class_lua_1_1_shooting.html#a41ac94c6ba5c407bd0fcbf0f6e938fd1">Lua.Shooting</a></li>
</ul>


<h3><a id="index_c" name="index_c"></a>- c -</h3><ul>
<li>CanLookAtPlayer&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a7dfc341caa3b11cc42ef45226689741c">Lua.Entity</a></li>
<li>childCount&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#a9d77f87171bcb8090f086ae405c4f89e">Lua.Transform</a></li>
<li>climbing&#160;:&#160;<a class="el" href="class_lua_1_1_player.html#a4e88291e27a05dc2c1c7c458b6fc5e2a">Lua.Player</a></li>
<li>climbSpeed&#160;:&#160;<a class="el" href="class_lua_1_1_player.html#acfd04cccae35d05632d90b022a19dd3e">Lua.Player</a></li>
<li>clip&#160;:&#160;<a class="el" href="class_lua_1_1_audio_source.html#a4913b6f1fa8dfe5eb528cd7e40f91684">Lua.AudioSource</a></li>
</ul>


<h3><a id="index_d" name="index_d"></a>- d -</h3><ul>
<li>Deg2Rad&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a5f003a3aab6299095b301066d0af6eab">Lua.Mathf</a></li>
<li>deltaTime&#160;:&#160;<a class="el" href="class_lua_1_1_time.html#a0c34615f0ecde357e396cab65ecd4428">Lua.Time</a></li>
<li>dict&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#ae0c707512eed832f2211ace61d3be75d">Lua.Entity</a></li>
<li>down&#160;:&#160;<a class="el" href="class_lua_1_1_vector3.html#adfb20fbfe1e5224b0b0fade3da0d4c8f">Lua.Vector3</a></li>
<li>drag&#160;:&#160;<a class="el" href="class_lua_1_1_rigidbody.html#ac537e281d009b3e07c93f7357fa743cd">Lua.Rigidbody</a></li>
</ul>


<h3><a id="index_e" name="index_e"></a>- e -</h3><ul>
<li>enabled&#160;:&#160;<a class="el" href="class_lua_1_1_i_k.html#a9cbf5715c753f97860bf9d268e76abcc">Lua.IK</a></li>
<li>entity&#160;:&#160;<a class="el" href="class_lua_1_1_player.html#abfa8836f5980aeeee9459a69b3dcecf6">Lua.Player</a>, <a class="el" href="class_lua_1_1_transform.html#a1a4480b448b89a7e1f392af4c842cc28">Lua.Transform</a></li>
<li>Epsilon&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a121e67b35c4d96893e79a5be089ebc8a">Lua.Mathf</a></li>
<li>eulerAngles&#160;:&#160;<a class="el" href="class_lua_1_1_quaternion.html#ac50cf6b67c4cb0363b834b7054cdd5fa">Lua.Quaternion</a>, <a class="el" href="class_lua_1_1_transform.html#ad9a5f0534a08dc2d6cb9ad32b6581b8d">Lua.Transform</a></li>
</ul>


<h3><a id="index_f" name="index_f"></a>- f -</h3><ul>
<li>fieldOfView&#160;:&#160;<a class="el" href="class_lua_1_1_senses.html#ac4a708caaa7a7381870d4f21d619c2e9">Lua.Senses</a></li>
<li>firingEnabled&#160;:&#160;<a class="el" href="class_lua_1_1_lua_player_raygun.html#a002f731fc9b1c217a12b03045e87a0cd">Lua.LuaPlayerRaygun</a></li>
<li>firingInterval&#160;:&#160;<a class="el" href="class_lua_1_1_shooting.html#aece1867c834c0c4fc31af53c55c8040e">Lua.Shooting</a></li>
<li>fixedDeltaTime&#160;:&#160;<a class="el" href="class_lua_1_1_time.html#a8b9eb6a7ddf143242c72e6e9378604c6">Lua.Time</a></li>
<li>flySpeed&#160;:&#160;<a class="el" href="class_lua_1_1_player.html#aba74b8604fe18ff58fbe7cab856c759b">Lua.Player</a></li>
<li>forward&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#ad07cf6c2802bfbab50272030379f1826">Lua.Transform</a>, <a class="el" href="class_lua_1_1_vector3.html#ad8be15240d9bfa336d926ab023f11ad4">Lua.Vector3</a></li>
<li>frameCount&#160;:&#160;<a class="el" href="class_lua_1_1_time.html#ae821e279218bd2e418f6bafc2e66cc4f">Lua.Time</a></li>
<li>freezeRotation&#160;:&#160;<a class="el" href="class_lua_1_1_rigidbody.html#ad3f2380221d01eedada3df4d282a37d4">Lua.Rigidbody</a></li>
<li>fullScreen&#160;:&#160;<a class="el" href="class_lua_1_1_screen.html#a7b08588981d36493e586b0d59a1f1b7a">Lua.Screen</a></li>
</ul>


<h3><a id="index_g" name="index_g"></a>- g -</h3><ul>
<li>globalSpeed&#160;:&#160;<a class="el" href="class_lua_1_1_all_giantess.html#a76e7e6cca768273ffd53a6b8a3127ffe">Lua.AllGiantess</a></li>
<li>gravity&#160;:&#160;<a class="el" href="class_lua_1_1_world.html#a22ab3b01f22a00741c9b11a1f32ff7db">Lua.World</a></li>
</ul>


<h3><a id="index_h" name="index_h"></a>- h -</h3><ul>
<li>head&#160;:&#160;<a class="el" href="class_lua_1_1_bones.html#a6ee9efaf692471552da3f885987361ca">Lua.Bones</a></li>
<li>height&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a6b6baf8292fe2447ad0620722bc24526">Lua.Entity</a>, <a class="el" href="class_lua_1_1_screen.html#a7e3459d0ccc2641709d1bad599092fdc">Lua.Screen</a></li>
<li>hips&#160;:&#160;<a class="el" href="class_lua_1_1_bones.html#aaf90bd7cb3fbd050f890e985d7a8a160">Lua.Bones</a></li>
</ul>


<h3><a id="index_i" name="index_i"></a>- i -</h3><ul>
<li>id&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a7776e8422e86d2ab5670ca314a65aab5">Lua.Entity</a></li>
<li>identity&#160;:&#160;<a class="el" href="class_lua_1_1_quaternion.html#ac7d56f4c2496af59e66550e35bff614c">Lua.Quaternion</a></li>
<li>ik&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a42cd1e5e507a1e79eb1161799564da88">Lua.Entity</a></li>
<li>inaccuracyFactor&#160;:&#160;<a class="el" href="class_lua_1_1_shooting.html#a7f8c04173b4649a5b6134344e67dc10a">Lua.Shooting</a></li>
<li>Infinity&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#ac3686fdccff0df0a6c797af8ba4722b1">Lua.Mathf</a></li>
<li>insideUnitCircle&#160;:&#160;<a class="el" href="class_lua_1_1_random.html#a2e9c7e49b4362f7ab3335f0a46778b70">Lua.Random</a></li>
<li>insideUnitSphere&#160;:&#160;<a class="el" href="class_lua_1_1_random.html#a1c3fa5f6de20e35af1e85564ef928137">Lua.Random</a></li>
<li>isAiming&#160;:&#160;<a class="el" href="class_lua_1_1_player.html#af146bd925307c2a878d8d7aa6ac4f990">Lua.Player</a>, <a class="el" href="class_lua_1_1_shooting.html#a0c512eac7dea63f8594bbfcb0976a34a">Lua.Shooting</a></li>
<li>isFiring&#160;:&#160;<a class="el" href="class_lua_1_1_shooting.html#a0b4d9622c4a1888f424ebdc13db498d1">Lua.Shooting</a></li>
<li>isKinematic&#160;:&#160;<a class="el" href="class_lua_1_1_rigidbody.html#aee1018d4d56ab085d013acc494c0d0f9">Lua.Rigidbody</a></li>
<li>isPlaying&#160;:&#160;<a class="el" href="class_lua_1_1_audio_source.html#aaef47740090cbe41ff5b4f4b40aad7b7">Lua.AudioSource</a></li>
</ul>


<h3><a id="index_j" name="index_j"></a>- j -</h3><ul>
<li>jumpPower&#160;:&#160;<a class="el" href="class_lua_1_1_player.html#ab34c0553a5093faa0dc839e3e5c1e2fd">Lua.Player</a></li>
</ul>


<h3><a id="index_l" name="index_l"></a>- l -</h3><ul>
<li>left&#160;:&#160;<a class="el" href="class_lua_1_1_vector3.html#a5a08c13ed00efb9fb6728fded1e8a472">Lua.Vector3</a></li>
<li>leftFoot&#160;:&#160;<a class="el" href="class_lua_1_1_bones.html#a6ee2d4c0e55ea06ca64e086680d87331">Lua.Bones</a>, <a class="el" href="class_lua_1_1_i_k.html#a8cea34c6258871a550fc9d56f8facea1">Lua.IK</a></li>
<li>leftHand&#160;:&#160;<a class="el" href="class_lua_1_1_bones.html#a3242c39368adafc9965e274f49d63283">Lua.Bones</a>, <a class="el" href="class_lua_1_1_i_k.html#a88ba05bd1557a61e1f69e21fd172641c">Lua.IK</a></li>
<li>leftLowerArm&#160;:&#160;<a class="el" href="class_lua_1_1_bones.html#ac1c0091627a3dda698685dd84d8b6e6a">Lua.Bones</a></li>
<li>leftLowerLeg&#160;:&#160;<a class="el" href="class_lua_1_1_bones.html#acade1832236cb1887d8d4e30af54fbef">Lua.Bones</a></li>
<li>leftUpperArm&#160;:&#160;<a class="el" href="class_lua_1_1_bones.html#a3a134608353ae7791f13f2cce227ce7b">Lua.Bones</a></li>
<li>leftUpperLeg&#160;:&#160;<a class="el" href="class_lua_1_1_bones.html#a260c8638158d2f0580c1275351f4a1f2">Lua.Bones</a></li>
<li>list&#160;:&#160;<a class="el" href="class_lua_1_1_all_giantess.html#adc5824c47c4090e1d8769ddde5c9da6a">Lua.AllGiantess</a>, <a class="el" href="class_lua_1_1_all_micros.html#a1e4ae6a9f876819f84b19737405d2a8e">Lua.AllMicros</a></li>
<li>localEulerAngles&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#a7923f3c584b87b8e56de6b32acbfba99">Lua.Transform</a></li>
<li>localPosition&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#ab081c482002c1e4fedbcfa090b19b90e">Lua.Transform</a></li>
<li>localRotation&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#a2397fd50baf04311df6a50e4dcc302bd">Lua.Transform</a></li>
<li>localScale&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#a40e2891bff5d714d77449aeee6d84492">Lua.Transform</a></li>
<li>loop&#160;:&#160;<a class="el" href="class_lua_1_1_audio_source.html#a4ffd0dfe8f989efe964e368cc2a5995c">Lua.AudioSource</a></li>
<li>lossyScale&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#a55680638b6e6ae6b1bd4b5095b1822f1">Lua.Transform</a></li>
</ul>


<h3><a id="index_m" name="index_m"></a>- m -</h3><ul>
<li>magnitude&#160;:&#160;<a class="el" href="class_lua_1_1_vector3.html#af7a889228914dff8226722de5c47a9b7">Lua.Vector3</a></li>
<li>Major&#160;:&#160;<a class="el" href="class_lua_1_1_game_1_1_version.html#a8c329c68bcf96acc94c59705eace0791">Lua.Game.Version</a></li>
<li>mass&#160;:&#160;<a class="el" href="class_lua_1_1_rigidbody.html#aeab0f1c55ada296d501909dd61533a35">Lua.Rigidbody</a></li>
<li>maxAngularVelocity&#160;:&#160;<a class="el" href="class_lua_1_1_rigidbody.html#a8171fc4d6eb8d7e448eeb45f9fbc05d8">Lua.Rigidbody</a></li>
<li>maxDistance&#160;:&#160;<a class="el" href="class_lua_1_1_audio_source.html#a55ad4d09380c973f2001b2164aba4771">Lua.AudioSource</a></li>
<li>maxSize&#160;:&#160;<a class="el" href="class_lua_1_1_all_giantess.html#af82af573b560f17c3b0cd575bd61ed00">Lua.AllGiantess</a>, <a class="el" href="class_lua_1_1_entity.html#a133133afe701b7ca4f0e2d6632beae33">Lua.Entity</a>, <a class="el" href="class_lua_1_1_player.html#aa940451f7c74d6b8bdf34c728b6a688d">Lua.Player</a></li>
<li>maxSpeed&#160;:&#160;<a class="el" href="class_lua_1_1_animation.html#a49aa08ef58b67f1af48526781176fce3">Lua.Animation</a></li>
<li>metricHeight&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#ac821c347ba04965d00132f02e88adecd">Lua.Entity</a></li>
<li>minDistance&#160;:&#160;<a class="el" href="class_lua_1_1_audio_source.html#a66155ad664c665455476ea70f4779669">Lua.AudioSource</a></li>
<li>Minor&#160;:&#160;<a class="el" href="class_lua_1_1_game_1_1_version.html#aec3cfaebb94290f3405dbeeaee9277d6">Lua.Game.Version</a></li>
<li>minSize&#160;:&#160;<a class="el" href="class_lua_1_1_all_giantess.html#a4852dd60230117796328807f41509dbf">Lua.AllGiantess</a>, <a class="el" href="class_lua_1_1_entity.html#a642603e2952e4fcea70979837049f813">Lua.Entity</a>, <a class="el" href="class_lua_1_1_player.html#a101475733e91a310fdd874be94d5464b">Lua.Player</a></li>
<li>minSpeed&#160;:&#160;<a class="el" href="class_lua_1_1_animation.html#a7dee7e3d2e6bdffed96f20822e4bb307">Lua.Animation</a></li>
<li>modelName&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#acd67b39a7c95e3cb87171073c2877de1">Lua.Entity</a></li>
<li>morphs&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#aa9723210eb2494461ff12c55b52e8844">Lua.Entity</a></li>
<li>mousePosition&#160;:&#160;<a class="el" href="class_lua_1_1_input.html#ab5d4bcb7c637ec0760fc8ca8033ecec7">Lua.Input</a></li>
<li>mouseScrollDelta&#160;:&#160;<a class="el" href="class_lua_1_1_input.html#ac508b474e85e336be67f2ad7ef94f751">Lua.Input</a></li>
<li>movement&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a2845d63d6164b33ee49f760211fa4116">Lua.Entity</a></li>
<li>mute&#160;:&#160;<a class="el" href="class_lua_1_1_audio_source.html#ad82dcfe66567f1dac8a15edc327c89ff">Lua.AudioSource</a></li>
</ul>


<h3><a id="index_n" name="index_n"></a>- n -</h3><ul>
<li>name&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a8155b6c6ef0f0630ec7e818dd4cdaec4">Lua.Entity</a>, <a class="el" href="class_lua_1_1_transform.html#af1ca076a9406c3865fef9cbf8393e484">Lua.Transform</a></li>
<li>NegativeInfinity&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a0f4a13d448b3d1a96f3aaff159d6636b">Lua.Mathf</a></li>
<li>normalized&#160;:&#160;<a class="el" href="class_lua_1_1_vector3.html#a4e2f1c26b10b35c7a23108cfaa63320c">Lua.Vector3</a></li>
</ul>


<h3><a id="index_o" name="index_o"></a>- o -</h3><ul>
<li>one&#160;:&#160;<a class="el" href="class_lua_1_1_vector3.html#a16592c4087c4d02cf1dd00a06d5baced">Lua.Vector3</a></li>
<li>onUnitSphere&#160;:&#160;<a class="el" href="class_lua_1_1_random.html#a558a1a73871855fb64ba923ebc7353c4">Lua.Random</a></li>
</ul>


<h3><a id="index_p" name="index_p"></a>- p -</h3><ul>
<li>parent&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#ad7ea4f4af441f263f3cbfcb853d3edfa">Lua.Transform</a></li>
<li>PI&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a408b4fa7c06dd48e2aa0d6fcde7adedc">Lua.Mathf</a></li>
<li>pitch&#160;:&#160;<a class="el" href="class_lua_1_1_audio_source.html#a49d0053bb3cfa0b42c70f3b678e0d78f">Lua.AudioSource</a></li>
<li>position&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#ad8bd97d98fddc9b89f8410512b502c3f">Lua.Entity</a>, <a class="el" href="class_lua_1_1_i_k_effector.html#a8fca3762ba9e4b8e90d21f7bd701048a">Lua.IKEffector</a>, <a class="el" href="class_lua_1_1_rigidbody.html#a9bef020808bd389b43ac5d2f7d429dc9">Lua.Rigidbody</a>, <a class="el" href="class_lua_1_1_transform.html#a789b6abed611a7576ca2262bb9c5e6c3">Lua.Transform</a></li>
<li>positionWeight&#160;:&#160;<a class="el" href="class_lua_1_1_i_k_effector.html#a9237f631ddbe3043a8be096d4a51a5dd">Lua.IKEffector</a></li>
<li>predictiveAiming&#160;:&#160;<a class="el" href="class_lua_1_1_shooting.html#adc410fd560cfdc7bdbd5b870a7adedbf">Lua.Shooting</a></li>
</ul>


<h3><a id="index_r" name="index_r"></a>- r -</h3><ul>
<li>Rad2Deg&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#aed19ee907a834cbea518af347c4f39d7">Lua.Mathf</a></li>
<li>raygun&#160;:&#160;<a class="el" href="class_lua_1_1_player.html#a39905d8dbddc9f80365df2822a1a093c">Lua.Player</a></li>
<li>right&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#afa7cbcc49408b1a75564f5d379c877ac">Lua.Transform</a>, <a class="el" href="class_lua_1_1_vector3.html#ae8a79f66fb993fb1ad05c81fd3bbb9e3">Lua.Vector3</a></li>
<li>rightFoot&#160;:&#160;<a class="el" href="class_lua_1_1_bones.html#ae0707d3e06ec285d2d4634b26b046bbe">Lua.Bones</a>, <a class="el" href="class_lua_1_1_i_k.html#a0868dad53aa0ea22b3554edc766ee8bc">Lua.IK</a></li>
<li>rightHand&#160;:&#160;<a class="el" href="class_lua_1_1_bones.html#a33f155d4f1abdedd2c8f514fc9f768dd">Lua.Bones</a>, <a class="el" href="class_lua_1_1_i_k.html#a1321851e538f60beb7fa5d655e18ab75">Lua.IK</a></li>
<li>rightLowerArm&#160;:&#160;<a class="el" href="class_lua_1_1_bones.html#aa765e8509188018d417bfae9b678f4b7">Lua.Bones</a></li>
<li>rightLowerLeg&#160;:&#160;<a class="el" href="class_lua_1_1_bones.html#a7322bb4e06909a72de8cb7899bbdda28">Lua.Bones</a></li>
<li>rightUpperArm&#160;:&#160;<a class="el" href="class_lua_1_1_bones.html#ae5b640e142fcbe64ab63218e48bc5271">Lua.Bones</a></li>
<li>rightUpperLeg&#160;:&#160;<a class="el" href="class_lua_1_1_bones.html#a2192099d2fee781ee13c3591caa8c78c">Lua.Bones</a></li>
<li>rigidbody&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a56d48f666679b251eefa10e6f65bbb60">Lua.Entity</a></li>
<li>root&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#ac54361eab00110ecfa6d6c53ffb78533">Lua.Transform</a></li>
<li>rotation&#160;:&#160;<a class="el" href="class_lua_1_1_i_k_effector.html#adb93ca27f68dbfc04d70e0df9f285210">Lua.IKEffector</a>, <a class="el" href="class_lua_1_1_random.html#a2705c860d3280bb5e5d0593d59fad8e3">Lua.Random</a>, <a class="el" href="class_lua_1_1_rigidbody.html#a6cb1207363fce98ec04cacf8c6f776cc">Lua.Rigidbody</a>, <a class="el" href="class_lua_1_1_transform.html#ab0b5488416c3d0f6e3de7b426227198c">Lua.Transform</a></li>
<li>rotationUniform&#160;:&#160;<a class="el" href="class_lua_1_1_random.html#a99f73343c5e319aff692abcca2ca51a1">Lua.Random</a></li>
<li>rotationWeight&#160;:&#160;<a class="el" href="class_lua_1_1_i_k_effector.html#a1c101608c632fc144c0a098fc3a37986">Lua.IKEffector</a></li>
<li>runSpeed&#160;:&#160;<a class="el" href="class_lua_1_1_player.html#a37a67ff4633be4a46486ed6d34c840f4">Lua.Player</a></li>
</ul>


<h3><a id="index_s" name="index_s"></a>- s -</h3><ul>
<li>scale&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a784673c0e6fbf29381a309a5df0ee10e">Lua.Entity</a>, <a class="el" href="class_lua_1_1_player.html#aa45522a569c8559f60eb7ed78f436ca1">Lua.Player</a></li>
<li>senses&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a9a5a67b2da9b9d95e31c766aa68760ee">Lua.Entity</a></li>
<li>shooting&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#af00214fc6ff19d22818d8d0810630cea">Lua.Entity</a></li>
<li>sizeChangeSpeed&#160;:&#160;<a class="el" href="class_lua_1_1_player.html#aeb994edf649d41658544101518a774db">Lua.Player</a></li>
<li>spatialBlend&#160;:&#160;<a class="el" href="class_lua_1_1_audio_source.html#a88e74fc4c2c4cf17747d6bbdad516a8f">Lua.AudioSource</a></li>
<li>speed&#160;:&#160;<a class="el" href="class_lua_1_1_movement.html#a72e61b8d3a308361d8bb6d80367af2df">Lua.Movement</a></li>
<li>speedMultiplier&#160;:&#160;<a class="el" href="class_lua_1_1_animation.html#a4418e6d09f625ad76932922c568bf987">Lua.Animation</a></li>
<li>spine&#160;:&#160;<a class="el" href="class_lua_1_1_bones.html#a28da7e69ef1c810bd255236404ad039a">Lua.Bones</a></li>
<li>sprintSpeed&#160;:&#160;<a class="el" href="class_lua_1_1_player.html#a41256b9ca25487dc16cc3234697bdcb0">Lua.Player</a></li>
<li>sqrMagnitude&#160;:&#160;<a class="el" href="class_lua_1_1_vector3.html#a29cb25eb011222ce12a27338eb3aa0a2">Lua.Vector3</a></li>
<li>superFlySpeed&#160;:&#160;<a class="el" href="class_lua_1_1_player.html#a89f50497773ef2a3998950a1f20b2cd8">Lua.Player</a></li>
</ul>


<h3><a id="index_t" name="index_t"></a>- t -</h3><ul>
<li>Text&#160;:&#160;<a class="el" href="class_lua_1_1_game_1_1_version.html#ae0129c5ed39c108d4d6f9a5935c34517">Lua.Game.Version</a></li>
<li>time&#160;:&#160;<a class="el" href="class_lua_1_1_time.html#a6a7753473015073c35d5ae5bc4edfdf3">Lua.Time</a></li>
<li>timeSinceLevelLoad&#160;:&#160;<a class="el" href="class_lua_1_1_time.html#af1a087ea59af5ee339aa26ae49c13370">Lua.Time</a></li>
<li>transform&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a51edf8c42bd2acb730ae73d045341320">Lua.Entity</a></li>
<li>transitionDuration&#160;:&#160;<a class="el" href="class_lua_1_1_animation.html#ad1f57da869e710f55a79e82ceb579cc0">Lua.Animation</a></li>
</ul>


<h3><a id="index_u" name="index_u"></a>- u -</h3><ul>
<li>up&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#a98b72263be2f13a2917369c22b8539f3">Lua.Transform</a>, <a class="el" href="class_lua_1_1_vector3.html#a63cc0e06476f86bd603c0a56bfa09fae">Lua.Vector3</a></li>
<li>useGravity&#160;:&#160;<a class="el" href="class_lua_1_1_rigidbody.html#a3838f1418140279bcec4d7a2f8ebbae2">Lua.Rigidbody</a></li>
</ul>


<h3><a id="index_v" name="index_v"></a>- v -</h3><ul>
<li>value&#160;:&#160;<a class="el" href="class_lua_1_1_random.html#a058b72bbd12cd2d665b14d0479b52ad7">Lua.Random</a></li>
<li>velocity&#160;:&#160;<a class="el" href="class_lua_1_1_rigidbody.html#abbc468f41391b7d34120f11f3f39b6fb">Lua.Rigidbody</a></li>
<li>volume&#160;:&#160;<a class="el" href="class_lua_1_1_audio_source.html#a6e631df6c296491e5a29c8025878ddb4">Lua.AudioSource</a></li>
</ul>


<h3><a id="index_w" name="index_w"></a>- w -</h3><ul>
<li>w&#160;:&#160;<a class="el" href="class_lua_1_1_quaternion.html#a4a66f5c598907b4d906b0c5dd0e28526">Lua.Quaternion</a></li>
<li>walkSpeed&#160;:&#160;<a class="el" href="class_lua_1_1_player.html#a7176133efd6891f96cfa86f67faef97b">Lua.Player</a></li>
<li>width&#160;:&#160;<a class="el" href="class_lua_1_1_screen.html#ae44386bf8759e8f85e04358297f3dd95">Lua.Screen</a></li>
<li>worldCenterOfMass&#160;:&#160;<a class="el" href="class_lua_1_1_rigidbody.html#a23530e52ed361ac1f758e8204a1c833b">Lua.Rigidbody</a></li>
</ul>


<h3><a id="index_x" name="index_x"></a>- x -</h3><ul>
<li>x&#160;:&#160;<a class="el" href="class_lua_1_1_quaternion.html#aefb405b7fafa79708a6d8120781debce">Lua.Quaternion</a>, <a class="el" href="class_lua_1_1_vector3.html#af2367a6c9fb9484cd8703ef20bbe3e2b">Lua.Vector3</a></li>
</ul>


<h3><a id="index_y" name="index_y"></a>- y -</h3><ul>
<li>y&#160;:&#160;<a class="el" href="class_lua_1_1_quaternion.html#ab7eb002a81cfc537a9c3afc8965ef2ec">Lua.Quaternion</a>, <a class="el" href="class_lua_1_1_vector3.html#a93844cc4b95c4b4cc2152ecd3d6a69ed">Lua.Vector3</a></li>
</ul>


<h3><a id="index_z" name="index_z"></a>- z -</h3><ul>
<li>z&#160;:&#160;<a class="el" href="class_lua_1_1_quaternion.html#ac26c0a2710dd86783dee62c8645ee55c">Lua.Quaternion</a>, <a class="el" href="class_lua_1_1_vector3.html#a80737d9f0e18357fd716e47a1b82ef6a">Lua.Vector3</a></li>
<li>zero&#160;:&#160;<a class="el" href="class_lua_1_1_vector3.html#aa45f0524375ebdaeb4244325ffb08210">Lua.Vector3</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_shooting.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle"><div class="title">Lua.Shooting Member List</div></div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="class_lua_1_1_shooting.html">Lua.Shooting</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_shooting.html#af30c3a5b6caca44684fcd88ac7f7f4a7">accurateFiring</a></td><td class="entry"><a class="el" href="class_lua_1_1_shooting.html">Lua.Shooting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_shooting.html#a6ddbbd79abebeb560511e7c00093fb1a">burstFire</a></td><td class="entry"><a class="el" href="class_lua_1_1_shooting.html">Lua.Shooting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_shooting.html#a51b27960d8f74262edb79dc173f75670">burstFireInterval</a></td><td class="entry"><a class="el" href="class_lua_1_1_shooting.html">Lua.Shooting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_shooting.html#a41ac94c6ba5c407bd0fcbf0f6e938fd1">burstFireRounds</a></td><td class="entry"><a class="el" href="class_lua_1_1_shooting.html">Lua.Shooting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_shooting.html#aece1867c834c0c4fc31af53c55c8040e">firingInterval</a></td><td class="entry"><a class="el" href="class_lua_1_1_shooting.html">Lua.Shooting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_shooting.html#a8bd9055060bba675e803483c5a1f71d2">FixGunAimingOrientation</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_shooting.html">Lua.Shooting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_shooting.html#a7f8c04173b4649a5b6134344e67dc10a">inaccuracyFactor</a></td><td class="entry"><a class="el" href="class_lua_1_1_shooting.html">Lua.Shooting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_shooting.html#a0c512eac7dea63f8594bbfcb0976a34a">isAiming</a></td><td class="entry"><a class="el" href="class_lua_1_1_shooting.html">Lua.Shooting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_shooting.html#a0b4d9622c4a1888f424ebdc13db498d1">isFiring</a></td><td class="entry"><a class="el" href="class_lua_1_1_shooting.html">Lua.Shooting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_shooting.html#adc410fd560cfdc7bdbd5b870a7adedbf">predictiveAiming</a></td><td class="entry"><a class="el" href="class_lua_1_1_shooting.html">Lua.Shooting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_shooting.html#af0e7769dd39d32787d3197ebee1a3247">SetBurstFire</a>(bool enable)</td><td class="entry"><a class="el" href="class_lua_1_1_shooting.html">Lua.Shooting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_shooting.html#af28162d50b374775ee7618d1cc6c2063">SetFiringSFX</a>(string clip)</td><td class="entry"><a class="el" href="class_lua_1_1_shooting.html">Lua.Shooting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_shooting.html#adbbd67fdfd65fd5d754e66c44907c974">SetProjectileColor</a>(int r, int g, int b)</td><td class="entry"><a class="el" href="class_lua_1_1_shooting.html">Lua.Shooting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_shooting.html#aa89af0a3474a9f2ac09e3305986df10d">SetProjectileImpactSFX</a>(string clip)</td><td class="entry"><a class="el" href="class_lua_1_1_shooting.html">Lua.Shooting</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_shooting.html#a376edd82b6c42daab355831d5ecbc340">SetProjectileScale</a>(float scaleMult)</td><td class="entry"><a class="el" href="class_lua_1_1_shooting.html">Lua.Shooting</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_shooting.html#a916d6868d5ab051898ea2f7d676587aa">SetProjectileSpeed</a>(float speedMult)</td><td class="entry"><a class="el" href="class_lua_1_1_shooting.html">Lua.Shooting</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

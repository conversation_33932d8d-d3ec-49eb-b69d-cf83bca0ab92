SizeboxZ = RegisterBehavior("SizeboxZ")
SizeboxZ.data = {
    menuEntry = "Game Modes/Sizebox Z",
    secondary = true,
    flags = { BEHAVIOR_FLAG },
    agent = {
        type = { "micro"}
    },
    target = {
        type = {"oneself"}
    },
    settings = {
        { "use_mouse_controls", "Use Mouse Combat Controls?", "bool", true },
        { "light_attack_key", "Light Attack", "keybind", "q"},
        { "strong_attack_key", "Light Attack", "keybind", "r"},
        { "target_key", "Toggle Targetting", "keybind", "f" },

    }
}
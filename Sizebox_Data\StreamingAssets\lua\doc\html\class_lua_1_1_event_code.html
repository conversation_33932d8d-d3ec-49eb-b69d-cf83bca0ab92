<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.13"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.EventCode Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
</script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.13 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('class_lua_1_1_event_code.html','');});
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="class_lua_1_1_event_code-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">Lua.EventCode Class Reference</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:af0028cf602b24a1865397d0cf1df1623"><td class="memItemLeft" align="right" valign="top">static string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_event_code.html#af0028cf602b24a1865397d0cf1df1623">OnCrush</a> = &quot;OnCrush&quot;</td></tr>
<tr class="memdesc:af0028cf602b24a1865397d0cf1df1623"><td class="mdescLeft">&#160;</td><td class="mdescRight">Giantess crushing micro event.  <a href="#af0028cf602b24a1865397d0cf1df1623">More...</a><br /></td></tr>
<tr class="separator:af0028cf602b24a1865397d0cf1df1623"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adba8e0cdbec45f5694b0be4ea1fbfe7a"><td class="memItemLeft" align="right" valign="top">static string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_event_code.html#adba8e0cdbec45f5694b0be4ea1fbfe7a">OnStep</a> = &quot;OnStep&quot;</td></tr>
<tr class="memdesc:adba8e0cdbec45f5694b0be4ea1fbfe7a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Giantess footstep event.  <a href="#adba8e0cdbec45f5694b0be4ea1fbfe7a">More...</a><br /></td></tr>
<tr class="separator:adba8e0cdbec45f5694b0be4ea1fbfe7a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a30395eacc4a00fc49a4311b95d23727d"><td class="memItemLeft" align="right" valign="top">static string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_event_code.html#a30395eacc4a00fc49a4311b95d23727d">OnSpawn</a> = &quot;OnSpawn&quot;</td></tr>
<tr class="memdesc:a30395eacc4a00fc49a4311b95d23727d"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="class_lua_1_1_entity.html" title="A Entity represents Characters and Objects ">Entity</a> spawned event.  <a href="#a30395eacc4a00fc49a4311b95d23727d">More...</a><br /></td></tr>
<tr class="separator:a30395eacc4a00fc49a4311b95d23727d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a48f2f42f01d3d763b55233e1e9c1047c"><td class="memItemLeft" align="right" valign="top">static string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_event_code.html#a48f2f42f01d3d763b55233e1e9c1047c">OnActionComplete</a> = &quot;OnActionComplete&quot;</td></tr>
<tr class="memdesc:a48f2f42f01d3d763b55233e1e9c1047c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Action complete event.  <a href="#a48f2f42f01d3d763b55233e1e9c1047c">More...</a><br /></td></tr>
<tr class="separator:a48f2f42f01d3d763b55233e1e9c1047c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Data Documentation</h2>
<a id="a48f2f42f01d3d763b55233e1e9c1047c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a48f2f42f01d3d763b55233e1e9c1047c">&#9670;&nbsp;</a></span>OnActionComplete</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">string Lua.EventCode.OnActionComplete = &quot;OnActionComplete&quot;</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Action complete event. </p>
<p>Listeners will be given following data:</p><ul>
<li><code>agent</code> - action entity</li>
<li><code>action</code> - action name </li>
</ul>

</div>
</div>
<a id="af0028cf602b24a1865397d0cf1df1623"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af0028cf602b24a1865397d0cf1df1623">&#9670;&nbsp;</a></span>OnCrush</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">string Lua.EventCode.OnCrush = &quot;OnCrush&quot;</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Giantess crushing micro event. </p>
<p>Listeners will be given following data:</p><ul>
<li><code>victim</code> - crushed micro entity</li>
<li><code>crusher</code> - crushing giantess entity </li>
</ul>

</div>
</div>
<a id="a30395eacc4a00fc49a4311b95d23727d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a30395eacc4a00fc49a4311b95d23727d">&#9670;&nbsp;</a></span>OnSpawn</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">string Lua.EventCode.OnSpawn = &quot;OnSpawn&quot;</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p><a class="el" href="class_lua_1_1_entity.html" title="A Entity represents Characters and Objects ">Entity</a> spawned event. </p>
<p>Listeners will be given following data:</p><ul>
<li><code>entity</code> - spawned entity </li>
</ul>

</div>
</div>
<a id="adba8e0cdbec45f5694b0be4ea1fbfe7a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adba8e0cdbec45f5694b0be4ea1fbfe7a">&#9670;&nbsp;</a></span>OnStep</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">string Lua.EventCode.OnStep = &quot;OnStep&quot;</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Giantess footstep event. </p>
<p>Listeners will be given following data:</p><ul>
<li><code>gts</code> - giantess entity</li>
<li><code>position</code> - position of the step epicenter (vector)</li>
<li><code>magnitude</code> - force of the step (float)</li>
<li><code>foot</code> - foot numer (0 - left, 1 - right) </li>
</ul>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>EventCode.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_event_code.html">EventCode</a></li>
    <li class="footer">Generated by
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.13 </li>
  </ul>
</div>
</body>
</html>

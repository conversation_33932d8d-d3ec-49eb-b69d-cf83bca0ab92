<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.AI Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_a_i.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="class_lua_1_1_a_i-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.AI Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Controls the <a class="el" href="class_lua_1_1_a_i.html" title="Controls the AI of humanoid agent.">AI</a> of humanoid agent.  
 <a href="class_lua_1_1_a_i.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:ab29a427f16c210c0839771b2552b21ce"><td class="memItemLeft" align="right" valign="top"><a id="ab29a427f16c210c0839771b2552b21ce" name="ab29a427f16c210c0839771b2552b21ce"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>StopAction</b> ()</td></tr>
<tr class="memdesc:ab29a427f16c210c0839771b2552b21ce"><td class="mdescLeft">&#160;</td><td class="mdescRight">Stop all actions, including the current one. <br /></td></tr>
<tr class="separator:ab29a427f16c210c0839771b2552b21ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acae3b8822867d658c5dc7c409af76e11"><td class="memItemLeft" align="right" valign="top"><a id="acae3b8822867d658c5dc7c409af76e11" name="acae3b8822867d658c5dc7c409af76e11"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>CancelQueuedActions</b> ()</td></tr>
<tr class="memdesc:acae3b8822867d658c5dc7c409af76e11"><td class="mdescLeft">&#160;</td><td class="mdescRight">Will cancel all future actions, except the current one. <br /></td></tr>
<tr class="separator:acae3b8822867d658c5dc7c409af76e11"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1c8effd2d78be1f69eb84e3db9b02b0f"><td class="memItemLeft" align="right" valign="top"><a id="a1c8effd2d78be1f69eb84e3db9b02b0f" name="a1c8effd2d78be1f69eb84e3db9b02b0f"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>CancelQueuedBehaviors</b> ()</td></tr>
<tr class="memdesc:a1c8effd2d78be1f69eb84e3db9b02b0f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Will cancel all future behaviors, except the current one. <br /></td></tr>
<tr class="separator:a1c8effd2d78be1f69eb84e3db9b02b0f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7d2bab4d5dfc0389ab5eeabed6befad7"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_a_i.html#a7d2bab4d5dfc0389ab5eeabed6befad7">HasQueuedActions</a> ()</td></tr>
<tr class="memdesc:a7d2bab4d5dfc0389ab5eeabed6befad7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if the agent has queued future actions.  <a href="class_lua_1_1_a_i.html#a7d2bab4d5dfc0389ab5eeabed6befad7">More...</a><br /></td></tr>
<tr class="separator:a7d2bab4d5dfc0389ab5eeabed6befad7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a51825e643d119b005db422ef6f51d353"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_a_i.html#a51825e643d119b005db422ef6f51d353">HasQueuedBehaviors</a> ()</td></tr>
<tr class="memdesc:a51825e643d119b005db422ef6f51d353"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if the agent has queued future behaviors.  <a href="class_lua_1_1_a_i.html#a51825e643d119b005db422ef6f51d353">More...</a><br /></td></tr>
<tr class="separator:a51825e643d119b005db422ef6f51d353"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa05b26304b734cfe6f5a220bb54d9bc7"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_a_i.html#aa05b26304b734cfe6f5a220bb54d9bc7">IsActionActive</a> ()</td></tr>
<tr class="memdesc:aa05b26304b734cfe6f5a220bb54d9bc7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if the agent is doing any action, or has queued future actions.  <a href="class_lua_1_1_a_i.html#aa05b26304b734cfe6f5a220bb54d9bc7">More...</a><br /></td></tr>
<tr class="separator:aa05b26304b734cfe6f5a220bb54d9bc7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2d682616c9d7a8fd7ea045906a716e02"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_a_i.html#a2d682616c9d7a8fd7ea045906a716e02">IsBehaviorActive</a> ()</td></tr>
<tr class="memdesc:a2d682616c9d7a8fd7ea045906a716e02"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if the entity is currently executing a main behavior.  <a href="class_lua_1_1_a_i.html#a2d682616c9d7a8fd7ea045906a716e02">More...</a><br /></td></tr>
<tr class="separator:a2d682616c9d7a8fd7ea045906a716e02"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa30ed6fc0195cd828d3f5971b80ae053"><td class="memItemLeft" align="right" valign="top"><a id="aa30ed6fc0195cd828d3f5971b80ae053" name="aa30ed6fc0195cd828d3f5971b80ae053"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>StopBehavior</b> ()</td></tr>
<tr class="memdesc:aa30ed6fc0195cd828d3f5971b80ae053"><td class="mdescLeft">&#160;</td><td class="mdescRight">Will stop the current main behavior. It will trigger the Behavior:Exit() method. For stopping secondary behaviors use <a class="el" href="class_lua_1_1_a_i.html#a8c9a883b10e07fe120ff68d75391fe2b" title="Will stop a secondary behavior having a specified flag. It will trigger the Behavior:Exit() method....">AI.StopSecondaryBehavior</a>. <br /></td></tr>
<tr class="separator:aa30ed6fc0195cd828d3f5971b80ae053"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8c9a883b10e07fe120ff68d75391fe2b"><td class="memItemLeft" align="right" valign="top"><a id="a8c9a883b10e07fe120ff68d75391fe2b" name="a8c9a883b10e07fe120ff68d75391fe2b"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>StopSecondaryBehavior</b> (string flag)</td></tr>
<tr class="memdesc:a8c9a883b10e07fe120ff68d75391fe2b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Will stop a secondary behavior having a specified flag. It will trigger the Behavior:Exit() method. For stopping main behavior use <a class="el" href="class_lua_1_1_a_i.html#aa30ed6fc0195cd828d3f5971b80ae053" title="Will stop the current main behavior. It will trigger the Behavior:Exit() method. For stopping seconda...">AI.StopBehavior</a>. <br /></td></tr>
<tr class="separator:a8c9a883b10e07fe120ff68d75391fe2b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af829734ccdb97c9440ebabba2868ee05"><td class="memItemLeft" align="right" valign="top"><a id="af829734ccdb97c9440ebabba2868ee05" name="af829734ccdb97c9440ebabba2868ee05"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>DisableAI</b> ()</td></tr>
<tr class="memdesc:af829734ccdb97c9440ebabba2868ee05"><td class="mdescLeft">&#160;</td><td class="mdescRight">Disables the <a class="el" href="class_lua_1_1_a_i.html" title="Controls the AI of humanoid agent.">AI</a> Decision Maker, the agent will only accept commands, by the menu, or by the other scrips. <br /></td></tr>
<tr class="separator:af829734ccdb97c9440ebabba2868ee05"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abdd5b0d3983359ec46851cd99bdd2cb2"><td class="memItemLeft" align="right" valign="top"><a id="abdd5b0d3983359ec46851cd99bdd2cb2" name="abdd5b0d3983359ec46851cd99bdd2cb2"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>EnableAI</b> ()</td></tr>
<tr class="memdesc:abdd5b0d3983359ec46851cd99bdd2cb2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Enables the <a class="el" href="class_lua_1_1_a_i.html" title="Controls the AI of humanoid agent.">AI</a> Decision Maker, the agent will automatically choose another behavior once the current one finishes. <br /></td></tr>
<tr class="separator:abdd5b0d3983359ec46851cd99bdd2cb2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afbbbf8be061465aba93a7b1c73402ae4"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_a_i.html#afbbbf8be061465aba93a7b1c73402ae4">IsAIEnabled</a> ()</td></tr>
<tr class="memdesc:afbbbf8be061465aba93a7b1c73402ae4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if the <a class="el" href="class_lua_1_1_a_i.html" title="Controls the AI of humanoid agent.">AI</a> Decision Maker is enabled. The <a class="el" href="class_lua_1_1_a_i.html" title="Controls the AI of humanoid agent.">AI</a> Decision Maker will automatically choose another behavior for the agent once the current one finishes.  <a href="class_lua_1_1_a_i.html#afbbbf8be061465aba93a7b1c73402ae4">More...</a><br /></td></tr>
<tr class="separator:afbbbf8be061465aba93a7b1c73402ae4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2ab87c868530bf4f91fb44374cd58337"><td class="memItemLeft" align="right" valign="top"><a id="a2ab87c868530bf4f91fb44374cd58337" name="a2ab87c868530bf4f91fb44374cd58337"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>SetBehavior</b> (string name)</td></tr>
<tr class="memdesc:a2ab87c868530bf4f91fb44374cd58337"><td class="mdescLeft">&#160;</td><td class="mdescRight">Change the current behavior for another one. The name must the string passed to RegisterBehavior. <br /></td></tr>
<tr class="separator:a2ab87c868530bf4f91fb44374cd58337"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9555e89507ac0b07384beab7238dc419"><td class="memItemLeft" align="right" valign="top"><a id="a9555e89507ac0b07384beab7238dc419" name="a9555e89507ac0b07384beab7238dc419"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>SetBehavior</b> (string name, <a class="el" href="class_lua_1_1_entity.html">Entity</a> target)</td></tr>
<tr class="memdesc:a9555e89507ac0b07384beab7238dc419"><td class="mdescLeft">&#160;</td><td class="mdescRight">Change the current behavior for another one. The name must the string passed to RegisterBehavior. <br /></td></tr>
<tr class="separator:a9555e89507ac0b07384beab7238dc419"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7e50aee44b076aa5c37535dcc55b2c55"><td class="memItemLeft" align="right" valign="top"><a id="a7e50aee44b076aa5c37535dcc55b2c55" name="a7e50aee44b076aa5c37535dcc55b2c55"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>SetBehavior</b> (string name, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> position)</td></tr>
<tr class="memdesc:a7e50aee44b076aa5c37535dcc55b2c55"><td class="mdescLeft">&#160;</td><td class="mdescRight">Change the current behavior for another one. The position will act as the cursor.position. <br /></td></tr>
<tr class="separator:a7e50aee44b076aa5c37535dcc55b2c55"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a60b9147746afd2cbbdf7e6ee5f7c6aff"><td class="memItemLeft" align="right" valign="top"><a id="a60b9147746afd2cbbdf7e6ee5f7c6aff" name="a60b9147746afd2cbbdf7e6ee5f7c6aff"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>QueueBehavior</b> (string name)</td></tr>
<tr class="memdesc:a60b9147746afd2cbbdf7e6ee5f7c6aff"><td class="mdescLeft">&#160;</td><td class="mdescRight">Add a behavior to behavior queue. The name must the string passed to RegisterBehavior. <br /></td></tr>
<tr class="separator:a60b9147746afd2cbbdf7e6ee5f7c6aff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af74652cc45af0d8a62bcd75caa595ea5"><td class="memItemLeft" align="right" valign="top"><a id="af74652cc45af0d8a62bcd75caa595ea5" name="af74652cc45af0d8a62bcd75caa595ea5"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>QueueBehavior</b> (string name, <a class="el" href="class_lua_1_1_entity.html">Entity</a> target)</td></tr>
<tr class="memdesc:af74652cc45af0d8a62bcd75caa595ea5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Add a behavior to behavior queue. The name must the string passed to RegisterBehavior. <br /></td></tr>
<tr class="separator:af74652cc45af0d8a62bcd75caa595ea5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad04e867b1b20eefaa0f4d60af5a9422c"><td class="memItemLeft" align="right" valign="top"><a id="ad04e867b1b20eefaa0f4d60af5a9422c" name="ad04e867b1b20eefaa0f4d60af5a9422c"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>QueueBehavior</b> (string name, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> position)</td></tr>
<tr class="memdesc:ad04e867b1b20eefaa0f4d60af5a9422c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Add a behavior to behavior queue. The position will act as the cursor.position. <br /></td></tr>
<tr class="separator:ad04e867b1b20eefaa0f4d60af5a9422c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >Controls the <a class="el" href="class_lua_1_1_a_i.html" title="Controls the AI of humanoid agent.">AI</a> of humanoid agent. </p>
<p >This component will only exists if the current entity is the type humanoid and is controllable for the computer.</p>
<p >The internal <a class="el" href="class_lua_1_1_a_i.html" title="Controls the AI of humanoid agent.">AI</a> of characters in divided into 3 levels.</p>
<p >Actions: is the most simple, and is composed of a single action like <a class="el" href="class_lua_1_1_entity.html#a0a8058c8b504215e492471b4e7d557d4" title="If the entity is a giantess, she will stomp a target entity.">Entity.Stomp</a>, <a class="el" href="class_lua_1_1_entity.html#a5d6cfb68967adf948db2b6c09d7dfd38" title="The entity will walk to a designed point.">Entity.MoveTo</a>, <a class="el" href="class_lua_1_1_entity.html#a92feb21c4219c60a7e5935733302083f" title="Adds a priority Grow action at the beginning of the action queue.">Entity.Grow</a>. They are added to a queue that manages the sequences of actions.</p>
<p >Behaviors: Those are sequences of actions, but also can include custom scripting as well. Those are scripted in .lua files. One agent can do only one behavior at the time. There is the possibilty to queue multiple behaviors.</p>
<p >DecisionMaker: This is internal to the engine. Their function is to choose between multiple behaviors depending in some conditions. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a7d2bab4d5dfc0389ab5eeabed6befad7" name="a7d2bab4d5dfc0389ab5eeabed6befad7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7d2bab4d5dfc0389ab5eeabed6befad7">&#9670;&nbsp;</a></span>HasQueuedActions()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.AI.HasQueuedActions </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns true if the agent has queued future actions. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a51825e643d119b005db422ef6f51d353" name="a51825e643d119b005db422ef6f51d353"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a51825e643d119b005db422ef6f51d353">&#9670;&nbsp;</a></span>HasQueuedBehaviors()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.AI.HasQueuedBehaviors </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns true if the agent has queued future behaviors. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="aa05b26304b734cfe6f5a220bb54d9bc7" name="aa05b26304b734cfe6f5a220bb54d9bc7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa05b26304b734cfe6f5a220bb54d9bc7">&#9670;&nbsp;</a></span>IsActionActive()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.AI.IsActionActive </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns true if the agent is doing any action, or has queued future actions. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="afbbbf8be061465aba93a7b1c73402ae4" name="afbbbf8be061465aba93a7b1c73402ae4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afbbbf8be061465aba93a7b1c73402ae4">&#9670;&nbsp;</a></span>IsAIEnabled()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.AI.IsAIEnabled </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns true if the <a class="el" href="class_lua_1_1_a_i.html" title="Controls the AI of humanoid agent.">AI</a> Decision Maker is enabled. The <a class="el" href="class_lua_1_1_a_i.html" title="Controls the AI of humanoid agent.">AI</a> Decision Maker will automatically choose another behavior for the agent once the current one finishes. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a2d682616c9d7a8fd7ea045906a716e02" name="a2d682616c9d7a8fd7ea045906a716e02"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2d682616c9d7a8fd7ea045906a716e02">&#9670;&nbsp;</a></span>IsBehaviorActive()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.AI.IsBehaviorActive </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns true if the entity is currently executing a main behavior. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaAI.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_a_i.html">AI</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

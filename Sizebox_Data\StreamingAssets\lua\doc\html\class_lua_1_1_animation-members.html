<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_animation.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle"><div class="title">Lua.Animation Member List</div></div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="class_lua_1_1_animation.html">Lua.Animation</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_animation.html#a1002a4f745d48a64fd26affa514ee0d2">AnimationExists</a>(string animationName)</td><td class="entry"><a class="el" href="class_lua_1_1_animation.html">Lua.Animation</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_animation.html#afa4d0d4dc374917da05e5c26e75c20b0">Get</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_animation.html">Lua.Animation</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_animation.html#a8a1938fee4f3fc1967112c49c26105b3">GetAnimationList</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_animation.html">Lua.Animation</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_animation.html#ad91c4ef8fcb303877802e1e38fd27b85">GetGlobalSpeed</a>(float speed)</td><td class="entry"><a class="el" href="class_lua_1_1_animation.html">Lua.Animation</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_animation.html#a8da88aefb747e3128bcbf35be8451b21">GetLength</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_animation.html">Lua.Animation</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_animation.html#af7313d3ee3fd378b8136d8f72f1d90a1">GetPoseList</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_animation.html">Lua.Animation</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_animation.html#ae9196e188d96824f23cf65b1f835aa1a">GetProgress</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_animation.html">Lua.Animation</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_animation.html#ac615b08b06a84330cddb327e6d28f6c9">GetSpeed</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_animation.html">Lua.Animation</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_animation.html#ad7c23e47dae3b3c354d317724e93e887">GetTime</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_animation.html">Lua.Animation</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_animation.html#afd07956e9f1dc6f551d8ca036493a646">IsCompleted</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_animation.html">Lua.Animation</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_animation.html#aede5bb0940e1daed76c816ba30dac6f2">IsInPose</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_animation.html">Lua.Animation</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_animation.html#a6f6e8dabc438a05a0338f69a25a61d71">IsInTransition</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_animation.html">Lua.Animation</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_animation.html#a49aa08ef58b67f1af48526781176fce3">maxSpeed</a></td><td class="entry"><a class="el" href="class_lua_1_1_animation.html">Lua.Animation</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_animation.html#a7dee7e3d2e6bdffed96f20822e4bb307">minSpeed</a></td><td class="entry"><a class="el" href="class_lua_1_1_animation.html">Lua.Animation</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_animation.html#a7826d8216e7d202f625e697341ae62fc">PoseExists</a>(string poseName)</td><td class="entry"><a class="el" href="class_lua_1_1_animation.html">Lua.Animation</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_animation.html#a27ca2f8a6b74867c4727315bbce3878f">Set</a>(string animationName)</td><td class="entry"><a class="el" href="class_lua_1_1_animation.html">Lua.Animation</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_animation.html#afbab459005c0736f8b52459185cf1637">SetAndWait</a>(string animationName)</td><td class="entry"><a class="el" href="class_lua_1_1_animation.html">Lua.Animation</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_animation.html#af1f4b9f2d2a3e595a08a7f5e5d095580">SetGlobalSpeed</a>(float speed)</td><td class="entry"><a class="el" href="class_lua_1_1_animation.html">Lua.Animation</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_animation.html#ad3fe96a39d87c9f3b1121fbbd05f61ea">SetPose</a>(string poseName)</td><td class="entry"><a class="el" href="class_lua_1_1_animation.html">Lua.Animation</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_animation.html#aefdc9f4c78bada7dacfdb39d07c4b576">SetSpeed</a>(float speed)</td><td class="entry"><a class="el" href="class_lua_1_1_animation.html">Lua.Animation</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_animation.html#a4418e6d09f625ad76932922c568bf987">speedMultiplier</a></td><td class="entry"><a class="el" href="class_lua_1_1_animation.html">Lua.Animation</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_animation.html#ad1f57da869e710f55a79e82ceb579cc0">transitionDuration</a></td><td class="entry"><a class="el" href="class_lua_1_1_animation.html">Lua.Animation</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

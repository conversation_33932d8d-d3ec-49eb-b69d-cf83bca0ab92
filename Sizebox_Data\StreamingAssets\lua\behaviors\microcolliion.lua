-- Create a new behavior
local DisableCollisionAndFreeze = RegisterBehavior("DisableCollisionAndFreeze")

-- Define the behavior's data
DisableCollisionAndFreeze.data = {
  -- Menu entry for the behavior
  menuEntry = "Model/Disable Collision and Freeze",
  
  -- Flags for the behavior
  flags = { "disable_collision_and_freeze" },
  
  -- Agent type for the behavior
  agent = {
    type = { "model" }
  },
  
  -- Target type for the behavior
  target = {
    type = { "oneself" }
  },
  
  -- Settings for the behavior
  settings = {}
}

-- Function to toggle the collision and freezing
local function toggleCollisionAndFreeze(model)
  -- Check if the model exists
  if model then
    -- Toggle collision and freezing
    model.Collision = not model.Collision
    model.Anchored = not model.Anchored
    model.CanCollide = not model.Anchored

    -- Update the position and rotation if the model is being frozen
    if model.Anchored then
      local position = model.Position
      local rotation = model.Rotation

      -- Create a function to reset the position and rotation
      local function resetPositionAndRotation()
        model.Position = position
        model.Rotation = rotation
      end

      -- Connect the function to the position and rotation changed signals
      model:GetPropertyChangedSignal("Position"):Connect(resetPositionAndRotation)
      model:GetPropertyChangedSignal("Rotation"):Connect(resetPositionAndRotation)
    else
      -- Disconnect the position and rotation signals if the model is being unfrozen
      for _, signal in pairs(model:GetPropertyChangedSignals()) do
        if signal.Name == "Position" or signal.Name == "Rotation" then
          signal:Disconnect()
        end
      end
    end
  else
    warn("Model not found")
  end
end

-- Function to run when the behavior is activated
local function onActivate(model)
  toggleCollisionAndFreeze(model)
end

-- Function to run when the behavior is deactivated
local function onDeactivate(model)
  toggleCollisionAndFreeze(model)
end

-- Connect the functions to the behavior's events
DisableCollisionAndFreeze.onActivate = onActivate
DisableCollisionAndFreeze.onDeactivate = onDeactivate

-- CONSTANTS
local BEHAVIOR_FLAG = "Body Edit"
local MIN_SCALE_SPEED = 0.001


-- REGISTER BODY GROWTH
BodyEditLite = RegisterBehavior("BodyEditLite")
BodyEditLite.data = {
    menuEntry = "Body/Edit (Lite)",
    secondary = true,
    flags = { BEHAVIOR_FLAG },
    agent = {
        type = { "humanoid"}
    },
    target = {
        type = {"oneself"}
    },
    settings = {

    	{ "all_up", "+Scale Key", "string", "[6]" },
		{ "all_down", "-Scale Key", "string", "[4]" },

		{ "x_up", "+X Key", "string", "[7]" },
		{ "x_down", "-X Key", "string", "[1]" },

		{ "y_up", "+Y Key", "string", "[8]" },
		{ "y_down", "-Y Key", "string", "[2]" },

		{ "z_up", "+Z Key", "string", "[9]" },
		{ "z_down", "-<PERSON> <PERSON>", "string", "[3]" },

		{ "next_bone_key", "Next Bone", "string", "[*]" },
		{ "prev_bone_key", "Previous Bone", "string", "[/]" },

		{ "reset_key", "Reset Bone", "string", "[5]" },

		{ "speed_up", "Increase Scale Speed", "string", "[+]" },
		{ "speed_down", "Decrease Scale Speed", "string", "[-]" }
	}
}

-- The ugly start function
function BodyEditLite:Start()
	Log("Starting Body Edit Lite")
	if self.agent.dict["body_edit_tainted"] then
		Log("Cannot start Body Edit Lite")
		Log("This model's skeleton has been edited by the full Body Edit")
		self.agent.ai.StopSecondaryBehavior(BEHAVIOR_FLAG)
		return
	end

	self.scaleSpeed = MIN_SCALE_SPEED
	self.moveSpeed = MIN_SCALE_SPEED
	self.loggedSelectionError = false
	self.debug = true

	self.modelBones = {}
	self.boneCount = 0
	self.selectionIndex = 0

	local left = nil
	local right = nil

	-- FIND ALL THE BONES THAT WE CAN
	local head = nil
	local upperArms = nil
	local lowerArms = nil
	local hands = nil
	local breasts = nil	
	local spine = nil
	local hips = nil
	local upperLegs = nil
	local lowerLegs = nil
	local feet = nil
	-- FIND REGULAR BONES
		-- HEAD
		head = self.agent.bones.head

		-- HANDS
		left = self.agent.bones.leftHand
		right = self.agent.bones.rightHand
		if left and right then
			hands = {}
			hands.left = left
			hands.right = right
		end

		-- LOWER ARMS
		left = self.agent.bones.leftLowerArm
		right = self.agent.bones.rightLowerArm
		if left and right then
			lowerArms = {}
			lowerArms.left = left
			lowerArms.right = right
		end

		-- UPPER ARMS
		left = self.agent.bones.leftUpperArm
		right = self.agent.bones.rightUpperArm
		if left and right then
			upperArms = {}
			upperArms.left = left
			upperArms.right = right
		end

		-- BREASTS
		self:FindBreastBones(self.agent.transform)
		if self.leftBreast and self.rightBreast then
			breasts = {}
			breasts.left = self.leftBreast
			breasts.right = self.rightBreast
		end

		-- SPINE AND HIPS
		spine = self.agent.bones.spine
		hips = self.agent.bones.hips		--ROOT
	
		-- UPPER LEGS
		left = self.agent.bones.leftUpperLeg
		right = self.agent.bones.rightUpperLeg
		if left and right then
			upperLegs = {}
			upperLegs.left = left
			upperLegs.right = right
		end
		
		-- LOWER LEGS
		left = self.agent.bones.leftLowerLeg
		right = self.agent.bones.rightLowerLeg
		if left and right then
			lowerLegs = {}
			lowerLegs.left = left
			lowerLegs.right = right
		end

		-- FEET
		left = self.agent.bones.leftFoot
		right = self.agent.bones.rightFoot
		if left and right then
			feet = {}
			feet.left = left
			feet.right = right
		end
	-- END FIND REGULAR BONES


	local neck = nil
	local spine2 = nil
	local breastRoot = nil
	local knees = nil
	local ankles = nil
	-- FIND EXTENDED BONES

		-- NECK (FROM HEAD)
		if head then
			neck = head.parent
			if (spine and neck == spine) or (hips and neck == hips) then
				neck = nil
			end
		end

		-- SECONDARY SPINE (FROM NECK)
		if neck then
			spine2 = neck.parent
			if (spine and spine2 == spine)  or (hips and spine2 == hips) then
				spine2 = nil
			end
		end


		-- BREAST ROOT
		breastRoot = self:FindBreastRoot()
		if breastRoot then
			if (spine and breastRoot == spine) or (spine2 and breastRoot == spine2) then
				breastRoot = nil
			end
		end


		-- KNEES (FROM LOWER LEGS)
		if lowerLegs then
			knees = {}
			knees.left = lowerLegs.left.parent
			knees.right = lowerLegs.right.parent

			if upperLegs and knees.left == upperLegs.left then
				knees = nil
			end
		end

		-- ANKLES (FROM FEET)
		if feet then
			ankles = {}
			ankles.left = feet.left.parent
			ankles.right = feet.right.parent

			if lowerLegs and ankles.left == lowerLegs.left then
				ankles = nil
			end
		end
	-- END FIND EXTENDED BONES


	-- REGISTER THE BONES WE FOUND
	if head then
		self:RegisterBones(head, "Head")
	end
	if neck then
		self:RegisterBones(neck, "Neck")
	end
	if hands then
		self:RegisterBones(hands, "Hands")
	end
	if lowerArms then
		self:RegisterBones(lowerArms, "Lower Arms")
	end
	if upperArms then
		self:RegisterBones(upperArms, "Upper Arms")
	end
	if breasts then
		self:RegisterBones(breasts, "Breasts")
	end
	if breastRoot then
		self:RegisterBones(breastRoot, "Breasts Root")
	end
	if spine2 then
		self:RegisterBones(spine2, "Spine 2")
	end
	if spine then
		self:RegisterBones(spine, "Spine")
	end
	if hips then
		self.selectionIndex = self.boneCount	-- The hips is the default bone
		self:RegisterBones(hips, "Waist [Root]")
	end
	if upperLegs then
		self:RegisterBones(upperLegs, "Upper Legs")
	end
	if knees then
		self:RegisterBones(knees, "Knees")
	end
	if lowerLegs then
		self:RegisterBones(lowerLegs, "Lower Legs")
	end
	if ankles then
		self:RegisterBones(ankles, "Ankles")
	end
	if feet then
		self:RegisterBones(feet, "Feet")
	end

	-- Select the default bone
	self:SelectBone()
end

function BodyEditLite:RegisterBones(inBones, inName)
	if not inBones then
		return
	end

	if type(inBones) == "userdata" then
		local newBone = inBones
		self.modelBones[self.boneCount] = { bone = newBone, name = inName }
		self.boneCount = self.boneCount + 1
	else	
		local leftBone = inBones.left
		local rightBone = inBones.right

		self.modelBones[self.boneCount] = {}
		self.modelBones[self.boneCount].name = inName
		self.modelBones[self.boneCount].left = leftBone
		self.modelBones[self.boneCount].right = rightBone

		self.boneCount = self.boneCount + 1
	end
end

function BodyEditLite:SelectBone()
	-- CLAMP INDEX
	if self.selectionIndex >= self.boneCount then
		self.selectionIndex = 0
	elseif self.selectionIndex < 0 then
		self.selectionIndex = self.boneCount - 1
	end

	-- SELECT BONE
	local selection = self.modelBones[self.selectionIndex].bone

	-- If no selection, try to select BONES 
	if not selection then
		selection = self.modelBones[self.selectionIndex]
		if not selection then
			Log("Error, No Bone")
			return
		end
	end

	self.selected = selection
	Log("Scaling: "..self.modelBones[self.selectionIndex].name)
end

function BodyEditLite:Update()
	if self.agent.dict["body_edit_tainted"] then
		return
	end

	-- Return if I'm not the current selected entity
	if Entity.GetSelectedEntity() ~= self.agent then
		return
	end

	-- SELECTION
	if Input.GetKeyDown(self.prev_bone_key) then
		self.selectionIndex = self.selectionIndex + 1
		self:SelectBone()
	elseif Input.GetKeyDown(self.next_bone_key) then
		self.selectionIndex = self.selectionIndex - 1
		self:SelectBone()
	end

	local selection = self.selected
	-- PROCESS SCALING FOR EACH SELECTED BONE
	if type(selection) == "userdata" then
		self:ProcessScaling(selection)
	else
		self:ProcessScaling(selection.left)
		self:ProcessScaling(selection.right)
	end

end


-- RESET CODE
function resetScale(transform)
	transform.localScale = Vector3.New(1,1,1)
end
function resetPos(transform)
	transform.localPosition = Vector3.New(0,0,0)
end


function BodyEditLite:ProcessScaling(bone)
	-- SCALE SPEED
	if Input.GetKey(self.speed_up) then
		self.scaleSpeed = self.scaleSpeed + MIN_SCALE_SPEED
	elseif Input.GetKey(self.speed_down) then
		self.scaleSpeed = self.scaleSpeed - MIN_SCALE_SPEED
	end
	-- CLAMP SCALE SPEED
	if self.scaleSpeed < MIN_SCALE_SPEED then
		self.scaleSpeed = MIN_SCALE_SPEED
	end

	-- RESET
	if Input.GetKey(self.reset_key) then
		resetScale(bone)
		return
	end

	-- X AXIS
	if Input.GetKey(self.x_up) then
		scaleX(bone, self.scaleSpeed)
	elseif Input.GetKey(self.x_down) then
		scaleX(bone, -self.scaleSpeed)
	end

	-- Y AXIS
	if Input.GetKey(self.y_up) then
		scaleY(bone, self.scaleSpeed)
	elseif Input.GetKey(self.y_down) then
		scaleY(bone, -self.scaleSpeed)
	end

	-- Z AXIS
	if Input.GetKey(self.z_up) then
		scaleZ(bone, self.scaleSpeed)
	elseif Input.GetKey(self.z_down) then
		scaleZ(bone, -self.scaleSpeed)
	end

	-- ALL
	if Input.GetKey(self.all_up) then
		scaleAll(bone, self.scaleSpeed)
	elseif Input.GetKey(self.all_down) then
		scaleAll(bone, -self.scaleSpeed)
	end
end

function BodyEditLite:Exit()
    Log("Quitting Body Edit Lite")
end


-- SCALING CODE
function scaleAll(transform, factor)
	local scale = transform.localScale
	scale.x = scale.x + factor
	scale.y = scale.y + factor
	scale.z = scale.z + factor
	transform.localScale = scale
end

function scaleX(transform, factor)
	local scale = transform.localScale
	scale.x = scale.x + factor
	transform.localScale = scale
end

function scaleY(transform, factor)
	local scale = transform.localScale
	scale.y = scale.y + factor
	transform.localScale = scale
end

function scaleZ(transform, factor)
	local scale = transform.localScale
	scale.z = scale.z + factor
	transform.localScale = scale
end


local LEFT_BREAST_NAMES = { "LeftBreast", "leftbreast", "breast left", "hidarichichi", "lPectoral" }
local RIGHT_BREAST_NAMES = { "RightBreast", "rightbreast", "breast right", "migichichi", "rPectoral" }
function BodyEditLite:FindBreastBones(transform) 
    
	-- If we don't have the left breast
    if not self.leftBreast then
    	-- For each entry in LEFT_BREAST_NAMES
    	for _, leftBreastName in ipairs(LEFT_BREAST_NAMES) do
		  -- Check if the transform's name contains the entry
		  if string.match(transform.name, leftBreastName) then
		  	self.leftBreast = transform
		  	break
		  end
		end
    end

    if not self.rightBreast then
    	for _, rightBreastName in ipairs(RIGHT_BREAST_NAMES) do
		  if string.match(transform.name, rightBreastName) then
		  	self.rightBreast = transform
		  	break
		  end
		end
    end

    -- Use recursion to check each transforms child until we run out of children or find the boobs
    if not self.leftBreast or not self.rightBreast then
        local n = transform.childCount - 1
        for i=0,n do
            local child = transform.GetChild(i)
            self:FindBreastBones(child)
        end
    end
end

-- Tries to find the root bone of the breats by finding the common parent
function BodyEditLite:FindBreastRoot()
	if not self.leftBreast or not self.rightBreast then
		return self:FindBreastRootByName(self.agent.bones.hips)
	end

	local left = self.leftBreast.parent
	local right = self.rightBreast.parent

	while left ~= right and left and right do
		left = left.parent
		right = right.parent
	end

	if not left or not right then
		return nil
	end

	return left
end

-- Fall back method to find the breast root where it looks by name since no breasts are available
BREAST_ROOT_NAMES = { "Chest" }
function BodyEditLite:FindBreastRootByName(transform)
	for _, rootName in ipairs(BREAST_ROOT_NAMES) do
	  if string.match(transform.name, rootName) then
	  	return transform
	  end
	end

    -- Use recursion to check each transforms child until we run out of children or find the root
    if transform.childCount ~= 0 then
	    local n = transform.childCount - 1
	    for i=0,n do
	        local child = transform.GetChild(i)
	        local result = self:FindBreastRootByName(child)
	        if result then
	        	return result
	        end
	    end
	end

	return nil
end


-- Registers a stop button if you don't have Body Edit as well 
if not globals["body_edit"] then

	BodyEditStop = RegisterBehavior("BodyEditStop")
	BodyEditStop.data = {
	    menuEntry = "Body/Stop",
	    
	    secondary = true,
	    flags = { BEHAVIOR_FLAG },

	    agent = {
	        type = { "humanoid"}
	    },
	    target = {
	        type = {"oneself"}
	    }
	}

	-- Stops BodyEdit when the stop button is pressed
	function BodyEditStop:Start()
		self.agent.ai.StopSecondaryBehavior(BEHAVIOR_FLAG)
	end

end

globals["body_edit"] = true

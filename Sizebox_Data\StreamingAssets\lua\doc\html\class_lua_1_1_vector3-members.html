<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_vector3.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle"><div class="title">Lua.Vector3 Member List</div></div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#ab278cbf1ea74ca78089e58c0a4313c6a">Angle</a>(Vector3 from, Vector3 to)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a3742b113252e0163b006a17a76cb558c">back</a></td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#ab4ac1349372b28a8e490a94e96d950aa">ClampMagnitude</a>(Vector3 vector, float maxLength)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a7a6a85bde6d3a1072d52ebf902f291e0">Concat</a>(Vector3 o, string v)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a185d18054534f55a4ce5f951e157df96">Concat</a>(string v, Vector3 o)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#aa50462b9cd533a0b1216975d52940d5a">Concat</a>(Vector3 o1, Vector3 o2)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#aada99c9426a2253aed6e6a150efade76">Cross</a>(Vector3 lhs, Vector3 rhs)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a8c4bd7e288f7857355aff2f159e86b83">Distance</a>(Vector3 a, Vector3 b)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#aee6533ff540a011a854efbbe29664fe4">Dot</a>(Vector3 lhs, Vector3 rhs)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#adfb20fbfe1e5224b0b0fade3da0d4c8f">down</a></td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#aaa648399828fb59c6ad750f2f3ad09d6">Eq</a>(Vector3 o1, Vector3 o2)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#ad8be15240d9bfa336d926ab023f11ad4">forward</a></td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a5a08c13ed00efb9fb6728fded1e8a472">left</a></td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a2ac180084d2490e519612ccba40da454">Lerp</a>(Vector3 a, Vector3 b, float t)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a63b62cb18ab91477aee5c9bd0d975400">LerpUnclamped</a>(Vector3 a, Vector3 b, float t)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#af7a889228914dff8226722de5c47a9b7">magnitude</a></td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a3495c2bc3847d7ec201c1e4209359c25">Max</a>(Vector3 lhs, Vector3 rhs)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a514e1f8b6c974e522e290ccf3f113a7e">Min</a>(Vector3 lhs, Vector3 rhs)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#aa4b2a5ff6af794cc8a83617227bee73b">MoveTowards</a>(Vector3 current, Vector3 target, float maxDistanceDelta)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#ad6514c4819aa2dabb95253f9d6f5248c">New</a>(float x, float y, float z)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a5855d8e4953dffcb076b9e5949406203">Normalize</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a4e2f1c26b10b35c7a23108cfaa63320c">normalized</a></td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a16592c4087c4d02cf1dd00a06d5baced">one</a></td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a9f7843705e8cb31af4a2fa885ec599de">operator*</a>(Vector3 o1, float f)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a78ff76fd80ba422a745612959099a1fa">operator*</a>(float f, Vector3 o1)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#ae40267fccb8c1e9d79d704776b07e949">operator+</a>(Vector3 o1, Vector3 o2)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a62cecbc7efa2173accf443a1d3ff41e4">operator-</a>(Vector3 o1, Vector3 o2)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a40ea1014f1fd49f4c2fce2b7c77bcfc1">operator-</a>(Vector3 o1)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a2e065e07d84200e5a9cec3270ef1d30f">operator/</a>(Vector3 o1, float f)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#aa9b7aad25b72d46c5e49e847bbc41353">Project</a>(Vector3 vector, Vector3 onNormal)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#af4bc2b40c64c31d8ade94277052e46d1">ProjectOnPlane</a>(Vector3 vector, Vector3 planeNormal)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a6973141d227d4b18eb09caaa3cb965b4">Reflect</a>(Vector3 inDirection, Vector3 inNormal)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#ae8a79f66fb993fb1ad05c81fd3bbb9e3">right</a></td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a938bc42ace148202da5445f245581927">RotateTowards</a>(Vector3 current, Vector3 target, float maxRadiansDelta, float maxMagnitudeDelta)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a1b7601ca79dfd9b6f3ab3636508c197a">Scale</a>(Vector3 a, Vector3 b)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a5f40e6344654b7590958df867f1d5b03">Set</a>(float x, float y, float z)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a952dff0d8cc76a32589c2020e957adbf">Slerp</a>(Vector3 a, Vector3 b, float t)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a951d3fea17d487e6c7a27d842fcaf3f7">SlerpUnclamped</a>(Vector3 a, Vector3 b, float t)</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a29cb25eb011222ce12a27338eb3aa0a2">sqrMagnitude</a></td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a63129be99b82f76bb94c8267b0dcd692">ToString</a>()</td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a63cc0e06476f86bd603c0a56bfa09fae">up</a></td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#af2367a6c9fb9484cd8703ef20bbe3e2b">x</a></td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a93844cc4b95c4b4cc2152ecd3d6a69ed">y</a></td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#a80737d9f0e18357fd716e47a1b82ef6a">z</a></td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_vector3.html#aa45f0524375ebdaeb4244325ffb08210">zero</a></td><td class="entry"><a class="el" href="class_lua_1_1_vector3.html">Lua.Vector3</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
</table></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

--Change Notes
--0.1 Inital
--0.5 Added Storage Mode and Aim at Self

RaygunE = RegisterBehavior("Raygun_exchanger")
RaygunE.data =  {
    menuEntry = "Raygun/Exchanger",
    secondary = true,
	flags = {"Raygun_exchanger,Raygun"},
    agent = {
        type = { "player" }
    },
    target = {
        type = { "oneself" }
    },
	settings = {
		--{ "walkAnimation", "walking animation", "string", "Walk" },
		--{ "idleAnimation", "idle animation", "string", "Idle 2" },
		--{ "enrage_mult", "enrage multiplier", "float", 0.5 },
		{ "magnitudeDivider", "Power Divider", "string", "200"},
		{ "chargeMultipler", "Charge Multiplier", "string", "20"},
		{ "stop_key", "Stop key", "keybind", "k" },
		{ "storage_key", "Storage Mode", "keybind","q"},
		{ "battery", "Inital Battery Charge","string","100"},
		{ "aimatselfkey", "Aim at Yourself!", "keybind","b"},
		{ "colour_key", "Self Growth Mode key", "keybind","m"}
	}
}

-- OnPlayerRaygunEHit event will pass the following data to this function:
-- target - entity that has been hit
-- magnitude - RaygunE's magnitude value (float | ranges from -1 to 1)
-- firingMode - type of emission that hit target (int | 0 - projectile, 1 - laser, 2 - sonic)
-- chargeValue - value of hit projectile charge value (float | ranges from 0.125 - 1)
function RaygunE:Start()
	--followers = {}
	self.laserTarget = nil
	self.laserCrusher = nil
	--init values
	--we'll use this for setting growthrates later
	self.growthRate = 0
	--default to instant exchange mode
	self.storagemode = false
	-- divide magnitude by this value - lower numbers increase the effect.
	self.magnitudeDivider = tonumber(self.magnitudeDivider)
	-- multiply chargevalue by this.
	self.chargeMultipler = tonumber(self.chargeMultipler)
	--battery for storage mode
	self.battery = tonumber(self.battery)
	-- how long size effects should take to take effect in seconds - only affects projectile mode
	self.growthTime = 5
	--Max/Min Size - Defaults are Micro min - 0.001 max 1000 Giantess min 1 max 1000000
	self.minMicroScale = 0.001
	self.maxMicroScale = 1000
	self.minGiantessScale = 1
	self.maxGiantessScale = 1000000
	--we default to aiming the glowing end away from ourselves!
	self.aimatself = false
	--default to growing self.
	self.growSelf = true
    -- subscribe Listener() to a "OnPlayerRaygunEHit" event
    self.agent.dict.OnPlayerRaygunHit = Event.Register(self, EventCode.OnPlayerRaygunHit, self.Listener)
end

function RaygunE:Listener(data)

	--debugstuff
	--log(self.storagemode)
	--log(self.battery)
	--log(data.firingMode)
	
	if data.firingMode == 0 then
		if data.target.isGiantess() then
			-- data.target.dict.killCount = Mathf.Round(data.chargeValue * self.enrage_mult * 10)
			-- data.target.ai.SetBehavior("EnragedGTS")
		end
		if data.magnitude >=0 then
			self.growthRate = 1 - (((data.magnitude / self.magnitudeDivider) * (data.chargeValue * self.chargeMultipler)) / ((1 / self.magnitudeDivider) * (1 * self.chargeMultipler)))
		elseif data.magnitude <=0 then
			self.growthRate = 1 - (((data.magnitude / self.magnitudeDivider) * (data.chargeValue * self.chargeMultipler)) / ((1 / self.magnitudeDivider)  * (1 * self.chargeMultipler)) * -1)
		end		
		
		if data.magnitude <0 then
			Log("Shrinking Target")
			--Log("Growth Rate: " .. self.growthRate)
			--Log("Current Target Scale: " .. data.target.scale)
			--Log("Current User Scale: " .. self.agent.scale)
			-- calculate end heights
			self.targetHeightEnd = (data.target.scale * self.growthRate)
			self.userHeightEnd = (self.agent.scale + (data.target.scale - self.targetHeightEnd))
			if self.aimatself then
				self.userHeightEnd = (self.agent.scale * self.growthRate)
			end
			--Log("End Target Scale: " .. self.targetHeightEnd)
			--Log("End User Scale: " .. self.userHeightEnd)
		elseif data.magnitude >0 then
			Log("Growing Target")
			--Log("Growth Rate: " .. self.growthRate)
			--Log("Current Target Scale: " .. data.target.scale)
			--Log("Current User Scale: " .. self.agent.scale)
			-- calculate end heights
			self.userHeightEnd = (self.agent.scale * self.growthRate)
			self.targetHeightEnd = (data.target.scale + (self.agent.scale - self.userHeightEnd))
			--Log("End Target Scale: " .. self.targetHeightEnd)
			--Log("End User Scale: " .. self.userHeightEnd)
		end

		--make sure we can't go under the minimum / over the max
		--what's my target
		if data.target.isGiantess() then
			if self.targetHeightEnd <= self.minGiantessScale then
				self.targetHeightEnd = self.minGiantessScale
				--need to cancel users change
				self.userHeightEnd = self.agent.scale
			end
			if self.targetHeightEnd >= self.maxGiantessScale then
				self.targetHeightEnd = self.maxGiantessScale
				--need to cancel users change
				self.userHeightEnd = self.agent.scale
			end
		elseif not data.target.isGiantess() then
			if self.targetHeightEnd <= self.minMicroScale then
				self.targetHeightEnd = self.minMicroScale
				--need to cancel users change
				self.userHeightEnd = self.agent.scale
			end
			if self.targetHeightEnd >= self.maxMicroScale then
				self.targetHeightEnd = self.maxMicroScale
				--need to cancel users change
				self.userHeightEnd = self.agent.scale
			end
		end
		--same for the Raygun user
		if self.userHeightEnd <= self.minMicroScale then
			self.userHeightEnd = self.minMicroScale
			--need to cancel targets change
			self.targetHeightEnd = data.target.scale
		end
		if self.userHeightEnd >= self.maxMicroScale then
			self.userHeightEnd = self.maxMicroScale
			--need to cancel targets change
			self.targetHeightEnd = data.target.scale
		end
		
		--storage mode logic
		if self.storagemode then
			--user height should not change with this.
			self.userHeightEnd = self.agent.scale
			--calculate what the difference in height is from what the target is, if shrinking add charge
			if data.target.scale >= self.targetHeightEnd then
				self.battery = self.battery + (data.target.scale - self.targetHeightEnd)
			end
			--if growing remove charge. stop if out.
			if data.target.scale <= self.targetHeightEnd then
				if self.battery - (self.targetHeightEnd - data.target.scale ) <= 0 then
					--we'd run out of charge before completing - just add the charge to current scale.
					self.targetHeightEnd = data.target.scale + self.battery
					self.battery = 0
				elseif self.battery - (self.targetHeightEnd - data.target.scale ) > 0 then
					--we have enough charge, just take away the charge and continue.
					self.battery = self.battery - (self.targetHeightEnd - data.target.scale)
				end
			--log charge level.	
			end
			log("Battery Charge: " .. self.battery)
		end
		
		if self.aimatself then
			--disable growth stuff
		elseif not self.aimatself then	
			--actually do the growing/shrinking!			
			if data.target.scale >= self.targetHeightEnd then			
				self.scaleRate = 1 - (self.targetHeightEnd / data.target.scale)
				--Log("Target Scale Rate: -" .. self.scaleRate)
				data.target.Grow (-self.scaleRate, self.growthTime)
			elseif data.target.scale <= self.targetHeightEnd then
				self.scaleRate = 1 - (data.target.scale / self.targetHeightEnd )
				--Log("Target Scale Rate: " .. self.scaleRate)
				data.target.Grow (self.scaleRate, self.growthTime)
			end
			if not self.storagemode then
				if self.agent.scale >= self.userHeightEnd then
					self.scaleRate = 1 - (self.userHeightEnd / self.agent.scale)
					--Log("User Scale Rate: -" .. self.scaleRate)
					self.agent.Grow (-self.scaleRate, self.growthTime)
				elseif self.agent.scale <= self.userHeightEnd then
					self.scaleRate = 1 - (self.agent.scale / self.userHeightEnd)
					--Log("User Scale Rate: " .. self.scaleRate)
					self.agent.Grow (self.scaleRate, self.growthTime)
				end	
			end
		end
	end	

	if not self.storagemode then
		Log("Exchanging Sizes...")
	end
	if data.firingMode >= 1 then
		if data.magnitude <0 then
			--Log("Shrinking Target")
			self.growthRate = (data.magnitude / -self.magnitudeDivider)
			self.targetHeightEnd = (data.target.scale / (self.growthRate + 1))
			self.userHeightEnd = (self.agent.scale * (self.growthRate + 1))
		elseif data.magnitude >0 then
			--Log("Growing Target")
			self.growthRate = (data.magnitude / self.magnitudeDivider)
			self.targetHeightEnd = (data.target.scale * (self.growthRate + 1))
			self.userHeightEnd = (self.agent.scale / (self.growthRate + 1))
		end
		--make sure we can't go under the minimum / over the max
		--what's my target
		if data.target.isGiantess() then
			if self.targetHeightEnd <= self.minGiantessScale then
				self.targetHeightEnd = self.minGiantessScale
				--need to cancel users change
				self.userHeightEnd = self.agent.scale
			end
			if self.targetHeightEnd >= self.maxGiantessScale then
				self.targetHeightEnd = self.maxGiantessScale
				--need to cancel users change
				self.userHeightEnd = self.agent.scale
			end
		elseif not data.target.isGiantess() then
			if self.targetHeightEnd <= self.minMicroScale then
				self.targetHeightEnd = self.minMicroScale
				--need to cancel users change
				self.userHeightEnd = self.agent.scale
			end
			if self.targetHeightEnd >= self.maxMicroScale then
				self.targetHeightEnd = self.maxMicroScale
				--need to cancel users change
				self.userHeightEnd = self.agent.scale
			end
		end
		--same for the Raygun user
		if self.userHeightEnd <= self.minMicroScale then
			self.userHeightEnd = self.minMicroScale
			--need to cancel targets change
			self.targetHeightEnd = data.target.scale
		end
		if self.userHeightEnd >= self.maxMicroScale then
			self.userHeightEnd = self.maxMicroScale
			--need to cancel targets change
			self.targetHeightEnd = data.target.scale
		end
		--storage mode logic
		if self.storagemode then
			--user height should not change with this.
			self.userHeightEnd = self.agent.scale
			--calculate what the difference in height is from what the target is, if shrinking add charge
			if data.target.scale >= self.targetHeightEnd then
				self.battery = self.battery + (data.target.scale - self.targetHeightEnd)
			end
			--if growing remove charge. stop if out.
			if data.target.scale <= self.targetHeightEnd then
				if self.battery - (self.targetHeightEnd - data.target.scale ) <= 0 then
					--we'd run out of charge before completing - just add the charge to current scale.
					self.targetHeightEnd = data.target.scale + self.battery
					self.battery = self.battery - (self.targetHeightEnd - data.target.scale )
				elseif self.battery - (self.targetHeightEnd - data.target.scale ) > 0 then
					--we have enough charge, just take away the charge and continue.
					self.battery = self.battery - (self.targetHeightEnd - data.target.scale)
				end
			--log charge level.	
			log("Battery Charge: " .. self.battery)
			end
		end
		--aim at self logic
		if self.aimatself then
			--no growth here
		elseif not self.aimatself then
			--GROW/SHRINK
			data.target.scale = self.targetHeightEnd
			self.agent.scale = self.userHeightEnd
		end
	end
end



function RaygunE:Update()
	if Input.GetKeyDown(self.stop_key) then
		self.agent.ai.StopSecondaryBehavior("Raygun_exchanger")
	end
	if Input.GetKeyDown(self.storage_key) then
		if not self.storagemode then
			Log("Enabling Storage Mode")
			self.storagemode = true
			if self.aimatself then
				--disable aimatself
				self.aimatself = false
				Log("You return to aiming a gun safely!")
			end
			--log charge level.	
			log("Battery Charge: " .. self.battery)
		elseif self.storagemode then
			Log("Disablng Storage Mode")
			self.storagemode = false
		end
	end
	if Input.GetKeyDown(self.aimatselfkey) then
		if not self.aimatself then
			Log("You point the glowing end at yourself...")
			Log("Disabling Storage Mode")
			self.storagemode = false
			self.aimatself = true
			--log charge level.	
			log("Battery Charge: " .. self.battery)
		elseif self.aimatself then
			Log("You return to aiming a gun safely!")
			self.aimatself = false
		end
	end
	if Input.GetKeyDown(self.colour_key) then
		if not self.growSelf then
			Log("The gun begins glowing green...")
			self.growSelf = true
		elseif self.growSelf then
			Log("The gun begins glowing purple...")
			self.growSelf = false
		end
	end
	if self.aimatself then
		if Input.GetMouseButton(0) then
			--basic af size calc
			if self.growSelf then
				self.userHeightEnd = (self.agent.scale * 1.01)
			elseif not self.growSelf then
				self.userHeightEnd = (self.agent.scale * 0.99)
			end
			--don't go over limits
			if self.userHeightEnd <= self.minMicroScale then
				self.userHeightEnd = self.minMicroScale
			end
			if self.userHeightEnd >= self.maxMicroScale then
				self.userHeightEnd = self.maxMicroScale
			end
			
			--calculate what the difference in height is from what the target is, if shrinking add charge
			if self.agent.scale >= self.userHeightEnd then
				self.battery = self.battery + (self.agent.scale - self.userHeightEnd)
			end
			--if growing remove charge. stop if out.
			if self.agent.scale <= self.userHeightEnd then
				if self.battery - (self.userHeightEnd - self.agent.scale ) <= 0 then
					--we'd run out of charge before completing - just add the charge to current scale.
					self.userHeightEnd = self.agent.scale + self.battery
					self.battery = self.battery - (self.userHeightEnd - self.agent.scale )
				elseif self.battery - (self.userHeightEnd - self.agent.scale ) > 0 then
					--we have enough charge, just take away the charge and continue.
					self.battery = self.battery - (self.userHeightEnd - self.agent.scale)
				end
			end
			--log charge level
			log("Battery Charge: " .. self.battery)
			self.agent.scale = self.userHeightEnd

		end
	end
end

function RaygunE:Exit()
	self.agent.ai.StopSecondaryBehavior("Raygun_exchanger")
end


<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.Animation Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_animation.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="class_lua_1_1_animation-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.Animation Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Component to control the animation for humanoid entities.  
 <a href="class_lua_1_1_animation.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a27ca2f8a6b74867c4727315bbce3878f"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_animation.html#a27ca2f8a6b74867c4727315bbce3878f">Set</a> (string animationName)</td></tr>
<tr class="memdesc:a27ca2f8a6b74867c4727315bbce3878f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Will transition to the specified animation. This function is ignored if the entity is a player.  <a href="class_lua_1_1_animation.html#a27ca2f8a6b74867c4727315bbce3878f">More...</a><br /></td></tr>
<tr class="separator:a27ca2f8a6b74867c4727315bbce3878f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afbab459005c0736f8b52459185cf1637"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_animation.html#afbab459005c0736f8b52459185cf1637">SetAndWait</a> (string animationName)</td></tr>
<tr class="memdesc:afbab459005c0736f8b52459185cf1637"><td class="mdescLeft">&#160;</td><td class="mdescRight">Will transition to the specified animation and it will wait until completes before doing another action.  <a href="class_lua_1_1_animation.html#afbab459005c0736f8b52459185cf1637">More...</a><br /></td></tr>
<tr class="separator:afbab459005c0736f8b52459185cf1637"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afa4d0d4dc374917da05e5c26e75c20b0"><td class="memItemLeft" align="right" valign="top">string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_animation.html#afa4d0d4dc374917da05e5c26e75c20b0">Get</a> ()</td></tr>
<tr class="memdesc:afa4d0d4dc374917da05e5c26e75c20b0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the name of the current animation or pose.  <a href="class_lua_1_1_animation.html#afa4d0d4dc374917da05e5c26e75c20b0">More...</a><br /></td></tr>
<tr class="separator:afa4d0d4dc374917da05e5c26e75c20b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad3fe96a39d87c9f3b1121fbbd05f61ea"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_animation.html#ad3fe96a39d87c9f3b1121fbbd05f61ea">SetPose</a> (string poseName)</td></tr>
<tr class="memdesc:ad3fe96a39d87c9f3b1121fbbd05f61ea"><td class="mdescLeft">&#160;</td><td class="mdescRight">Will set the specified pose.  <a href="class_lua_1_1_animation.html#ad3fe96a39d87c9f3b1121fbbd05f61ea">More...</a><br /></td></tr>
<tr class="separator:ad3fe96a39d87c9f3b1121fbbd05f61ea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac615b08b06a84330cddb327e6d28f6c9"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_animation.html#ac615b08b06a84330cddb327e6d28f6c9">GetSpeed</a> ()</td></tr>
<tr class="memdesc:ac615b08b06a84330cddb327e6d28f6c9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the current Speed.  <a href="class_lua_1_1_animation.html#ac615b08b06a84330cddb327e6d28f6c9">More...</a><br /></td></tr>
<tr class="separator:ac615b08b06a84330cddb327e6d28f6c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aefdc9f4c78bada7dacfdb39d07c4b576"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_animation.html#aefdc9f4c78bada7dacfdb39d07c4b576">SetSpeed</a> (float speed)</td></tr>
<tr class="memdesc:aefdc9f4c78bada7dacfdb39d07c4b576"><td class="mdescLeft">&#160;</td><td class="mdescRight">Changes the speed of the <a class="el" href="class_lua_1_1_animation.html" title="Component to control the animation for humanoid entities.">Animation</a>. Default is 1. The final speed can be affected by the global speed and the scale of the giantess.  <a href="class_lua_1_1_animation.html#aefdc9f4c78bada7dacfdb39d07c4b576">More...</a><br /></td></tr>
<tr class="separator:aefdc9f4c78bada7dacfdb39d07c4b576"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad7c23e47dae3b3c354d317724e93e887"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_animation.html#ad7c23e47dae3b3c354d317724e93e887">GetTime</a> ()</td></tr>
<tr class="memdesc:ad7c23e47dae3b3c354d317724e93e887"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get current animation time. May exceed animation length for looped animations.  <a href="class_lua_1_1_animation.html#ad7c23e47dae3b3c354d317724e93e887">More...</a><br /></td></tr>
<tr class="separator:ad7c23e47dae3b3c354d317724e93e887"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8da88aefb747e3128bcbf35be8451b21"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_animation.html#a8da88aefb747e3128bcbf35be8451b21">GetLength</a> ()</td></tr>
<tr class="memdesc:a8da88aefb747e3128bcbf35be8451b21"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get current animation length in seconds. Depends on animation speed.  <a href="class_lua_1_1_animation.html#a8da88aefb747e3128bcbf35be8451b21">More...</a><br /></td></tr>
<tr class="separator:a8da88aefb747e3128bcbf35be8451b21"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae9196e188d96824f23cf65b1f835aa1a"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_animation.html#ae9196e188d96824f23cf65b1f835aa1a">GetProgress</a> ()</td></tr>
<tr class="memdesc:ae9196e188d96824f23cf65b1f835aa1a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get current animation completion percentage. Values above 100% are possible for looped animations.  <a href="class_lua_1_1_animation.html#ae9196e188d96824f23cf65b1f835aa1a">More...</a><br /></td></tr>
<tr class="separator:ae9196e188d96824f23cf65b1f835aa1a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afd07956e9f1dc6f551d8ca036493a646"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_animation.html#afd07956e9f1dc6f551d8ca036493a646">IsCompleted</a> ()</td></tr>
<tr class="memdesc:afd07956e9f1dc6f551d8ca036493a646"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if the animation has been completed.  <a href="class_lua_1_1_animation.html#afd07956e9f1dc6f551d8ca036493a646">More...</a><br /></td></tr>
<tr class="separator:afd07956e9f1dc6f551d8ca036493a646"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6f6e8dabc438a05a0338f69a25a61d71"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_animation.html#a6f6e8dabc438a05a0338f69a25a61d71">IsInTransition</a> ()</td></tr>
<tr class="memdesc:a6f6e8dabc438a05a0338f69a25a61d71"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if the animation is in transition to another animation.  <a href="class_lua_1_1_animation.html#a6f6e8dabc438a05a0338f69a25a61d71">More...</a><br /></td></tr>
<tr class="separator:a6f6e8dabc438a05a0338f69a25a61d71"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aede5bb0940e1daed76c816ba30dac6f2"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_animation.html#aede5bb0940e1daed76c816ba30dac6f2">IsInPose</a> ()</td></tr>
<tr class="memdesc:aede5bb0940e1daed76c816ba30dac6f2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if the entity is in a pose.  <a href="class_lua_1_1_animation.html#aede5bb0940e1daed76c816ba30dac6f2">More...</a><br /></td></tr>
<tr class="separator:aede5bb0940e1daed76c816ba30dac6f2"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-static-methods" name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:a1002a4f745d48a64fd26affa514ee0d2"><td class="memItemLeft" align="right" valign="top">static bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_animation.html#a1002a4f745d48a64fd26affa514ee0d2">AnimationExists</a> (string animationName)</td></tr>
<tr class="memdesc:a1002a4f745d48a64fd26affa514ee0d2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if an animation with the specified name exists.  <a href="class_lua_1_1_animation.html#a1002a4f745d48a64fd26affa514ee0d2">More...</a><br /></td></tr>
<tr class="separator:a1002a4f745d48a64fd26affa514ee0d2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8a1938fee4f3fc1967112c49c26105b3"><td class="memItemLeft" align="right" valign="top"><a id="a8a1938fee4f3fc1967112c49c26105b3" name="a8a1938fee4f3fc1967112c49c26105b3"></a>
static IList&lt; string &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>GetAnimationList</b> ()</td></tr>
<tr class="memdesc:a8a1938fee4f3fc1967112c49c26105b3"><td class="mdescLeft">&#160;</td><td class="mdescRight">A list of all animations. <br /></td></tr>
<tr class="separator:a8a1938fee4f3fc1967112c49c26105b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7826d8216e7d202f625e697341ae62fc"><td class="memItemLeft" align="right" valign="top">static bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_animation.html#a7826d8216e7d202f625e697341ae62fc">PoseExists</a> (string poseName)</td></tr>
<tr class="memdesc:a7826d8216e7d202f625e697341ae62fc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if a pose with the specified name exists.  <a href="class_lua_1_1_animation.html#a7826d8216e7d202f625e697341ae62fc">More...</a><br /></td></tr>
<tr class="separator:a7826d8216e7d202f625e697341ae62fc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af7313d3ee3fd378b8136d8f72f1d90a1"><td class="memItemLeft" align="right" valign="top"><a id="af7313d3ee3fd378b8136d8f72f1d90a1" name="af7313d3ee3fd378b8136d8f72f1d90a1"></a>
static IList&lt; string &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>GetPoseList</b> ()</td></tr>
<tr class="memdesc:af7313d3ee3fd378b8136d8f72f1d90a1"><td class="mdescLeft">&#160;</td><td class="mdescRight">A list of all poses. <br /></td></tr>
<tr class="separator:af7313d3ee3fd378b8136d8f72f1d90a1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af1f4b9f2d2a3e595a08a7f5e5d095580"><td class="memItemLeft" align="right" valign="top">static void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_animation.html#af1f4b9f2d2a3e595a08a7f5e5d095580">SetGlobalSpeed</a> (float speed)</td></tr>
<tr class="memdesc:af1f4b9f2d2a3e595a08a7f5e5d095580"><td class="mdescLeft">&#160;</td><td class="mdescRight">Changes the global speed of giantess. It affects all the giantess on the scene.  <a href="class_lua_1_1_animation.html#af1f4b9f2d2a3e595a08a7f5e5d095580">More...</a><br /></td></tr>
<tr class="separator:af1f4b9f2d2a3e595a08a7f5e5d095580"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad91c4ef8fcb303877802e1e38fd27b85"><td class="memItemLeft" align="right" valign="top">static void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_animation.html#ad91c4ef8fcb303877802e1e38fd27b85">GetGlobalSpeed</a> (float speed)</td></tr>
<tr class="memdesc:ad91c4ef8fcb303877802e1e38fd27b85"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the global speed of giantess.  <a href="class_lua_1_1_animation.html#ad91c4ef8fcb303877802e1e38fd27b85">More...</a><br /></td></tr>
<tr class="separator:ad91c4ef8fcb303877802e1e38fd27b85"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="properties" name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a7dee7e3d2e6bdffed96f20822e4bb307"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_animation.html#a7dee7e3d2e6bdffed96f20822e4bb307">minSpeed</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a7dee7e3d2e6bdffed96f20822e4bb307"><td class="mdescLeft">&#160;</td><td class="mdescRight">The slowest speed when the giantess is at maximun scale. (Without applying the speed multiplier)  <a href="class_lua_1_1_animation.html#a7dee7e3d2e6bdffed96f20822e4bb307">More...</a><br /></td></tr>
<tr class="separator:a7dee7e3d2e6bdffed96f20822e4bb307"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a49aa08ef58b67f1af48526781176fce3"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_animation.html#a49aa08ef58b67f1af48526781176fce3">maxSpeed</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a49aa08ef58b67f1af48526781176fce3"><td class="mdescLeft">&#160;</td><td class="mdescRight">The fastest speed when the giantess is at minimun scale. (Without applying the speed multiplier)  <a href="class_lua_1_1_animation.html#a49aa08ef58b67f1af48526781176fce3">More...</a><br /></td></tr>
<tr class="separator:a49aa08ef58b67f1af48526781176fce3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad1f57da869e710f55a79e82ceb579cc0"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_animation.html#ad1f57da869e710f55a79e82ceb579cc0">transitionDuration</a><code> [get, set]</code></td></tr>
<tr class="memdesc:ad1f57da869e710f55a79e82ceb579cc0"><td class="mdescLeft">&#160;</td><td class="mdescRight">How long it takes to transition from one animation to another  <a href="class_lua_1_1_animation.html#ad1f57da869e710f55a79e82ceb579cc0">More...</a><br /></td></tr>
<tr class="separator:ad1f57da869e710f55a79e82ceb579cc0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4418e6d09f625ad76932922c568bf987"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_animation.html#a4418e6d09f625ad76932922c568bf987">speedMultiplier</a><code> [get]</code></td></tr>
<tr class="memdesc:a4418e6d09f625ad76932922c568bf987"><td class="mdescLeft">&#160;</td><td class="mdescRight">The giantess speed is multiplied by this factor (the one that you find in the animation panel). The default value is 1. (Read Only)  <a href="class_lua_1_1_animation.html#a4418e6d09f625ad76932922c568bf987">More...</a><br /></td></tr>
<tr class="separator:a4418e6d09f625ad76932922c568bf987"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >Component to control the animation for humanoid entities. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a1002a4f745d48a64fd26affa514ee0d2" name="a1002a4f745d48a64fd26affa514ee0d2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1002a4f745d48a64fd26affa514ee0d2">&#9670;&nbsp;</a></span>AnimationExists()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static bool Lua.Animation.AnimationExists </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>animationName</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns true if an animation with the specified name exists. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">animationName</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="afa4d0d4dc374917da05e5c26e75c20b0" name="afa4d0d4dc374917da05e5c26e75c20b0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afa4d0d4dc374917da05e5c26e75c20b0">&#9670;&nbsp;</a></span>Get()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">string Lua.Animation.Get </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the name of the current animation or pose. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ad91c4ef8fcb303877802e1e38fd27b85" name="ad91c4ef8fcb303877802e1e38fd27b85"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad91c4ef8fcb303877802e1e38fd27b85">&#9670;&nbsp;</a></span>GetGlobalSpeed()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static void Lua.Animation.GetGlobalSpeed </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>speed</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns the global speed of giantess. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">speed</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a8da88aefb747e3128bcbf35be8451b21" name="a8da88aefb747e3128bcbf35be8451b21"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8da88aefb747e3128bcbf35be8451b21">&#9670;&nbsp;</a></span>GetLength()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Animation.GetLength </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get current animation length in seconds. Depends on animation speed. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ae9196e188d96824f23cf65b1f835aa1a" name="ae9196e188d96824f23cf65b1f835aa1a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae9196e188d96824f23cf65b1f835aa1a">&#9670;&nbsp;</a></span>GetProgress()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Animation.GetProgress </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get current animation completion percentage. Values above 100% are possible for looped animations. </p>
<p >Equivalent to GetTime / GetLength </p><dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ac615b08b06a84330cddb327e6d28f6c9" name="ac615b08b06a84330cddb327e6d28f6c9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac615b08b06a84330cddb327e6d28f6c9">&#9670;&nbsp;</a></span>GetSpeed()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Animation.GetSpeed </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the current Speed. </p>
<p >The current speed is calculated by: globalSpeed * scaleModifier * speedMultiplier. The scale modifier slow down the giantess according to the size. </p><dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ad7c23e47dae3b3c354d317724e93e887" name="ad7c23e47dae3b3c354d317724e93e887"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad7c23e47dae3b3c354d317724e93e887">&#9670;&nbsp;</a></span>GetTime()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Animation.GetTime </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get current animation time. May exceed animation length for looped animations. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="afd07956e9f1dc6f551d8ca036493a646" name="afd07956e9f1dc6f551d8ca036493a646"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afd07956e9f1dc6f551d8ca036493a646">&#9670;&nbsp;</a></span>IsCompleted()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.Animation.IsCompleted </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns true if the animation has been completed. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="aede5bb0940e1daed76c816ba30dac6f2" name="aede5bb0940e1daed76c816ba30dac6f2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aede5bb0940e1daed76c816ba30dac6f2">&#9670;&nbsp;</a></span>IsInPose()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.Animation.IsInPose </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns true if the entity is in a pose. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a6f6e8dabc438a05a0338f69a25a61d71" name="a6f6e8dabc438a05a0338f69a25a61d71"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6f6e8dabc438a05a0338f69a25a61d71">&#9670;&nbsp;</a></span>IsInTransition()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.Animation.IsInTransition </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns true if the animation is in transition to another animation. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a7826d8216e7d202f625e697341ae62fc" name="a7826d8216e7d202f625e697341ae62fc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7826d8216e7d202f625e697341ae62fc">&#9670;&nbsp;</a></span>PoseExists()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static bool Lua.Animation.PoseExists </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>poseName</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Returns true if a pose with the specified name exists. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">poseName</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a27ca2f8a6b74867c4727315bbce3878f" name="a27ca2f8a6b74867c4727315bbce3878f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a27ca2f8a6b74867c4727315bbce3878f">&#9670;&nbsp;</a></span>Set()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Animation.Set </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>animationName</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Will transition to the specified animation. This function is ignored if the entity is a player. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">animationName</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="afbab459005c0736f8b52459185cf1637" name="afbab459005c0736f8b52459185cf1637"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afbab459005c0736f8b52459185cf1637">&#9670;&nbsp;</a></span>SetAndWait()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Animation.SetAndWait </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>animationName</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Will transition to the specified animation and it will wait until completes before doing another action. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">animationName</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="af1f4b9f2d2a3e595a08a7f5e5d095580" name="af1f4b9f2d2a3e595a08a7f5e5d095580"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af1f4b9f2d2a3e595a08a7f5e5d095580">&#9670;&nbsp;</a></span>SetGlobalSpeed()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static void Lua.Animation.SetGlobalSpeed </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>speed</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Changes the global speed of giantess. It affects all the giantess on the scene. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">speed</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ad3fe96a39d87c9f3b1121fbbd05f61ea" name="ad3fe96a39d87c9f3b1121fbbd05f61ea"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad3fe96a39d87c9f3b1121fbbd05f61ea">&#9670;&nbsp;</a></span>SetPose()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Animation.SetPose </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>poseName</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Will set the specified pose. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">poseName</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="aefdc9f4c78bada7dacfdb39d07c4b576" name="aefdc9f4c78bada7dacfdb39d07c4b576"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aefdc9f4c78bada7dacfdb39d07c4b576">&#9670;&nbsp;</a></span>SetSpeed()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Animation.SetSpeed </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>speed</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Changes the speed of the <a class="el" href="class_lua_1_1_animation.html" title="Component to control the animation for humanoid entities.">Animation</a>. Default is 1. The final speed can be affected by the global speed and the scale of the giantess. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">speed</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="a49aa08ef58b67f1af48526781176fce3" name="a49aa08ef58b67f1af48526781176fce3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a49aa08ef58b67f1af48526781176fce3">&#9670;&nbsp;</a></span>maxSpeed</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Animation.maxSpeed</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The fastest speed when the giantess is at minimun scale. (Without applying the speed multiplier) </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a7dee7e3d2e6bdffed96f20822e4bb307" name="a7dee7e3d2e6bdffed96f20822e4bb307"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7dee7e3d2e6bdffed96f20822e4bb307">&#9670;&nbsp;</a></span>minSpeed</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Animation.minSpeed</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The slowest speed when the giantess is at maximun scale. (Without applying the speed multiplier) </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a4418e6d09f625ad76932922c568bf987" name="a4418e6d09f625ad76932922c568bf987"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4418e6d09f625ad76932922c568bf987">&#9670;&nbsp;</a></span>speedMultiplier</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Animation.speedMultiplier</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The giantess speed is multiplied by this factor (the one that you find in the animation panel). The default value is 1. (Read Only) </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ad1f57da869e710f55a79e82ceb579cc0" name="ad1f57da869e710f55a79e82ceb579cc0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad1f57da869e710f55a79e82ceb579cc0">&#9670;&nbsp;</a></span>transitionDuration</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Animation.transitionDuration</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>How long it takes to transition from one animation to another </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaAnimation.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_animation.html">Animation</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

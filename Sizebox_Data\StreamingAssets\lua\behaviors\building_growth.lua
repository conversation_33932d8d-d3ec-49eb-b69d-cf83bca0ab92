-- Define the Building Destroy Growth data
BuildingDestroyGrowth = {
    data = {
        menuEntry = "Size/Building Destroy Growth", -- Menu path and entry name
        secondary = true,                           -- Indicates it's a secondary action
        flags = { "grow" },                         -- Flags to describe the behavior
        agent = { type = { "giantess" } },          -- Can only be applied to giantess characters
        target = { type = { "oneself" } }           -- Targets oneself
    }
}

-- Function to initialize the behavior
function BuildingDestroyGrowth:Start()
    math.randomseed(os.time()) -- Seed the random number generator
    self.grew = false -- Ensure growth happens only once
    self.blocks = {} -- Table to store all found buildings

    -- Find all buildings (replace "cCityBlock" with the correct class/tag for buildings)
    for _, actor in ipairs(Game.FindActorsByClass("cCityBlock")) do
        table.insert(self.blocks, actor)
    end

    log("BuildingDestroyGrowth initialized. Found " .. #self.blocks .. " buildings.")
end

-- Function to update the behavior every frame
function BuildingDestroyGrowth:Update()
    if self.grew then return end -- Prevent further actions if already grown

    -- Check if any building is still intact
    local intact = false
    for _, blk in ipairs(self.blocks) do
        if blk and blk:IsValid() then
            intact = true
            break
        end
    end

    -- If no buildings are intact, trigger growth
    if not intact then
        self.grew = true
        local player = Game.GetPlayer()
        if player then
            local growthFactor = math.random(10, 30) * 0.01 -- Random growth factor (10%–30%)
            local duration = math.random(3, 8) -- Random growth duration (3–8 seconds)

            -- Smoothly grow the player over the duration
            local startScale = player:GetScale()
            local targetScale = startScale * (1 + growthFactor)
            self.elapsed = 0
            self.duration = duration

            self.onGrowTick = function(_, dt)
                self.elapsed = self.elapsed + dt
                local t = math.min(self.elapsed / self.duration, 1.0)
                local newScale = startScale + (targetScale - startScale) * t
                player:SetScale(newScale)
                if t >= 1.0 then
                    self.onGrowTick = nil -- End the growth process
                    log("Growth complete. Final scale: " .. player:GetScale())
                end
            end
        end
    end

    -- If in growth transition, apply it
    if self.onGrowTick then
        self.onGrowTick(self, dt)
    end
end

-- Function to clean up when the behavior ends
function BuildingDestroyGrowth:Exit()
    log("BuildingDestroyGrowth behavior exited.")
end
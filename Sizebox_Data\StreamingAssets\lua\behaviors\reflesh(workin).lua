Rec = RegisterBehavior("Reflesh")
Rec.data = {
    menuEntry = "RefleshD",
    secondary = true,
    flags = { "grow" },
    agent = {
        type = { "giantess" }
    },
    target = {
        type = { "giantess" }
    }
}

-- Define growth sounds
local growthSounds = {
    "GaspMoan001_Mmm.ogg",
    "GaspMoan002_Augh_MidLong.ogg",
    "GaspMoan003_Ahh_MidLong.ogg",
    "GaspMoan005_Mmm_Long.ogg"
}

function Rec:Start()
    phi = 0
    status = 0
    killing = true
    superGrowth = false
    superGrowthset = false
    growing = false
    GS = 0
    rate = 0
    duration = 0
    walkSet = false
    queuedAnim = nil
    refleshTimer = 0
    refleshDuration = 2.5

    -- Growth mode
    growthMode = "linear" -- Default growth mode is now linear
    baseRate = 0.12
    currentRate = baseRate
    exponentialMultiplier = 1.5
    growthCap = 1.1

    -- Audio setup
    self.audio_source = AudioSource:new(self.agent.bones.spine)
    self.audio_source.spatialBlend = 1
    self.audio_source.loop = false
    self.audio_source.volume = 1

    -- UI Toast for growth mode
    modeToast = Game.Toast.New()
    modeToast.Print("GROWTH MODE: LINEAR") -- Show on load
end

function Rec:Update()
    -- Change growth modes based on input
    if Input.GetKeyDown("m") then
        growthMode = "linear"
        currentRate = baseRate
        modeToast.Print("GROWTH MODE: LINEAR") -- Update UI Toast
    end

    if Input.GetKeyDown("k") then
        growthMode = "exponential"
        modeToast.Print("GROWTH MODE: EXPONENTIAL")
    end

    if Input.GetKeyDown("j") then
        growthMode = "random"
        modeToast.Print("GROWTH MODE: RANDOM")
    end

    -- Growth triggers when pressing "r" or "t"
    if Input.GetKeyDown("r") or Input.GetKeyDown("t") then
        self.agent.ai.StopAction()
        killing = false
        superGrowth = true
        queuedAnim = Input.GetKeyDown("r") and "Masturbation 1" or "Massage Breasts 5"

        -- 25% chance to play sound
        if math.random() <= 0.25 then
            self.audio_source.clip = growthSounds[math.random(1, #growthSounds)]
            self.audio_source:Play()
        end
    end

    -- Handle actual growth
    if growing then
        status = 1
        phi = phi + Time.deltaTime / duration
        GS = math.sin(phi) * currentRate

        if GS <= 0 then
            growing = false
            phi = 0
            GS = 0
            status = 2
        end

        self.agent.grow(GS)
    end

    -- When super growth is triggered (queued animation)
    if superGrowth and queuedAnim then
        self.agent.ai.StopAction()
        self.agent.animation.Set(queuedAnim)

        -- Growth rate based on mode
        if growthMode == "linear" then
            currentRate = baseRate
        elseif growthMode == "exponential" then
            currentRate = math.min(currentRate * exponentialMultiplier, growthCap)
        elseif growthMode == "random" then
            currentRate = math.min(math.random() * baseRate * 5, growthCap)
        end

        rate = currentRate
        duration = 1
        growing = true
        superGrowth = false
        superGrowthset = true
        walkSet = false
    end

    -- After growth completes, play reflesh animation
    if superGrowthset and status == 2 then
        superGrowthset = false
        status = 3

        -- Only set reflesh if the growth mode is not linear
        if growthMode ~= "linear" then
            self.agent.animation.Set("Reflesh")
            refleshTimer = refleshDuration
        end
    end

    -- Wait for the reflesh animation to finish
    if status == 3 then
        refleshTimer = refleshTimer - Time.deltaTime
        if refleshTimer <= 0 then
            log("Reflesh done")
            killing = true
            status = 0
            queuedAnim = nil
        end
    end

    -- Reset to idle
    if killing and not walkSet then
        self.agent.animation.Set("Idle")
        walkSet = true
    end
end

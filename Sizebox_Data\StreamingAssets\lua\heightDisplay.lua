hKey = "l"
function Start()
	toggleOutput = false
	hToast = Game.Toast.New()
	checkedAgent = Entity.GetSelectedEntity()
	setAgentName()
	OnSelectChange = Event.Register(self, EventCode.OnLocalSelectionChanged, SelectionListener)
	eTypes = {"object", "gts", "micro"}
end

function SelectionListener()
	checkedAgent = Entity.GetSelectedEntity()
	setAgentName()
end

function setAgentName()
	if checkedAgent then
		local s = checkedAgent.name
		if string.find(s,"(.object\\)") then
			s = string.match(checkedAgent.name,"%.object\\(.*)$")
		elseif string.find(s,"(.gts\\)") then
			s = string.match(checkedAgent.name,"%.gts\\(.*)$")
		elseif string.find(s,"(.micro\\)") then
			s = string.match(checkedAgent.name,"%.micro\\(.*)$")
		end
		checkedAgentName = s
	end
end

function printHeight()
	if not checkedAgent then return end
	checkedAgentSize = math.floor(checkedAgent.height*100) * 0.01
	hToast.Print(checkedAgentName.."'s size is: "..checkedAgentSize.." m")
end

function Update3()
	if toggleOutput then
		printHeight()
	end
	if Input.GetKey("left shift") and Input.GetKey("left ctrl") and Input.GetKeyDown(hKey) then
		toggleOutput = not toggleOutput
		if not toggleOutput then hToast.Print(nil) end
	elseif Input.GetKey("left shift") and Input.GetKeyDown(hKey) then
		printHeight()
	end
end
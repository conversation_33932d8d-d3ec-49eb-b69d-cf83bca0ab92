<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_rigidbody.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle"><div class="title">Lua.Rigidbody Member List</div></div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="class_lua_1_1_rigidbody.html">Lua.Rigidbody</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html#a5d57a953b33d659f6e3b5508cd304960">AddExplosionForce</a>(float explosionForce, Vector3 explosionPosition, float explosionRadius)</td><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html">Lua.Rigidbody</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html#a57eff3c6b45bf8f5a8dfdb7fa1fc9f8f">AddForce</a>(Vector3 force)</td><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html">Lua.Rigidbody</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html#a9770aa2b61c085f2581392a782f6742c">AddRelativeForce</a>(Vector3 force)</td><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html">Lua.Rigidbody</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html#a901b3213408100236b17a3e55b64e6f7">angularDrag</a></td><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html">Lua.Rigidbody</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html#ab88493ae1a778194017c0e3a87c0625d">angularVelocity</a></td><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html">Lua.Rigidbody</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html#a267bf6dacb4ef9d2aeb0c798d2460245">ClosestPointOnBounds</a>(Vector3 position)</td><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html">Lua.Rigidbody</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html#ac537e281d009b3e07c93f7357fa743cd">drag</a></td><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html">Lua.Rigidbody</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html#ad3f2380221d01eedada3df4d282a37d4">freezeRotation</a></td><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html">Lua.Rigidbody</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html#aee1018d4d56ab085d013acc494c0d0f9">isKinematic</a></td><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html">Lua.Rigidbody</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html#aeab0f1c55ada296d501909dd61533a35">mass</a></td><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html">Lua.Rigidbody</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html#a8171fc4d6eb8d7e448eeb45f9fbc05d8">maxAngularVelocity</a></td><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html">Lua.Rigidbody</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html#a413146fdf9b4e57b433cbc01dc1bc288">MovePosition</a>(Vector3 position)</td><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html">Lua.Rigidbody</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html#a064bd1441d0a8d7e636619c87a98f7cf">MoveRotation</a>(Quaternion rot)</td><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html">Lua.Rigidbody</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html#a9bef020808bd389b43ac5d2f7d429dc9">position</a></td><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html">Lua.Rigidbody</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html#a6cb1207363fce98ec04cacf8c6f776cc">rotation</a></td><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html">Lua.Rigidbody</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html#a3838f1418140279bcec4d7a2f8ebbae2">useGravity</a></td><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html">Lua.Rigidbody</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html#abbc468f41391b7d34120f11f3f39b6fb">velocity</a></td><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html">Lua.Rigidbody</a></td><td class="entry"></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html#a23530e52ed361ac1f758e8204a1c833b">worldCenterOfMass</a></td><td class="entry"><a class="el" href="class_lua_1_1_rigidbody.html">Lua.Rigidbody</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

local Rec = RegisterBehavior("Size Fever Multi")
Rec.data = {
    menuEntry = "Size Fever Multi",
    agent = { type = { "giantess" } },
    secondary = true
}

local minScale = 0.01
local maxScale = 1e8
local energy = 0
local mode = 1
local modeNames = {
    "Click Charge",
    "Walk Charge",
    "Random Pulse"
}
local growthMode = "Burst" -- "Burst" or "Continuous"
local soundList = {
    "GaspMoan001_Mmm.ogg",
    "GaspMoan002_Augh_MidLong.ogg",
    "GaspMoan003_Ahh_MidLong.ogg"
}
local audioSource
local toast
local lastToastTime = 0
local lastPos = nil
local growing = false

function Rec:Start()
    audioSource = AudioSource:new(self.agent.bones.spine)
    audioSource.spatialBlend = 1
    audioSource.loop = false
    audioSource.volume = 1
    toast = Game.Toast.New()
    lastPos = self.agent.transform.position
    toast.Print("Mode: " .. modeNames[mode] .. " | Growth: " .. growthMode .. "\nPress M to change mode, B to toggle growth mode.")
end

function Rec:Update()
    local dt = Time.deltaTime

    -- Mode toggle
    if Input.GetKeyDown("m") then
        mode = mode + 1
        if mode > #modeNames then mode = 1 end
        energy = 0
        toast.Print("Mode: " .. modeNames[mode] .. " | Growth: " .. growthMode)
    end

    -- Growth mode toggle
    if Input.GetKeyDown("b") then
        if growthMode == "Burst" then
            growthMode = "Continuous"
        else
            growthMode = "Burst"
        end
        toast.Print("Growth mode: " .. growthMode)
    end

    -- Mode 1: Click Charge (press G to charge, F to grow)
    if mode == 1 then
        if Input.GetKeyDown("g") then
            energy = energy + 1
            toast.Print("Click Charge - Energy: " .. energy)
        end
        if growthMode == "Burst" then
            if Input.GetKeyDown("f") then
                if energy > 0 then
                    self.agent:grow(energy)
                    toast.Print("Grew by " .. energy .. "! (Press G to charge again)")
                    local sound = soundList[math.random(1, #soundList)]
                    audioSource:Play(sound)
                    energy = 0
                else
                    toast.Print("No energy! Press G to add energy.")
                end
            end
        else -- Continuous
            if Input.GetKey("f") and energy > 0 then
                self.agent:grow(energy * dt * 2) -- slower, smoother growth
                growing = true
            elseif growing then
                growing = false
                toast.Print("Stopped growing. (Hold F to grow)")
            end
        end

    -- Mode 2: Walk Charge (energy builds as you move)
    elseif mode == 2 then
        local pos = self.agent.transform.position
        if lastPos then
            local dx = (pos.x or 0) - (lastPos.x or 0)
            local dy = (pos.y or 0) - (lastPos.y or 0)
            local dz = (pos.z or 0) - (lastPos.z or 0)
            local dist = math.sqrt(dx*dx + dy*dy + dz*dz)
            if dist > 0.01 then
                energy = math.min(energy + dist * 10, 100)
                if math.floor(energy) % 10 == 0 then
                    toast.Print("Walk Charge - Energy: " .. math.floor(energy))
                end
            end
        end
        lastPos = {x=pos.x, y=pos.y, z=pos.z}
        if growthMode == "Burst" then
            if Input.GetKeyDown("f") then
                if energy > 0 then
                    self.agent:grow(energy)
                    toast.Print("Grew by " .. math.floor(energy) .. "! (Walk to charge again)")
                    local sound = soundList[math.random(1, #soundList)]
                    audioSource:Play(sound)
                    energy = 0
                else
                    toast.Print("No energy! Walk to charge.")
                end
            end
        else -- Continuous
            if Input.GetKey("f") and energy > 0 then
                self.agent:grow(energy * dt * 2)
                growing = true
            elseif growing then
                growing = false
                toast.Print("Stopped growing. (Hold F to grow)")
            end
        end

    -- Mode 3: Random Pulse (press F for random size jump)
    elseif mode == 3 then
        if Input.GetKeyDown("f") then
            local pulse = math.random(10, 1000)
            if math.random() > 0.5 then
                self.agent:grow(pulse)
                toast.Print("Random Pulse: Grew by " .. pulse)
            else
                self.agent:grow(-pulse)
                toast.Print("Random Pulse: Shrunk by " .. pulse)
            end
            local sound = soundList[math.random(1, #soundList)]
            audioSource:Play(sound)
        end
    end

    -- Show a toast every 5 seconds to prove the script is running
    lastToastTime = lastToastTime + dt
    if lastToastTime > 5 then
        toast.Print("Mode: " .. modeNames[mode] .. " | Growth: " .. growthMode .. " | Energy: " .. math.floor(energy))
        lastToastTime = 0
    end
end

local Fever = RegisterBehavior("Size Fever")
Fever.data = {
    menuEntry = "Size Fever",
    agent = { type = { "giantess" } },
    secondary = true
}

local feverActive = false
local feverTimer = 0
local feverDuration = 10 -- seconds
local feverInterval = 0.25
local feverStep = 0.15
local feverDir = 1
local feverToast

function Fever:Start()
    feverActive = false
    feverTimer = 0
    feverToast = Game.Toast.New()
    feverToast.Print("Press F to start Size Fever!")
end

function Fever:Update()
    if Input.GetKeyDown("f") then
        feverActive = not feverActive
        feverTimer = 0
        feverToast.Print(feverActive and "Size Fever started!" or "Size Fever stopped!")
    end

    if not feverActive then return end

    feverTimer = feverTimer + Time.deltaTime
    if feverTimer > feverDuration then
        feverActive = false
        feverToast.Print("Size Fever ended!")
        return
    end

    if math.floor(feverTimer / feverInterval) % 2 == 0 then
        feverDir = 1
    else
        feverDir = -1
    end

    local scale = (self.agent.scale or self.agent.localScale or 1.0) + feverStep * feverDir
    if self.agent.scale ~= nil then
        self.agent.scale = math.max(0.1, scale)
    elseif self.agent.localScale ~= nil then
        self.agent.localScale = math.max(0.1, scale)
    end
end
@echo off
echo Analyzing Sizebox Assembly-CSharp.dll...
echo ========================================

echo.
echo Searching for city-related strings...
findstr /i "citybuilder" "Sizebox_Data\Managed\Assembly-CSharp.dll" > nul
if %errorlevel% == 0 (
    echo [FOUND] CityBuilder
) else (
    echo [NOT FOUND] CityBuilder
)

findstr /i "executecity" "Sizebox_Data\Managed\Assembly-CSharp.dll" > nul
if %errorlevel% == 0 (
    echo [FOUND] ExecuteCity
) else (
    echo [NOT FOUND] ExecuteCity
)

findstr /i "generatecity" "Sizebox_Data\Managed\Assembly-CSharp.dll" > nul
if %errorlevel% == 0 (
    echo [FOUND] GenerateCity
) else (
    echo [NOT FOUND] GenerateCity
)

findstr /i "spawnobject" "Sizebox_Data\Managed\Assembly-CSharp.dll" > nul
if %errorlevel% == 0 (
    echo [FOUND] SpawnObject
) else (
    echo [NOT FOUND] SpawnObject
)

findstr /i "buildcity" "Sizebox_Data\Managed\Assembly-CSharp.dll" > nul
if %errorlevel% == 0 (
    echo [FOUND] BuildCity
) else (
    echo [NOT FOUND] BuildCity
)

echo.
echo Analysis complete!
echo.
echo Based on earlier findings, we know these exist:
echo - CityBuilder class
echo - ExecuteCityBuilding method
echo - CityPopulationManager
echo - CityBuilding with destruction animations
echo.
echo Ready to proceed with modification...
pause

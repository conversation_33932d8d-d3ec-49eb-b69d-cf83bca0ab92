spurtBMSettings = {
	{ "baseSpeed", "Grow Speed", "string", "0.13" },
	{ "duration", "Shortest Duration", "string", "1.3"},
	{ "smoothifier", "Easing Duration", "string", "1"},
	{ "randomizedTimings", "Variable Timings", "bool", true},
	{ "decelQuicken", "Deceleration Boost", "string", "5"},
	{ "linear", "Linear Growth", "bool", false},
	{ "linearMult", "Linear Boost", "string", "5"},
	{ "soundFile", "Use Sound Clip (filename):", "string", "none"},
	{ "loopClip", "Loop Sound", "bool", true},
	{ "playSpurtAnim", "Play Spurt Animation", "bool", true},
	{ "spurtAnim", "Spurt Animation", "string", "Defeat"},
	{ "randomIdleAnim", "Random Idle Animation", "bool", true}
}

-- This 'spurt' table is used with all behaviors to avoid redundant code, but a few need a slight variant, so this clones it into a separate 'spurts' table.

spurtsBMSettings = {}
for i,v in pairs(spurtBMSettings) do
	spurtsBMSettings[i] = {}
	for ii,vv in pairs(spurtBMSettings[i]) do
		spurtsBMSettings[i][ii] = vv
	end
end

table.insert(spurtsBMSettings, 3, { "respite", "Time Between Spurts", "string", "5"})



Spurts = RegisterBehavior("Grow (Spurts)")
Spurts.data = {
	menuEntry = "Size/Grow (Spurts)",
	flags = { "grow" },
	agent = {
		type = { "humanoid" }
	},
	target = {
		type = { "oneself" }
	},
	settings = spurtsBMSettings
}

function Spurts:Start()
	self.autoSpurts = true
	SpurtStart(self)
end

function Spurts:Update()
	SpurtUpdate(self)
end

function Spurts:Exit()
	SpurtExit(self)
end



SpurtSingle = RegisterBehavior("Grow (Single Spurt)")
SpurtSingle.data = {
	menuEntry = "Size/Grow (Single Spurt)",
	flags = { "ai grow" },
	ai = true,
	agent = {
		type = { "giantess"}
	},
	target = {
		type = { "oneself" }
	},
	settings = spurtBMSettings
}


function SpurtSingle:Start()
	SpurtStart(self)
end

function SpurtSingle:Update()
	SpurtUpdate(self)
end

function SpurtSingle:Exit()
	SpurtExit(self)
end



mSpurtSingle = RegisterBehavior("Grow micro (S. Spurt)")
mSpurtSingle.data = {
	menuEntry = "Size/Grow micro (S. Spurt)",
	flags = { "ai grow" },
	ai = true,
	agent = {
		type = { "micro" }
	},
	target = {
		type = { "oneself" },
	},
	settings = spurtBMSettings
}

function mSpurtSingle:Start()
	SpurtStart(self)
end

function mSpurtSingle:Update()
	SpurtUpdate(self)
end

function mSpurtSingle:Exit()
	SpurtExit(self)
end



--[ Shared Functions ]--
function SpurtStart(self)
	if not globals["spurtRand"] then -- Do randomseed() only once per scene, per behavior
		math.randomseed(tonumber(os.time()) + self.agent.id)
		globals["spurtRand"] = true
	end
	InitValues(self)
end

function SpurtUpdate(self)
	if self.growing then
		ScaleAccelUpdate(self)
		PlaySFX(self)
		SpurtTimer(self)
	else
		if self.autoSpurts and self.waiting then
			WaitTimer(self)
		else
			ScaleDecelUpdate(self)
			StopSFX(self)
		end
	end
	ScaleUpdate(self)
end

function SpurtExit(self)
	ResetAnimVars(self)
	if not self.agent.ai.IsAIEnabled() then
		if self.randomIdleAnim then
			globals["Idle"] = "expressive"
		else
			globals["Idle"] = "basic"
		end
		self.agent.ai.SetBehavior("Idle")
	end
end

function InitValues(self)
	self.baseSpeed = math.abs(tonumber(self.baseSpeed))
	self.dynamicSpeed = 0
	self.spurtDuration = math.abs(tonumber(self.duration))
	self.smoothifier = math.abs(tonumber(self.smoothifier)) -- Scaling ease-in/out duration
	if self.randomizedTimings then
		local durationMult = math.random(1, (2 * math.random(1,2)))
		self.spurtDuration = self.spurtDuration * durationMult
		local smoothifierMult = math.random(1, (2 * math.random(1,2)))
		self.smoothifier = 1 / (self.smoothifier * smoothifierMult)
	end
	if self.autoSpurts then
		self.spurtRespite = math.abs(tonumber(self.respite))
		if self.randomizedTimings then
			local respiteMult = math.random(1, (2 * math.random(1,2)))
			self.spurtRespite = self.spurtRespite * respiteMult
		end
	end
	self.decelQuicken = math.abs(tonumber(self.decelQuicken))
	self.linearMult = math.abs(tonumber(self.linearMult))
	self.timer = Time.time
	self.growing = true
	self.fadedIn = false
	InitSFX(self)
	InitAnim(self)
end

function InitSFX(self)
	if self.soundFile == "none" then return end
	self.mVol, self.cVol, self.tVol = 0,0,0
	self.agent.dict.sSpurtAudio = AudioSource:new(self.agent.bones.spine)
	self.agent.dict.sSpurtAudio.clip = self.soundFile
	self.agent.dict.sSpurtAudio.loop = self.loopClip
	self.agent.dict.sSpurtAudio.spatialBlend = 1
	self.agent.dict.sSpurtAudio.volume = 0
	self.playerScale = Game.GetLocalPlayer() and Game.GetLocalPlayer().scale or self.agent.scale
	UpdateSFXVolByScale(self)
end

function InitAnim(self)
	if not self.playSpurtAnim then return end
	self.agent.ai.StopAction()
	self.OGtransitionD = self.agent.animation.transitionDuration
	self.OGanimSpeed = self.agent.animation.speedMultiplier
	self.agent.animation.SetSpeed(0.4)
	self.agent.animation.transitionDuration = 0.4
	self.timeSlack = 1.3 -- Calculated multiplier for additional time to properly finish spurt
	self.OGcanLook = self.agent.CanLookAtPlayer
	self.agent.CanLookAtPlayer = false
	self.agent.animation.Set(self.spurtAnim)
	if self.autoSpurts then
		self.respiteAnim = "Neutral Idle"
	end
	self.agent.Wait(self.spurtDuration * self.timeSlack)
end

function ScaleAccelUpdate(self)
	if self.dynamicSpeed < self.baseSpeed then
		self.dynamicSpeed = self.dynamicSpeed + self.baseSpeed * Time.deltaTime * self.smoothifier
	end
	if self.dynamicSpeed > self.baseSpeed then
		self.dynamicSpeed = self.baseSpeed
	end
end

function ScaleDecelUpdate(self)
	if self.dynamicSpeed > 0 then
		self.dynamicSpeed = self.dynamicSpeed - self.baseSpeed * Time.deltaTime * self.smoothifier * self.decelQuicken
	end
	if self.dynamicSpeed <= 0 then
		self.dynamicSpeed = 0
		if not self.playing then
			if self.autoSpurts then
				self.timer = Time.time
				self.waiting = true
				ResetAnimVars(self)
			else
				self.agent.ai.StopSecondaryBehavior("ai grow")
			end
		end
	end
end

function ScaleUpdate(self)
	if self.linear then
		self.agent.scale = self.agent.scale + (self.linearMult * self.dynamicSpeed * Time.deltaTime)
	else
		self.agent.scale = self.agent.scale * (1 + self.dynamicSpeed * Time.deltaTime)
	end
end

function SpurtTimer(self)
	if TimerCompleted(self,self.spurtDuration) then
		self.growing = false
		self.fadedIn = false
	end
end

function WaitTimer(self)
	if self.playSpurtAnim and self.agent.animation.Get() == self.spurtAnim and Time.time >= self.timer + self.spurtRespite / 4 then
		self.agent.animation.Set(self.respiteAnim)
	elseif TimerCompleted(self,self.spurtRespite) then
		self.waiting = false
		InitValues(self)
	end
end

function TimerCompleted(self,duration)
	if Time.time >= self.timer + duration then
		return true
	end
end

function PlaySFX(self)
	if self.soundFile == "none" then return end
	if not self.playing then -- Prevents starting the clip continuously as this is in an Update loop
		self.playing = true
		self.agent.dict.sSpurtAudio:Play() -- Start the audio clip
	end

	-- Adjust max volume with scale
	UpdateSFXVolByScale(self)

	-- Fade-in
	if self.loopClip then
		if self.fadedIn then
			self.agent.dict.sSpurtAudio.volume = self.vol
		else
			self.tVol = self.spurtDuration * 0.25 / Time.deltaTime
			self.mVol = self.mVol + 1
			self.cVol = self.cVol + self.mVol
			self.agent.dict.sSpurtAudio.volume = self.vol * self.cVol / (self.tVol * (self.tVol + 1) * 0.5)
			if self.agent.dict.sSpurtAudio.volume > self.vol then
				self.agent.dict.sSpurtAudio.volume = self.vol -- Redundancy necessary for volume to rise fluidly
				self.fadedIn = true
			end
		end
	else
		self.agent.dict.sSpurtAudio.volume = self.vol
	end
end

function StopSFX(self)
	if self.soundFile == "none" or not self.playing then return end

	-- Adjust max volume with scale
	UpdateSFXVolByScale(self)

	-- Fade-out
	if self.loopClip then
		if self.agent.dict.sSpurtAudio.volume > 0 then
			self.tVol = self.spurtDuration * 0.25 / Time.deltaTime
			self.cVol = self.cVol - self.mVol
			self.mVol = self.mVol - 1
			self.agent.dict.sSpurtAudio.volume = self.vol * self.cVol / (self.tVol * (self.tVol + 1) * 0.5)
		end
		if self.agent.dict.sSpurtAudio.volume <= 0 then
			self.agent.dict.sSpurtAudio.volume = 0
			self.agent.dict.sSpurtAudio:Pause()
			self.playing = false
		end
	else
		self.playing = false
	end
end

function UpdateSFXVolByScale(self)
	self.mPitch =
		(self.agent.scale * 0.2) / (0.04 / self.agent.scale) * self.agent.scale
		/ (self.agent.scale * self.agent.scale * math.sqrt(self.agent.scale))
		/ (
			  math.sqrt(math.sqrt(math.sqrt(math.sqrt(self.agent.scale))))
			* math.sqrt(math.sqrt(math.sqrt(self.agent.scale)))
		)
	self.agent.dict.sSpurtAudio.pitch = 0.65 + (
		self.agent.scale * (1.05 / (self.agent.scale * 5 / self.mPitch))
		* (1 / math.sqrt((self.agent.scale * 1000 * 0.008) + 1))
	)
	self.vol =
		(1.7 - self.agent.dict.sSpurtAudio.pitch)
		* (Mathf.Clamp(self.baseSpeed , 0, 1.5) / 1.875 + 0.2)
	self.agent.dict.sSpurtAudio.minDistance =
		(1000 * self.agent.scale / self.playerScale / (300 + self.agent.scale / self.playerScale)) * 0.5 * 0.0025
end

function ResetAnimVars(self)
	if self.playSpurtAnim then
		self.agent.animation.transitionDuration = self.OGtransitionD
		self.agent.animation.SetSpeed(self.OGanimSpeed)
		self.agent.CanLookAtPlayer = self.OGcanLook
	end
end

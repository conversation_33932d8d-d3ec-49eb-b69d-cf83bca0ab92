<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Class Index</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('classes.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle"><div class="title">Class Index</div></div>
</div><!--header-->
<div class="contents">
<div class="qindex"><a class="qindex" href="#letter_A">A</a>&#160;|&#160;<a class="qindex" href="#letter_B">B</a>&#160;|&#160;<a class="qindex" href="#letter_C">C</a>&#160;|&#160;<a class="qindex" href="#letter_E">E</a>&#160;|&#160;<a class="qindex" href="#letter_G">G</a>&#160;|&#160;<a class="qindex" href="#letter_I">I</a>&#160;|&#160;<a class="qindex" href="#letter_L">L</a>&#160;|&#160;<a class="qindex" href="#letter_M">M</a>&#160;|&#160;<a class="qindex" href="#letter_P">P</a>&#160;|&#160;<a class="qindex" href="#letter_Q">Q</a>&#160;|&#160;<a class="qindex" href="#letter_R">R</a>&#160;|&#160;<a class="qindex" href="#letter_S">S</a>&#160;|&#160;<a class="qindex" href="#letter_T">T</a>&#160;|&#160;<a class="qindex" href="#letter_V">V</a>&#160;|&#160;<a class="qindex" href="#letter_W">W</a></div>
<div class="classindex">
<dl class="classindex even">
<dt class="alphachar"><a id="letter_A" name="letter_A">A</a></dt>
<dd><a class="el" href="class_lua_1_1_a_i.html">AI</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd><dd><a class="el" href="class_lua_1_1_all_giantess.html">AllGiantess</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd><dd><a class="el" href="class_lua_1_1_all_micros.html">AllMicros</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd><dd><a class="el" href="class_lua_1_1_animation.html">Animation</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd><dd><a class="el" href="class_lua_1_1_audio_source.html">AudioSource</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd></dl>
<dl class="classindex odd">
<dt class="alphachar"><a id="letter_B" name="letter_B">B</a></dt>
<dd><a class="el" href="class_lua_1_1_bones.html">Bones</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd></dl>
<dl class="classindex even">
<dt class="alphachar"><a id="letter_C" name="letter_C">C</a></dt>
<dd><a class="el" href="class_lua_1_1_custom_sound_manager.html">CustomSoundManager</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd></dl>
<dl class="classindex odd">
<dt class="alphachar"><a id="letter_E" name="letter_E">E</a></dt>
<dd><a class="el" href="class_lua_1_1_entity.html">Entity</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd><dd><a class="el" href="class_lua_1_1_entity_initialized_callback.html">EntityInitializedCallback</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd><dd><a class="el" href="class_lua_1_1_event.html">Event</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd><dd><a class="el" href="class_event_code.html">EventCode</a></dd></dl>
<dl class="classindex even">
<dt class="alphachar"><a id="letter_G" name="letter_G">G</a></dt>
<dd><a class="el" href="class_lua_1_1_game.html">Game</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd><dd><a class="el" href="class_lua_1_1_globals.html">Globals</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd></dl>
<dl class="classindex odd">
<dt class="alphachar"><a id="letter_I" name="letter_I">I</a></dt>
<dd><a class="el" href="class_lua_1_1_i_k.html">IK</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd><dd><a class="el" href="class_lua_1_1_i_k_effector.html">IKEffector</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd><dd><a class="el" href="class_lua_1_1_input.html">Input</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd></dl>
<dl class="classindex even">
<dt class="alphachar"><a id="letter_L" name="letter_L">L</a></dt>
<dd><a class="el" href="class_lua_1_1_lua_player_raygun.html">LuaPlayerRaygun</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd></dl>
<dl class="classindex odd">
<dt class="alphachar"><a id="letter_M" name="letter_M">M</a></dt>
<dd><a class="el" href="class_lua_1_1_mathf.html">Mathf</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd><dd><a class="el" href="class_lua_1_1_morphs.html">Morphs</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd><dd><a class="el" href="class_lua_1_1_movement.html">Movement</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd></dl>
<dl class="classindex even">
<dt class="alphachar"><a id="letter_P" name="letter_P">P</a></dt>
<dd><a class="el" href="class_lua_1_1_player.html">Player</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd></dl>
<dl class="classindex odd">
<dt class="alphachar"><a id="letter_Q" name="letter_Q">Q</a></dt>
<dd><a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd></dl>
<dl class="classindex even">
<dt class="alphachar"><a id="letter_R" name="letter_R">R</a></dt>
<dd><a class="el" href="class_lua_1_1_random.html">Random</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd><dd><a class="el" href="class_lua_1_1_rigidbody.html">Rigidbody</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd></dl>
<dl class="classindex odd">
<dt class="alphachar"><a id="letter_S" name="letter_S">S</a></dt>
<dd><a class="el" href="class_lua_1_1_screen.html">Screen</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd><dd><a class="el" href="class_lua_1_1_senses.html">Senses</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd><dd><a class="el" href="class_lua_1_1_shooting.html">Shooting</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd></dl>
<dl class="classindex even">
<dt class="alphachar"><a id="letter_T" name="letter_T">T</a></dt>
<dd><a class="el" href="class_lua_1_1_time.html">Time</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd><dd><a class="el" href="class_lua_1_1_game_1_1_toast.html">Game.Toast</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd><dd><a class="el" href="class_lua_1_1_transform.html">Transform</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd></dl>
<dl class="classindex odd">
<dt class="alphachar"><a id="letter_V" name="letter_V">V</a></dt>
<dd><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd><dd><a class="el" href="class_lua_1_1_game_1_1_version.html">Game.Version</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd></dl>
<dl class="classindex even">
<dt class="alphachar"><a id="letter_W" name="letter_W">W</a></dt>
<dd><a class="el" href="class_lua_1_1_world.html">World</a> (<a class="el" href="namespace_lua.html">Lua</a>)</dd></dl>
</div>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

Grab = RegisterBehavior("Grab-test-V1")
Grab.scores = {
    normal = 10,    --[[ the scores are set this way, for each personality or state.. is a value from 0 to 100 ]]
    curiosity = 100 --[[ the higher the value the more likely to choose that action ]]
}
Grab.data = {
    agent = {
        type = { "giantess" }
    },
    target = {
        type = { "micro" }
    }
}

function Grab:Start()
    self.stop = false
end

function Grab:Update()
    if not self.agent.ai.IsActionActive() then

        if self.stop then
            self.agent.ai.StopBehavior() -- if you use Update() you must manually tell when to end the behavior, after this the Exit() method will run
            return
        else
            if not self.target or self.target.isDead() then -- when looping the action, it needs to change the self.target when
                self.target = self.agent.findClosestMicro()    -- the first self.target is dead
                if not self.target then
                    self.agent.ai.StopBehavior() -- if it can't find a new self.target, then cancel the action
                    return
                end
            end

            self.agent.lookAt(self.target)
            self.agent.animation.Set("Walk",true)
            self.agent.Chase(self.target)
self.agent.animation.Set("Dig and Plant Seeds",true)
            self.agent.grab(self.target)

            self.stop = self.agent.ai.IsAIEnabled() -- if the giantess has the AI mode Active, i want her to stop wandering and do another thing
        end                                          -- it will stop in the next loop so i can make sure that runs at least once
    end
end
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Class Members - Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('functions_func_r.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a id="index_r" name="index_r"></a>- r -</h3><ul>
<li>Range()&#160;:&#160;<a class="el" href="class_lua_1_1_random.html#ab17e85e47aeaa3f719b649e51bb49d28">Lua.Random</a></li>
<li>Reflect()&#160;:&#160;<a class="el" href="class_lua_1_1_vector3.html#a6973141d227d4b18eb09caaa3cb965b4">Lua.Vector3</a></li>
<li>Register()&#160;:&#160;<a class="el" href="class_lua_1_1_event.html#a439c6db49f39ede1ac95b7863b3a3f9a">Lua.Event</a></li>
<li>Repeat()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#abf1f882dcf08b3749a27a28f6f7f3630">Lua.Mathf</a></li>
<li>Require()&#160;:&#160;<a class="el" href="class_lua_1_1_game_1_1_version.html#af27e4dcdcc666de5c8e5f90e747d3fb6">Lua.Game.Version</a></li>
<li>ResetGrowEnergyColor()&#160;:&#160;<a class="el" href="class_lua_1_1_lua_player_raygun.html#aa7b98cc0da08ae879220e9729a47659e">Lua.LuaPlayerRaygun</a></li>
<li>ResetMorphs()&#160;:&#160;<a class="el" href="class_lua_1_1_morphs.html#a1fc28aa3c4e3aa18fd044f2420d9a32b">Lua.Morphs</a></li>
<li>ResetShrinkEnergyColor()&#160;:&#160;<a class="el" href="class_lua_1_1_lua_player_raygun.html#a80852badcc3f3b0c14e73196b5299c37">Lua.LuaPlayerRaygun</a></li>
<li>Rotate()&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#af5d67d5940a08bc18d105c884e53be84">Lua.Transform</a></li>
<li>RotateTowards()&#160;:&#160;<a class="el" href="class_lua_1_1_quaternion.html#abfa084dd85c43e01da75d9bcf3305871">Lua.Quaternion</a>, <a class="el" href="class_lua_1_1_vector3.html#a938bc42ace148202da5445f245581927">Lua.Vector3</a></li>
<li>Round()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a9f6511ccc1da8fd5228959f67b995d91">Lua.Mathf</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

local Timelapse = RegisterBehavior("Size Time-lapse")
Timelapse.data = {
    menuEntry = "Size Time-lapse",
    agent = { type = { "giantess" } },
    secondary = true
}

local recording = false
local playing = false
local timer = 0
local history = {}
local playIndex = 1
local toast

-- Playback variables for smooth interpolation
local interpStart = nil
local interpEnd = nil
local interpTimer = 0
local interpDuration = 0.02 -- will be set per step
local playbackSpeed = 1.5 -- 1.5 = default, >1 = faster, <1 = slower
local playbackSpeedToastTimer = 0
local currentAnim = nil

function Timelapse:Start()
    if not toast then toast = Game.Toast.New() end
    recording = false
    playing = false
    timer = 0
    history = {}
    playIndex = 1
    toast.Print("Press T to start/stop recording.\nPress P to play time-lapse.")
end

function getTotalTime(history)
    local total = 0
    for i = 1, #history do
        total = total + (history[i].dt or 0)
    end
    return total
end

function Timelapse:Update()
    -- Toggle recording
    if Input.GetKeyDown("t") then
        recording = not recording
        playing = false
        timer = 0
        if recording then
            history = {}
            toast.Print("Recording started!")
        else
            local totalTime = string.format("%.2f", getTotalTime(history))
            toast.Print("Recording stopped! " .. #history .. " frames saved.\nTotal time: " .. totalTime .. "s\nPress P to play.")
        end
    end

    -- Toggle playback
    if Input.GetKeyDown("p") and not recording and #history > 1 then
        playing = not playing
        timer = 0
        playIndex = 1
        interpStart = nil
        interpEnd = nil
        interpTimer = 0
        interpDuration = 0.02
        playbackSpeedToastTimer = 0
        local totalTime = string.format("%.2f", getTotalTime(history) / playbackSpeed)
        toast.Print((playing and "Playing time-lapse!" or "Playback stopped.") ..
            "\nSpeed: x" .. string.format("%.2f", playbackSpeed) ..
            (playing and ("\nPlayback time: " .. totalTime .. "s") or ""))

        -- Play Run animation when playback starts, stop when playback ends
        if self.agent.animation and self.agent.animation.Set then
            if playing then
                self.agent.animation.Set("Run")
            else
                self.agent.animation.Set("Idle")
            end
        end
    end

    -- Playback speed control
    if playing then
        if Input.GetKeyDown("up") then
            playbackSpeed = math.min(playbackSpeed + 0.25, 5)
            playbackSpeedToastTimer = 1.0
            toast.Print("Playback speed: x" .. string.format("%.2f", playbackSpeed) .. " (Saved)")
        elseif Input.GetKeyDown("down") then
            playbackSpeed = math.max(playbackSpeed - 0.25, 0.25)
            playbackSpeedToastTimer = 1.0
            toast.Print("Playback speed: x" .. string.format("%.2f", playbackSpeed) .. " (Saved)")
        elseif Input.GetKeyDown("o") then
            playbackSpeed = 1.5
            playbackSpeedToastTimer = 1.0
            toast.Print("Playback speed reset to default (x1.50) (Saved)")
        end
    end

    -- Record size, position, and delta time
    if recording then
        local scale = self.agent.scale or self.agent.localScale or 1.0
        local pos = self.agent.transform.position
        local rot = self.agent.transform.rotation
        local dt = Time.deltaTime
        table.insert(history, {
            size = scale,
            pos = { x = pos.x, y = pos.y, z = pos.z },
            rot = { x = rot.x, y = rot.y, z = rot.z, w = rot.w },
            dt = dt
        })
        toast.Print("Recorded size: " .. string.format("%.2f", scale) ..
            "\nPos: (" .. string.format("%.2f", pos.x) .. ", " .. string.format("%.2f", pos.y) .. ", " .. string.format("%.2f", pos.z) .. ")" ..
            "\nTotal: " .. #history)
    end

    -- Playback (smooth interpolation using recorded dt)
    if playing and #history > 1 then
        -- Stop at the last frame
        if playIndex >= #history then
            playing = false
            -- Set final position and rotation to last recorded frame
            local last = history[#history]
            if self.agent.scale ~= nil then
                self.agent.scale = last.size
            elseif self.agent.localScale ~= nil then
                self.agent.localScale = last.size
            end
            local pos = self.agent.transform.position
            pos.x = last.pos.x
            pos.y = last.pos.y
            pos.z = last.pos.z
            self.agent.transform.position = pos
            local rot = self.agent.transform.rotation
            rot.x = last.rot.x
            rot.y = last.rot.y
            rot.z = last.rot.z
            rot.w = last.rot.w
            self.agent.transform.rotation = rot
            -- Force animation to Idle immediately
            if self.agent.animation and self.agent.animation.Set then
                self.agent.animation.Set("Idle")
                currentAnim = "Idle"
            end
            local totalTime = string.format("%.2f", getTotalTime(history) / playbackSpeed)
            toast.Print("Time-lapse finished!\nPlayback time: " .. totalTime .. "s\nPress P to replay.")
            return
        end

        -- Use the dt recorded for this step (not the next one)
        if not interpStart then
            interpStart = history[playIndex]
            interpEnd = history[playIndex + 1]
            interpTimer = 0
            interpDuration = history[playIndex].dt
            if interpDuration == 0 then interpDuration = history[playIndex + 1].dt end
        end

        interpTimer = interpTimer + Time.deltaTime * playbackSpeed
        local t = math.min(interpTimer / interpDuration, 1)
        -- Interpolate size
        local scale = interpStart.size + (interpEnd.size - interpStart.size) * t
        -- Interpolate position
        local px = interpStart.pos.x + (interpEnd.pos.x - interpStart.pos.x) * t
        local py = interpStart.pos.y + (interpEnd.pos.y - interpStart.pos.y) * t
        local pz = interpStart.pos.z + (interpEnd.pos.z - interpStart.pos.z) * t

        if self.agent.scale ~= nil then
            self.agent.scale = scale
        elseif self.agent.localScale ~= nil then
            self.agent.localScale = scale
        end
        -- Robust Vector3 constructor for all Sizebox Lua versions
        if Vector3.New then
            self.agent.transform.position = Vector3.New(px, py, pz)
        else
            self.agent.transform.position = Vector3(px, py, pz)
        end

        -- Show playback speed toast for 1s after changing speed, then show normal info
        if playbackSpeedToastTimer > 0 then
            playbackSpeedToastTimer = playbackSpeedToastTimer - Time.deltaTime
        else
            local totalTime = string.format("%.2f", getTotalTime(history) / playbackSpeed)
            toast.Print("Time-lapse: Step " .. playIndex .. "/" .. #history ..
                "\nSize: " .. string.format("%.2f", scale) ..
                "\nPos: (" .. string.format("%.2f", px) .. ", " .. string.format("%.2f", py) .. ", " .. string.format("%.2f", pz) .. ")" ..
                "\nSpeed: x" .. string.format("%.2f", playbackSpeed) ..
                "\nPlayback time: " .. totalTime .. "s")
        end

        -- In the playback block, after interpolating position
        local function lerp(a, b, t) return a + (b - a) * t end
        local function slerpQuat(q1, q2, t)
            -- Simple linear interpolation for quaternions (not true slerp, but works for small steps)
            return {
                x = lerp(q1.x, q2.x, t),
                y = lerp(q1.y, q2.y, t),
                z = lerp(q1.z, q2.z, t),
                w = lerp(q1.w, q2.w, t)
            }
        end

        local rot1 = interpStart.rot
        local rot2 = interpEnd.rot
        local rotLerp = slerpQuat(rot1, rot2, t)
        local rot = self.agent.transform.rotation
        rot.x = rotLerp.x
        rot.y = rotLerp.y
        rot.z = rotLerp.z
        rot.w = rotLerp.w
        self.agent.transform.rotation = rot

        -- Calculate movement distance for animation switching
        local dx = interpEnd.pos.x - interpStart.pos.x
        local dy = interpEnd.pos.y - interpStart.pos.y
        local dz = interpEnd.pos.z - interpStart.pos.z
        local dist = math.sqrt(dx*dx + dy*dy + dz*dz)
        local walkThreshold = 0.3
        local runThreshold = 1.0
        local hysteresis = 0.1

        if self.agent.animation and self.agent.animation.Set then
            local newAnim = currentAnim or "Idle"
            if currentAnim == "Run" then
                if dist < runThreshold - hysteresis then
                    if dist > walkThreshold then
                        newAnim = "Walk"
                    else
                        newAnim = "Idle"
                    end
                end
            elseif currentAnim == "Walk" then
                if dist > runThreshold + hysteresis then
                    newAnim = "Run"
                elseif dist < walkThreshold - hysteresis then
                    newAnim = "Idle"
                end
            else -- Idle or unknown
                if dist > runThreshold + hysteresis then
                    newAnim = "Run"
                elseif dist > walkThreshold + hysteresis then
                    newAnim = "Walk"
                end
            end
            if currentAnim ~= newAnim then
                self.agent.animation.Set(newAnim)
                currentAnim = newAnim
            end
        end

        -- If we're on the last step, force Idle so there's no animation gap
        if playIndex == #history - 1 then
            if self.agent.animation and self.agent.animation.Set then
                self.agent.animation.Set("Idle")
                currentAnim = "Idle"
            end
        end

        toast.Print("dist: " .. string.format("%.3f", dist))

        if t >= 1 then
            playIndex = playIndex + 1
            interpStart = nil
            interpEnd = nil
            interpTimer = 0
        end
        return
    end
end
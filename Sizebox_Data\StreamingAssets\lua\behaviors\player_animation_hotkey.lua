AnimateGTS = Register<PERSON><PERSON><PERSON>or("Animation Hotkeys")
AnimateGTS.agentType = "giantess"
AnimateGTS.targetType = "oneself"
--AnimateGTS.scores = {
--    hostile = 50   --[[ the scores are set this way, for each personality or state.. is a value from 0 to 100 ]]
--                     --[[ the higher the value the more likely to choose that action ]]
--}

function AnimateGTS:Start()
    
end

function AnimateGTS:Update()
    if Input.anyKeyDown == true then
        if Input.GetKeyDown("1") then
            self.agent.animation.Set("Spink Kick")
        elseif Input.GetKeyDown("2") then
            self.agent.animation.Set("Crouch Idle")
        elseif Input.GetKeyDown("3") then
            self.agent.animation.Set("Sit 6")
        elseif Input.GetKeyDown("4") then
            self.agent.animation.Set("Stomping")
        end
    end
end

function AnimateGTS:Exit()

end
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Class Members - Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('functions_func_p.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a id="index_p" name="index_p"></a>- p -</h3><ul>
<li>Pause()&#160;:&#160;<a class="el" href="class_lua_1_1_audio_source.html#aad8aea6c6f265dfe440a6a8620416bf4">Lua.AudioSource</a></li>
<li>PerlinNoise()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a3044ff5b1dd835169520fd054c713d63">Lua.Mathf</a></li>
<li>PingPong()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a8eed89df943f9dc0df1398e541e023ad">Lua.Mathf</a></li>
<li>Play()&#160;:&#160;<a class="el" href="class_lua_1_1_audio_source.html#aef1a519a4611e2aa72570d113d92c904">Lua.AudioSource</a></li>
<li>PlayAs()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#aa3ec8ca2693205f43b8e1e620b209018">Lua.Entity</a></li>
<li>PlayClipAtPoint()&#160;:&#160;<a class="el" href="class_lua_1_1_audio_source.html#a132381c13acd88e5ef3e53e0e8c1ad66">Lua.AudioSource</a></li>
<li>PlayDelayed()&#160;:&#160;<a class="el" href="class_lua_1_1_audio_source.html#a10ba6ce0794c8050458466082302bc09">Lua.AudioSource</a></li>
<li>PlayOneShot()&#160;:&#160;<a class="el" href="class_lua_1_1_audio_source.html#ac8a51a95f2285660337ddf45fd10252e">Lua.AudioSource</a></li>
<li>PoseExists()&#160;:&#160;<a class="el" href="class_lua_1_1_animation.html#a7826d8216e7d202f625e697341ae62fc">Lua.Animation</a></li>
<li>Pow()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#ace6b91fa037354fa541a5de450ba6e23">Lua.Mathf</a></li>
<li>Print()&#160;:&#160;<a class="el" href="class_lua_1_1_game_1_1_toast.html#a5ebc4bf8c4b6a0a14947ac2ff5dfd25c">Lua.Game.Toast</a></li>
<li>Project()&#160;:&#160;<a class="el" href="class_lua_1_1_vector3.html#aa9b7aad25b72d46c5e49e847bbc41353">Lua.Vector3</a></li>
<li>ProjectOnPlane()&#160;:&#160;<a class="el" href="class_lua_1_1_vector3.html#af4bc2b40c64c31d8ade94277052e46d1">Lua.Vector3</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

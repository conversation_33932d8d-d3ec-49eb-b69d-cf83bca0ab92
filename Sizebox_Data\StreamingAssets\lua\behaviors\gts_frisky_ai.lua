GiantessAiBehavior = RegisterBehavior("Enable Frisky AI")
GiantessAiBehavior.agentType = "giantess"
GiantessAiBehavior.targetType = "micro"


function GiantessAiBehavior:Start()
-- Initial target check
if self.target == nil or self.target.isDead() then
self.target = self.agent.findClosestMicro()
if self.target == nil then
self.agent.cancelAction()
return
end
end
Log("gts_ai has been activated")
end

function wait(seconds)
local start = os.time()
repeat until os.time() > start + seconds
end

-- A few idle animations
idleAnimations = {
"Idle",
"Idle 2",
"Embar",
"Embar 2",
"Look Down",
"Wait Strech Arms",
"Wait Torso Twist",
"Jump 4",

}


-- Each of these correspond to likelihood of action. Make sure they add up to 100. Look at it like a config
idle = 15 --Chance for no movement
idleMove = 20 --Chance for movement then idle
stomp = 20 --Chance for stomp
wander = 20 --Chance for wander
bigger = 10 --Chance for spontaneous growth
grab = 15 --Chance for grabbing a micro

vore = 40 --Chance for eating micro after grabbing

friskystart = 10 --Base chance for frisky event after each loop
friskymax = 50 --Max chance for frisky event after each loop
friskyinc = 3 --Amount friskyness increases after certain events

-- setup for variables
-- these reference the previous variable to eventually build up to 100
-- if I had a switchcase this would have been easier
idleLow = idle + 1
idleMove = idleMove + idle

stompLow = idleMove + 1
stomp = stomp + idleMove

wanderLow = stomp + 1
wander = wander + stomp

biggerLow = wander + 1
bigger = bigger + wander

grabLow = bigger + 1
grab = grab + bigger

if wander >= 101 then
Log("The AI's probability values exceed 100. Did you do something wrong?")
end

-- Main update function
function GiantessAiBehavior:Update()
if self.agent.actionsCompleted() then

-- Variables for the actions
local eat = math.random(1, 100)
local actionNumber = math.random(1, 100)
local idleIndex = math.random(#idleAnimations)
local frisk = math.random(1, 100)

if friskystart > friskymax then
friskystart=friskymax
end

if friskystart > frisk then
for count=10,0,-1
do
self.agent.completeAnimation("Jump 4")
self.agent.grow(0.0001,0.05)
self.agent.grow(0.0002,0.05)
self.agent.grow(0.0003,0.05)
self.agent.grow(0.0005,0.05)
self.agent.grow(0.0010,0.05)
self.agent.grow(0.0015,0.1)
self.agent.grow(0.0020,0.1)
self.agent.grow(0.0015,0.1)
self.agent.grow(0.0010,0.05)
self.agent.grow(0.0002,0.1)
self.agent.grow(0.0001,0.1)
self.agent.grow(-0.0002,0.25)
self.agent.grow(-0.0004,0.25)
self.agent.grow(-0.0008,0.25)
self.agent.grow(-0.0004,0.25)
self.agent.grow(-0.0002,0.5)
self.agent.grow(-0.0001,0.5)

end

end

-- Meat of the program
if actionNumber <= idle then

-- Idle action, just looking at the target
self.agent.lookAt(self.target)
self.agent.completeAnimation(idleAnimations[idleIndex])

elseif actionNumber <= idleMove and actionNumber >= idleLow then

-- Idle action, moving to target first
self.agent.lookAt(self.target)
self.agent.setAnimation("Walk")
self.agent.moveTo(self.target)
self.agent.completeAnimation(idleAnimations[idleIndex])

elseif actionNumber <= stomp and actionNumber >= stompLow then

friskystart = friskystart + friskyinc
--while (x>0) do
--self.target = self.agent.findClosestMicro()
--self.target.Delete()
--x = x-1
--end
--self.target = self.agent.findClosestMicro()
-- Stomp action
self.agent.lookAt(self.target)
self.agent.setAnimation("Walk")
self.agent.moveTo(self.target)
self.agent.setAnimation("Idle 2")
self.agent.Stomp(self.target)
self.agent.completeAnimation(idleAnimations[idleIndex])

-- Recheck the target here
if self.target == nil or self.target.isDead() then
self.target = self.agent.findClosestMicro()
if self.target == nil then
self.agent.cancelAction()
return
end
end

elseif actionNumber <= wander and actionNumber >= wanderLow then

-- Variables
local time = math.random(4, 20)

-- Script
self.agent.setAnimation("Walk")
self.agent.wander(time)
self.agent.completeAnimation(idleAnimations[idleIndex])

elseif actionNumber <=bigger and actionNumber >=biggerLow then

self.agent.setAnimation("Idle 2")
self.agent.setAnimation("Jump 4")
self.agent.grow(0.0001,0.1)
self.agent.grow(0.0005,0.1)
self.agent.grow(0.0010,0.05)
self.agent.grow(0.0020,0.05)
self.agent.grow(0.0030,0.05)
self.agent.grow(0.0050,0.3)
self.agent.grow(0.0040,0.2)
self.agent.grow(0.0026,0.1)
self.agent.grow(0.0013,0.1)
self.agent.grow(0.0005,0.1)
self.agent.grow(0.0001,0.1)
self.agent.grow(-0.0004,0.5)
self.agent.grow(-0.0003,0.5)
self.agent.grow(-0.0002,0.5)
self.agent.grow(-0.0001,0.5)
self.agent.completeAnimation("Jump 4")
self.agent.setAnimation("Idle 2")
friskystart = friskystart + friskyinc + friskyinc

elseif actionNumber <=grab and actionNumber >=grabLow then
--x = x + 1
--if self.target == nil or self.target.isDead() then -- when looping the action, it needs to change the self.target when
self.target = self.agent.findClosestMicro() -- the first self.target is dead
if self.target == nil or self.target.isDead() then
self.agent.cancelAction() -- if it can't find a new self.target, then cancel the action
return
end
--end
self.agent.lookAt(self.target)
self.agent.setAnimation("Walk")
self.agent.moveTo(self.target)
self.agent.setAnimation("Idle 2")
self.agent.grab(self.target)
if vore>eat then
self.agent.completeAnimation("Thinking 2")
--self.target.Delete()
self.agent.grow(0.0001,.1)
self.agent.grow(0.0002,.2)
self.agent.grow(0.0004,.5)
self.agent.grow(0.0008,.2)
self.agent.grow(0.0003,.5)
self.agent.grow(0.0002,.2)
self.agent.grow(0.0001,.1)
if self.agent.actionsCompleted() then
self.target.Delete()
end
friskystart = friskystart + friskyinc

--else then
--self.agent.completeAnimation("Dig And Plant Seeds")
--self.target.Delete() -- this kills the player
end

end
end
end

function GiantessAiBehavior:Exit()
Log("Control mode engaged for agent")
end
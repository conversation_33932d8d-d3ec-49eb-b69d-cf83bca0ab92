StompSingle = RegisterBehavior("StompSingle")
StompSingle.scores = {
    hostile = 100,   --[[ the higher the value the more likely to choose that action ]]
    curious = -30,
}
StompSingle.data = {
    agent = {
        type = { "giantess" }
    },
    target = {
        type = { "micro" }
    }
}

KEY_STOMP_SUCCESS = "StompKill"
IDLE_ANIMATION = "Idle 2"
stompAnimModule = require "stomp_anim"

function StompSingle:Update()
    if self.target and self.target.IsDead() and self.started then
        self.agent.dict[KEY_STOMP_SUCCESS] = true
    end

    -- Stop when no target
    if not self.target or not self.target.IsTargettable() or self.target.IsDead() then  
        self.agent.ai.StopBehavior()
        return
    end

    if not self.agent.ai.IsActionActive() then
        self.started = true
        self.agent.lookAt(self.target)
        self.agent.animation.Set("Walk")
        self.agent.moveTo(self.target)
        self.agent.animation.Set(stompAnimModule.getRandomStompAnim())
        self.agent.Stomp(self.target)
    end
end

function StompSingle:Exit()
    self.agent.animation.Set(IDLE_ANIMATION)
end

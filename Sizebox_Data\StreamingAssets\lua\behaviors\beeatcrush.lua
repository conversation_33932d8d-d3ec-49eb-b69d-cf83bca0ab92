-- Define the behavior data
BE_GrowOnCrush = RegisterBehavior("BE Grow On Crush")
BE_GrowOnCrush.data = {
  menuEntry = "BE/BE Grow On Crush",
  flags = { "be" },
  agent = {
    type = { "humanoid" }
  },
  target = {
    type = { "oneself" }
  },
  settings = {
    { "boneNames", "Bone Names", "string", "<PERSON>reast,Ichichi" },
    { "limit", "Limit Size", "float", "3", { "1.2", "8" } },
    { "speed", "Rate of Change", "float", "0.03", { "0.03", "0.2" } }
  }
}

-- Define the growth modes
growthModes = require "growth_modes"
MODE = growthModes.QUADRIC -- change this to select growth mode (from growth_modes.lua)
GROW_DURATION = 1 -- in seconds

-- Define the behavior functions
function BE_GrowOnCrush:Start()
  -- Find the bones to expand
  self.bones = FindBoneNames(self.agent, self.boneNames)
  if not self.bones then
    Game.Toast.New().Print("No bones found to expand in model " .. self.agent.name)
  elseif not self.initiated then
    self.initiated = true
  end
  
  -- Subscribe to the OnCrush event
  self.agent.dict.OnCrush = Event.Register(self, EventCode.OnCrush, self.Listener)
end

function BE_GrowOnCrush:Listener(data)
  -- We need make sure crusher.id is the same as self.agent.id otherwise all event listeners will grow
  if data.crusher and data.crusher.id == self.agent.id and not data.victim.IsCrushed() then
    -- Expand the bones
    for k, v in ipairs(self.bones) do
      v.localScale = v.localScale * (1 + self.speed)
    end
  elseif data.victim and data.victim.id == self.agent.id then
    -- Expand the bones when the character is crushed
    for k, v in ipairs(self.bones) do
      v.localScale = v.localScale * (1 + self.speed)
    end
  end
end

-- Define the FindBoneNames function
function FindBoneNames(entity, boneNames)
  local count = 0
  local foundBones = {}
  for boneName in string.gmatch(boneNames, '([^,]+)') do
    local bones = entity.bones.GetBonesByName(boneName, true)
    if bones then
      for k, v in ipairs(bones) do
        table.insert(foundBones, v)
        count = count + 1
      end
    end
  end
  if count > 0 then
    return foundBones
  else
    return nil
  end
end

-- Size Chain Reaction Script
-- This script creates a chain reaction of size changes that propagate from one character to nearby characters
-- Press 'R' to toggle between growth and shrink mode
-- Press 'F' to toggle between different propagation modes
-- Press 'G' to manually trigger a size pulse that affects nearby characters

SizeChainReaction = RegisterBehavior("Size Chain Reaction")
SizeChainReaction.data = {
    menuEntry = "Size/Size Chain Reaction",
    secondary = true,
    flags = { "grow" },
    agent = {
        type = { "humanoid" }
    },
    target = {
        type = { "oneself" }
    }
}

-- Propagation modes
propagationModes = {
    "Radial",           -- Affects all nearby characters equally
    "Cascade",          -- Affects characters in order of distance
    "Alternating",      -- Alternates between growth and shrink
    "Exponential",      -- Effect diminishes exponentially with distance
    "Random"            -- Random effect on random nearby characters
}

function SizeChainReaction:Start()
    -- Initialize variables
    self.growthMode = true          -- True for growth, false for shrink
    self.propagationMode = 1        -- Current propagation mode
    self.pulseRadius = 50           -- Base radius for size pulse (in meters)
    self.pulseIntensity = 0.2       -- Base intensity of size change
    self.pulseDecay = 0.8           -- How quickly the effect diminishes with distance
    self.pulseCooldown = 3          -- Seconds between automatic pulses
    self.lastPulseTime = 0          -- Time of last pulse
    self.autoPulse = true           -- Whether to automatically pulse
    self.affectedEntities = {}      -- Track affected entities to prevent feedback loops
    
    -- Initialize audio
    self.audio_source = AudioSource:new(self.agent.bones.spine)
    self.audio_source.spatialBlend = 1
    self.audio_source.loop = false
    self.audio_source.clip = "Rumblev1.wav"
    self.audio_source.volume = 0.7
    self.audio_source.pitch = 1.0
    self.audio_source.minDistance = 0.01 * (self.agent.scale * 250)
    
    -- Log instructions
    Log("Size Chain Reaction script started!")
    Log("Press 'R' to toggle between growth and shrink mode")
    Log("Press 'F' to toggle between different propagation modes")
    Log("Press 'G' to manually trigger a size pulse that affects nearby characters")
    Log("Press 'T' to toggle automatic pulsing")
    Log("Current mode: " .. (self.growthMode and "Growth" or "Shrink") .. " - " .. propagationModes[self.propagationMode])
end

function SizeChainReaction:Update()
    -- Check for key presses
    if Input.GetKeyDown("r") then
        self.growthMode = not self.growthMode
        Log("Mode: " .. (self.growthMode and "Growth" or "Shrink"))
    end
    
    if Input.GetKeyDown("f") then
        self:CyclePropagationMode()
    end
    
    if Input.GetKeyDown("g") then
        self:TriggerPulse()
    end
    
    if Input.GetKeyDown("t") then
        self.autoPulse = not self.autoPulse
        Log("Auto pulse: " .. (self.autoPulse and "ON" or "OFF"))
    end
    
    -- Auto pulse logic
    if self.autoPulse and Time.time - self.lastPulseTime > self.pulseCooldown then
        self:TriggerPulse()
    end
end

function SizeChainReaction:TriggerPulse()
    self.lastPulseTime = Time.time
    self.affectedEntities = {}  -- Reset affected entities
    
    -- Add self to affected entities to prevent feedback
    self.affectedEntities[self.agent.id] = true
    
    -- Play sound effect
    self.audio_source.pitch = 0.8 + math.random() * 0.4
    self.audio_source:Play()
    
    -- Apply visual effect to self (placeholder)
    -- In a real implementation, you would use Unity's particle system or shader effects
    
    -- Find nearby entities and apply size change based on propagation mode
    self:PropagateEffect()
    
    -- Apply a small size change to self as well
    local selfGrowthFactor = self.growthMode and 1.05 or 0.95
    self.agent.scale = self.agent.scale * selfGrowthFactor
end

function SizeChainReaction:PropagateEffect()
    -- Get all entities in the scene
    local nearbyEntities = {}
    
    -- For micros
    for _, micro in pairs(micros.list) do
        if not self.affectedEntities[micro.id] then
            local distance = Vector3.Distance(self.agent.transform.position, micro.transform.position)
            if distance <= self.pulseRadius * self.agent.scale then
                table.insert(nearbyEntities, {
                    entity = micro,
                    distance = distance
                })
            end
        end
    end
    
    -- For giantesses
    for _, gts in pairs(giantesses.list) do
        if gts.id ~= self.agent.id and not self.affectedEntities[gts.id] then
            local distance = Vector3.Distance(self.agent.transform.position, gts.transform.position)
            if distance <= self.pulseRadius * self.agent.scale then
                table.insert(nearbyEntities, {
                    entity = gts,
                    distance = distance
                })
            end
        end
    end
    
    -- Sort entities by distance
    table.sort(nearbyEntities, function(a, b) return a.distance < b.distance end)
    
    -- Apply effect based on propagation mode
    if self.propagationMode == 1 then
        -- Radial: Affect all nearby entities equally
        for _, entityData in ipairs(nearbyEntities) do
            self:ApplySizeChange(entityData.entity, entityData.distance)
        end
        
    elseif self.propagationMode == 2 then
        -- Cascade: Affect entities in sequence with delay
        for i, entityData in ipairs(nearbyEntities) do
            -- Use coroutine to delay each entity's size change
            -- Since coroutines aren't directly available, we'll simulate with a time offset
            local delay = i * 0.2  -- 0.2 seconds between each entity
            entityData.entity.RunLater(function()
                self:ApplySizeChange(entityData.entity, entityData.distance)
            end, delay)
        end
        
    elseif self.propagationMode == 3 then
        -- Alternating: Alternate between growth and shrink
        local isGrowth = self.growthMode
        for i, entityData in ipairs(nearbyEntities) do
            self:ApplySizeChange(entityData.entity, entityData.distance, isGrowth)
            isGrowth = not isGrowth
        end
        
    elseif self.propagationMode == 4 then
        -- Exponential: Effect diminishes exponentially with distance
        for _, entityData in ipairs(nearbyEntities) do
            local distanceFactor = math.exp(-entityData.distance / (self.pulseRadius * self.agent.scale * 0.3))
            self:ApplySizeChange(entityData.entity, entityData.distance, nil, distanceFactor)
        end
        
    elseif self.propagationMode == 5 then
        -- Random: Random effect on random nearby entities
        for _, entityData in ipairs(nearbyEntities) do
            if math.random() > 0.5 then
                local randomGrowth = math.random() > 0.5
                self:ApplySizeChange(entityData.entity, entityData.distance, randomGrowth)
            end
        end
    end
end

function SizeChainReaction:ApplySizeChange(entity, distance, overrideGrowth, intensityMultiplier)
    -- Mark entity as affected
    self.affectedEntities[entity.id] = true
    
    -- Determine growth direction
    local isGrowth = overrideGrowth
    if isGrowth == nil then
        isGrowth = self.growthMode
    end
    
    -- Calculate intensity based on distance
    local distanceFactor = 1 - (distance / (self.pulseRadius * self.agent.scale))
    local intensity = self.pulseIntensity * math.pow(distanceFactor, self.pulseDecay)
    
    -- Apply intensity multiplier if provided
    if intensityMultiplier then
        intensity = intensity * intensityMultiplier
    end
    
    -- Calculate growth factor
    local growthFactor = isGrowth and (1 + intensity) or (1 - intensity * 0.5)
    
    -- Apply size change
    local newScale = entity.scale * growthFactor
    
    -- Ensure we don't exceed min/max size
    newScale = math.max(newScale, gts.minSize)
    newScale = math.min(newScale, gts.maxSize)
    
    -- Apply the size change
    entity.scale = newScale
    
    -- Create visual effect (placeholder)
    -- In a real implementation, you would use Unity's particle system or shader effects
end

function SizeChainReaction:CyclePropagationMode()
    self.propagationMode = self.propagationMode % #propagationModes + 1
    Log("Propagation mode: " .. propagationModes[self.propagationMode])
end
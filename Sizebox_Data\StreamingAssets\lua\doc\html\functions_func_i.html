<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Class Members - Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('functions_func_i.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a id="index_i" name="index_i"></a>- i -</h3><ul>
<li>InitState()&#160;:&#160;<a class="el" href="class_lua_1_1_random.html#ac69b6f407406ae02a7595403097ec8a8">Lua.Random</a></li>
<li>Inverse()&#160;:&#160;<a class="el" href="class_lua_1_1_quaternion.html#a324d82496815f927ebcaa21032843276">Lua.Quaternion</a></li>
<li>InverseLerp()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a4344694ab95eb4dc13046b0798a88ff3">Lua.Mathf</a></li>
<li>InverseTransformDirection()&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#a7bf8b1d272b1d893a606c5f38770c433">Lua.Transform</a></li>
<li>InverseTransformPoint()&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#aa32269bd79e72646057908fee2cb7f9e">Lua.Transform</a></li>
<li>InverseTransformVector()&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#ae6bb74c5b90a6f8db4c436a56f24f8eb">Lua.Transform</a></li>
<li>IsActionActive()&#160;:&#160;<a class="el" href="class_lua_1_1_a_i.html#aa05b26304b734cfe6f5a220bb54d9bc7">Lua.AI</a></li>
<li>IsAIEnabled()&#160;:&#160;<a class="el" href="class_lua_1_1_a_i.html#afbbbf8be061465aba93a7b1c73402ae4">Lua.AI</a></li>
<li>IsBehaviorActive()&#160;:&#160;<a class="el" href="class_lua_1_1_a_i.html#a2d682616c9d7a8fd7ea045906a716e02">Lua.AI</a></li>
<li>IsChildOf()&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#ad11ff475738f907fdbdc4009c81ee09e">Lua.Transform</a></li>
<li>IsCompleted()&#160;:&#160;<a class="el" href="class_lua_1_1_animation.html#afd07956e9f1dc6f551d8ca036493a646">Lua.Animation</a></li>
<li>IsCrushed()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a4b4bb9870796b854c0d5666c9cba9375">Lua.Entity</a></li>
<li>IsDead()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a6357c0a54e9aa79beb928d079b42aa76">Lua.Entity</a></li>
<li>isGiantess()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#af72e042e1fd05c66abceebb49ec2caf4">Lua.Entity</a></li>
<li>isHumanoid()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a27a8d5461e9d6890e085b06e2d00f6bd">Lua.Entity</a></li>
<li>IsInPose()&#160;:&#160;<a class="el" href="class_lua_1_1_animation.html#aede5bb0940e1daed76c816ba30dac6f2">Lua.Animation</a></li>
<li>IsInTransition()&#160;:&#160;<a class="el" href="class_lua_1_1_animation.html#a6f6e8dabc438a05a0338f69a25a61d71">Lua.Animation</a></li>
<li>isMicro()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#a5eaa128b6b8cf4aeb7f219edd030d61e">Lua.Entity</a></li>
<li>isPlayer()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#af7b9099c16b719f42e4fdfd82661d259">Lua.Entity</a></li>
<li>IsPowerOfTwo()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a44aca0d32ffbf1de0925f05b65d94032">Lua.Mathf</a></li>
<li>IsStuck()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#ab25d357d357ab0cf7ff9a2e81aa9cb08">Lua.Entity</a></li>
<li>IsTargettable()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#aa8688dacb32db168b780597d8f11622b">Lua.Entity</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

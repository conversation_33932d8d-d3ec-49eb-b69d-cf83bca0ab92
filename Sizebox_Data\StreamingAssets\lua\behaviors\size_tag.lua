local Tag = RegisterBehavior("Size Tag")
Tag.data = {
    menuEntry = "Size Tag",
    agent = { type = { "giantess" } },
    secondary = true
}

local running = false
local timer = 0
local roundTime = 7.0
local growStep = 0
local shrinkStep = 0
local targetSize = 1.0
local minSize = 0.1
local maxSize = 1000
local toast
local audioSource
local soundList = {
    "GaspMoan001_Mmm.ogg",
    "GaspMoan002_Augh_MidLong.ogg",
    "GaspMoan003_Ahh_MidLong.ogg"
}
local allowedMargin = 25
local targetRange = 100

function Tag:Start()
    if not toast then toast = Game.Toast.New() end
    audioSource = AudioSource:new(self.agent.bones.spine)
    audioSource.spatialBlend = 1
    audioSource.loop = false
    audioSource.volume = 1
    running = false
    timer = 0
    toast.Print("Press T to start/stop Size Tag!")
end

function Tag:Update()
    if Input.GetKeyDown("t") then
        running = not running
        timer = 0
        local scale = self.agent.scale or self.agent.localScale or 1.0
        -- Target is always within ±targetRange of current size, clamped to min/max
        targetSize = math.max(minSize, math.min(maxSize, scale + (math.random() * 2 - 1) * targetRange))
        toast.Print("Size Tag started!\nTarget: " .. string.format("%.2f", targetSize))
    end

    if not running then return end

    -- Step size is based on distance to target, minimum 1
    local scale = self.agent.scale or self.agent.localScale or 1.0
    local diff = math.abs(scale - targetSize)
    growStep = math.max(1, math.floor(diff / 5))
    shrinkStep = growStep

    if Input.GetKeyDown("up") then
        scale = math.min(maxSize, scale + growStep)
    elseif Input.GetKeyDown("down") then
        scale = math.max(minSize, scale - shrinkStep)
    end
    if self.agent.scale ~= nil then
        self.agent.scale = scale
    elseif self.agent.localScale ~= nil then
        self.agent.localScale = scale
    end

    timer = timer + Time.deltaTime
    toast.Print("Target: " .. string.format("%.2f", targetSize) ..
        "\nYour size: " .. string.format("%.2f", scale) ..
        "\nStep: " .. growStep ..
        "\nTime left: " .. string.format("%.1f", roundTime - timer) ..
        "\n(Use Up/Down arrows!)")

    if timer >= roundTime then
        timer = 0
        local diff = math.abs(scale - targetSize)
        if diff < allowedMargin then
            -- Reward!
            local newScale = math.min(maxSize, scale + growStep * 5)
            if self.agent.scale ~= nil then
                self.agent.scale = newScale
            elseif self.agent.localScale ~= nil then
                self.agent.localScale = newScale
            end
            audioSource.clip = soundList[math.random(1, #soundList)]
            audioSource:Play()
            toast.Print("Great match! Bonus growth!\nNew target coming up...")
        else
            -- Penalty!
            local newScale = math.max(minSize, scale - growStep * 3)
            if self.agent.scale ~= nil then
                self.agent.scale = newScale
            elseif self.agent.localScale ~= nil then
                self.agent.localScale = newScale
            end
            toast.Print("Missed! Penalty shrink!\nNew target coming up...")
        end
        -- New target is always within ±targetRange of current size, clamped
        local newScaleNow = self.agent.scale or self.agent.localScale or 1.0
        targetSize = math.max(minSize, math.min(maxSize, newScaleNow + (math.random() * 2 - 1) * targetRange))
    end
end
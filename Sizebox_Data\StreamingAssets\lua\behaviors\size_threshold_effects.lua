-- Size Threshold Effects Script
-- This script applies different effects when a character crosses certain size thresholds
-- Press 'H' to toggle the script on/off
-- Press 'J' to cycle through different threshold sets
-- Press 'K' to manually trigger growth to next threshold

SizeThresholdEffects = RegisterBehavior("Size Threshold Effects")
SizeThresholdEffects.data = {
    menuEntry = "Size/Size Threshold Effects",
    secondary = true,
    flags = { "grow" },
    agent = {
        type = { "humanoid" }
    },
    target = {
        type = { "oneself" }
    }
}

-- Threshold sets
thresholdSets = {
    {
        name = "Standard",
        thresholds = {
            { size = 2, name = "Slightly Tall", effect = "minor_boost" },
            { size = 5, name = "Mini Giantess", effect = "speed_boost" },
            { size = 15, name = "Giantess", effect = "growth_boost" },
            { size = 50, name = "Mega Giantess", effect = "stomp_effect" },
            { size = 150, name = "Colossal", effect = "earthquake" },
            { size = 500, name = "Titanic", effect = "destruction_aura" }
        }
    },
    {
        name = "Exponential",
        thresholds = {
            { size = 2, name = "Level 1", effect = "minor_boost" },
            { size = 4, name = "Level 2", effect = "speed_boost" },
            { size = 8, name = "Level 3", effect = "growth_boost" },
            { size = 16, name = "Level 4", effect = "stomp_effect" },
            { size = 32, name = "Level 5", effect = "earthquake" },
            { size = 64, name = "Level 6", effect = "destruction_aura" },
            { size = 128, name = "Level 7", effect = "all_effects" },
            { size = 256, name = "Level 8", effect = "super_growth" }
        }
    },
    {
        name = "Micro Focus",
        thresholds = {
            { size = 0.1, name = "Tiny", effect = "speed_boost" },
            { size = 0.25, name = "Miniature", effect = "minor_boost" },
            { size = 0.5, name = "Doll-sized", effect = "growth_boost" },
            { size = 1, name = "Normal", effect = "reset" }
        }
    }
}

-- Effect durations in seconds
effectDurations = {
    minor_boost = 10,
    speed_boost = 15,
    growth_boost = 8,
    stomp_effect = 12,
    earthquake = 6,
    destruction_aura = 20,
    all_effects = 30,
    super_growth = 5,
    reset = 0
}

function SizeThresholdEffects:Start()
    -- Initialize variables
    self.enabled = true
    self.currentSetIndex = 1
    self.currentSet = thresholdSets[self.currentSetIndex]
    self.lastThresholdIndex = self:GetCurrentThresholdIndex()
    self.activeEffects = {}
    self.growthRate = 0.05
    self.autoGrow = false
    self.startScale = self.agent.scale
    
    -- Initialize audio
    self.audio_source = AudioSource:new(self.agent.bones.spine)
    self.audio_source.spatialBlend = 1
    self.audio_source.loop = false
    self.audio_source.volume = 0.8
    self.audio_source.minDistance = 0.01 * (self.agent.scale * 250)
    
    -- Log instructions
    Log("Size Threshold Effects script started!")
    Log("Press 'H' to toggle the script on/off")
    Log("Press 'J' to cycle through different threshold sets")
    Log("Press 'K' to manually trigger growth to next threshold")
    Log("Press 'L' to toggle auto-growth mode")
    Log("Current threshold set: " .. self.currentSet.name)
    
    -- Display current threshold
    self:LogCurrentThreshold()
end

function SizeThresholdEffects:Update()
    -- Check for key presses
    if Input.GetKeyDown("h") then
        self.enabled = not self.enabled
        Log("Size Threshold Effects: " .. (self.enabled and "ON" or "OFF"))
    end
    
    if Input.GetKeyDown("j") then
        self:CycleThresholdSet()
    end
    
    if Input.GetKeyDown("k") then
        self:GrowToNextThreshold()
    end
    
    if Input.GetKeyDown("l") then
        self.autoGrow = not self.autoGrow
        Log("Auto-growth: " .. (self.autoGrow and "ON" or "OFF"))
    end
    
    -- Skip processing if disabled
    if not self.enabled then
        return
    end
    
    -- Auto-growth logic
    if self.autoGrow then
        self.agent.scale = self.agent.scale * (1 + self.growthRate * Time.deltaTime)
    end
    
    -- Check for threshold crossing
    local currentThresholdIndex = self:GetCurrentThresholdIndex()
    if currentThresholdIndex ~= self.lastThresholdIndex then
        -- Threshold crossed
        self:HandleThresholdCrossing(currentThresholdIndex)
        self.lastThresholdIndex = currentThresholdIndex
    end
    
    -- Update active effects
    self:UpdateActiveEffects()
end

function SizeThresholdEffects:GetCurrentThresholdIndex()
    local thresholds = self.currentSet.thresholds
    local currentSize = self.agent.scale
    
    for i = #thresholds, 1, -1 do
        if currentSize >= thresholds[i].size then
            return i
        end
    end
    
    return 0  -- Below all thresholds
end

function SizeThresholdEffects:HandleThresholdCrossing(newThresholdIndex)
    -- Get threshold info
    local thresholds = self.currentSet.thresholds
    
    -- Check if we've crossed to a higher threshold
    if newThresholdIndex > 0 and newThresholdIndex > self.lastThresholdIndex then
        local threshold = thresholds[newThresholdIndex]
        
        -- Log the achievement
        Log("THRESHOLD REACHED: " .. threshold.name .. " (" .. threshold.size .. "x)")
        
        -- Play sound effect
        self.audio_source.clip = "customboing.wav"
        self.audio_source.pitch = 0.8 + math.random() * 0.4
        self.audio_source:Play()
        
        -- Apply the effect
        self:ApplyEffect(threshold.effect)
    end
end

function SizeThresholdEffects:ApplyEffect(effectName)
    -- Add effect to active effects
    local duration = effectDurations[effectName] or 10
    table.insert(self.activeEffects, {
        name = effectName,
        endTime = Time.time + duration
    })
    
    -- Apply immediate effect
    if effectName == "minor_boost" then
        -- Small growth boost
        self.agent.scale = self.agent.scale * 1.1
        
    elseif effectName == "speed_boost" then
        -- Increase movement speed (placeholder)
        Log("Speed boost activated for " .. duration .. " seconds!")
        
    elseif effectName == "growth_boost" then
        -- Temporary faster growth
        self.growthRate = self.growthRate * 2
        Log("Growth rate doubled for " .. duration .. " seconds!")
        
    elseif effectName == "stomp_effect" then
        -- Add stomp effect (placeholder)
        Log("Stomp effect activated for " .. duration .. " seconds!")
        
    elseif effectName == "earthquake" then
        -- Earthquake effect (placeholder)
        Log("Earthquake effect activated for " .. duration .. " seconds!")
        
    elseif effectName == "destruction_aura" then
        -- Destruction aura (placeholder)
        Log("Destruction aura activated for " .. duration .. " seconds!")
        
    elseif effectName == "all_effects" then
        -- Apply all effects
        for effect, _ in pairs(effectDurations) do
            if effect ~= "all_effects" and effect ~= "super_growth" and effect ~= "reset" then
                self:ApplyEffect(effect)
            end
        end
        
    elseif effectName == "super_growth" then
        -- Extreme growth
        self.agent.scale = self.agent.scale * 2
        Log("Super growth activated!")
        
    elseif effectName == "reset" then
        -- Reset to starting size
        self.agent.scale = self.startScale
        Log("Size reset to starting value!")
    end
end

function SizeThresholdEffects:UpdateActiveEffects()
    local currentTime = Time.time
    local newActiveEffects = {}
    
    for _, effect in ipairs(self.activeEffects) do
        if currentTime < effect.endTime then
            -- Effect still active
            table.insert(newActiveEffects, effect)
        else
            -- Effect expired
            self:EndEffect(effect.name)
        end
    end
    
    self.activeEffects = newActiveEffects
end

function SizeThresholdEffects:EndEffect(effectName)
    -- Handle effect expiration
    if effectName == "growth_boost" then
        -- Reset growth rate
        self.growthRate = 0.05
        Log("Growth boost ended")
    end
    
    -- Other effect expirations would be handled here
end

function SizeThresholdEffects:CycleThresholdSet()
    self.currentSetIndex = self.currentSetIndex % #thresholdSets + 1
    self.currentSet = thresholdSets[self.currentSetIndex]
    self.lastThresholdIndex = self:GetCurrentThresholdIndex()
    
    Log("Threshold set changed to: " .. self.currentSet.name)
    self:LogCurrentThreshold()
end

function SizeThresholdEffects:GrowToNextThreshold()
    local currentThresholdIndex = self:GetCurrentThresholdIndex()
    local thresholds = self.currentSet.thresholds
    
    -- Find next threshold
    local nextThresholdIndex = currentThresholdIndex + 1
    if nextThresholdIndex <= #thresholds then
        local nextThreshold = thresholds[nextThresholdIndex]
        self.agent.scale = nextThreshold.size
        Log("Growing to " .. nextThreshold.name .. " (" .. nextThreshold.size .. "x)")
    else
        Log("Already at maximum threshold!")
    end
end

function SizeThresholdEffects:LogCurrentThreshold()
    local currentThresholdIndex = self:GetCurrentThresholdIndex()
    local thresholds = self.currentSet.thresholds
    
    if currentThresholdIndex > 0 then
        local currentThreshold = thresholds[currentThresholdIndex]
        Log("Current threshold: " .. currentThreshold.name .. " (" .. currentThreshold.size .. "x)")
    else
        Log("Current size is below all thresholds")
    end
end
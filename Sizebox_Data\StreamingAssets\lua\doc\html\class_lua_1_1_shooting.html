<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.Shooting Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_shooting.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="class_lua_1_1_shooting-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.Shooting Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Use this component to control the shooting-related and gun properties of agents.  
 <a href="class_lua_1_1_shooting.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:af0e7769dd39d32787d3197ebee1a3247"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_shooting.html#af0e7769dd39d32787d3197ebee1a3247">SetBurstFire</a> (bool enable)</td></tr>
<tr class="memdesc:af0e7769dd39d32787d3197ebee1a3247"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets/unsets the gun to burst-fire mode  <a href="class_lua_1_1_shooting.html#af0e7769dd39d32787d3197ebee1a3247">More...</a><br /></td></tr>
<tr class="separator:af0e7769dd39d32787d3197ebee1a3247"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adbbd67fdfd65fd5d754e66c44907c974"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_shooting.html#adbbd67fdfd65fd5d754e66c44907c974">SetProjectileColor</a> (int r, int g, int b)</td></tr>
<tr class="memdesc:adbbd67fdfd65fd5d754e66c44907c974"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the color for the projectiles that will be fired by this NPC's gun. RGB values (0-255)  <a href="class_lua_1_1_shooting.html#adbbd67fdfd65fd5d754e66c44907c974">More...</a><br /></td></tr>
<tr class="separator:adbbd67fdfd65fd5d754e66c44907c974"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a916d6868d5ab051898ea2f7d676587aa"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_shooting.html#a916d6868d5ab051898ea2f7d676587aa">SetProjectileSpeed</a> (float speedMult)</td></tr>
<tr class="memdesc:a916d6868d5ab051898ea2f7d676587aa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the speed multiplier for the projectiles that will be fired by this NPC's gun.  <a href="class_lua_1_1_shooting.html#a916d6868d5ab051898ea2f7d676587aa">More...</a><br /></td></tr>
<tr class="separator:a916d6868d5ab051898ea2f7d676587aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a376edd82b6c42daab355831d5ecbc340"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_shooting.html#a376edd82b6c42daab355831d5ecbc340">SetProjectileScale</a> (float scaleMult)</td></tr>
<tr class="memdesc:a376edd82b6c42daab355831d5ecbc340"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the scale/size multiplier for the projectiles that will be fired by this NPC's gun. Default: 1  <a href="class_lua_1_1_shooting.html#a376edd82b6c42daab355831d5ecbc340">More...</a><br /></td></tr>
<tr class="separator:a376edd82b6c42daab355831d5ecbc340"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af28162d50b374775ee7618d1cc6c2063"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_shooting.html#af28162d50b374775ee7618d1cc6c2063">SetFiringSFX</a> (string clip)</td></tr>
<tr class="memdesc:af28162d50b374775ee7618d1cc6c2063"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set a custom sound clip for this specific NPCs/AIs gun's projectile firing sound effect. Pass a nil/null to reset to its original sound effect.  <a href="class_lua_1_1_shooting.html#af28162d50b374775ee7618d1cc6c2063">More...</a><br /></td></tr>
<tr class="separator:af28162d50b374775ee7618d1cc6c2063"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa89af0a3474a9f2ac09e3305986df10d"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_shooting.html#aa89af0a3474a9f2ac09e3305986df10d">SetProjectileImpactSFX</a> (string clip)</td></tr>
<tr class="memdesc:aa89af0a3474a9f2ac09e3305986df10d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set a custom sound clip for this specific NPCs/AIs gun's projectile impact sound effect. Pass a nil/null to reset to its original sound effect.  <a href="class_lua_1_1_shooting.html#aa89af0a3474a9f2ac09e3305986df10d">More...</a><br /></td></tr>
<tr class="separator:aa89af0a3474a9f2ac09e3305986df10d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8bd9055060bba675e803483c5a1f71d2"><td class="memItemLeft" align="right" valign="top"><a id="a8bd9055060bba675e803483c5a1f71d2" name="a8bd9055060bba675e803483c5a1f71d2"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>FixGunAimingOrientation</b> ()</td></tr>
<tr class="memdesc:a8bd9055060bba675e803483c5a1f71d2"><td class="mdescLeft">&#160;</td><td class="mdescRight">In case the gun wasn't positioned correctly at aiming start-up, use this manual function to attempt to fix it. <br /></td></tr>
<tr class="separator:a8bd9055060bba675e803483c5a1f71d2"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="properties" name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a0c512eac7dea63f8594bbfcb0976a34a"><td class="memItemLeft" align="right" valign="top"><a id="a0c512eac7dea63f8594bbfcb0976a34a" name="a0c512eac7dea63f8594bbfcb0976a34a"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>isAiming</b><code> [get]</code></td></tr>
<tr class="memdesc:a0c512eac7dea63f8594bbfcb0976a34a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Whether the micro is currently aiming <br /></td></tr>
<tr class="separator:a0c512eac7dea63f8594bbfcb0976a34a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0b4d9622c4a1888f424ebdc13db498d1"><td class="memItemLeft" align="right" valign="top"><a id="a0b4d9622c4a1888f424ebdc13db498d1" name="a0b4d9622c4a1888f424ebdc13db498d1"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>isFiring</b><code> [get]</code></td></tr>
<tr class="memdesc:a0b4d9622c4a1888f424ebdc13db498d1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Whether the micro is currently firing <br /></td></tr>
<tr class="separator:a0b4d9622c4a1888f424ebdc13db498d1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af30c3a5b6caca44684fcd88ac7f7f4a7"><td class="memItemLeft" align="right" valign="top"><a id="af30c3a5b6caca44684fcd88ac7f7f4a7" name="af30c3a5b6caca44684fcd88ac7f7f4a7"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>accurateFiring</b><code> [get, set]</code></td></tr>
<tr class="memdesc:af30c3a5b6caca44684fcd88ac7f7f4a7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Whether the micro's firing is 100% accurate or not <br /></td></tr>
<tr class="separator:af30c3a5b6caca44684fcd88ac7f7f4a7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7f8c04173b4649a5b6134344e67dc10a"><td class="memItemLeft" align="right" valign="top"><a id="a7f8c04173b4649a5b6134344e67dc10a" name="a7f8c04173b4649a5b6134344e67dc10a"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><b>inaccuracyFactor</b><code> [get, set]</code></td></tr>
<tr class="memdesc:a7f8c04173b4649a5b6134344e67dc10a"><td class="mdescLeft">&#160;</td><td class="mdescRight">How intense the inaccuracy of the firing is (Recommended: 5-20) <br /></td></tr>
<tr class="separator:a7f8c04173b4649a5b6134344e67dc10a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adc410fd560cfdc7bdbd5b870a7adedbf"><td class="memItemLeft" align="right" valign="top"><a id="adc410fd560cfdc7bdbd5b870a7adedbf" name="adc410fd560cfdc7bdbd5b870a7adedbf"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>predictiveAiming</b><code> [get, set]</code></td></tr>
<tr class="memdesc:adc410fd560cfdc7bdbd5b870a7adedbf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Whether the micro should try to predict where a moving target is going to be. <br /></td></tr>
<tr class="separator:adc410fd560cfdc7bdbd5b870a7adedbf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aece1867c834c0c4fc31af53c55c8040e"><td class="memItemLeft" align="right" valign="top"><a id="aece1867c834c0c4fc31af53c55c8040e" name="aece1867c834c0c4fc31af53c55c8040e"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><b>firingInterval</b><code> [get, set]</code></td></tr>
<tr class="memdesc:aece1867c834c0c4fc31af53c55c8040e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Amount of time (in seconds) between each shot or burst fire period. Default: 4 <br /></td></tr>
<tr class="separator:aece1867c834c0c4fc31af53c55c8040e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6ddbbd79abebeb560511e7c00093fb1a"><td class="memItemLeft" align="right" valign="top"><a id="a6ddbbd79abebeb560511e7c00093fb1a" name="a6ddbbd79abebeb560511e7c00093fb1a"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>burstFire</b><code> [get]</code></td></tr>
<tr class="memdesc:a6ddbbd79abebeb560511e7c00093fb1a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Whether the gun is in burst-fire mode or single-fire <br /></td></tr>
<tr class="separator:a6ddbbd79abebeb560511e7c00093fb1a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a41ac94c6ba5c407bd0fcbf0f6e938fd1"><td class="memItemLeft" align="right" valign="top"><a id="a41ac94c6ba5c407bd0fcbf0f6e938fd1" name="a41ac94c6ba5c407bd0fcbf0f6e938fd1"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>burstFireRounds</b><code> [get, set]</code></td></tr>
<tr class="memdesc:a41ac94c6ba5c407bd0fcbf0f6e938fd1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of shots in a single burst fire. Default: 3 <br /></td></tr>
<tr class="separator:a41ac94c6ba5c407bd0fcbf0f6e938fd1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a51b27960d8f74262edb79dc173f75670"><td class="memItemLeft" align="right" valign="top"><a id="a51b27960d8f74262edb79dc173f75670" name="a51b27960d8f74262edb79dc173f75670"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><b>burstFireInterval</b><code> [get, set]</code></td></tr>
<tr class="memdesc:a51b27960d8f74262edb79dc173f75670"><td class="mdescLeft">&#160;</td><td class="mdescRight">Amount of time (in seconds) between each shot in a burst fire. Default: 0.75 <br /></td></tr>
<tr class="separator:a51b27960d8f74262edb79dc173f75670"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >Use this component to control the shooting-related and gun properties of agents. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="af0e7769dd39d32787d3197ebee1a3247" name="af0e7769dd39d32787d3197ebee1a3247"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af0e7769dd39d32787d3197ebee1a3247">&#9670;&nbsp;</a></span>SetBurstFire()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Shooting.SetBurstFire </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>enable</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Sets/unsets the gun to burst-fire mode </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">enable</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="af28162d50b374775ee7618d1cc6c2063" name="af28162d50b374775ee7618d1cc6c2063"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af28162d50b374775ee7618d1cc6c2063">&#9670;&nbsp;</a></span>SetFiringSFX()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Shooting.SetFiringSFX </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>clip</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set a custom sound clip for this specific NPCs/AIs gun's projectile firing sound effect. Pass a nil/null to reset to its original sound effect. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">clip</td><td>the sound clip file name</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="adbbd67fdfd65fd5d754e66c44907c974" name="adbbd67fdfd65fd5d754e66c44907c974"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adbbd67fdfd65fd5d754e66c44907c974">&#9670;&nbsp;</a></span>SetProjectileColor()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Shooting.SetProjectileColor </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>r</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>g</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>b</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set the color for the projectiles that will be fired by this NPC's gun. RGB values (0-255) </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">r</td><td>red color value (0-255)</td></tr>
    <tr><td class="paramname">g</td><td>green color value (0-255)</td></tr>
    <tr><td class="paramname">b</td><td>blue color value (0-255)</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="aa89af0a3474a9f2ac09e3305986df10d" name="aa89af0a3474a9f2ac09e3305986df10d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa89af0a3474a9f2ac09e3305986df10d">&#9670;&nbsp;</a></span>SetProjectileImpactSFX()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Shooting.SetProjectileImpactSFX </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>clip</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set a custom sound clip for this specific NPCs/AIs gun's projectile impact sound effect. Pass a nil/null to reset to its original sound effect. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">clip</td><td>the sound clip file name</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a376edd82b6c42daab355831d5ecbc340" name="a376edd82b6c42daab355831d5ecbc340"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a376edd82b6c42daab355831d5ecbc340">&#9670;&nbsp;</a></span>SetProjectileScale()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Shooting.SetProjectileScale </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>scaleMult</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set the scale/size multiplier for the projectiles that will be fired by this NPC's gun. Default: 1 </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">scaleMult</td><td>default: 1</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a916d6868d5ab051898ea2f7d676587aa" name="a916d6868d5ab051898ea2f7d676587aa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a916d6868d5ab051898ea2f7d676587aa">&#9670;&nbsp;</a></span>SetProjectileSpeed()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Shooting.SetProjectileSpeed </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>speedMult</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set the speed multiplier for the projectiles that will be fired by this NPC's gun. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">speedMult</td><td>default: 1</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaShooting.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_shooting.html">Shooting</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

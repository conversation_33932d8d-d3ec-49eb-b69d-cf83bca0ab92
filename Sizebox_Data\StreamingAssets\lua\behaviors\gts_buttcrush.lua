Buttcrush = RegisterBehavior("Buttcrush")
Buttcrush.scores = {
	normal = 30
}
Buttcrush.data = {
	menuEntry = "Buttcrush",
	ai = true,
	agent = {
		type = { "giantess" }
	},
	target = {
		type = { "micro" }
	},
	tags = "macro, movement, evil",
	settings = {
	}
}

function Buttcrush:Start()
	local targetPosition = self.target.position + (self.target.transform.forward * self:ComputeForwardOffset()) + (self.target.transform.right * self:ComputeSidewaysOffset())
	self.agent.animation.Set("Walk")
	self.agent.MoveTo(targetPosition)
	self.lastPosition = self.target.position
	self.pauseDelay = 0
end

function Buttcrush:Update()
	if not self.agent.ai.IsActionActive() then
		if self.pauseDelay == 0 then
			self.agent.animation.Set("Sit 6")
			self.pauseDelay = self.agent.animation.GetLength() + 1
		else
			self.pauseDelay = self.pauseDelay - Time.deltaTime
			if self.pauseDelay <= 0 then
				self.agent.ai.StopBehavior()
				return
			end
		end
	else
		if Vector3.Distance(self.lastPosition,self.target.position) > self.target.scale then
			local targetPosition = self.target.position + (self.target.transform.forward * self:ComputeForwardOffset()) + (self.target.transform.right * self:ComputeSidewaysOffset())
			self.agent.ai.StopAction()
			self.agent.animation.Set("Walk")
			self.agent.MoveTo(targetPosition)
			self.lastPosition = self.target.position
		end
	end
end

function Buttcrush:Exit()
	self.agent.animation.Set("Idle")
end

function Buttcrush:ComputeForwardOffset()
	local legPos = self.agent.bones.rightUpperLeg.position
	local agentPos = self.agent.position
	local forward = self.agent.transform.forward
	
	local worldOffset = legPos - agentPos
	local forwardOffset = Vector3.Dot(worldOffset,forward)
	
	return math.abs(forwardOffset) * ((self.agent.scale / 4) + self.target.scale)
end

function Buttcrush:ComputeSidewaysOffset()
	local legPos = self.agent.bones.rightUpperLeg.position
	local agentPos = self.agent.position
	local right = self.agent.transform.right -- normalized Vector3
	
	local worldOffset = legPos - agentPos
	local sidewaysOffset = Vector3.Dot(worldOffset,right)
	
	return math.abs(sidewaysOffset)
end

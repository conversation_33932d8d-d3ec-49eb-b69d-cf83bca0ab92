local Rec = RegisterBehavior("<PERSON><PERSON> Simon Say<PERSON>")
Rec.data = {
    menuEntry = "<PERSON><PERSON> Simon Says",
    agent = { type = { "giantess" } },
    secondary = true
}

local targetScale = 1.0
local smoothSpeed = 2.5

-- Make BE (breast expansion) instructions more rare by weighting the instruction pool
local instructions = {
    "Grow!","Grow!","Grow!","Grow!","Grow!","Grow!","Grow!","Grow!","Grow!","Grow!","Grow!","Grow!", -- 12x Grow!
    "Shrink!","Shrink!","Shrink!", -- 3x Shrink!
    "Stay!","Stay!","Stay!","Stay!","Stay!",
    "BE Grow!","BE Shrink!"
}
local keys = { G = "Grow!", H = "Shrink!", J = "Stay!", Y = "BE Grow!", N = "BE Shrink!" }
local currentInstruction = ""
local waitingForInput = false
local inputTimer = 0
local inputTimeLimit = 3.0
local round = 1
local score = 0
local combo = 0

local soundList = {
    "GaspMoan001_Mmm.ogg",
    "GaspMoan002_Augh_MidLong.ogg",
    "GaspMoan003_Ahh_MidLong.ogg"
}
local audioSource
local toast

local growthMultiplier = 5 -- Default multiplier

-- BE (breast expansion) variables
local beScale = 1.0
local beTargetScale = 1.0
local beStep = 0.05 -- More subtle BE change
local beSmoothSpeed = 5 -- Higher = faster interpolation
local beMin = 0.5   -- Minimum BE scale

-- Helper to "color" instruction for toast (ASCII style for compatibility)
local function colorInstruction(instr)
    if instr == "Grow!" then
        return ">>> GROW <<<"
    elseif instr == "Shrink!" then
        return "<<< SHRINK >>>"
    elseif instr == "Stay!" then
        return "--- STAY ---"
    elseif instr == "BE Grow!" then
        return ">>> BE GROW <<<"
    elseif instr == "BE Shrink!" then
        return "<<< BE SHRINK >>>"
    else
        return instr
    end
end

function Rec:Start()
    targetScale = self.agent.scale or self.agent.localScale or 1.0
    beScale = 1.0
    beTargetScale = 1.0
    audioSource = AudioSource:new(self.agent.bones.spine)
    audioSource.spatialBlend = 1
    audioSource.loop = false
    audioSource.volume = 1
    toast = Game.Toast.New()
    round = 1
    score = 0
    combo = 0

    -- True Size Unlock (global, like SizeUnlock.lua)
    if gts and gts.maxSize ~= nil then
        gts.maxSize = 0
    end
    if gts and gts.minSize ~= nil then
        gts.minSize = 0.0000000001 -- Super small min size!
    end
    local ok, _ = pcall(function()
        if self.agent.sizeLocked ~= nil then
            self.agent.sizeLocked = false
        end
    end)

    nextInstruction()
end

function nextInstruction()
    currentInstruction = instructions[math.random(1, #instructions)]
    waitingForInput = true
    inputTimer = 0
    toast.Print("Simon Says: " .. colorInstruction(currentInstruction) ..
        "\n(G=Grow, H=Shrink, J=Stay, Y=BE Grow, N=BE Shrink)\nRound: " .. round .. " | Score: " .. score .. " | Combo: " .. combo)
end

function Rec:Update()
    local dt = Time.deltaTime

    -- Adjust multiplier with arrow keys
    if Input.GetKeyDown("up") then
        growthMultiplier = growthMultiplier + 1
        toast.Print("Growth multiplier: " .. growthMultiplier)
    elseif Input.GetKeyDown("down") then
        growthMultiplier = math.max(0.1, growthMultiplier - 1)
        toast.Print("Growth multiplier: " .. growthMultiplier)
    end

    -- Adjust input time limit with = and -
    if Input.GetKeyDown("=") then
        inputTimeLimit = inputTimeLimit + 0.5
        toast.Print("Input time limit: " .. string.format("%.1f", inputTimeLimit) .. "s")
    elseif Input.GetKeyDown("-") then
        inputTimeLimit = math.max(0.5, inputTimeLimit - 0.5)
        toast.Print("Input time limit: " .. string.format("%.1f", inputTimeLimit) .. "s")
    end

    -- Gradually interpolate BE scale
    if math.abs(beScale - beTargetScale) > 0.001 then
        beScale = beScale + (beTargetScale - beScale) * math.min(1, dt * beSmoothSpeed)
        setBreastScale(self.agent, beScale)
    end

    -- Growth/shrink amount ramps up with combo
    local currentScale = self.agent.scale or self.agent.localScale or 1.0
    local growPercent = 0.10
    local growMin = 0.05
    local comboMultiplier = 1 + (combo * 0.1)
    local growAmount = math.max(growMin, currentScale * growPercent * comboMultiplier)

    local shrinkPercent = 0.10
    local shrinkMin = 0.05
    local shrinkAmount = math.max(shrinkMin, currentScale * shrinkPercent * comboMultiplier)

    -- Handle input if waiting
    if waitingForInput then
        inputTimer = inputTimer + dt
        local pressed = nil
        if Input.GetKeyDown("g") then pressed = "Grow!" end
        if Input.GetKeyDown("h") then pressed = "Shrink!" end
        if Input.GetKeyDown("j") then pressed = "Stay!" end
        if Input.GetKeyDown("y") then pressed = "BE Grow!" end
        if Input.GetKeyDown("n") then pressed = "BE Shrink!" end

        if pressed then
            if pressed == currentInstruction then
                -- Success!
                score = score + 1
                combo = combo + 1
                if pressed == "BE Grow!" then
                    beTargetScale = beTargetScale + beStep
                    audioSource.clip = soundList[math.random(1, #soundList)]
                    audioSource:Play()
                elseif pressed == "BE Shrink!" then
                    beTargetScale = math.max(beMin, beTargetScale - beStep)
                elseif pressed == "Grow!" then
                    targetScale = targetScale + growAmount
                elseif pressed == "Shrink!" then
                    targetScale = math.max(targetScale - shrinkAmount, gts and gts.minSize or 0.0000000001)
                end
                toast.Print("Success! (" .. colorInstruction(currentInstruction) .. ")\nScore: " .. score ..
                    "\nChange: " .. string.format("%.2f", growAmount) .. "\nMultiplier: " .. growthMultiplier ..
                    "\nCombo: " .. combo ..
                    "\nBE Scale: " .. string.format("%.2f", beTargetScale))
                waitingForInput = false
                round = round + 1
                inputTimeLimit = math.max(1.0, inputTimeLimit - 0.1)
            else
                -- Penalty for wrong input: lose a point, reset multiplier and combo, show combo break
                score = math.max(0, score - 1)
                growthMultiplier = 5
                combo = 0
                toast.Print("Wrong key! Combo broken!\nMultiplier reset to 5.\n(" .. colorInstruction(currentInstruction) .. ")\nScore: " .. score .. "\nCombo: 0")
                waitingForInput = false
                round = round + 1
            end
        elseif inputTimer > inputTimeLimit then
            -- Timeout = fail
            score = math.max(0, score - 1)
            growthMultiplier = 5
            combo = 0
            toast.Print("Too slow! Combo broken!\nMultiplier reset to 5.\n(" .. colorInstruction(currentInstruction) .. ")\nScore: " .. score ..
                "\nMultiplier: " .. growthMultiplier ..
                "\nCombo: 0" ..
                "\nBE Scale: " .. string.format("%.2f", beTargetScale))
            waitingForInput = false
            round = round + 1
            inputTimeLimit = math.max(1.0, inputTimeLimit - 0.1)
        end
    else
        -- Wait a moment, then next instruction
        inputTimer = inputTimer + dt
        if inputTimer > 1.0 then
            nextInstruction()
        end
    end

    -- Smoothly approach target scale
    if math.abs(currentScale - targetScale) > 0.01 then
        local newScale = currentScale + (targetScale - currentScale) * math.min(1, dt * smoothSpeed)
        if self.agent.scale ~= nil then
            self.agent.scale = newScale
        elseif self.agent.localScale ~= nil then
            self.agent.localScale = newScale
        end
    end
end

-- Helper function to set breast scale (works for most models)
function setBreastScale(agent, scale)
    local breastBones = {}
    local boneNames = { "Breast", "Ichichi", "LeftBreast", "RightBreast", "leftbreast", "rightbreast", "breast left", "breast right", "hidarichichi", "migichichi", "lPectoral", "rPectoral" }
    for _, name in ipairs(boneNames) do
        local bones = agent.bones.GetBonesByName(name, true)
        if bones then
            for _, bone in ipairs(bones) do
                table.insert(breastBones, bone)
            end
        end
    end
    for _, bone in ipairs(breastBones) do
        bone.localScale = Vector3.New(scale, scale, scale)
    end
end
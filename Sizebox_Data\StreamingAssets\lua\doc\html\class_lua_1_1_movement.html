<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.Movement Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_movement.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="class_lua_1_1_movement-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.Movement Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Use this component to control the movement of agents.  
 <a href="class_lua_1_1_movement.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a6e9301bc7326c56f23fbb2eac2ad2f54"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_movement.html#a6e9301bc7326c56f23fbb2eac2ad2f54">MoveTowards</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> point)</td></tr>
<tr class="memdesc:a6e9301bc7326c56f23fbb2eac2ad2f54"><td class="mdescLeft">&#160;</td><td class="mdescRight">Move the character towards a point in worlds space, during one frame (to be used in Update())  <a href="class_lua_1_1_movement.html#a6e9301bc7326c56f23fbb2eac2ad2f54">More...</a><br /></td></tr>
<tr class="separator:a6e9301bc7326c56f23fbb2eac2ad2f54"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab1a73835885dca908bb6e5d23509ce40"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_movement.html#ab1a73835885dca908bb6e5d23509ce40">MoveDirection</a> (<a class="el" href="class_lua_1_1_vector3.html">Vector3</a> direction)</td></tr>
<tr class="memdesc:ab1a73835885dca908bb6e5d23509ce40"><td class="mdescLeft">&#160;</td><td class="mdescRight">Moves to a direction relative to the player position during one frame (to be used in update).  <a href="class_lua_1_1_movement.html#ab1a73835885dca908bb6e5d23509ce40">More...</a><br /></td></tr>
<tr class="separator:ab1a73835885dca908bb6e5d23509ce40"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a26e7ee9591b48c6ddf74becf73ebe078"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_movement.html#a26e7ee9591b48c6ddf74becf73ebe078">Turn</a> (float degrees)</td></tr>
<tr class="memdesc:a26e7ee9591b48c6ddf74becf73ebe078"><td class="mdescLeft">&#160;</td><td class="mdescRight">Turns the player the specified amount of degrees. Positive degree is right, and negative is left.  <a href="class_lua_1_1_movement.html#a26e7ee9591b48c6ddf74becf73ebe078">More...</a><br /></td></tr>
<tr class="separator:a26e7ee9591b48c6ddf74becf73ebe078"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="properties" name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a72e61b8d3a308361d8bb6d80367af2df"><td class="memItemLeft" align="right" valign="top"><a id="a72e61b8d3a308361d8bb6d80367af2df" name="a72e61b8d3a308361d8bb6d80367af2df"></a>
float&#160;</td><td class="memItemRight" valign="bottom"><b>speed</b><code> [get, set]</code></td></tr>
<tr class="memdesc:a72e61b8d3a308361d8bb6d80367af2df"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maximal movement speed. <br /></td></tr>
<tr class="separator:a72e61b8d3a308361d8bb6d80367af2df"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >Use this component to control the movement of agents. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="ab1a73835885dca908bb6e5d23509ce40" name="ab1a73835885dca908bb6e5d23509ce40"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab1a73835885dca908bb6e5d23509ce40">&#9670;&nbsp;</a></span>MoveDirection()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Movement.MoveDirection </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>direction</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Moves to a direction relative to the player position during one frame (to be used in update). </p>
<p ><a class="el" href="class_lua_1_1_vector3.html#ad8be15240d9bfa336d926ab023f11ad4" title="Shorthand for writing Vector3(0, 0, 1).">Vector3.forward</a> (0,0,1) will move the player forward. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">direction</td><td>Direction relative to the player point of view.</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a6e9301bc7326c56f23fbb2eac2ad2f54" name="a6e9301bc7326c56f23fbb2eac2ad2f54"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6e9301bc7326c56f23fbb2eac2ad2f54">&#9670;&nbsp;</a></span>MoveTowards()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Movement.MoveTowards </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>point</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Move the character towards a point in worlds space, during one frame (to be used in Update()) </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">point</td><td>The point where the character will be heading</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a26e7ee9591b48c6ddf74becf73ebe078" name="a26e7ee9591b48c6ddf74becf73ebe078"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a26e7ee9591b48c6ddf74becf73ebe078">&#9670;&nbsp;</a></span>Turn()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Movement.Turn </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>degrees</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Turns the player the specified amount of degrees. Positive degree is right, and negative is left. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">degrees</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaMovement.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_movement.html">Movement</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

#!/usr/bin/env python3
"""
Array-based Sizebox Spawn Key Patcher
Targets P-O byte pairs that might be key mapping arrays
"""

import os
import shutil

def backup_file(source, backup_dir):
    """Create a backup of the original file"""
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    
    backup_path = os.path.join(backup_dir, os.path.basename(source) + '.array_backup')
    shutil.copy2(source, backup_path)
    print(f"Backup created: {backup_path}")
    return backup_path

def find_po_arrays(dll_data):
    """Find P-O byte pairs that might be key arrays"""
    
    print("Searching for P-O key arrays...")
    
    p_byte = 0x50  # P
    o_byte = 0x4F  # O
    f9_byte = 0x78  # F9
    f10_byte = 0x79  # F10
    
    # Find places where P and O bytes are close together
    po_pairs = []
    
    for i in range(len(dll_data) - 20):
        if dll_data[i] == p_byte:
            # Look for O within next 20 bytes
            for j in range(i + 1, min(i + 21, len(dll_data))):
                if dll_data[j] == o_byte:
                    # Found P followed by O within 20 bytes
                    context_start = max(0, i - 30)
                    context_end = min(len(dll_data), j + 30)
                    context_bytes = dll_data[context_start:context_end]
                    
                    # Check if this looks like a key array (has other key-like bytes)
                    key_like_bytes = 0
                    for k in range(context_start, context_end):
                        byte_val = dll_data[k]
                        # Count bytes that look like key codes (A-Z range mostly)
                        if 0x41 <= byte_val <= 0x5A:  # A-Z
                            key_like_bytes += 1
                    
                    po_pairs.append({
                        'p_pos': i,
                        'o_pos': j,
                        'distance': j - i,
                        'context': context_bytes,
                        'key_like_score': key_like_bytes,
                        'context_start': context_start,
                        'context_end': context_end
                    })
    
    # Sort by key_like_score (higher is better)
    po_pairs.sort(key=lambda x: x['key_like_score'], reverse=True)
    
    print(f"Found {len(po_pairs)} P-O byte pairs")
    
    # Show top candidates
    print("\nTop P-O array candidates:")
    for i, pair in enumerate(po_pairs[:10]):
        print(f"{i+1}. P at 0x{pair['p_pos']:08X}, O at 0x{pair['o_pos']:08X}")
        print(f"   Distance: {pair['distance']}, Key-like score: {pair['key_like_score']}")
        print(f"   Context: {pair['context'].hex()}")
        
        # Try to decode context as ASCII to see if it makes sense
        try:
            ascii_context = ''.join([chr(b) if 32 <= b <= 126 else '.' for b in pair['context']])
            print(f"   ASCII: {ascii_context}")
        except:
            pass
        print()
    
    return po_pairs

def patch_po_arrays():
    """Main array patching function"""
    dll_path = r"Sizebox_Data\Managed\Assembly-CSharp.dll"
    backup_dir = r"Sizebox_Backup_Array"
    
    print("Array-based Sizebox Spawn Key Patcher")
    print("====================================")
    print("This will target P-O byte pairs that might be key arrays")
    print()
    
    # Verify file exists
    if not os.path.exists(dll_path):
        print(f"ERROR: {dll_path} not found!")
        return False
    
    # Create backup
    backup_path = backup_file(dll_path, backup_dir)
    
    # Load the DLL
    with open(dll_path, 'rb') as f:
        dll_data = bytearray(f.read())
    
    print(f"Loaded DLL: {len(dll_data)} bytes")
    
    # Find P-O arrays
    po_pairs = find_po_arrays(dll_data)
    
    if not po_pairs:
        print("No P-O arrays found!")
        return False
    
    # Focus on the most promising candidates (top 5)
    top_candidates = po_pairs[:5]
    
    print(f"\nWill patch top {len(top_candidates)} P-O array candidates")
    print("These are most likely to be key mapping arrays")
    
    proceed = input(f"\nPatch {len(top_candidates)} P-O arrays? (y/N): ").lower().strip()
    if proceed != 'y':
        print("Aborted.")
        return False
    
    # Apply array patches
    patches_applied = 0
    
    for i, pair in enumerate(top_candidates):
        p_pos = pair['p_pos']
        o_pos = pair['o_pos']
        
        print(f"\nPatching array {i+1}:")
        print(f"  P at 0x{p_pos:08X} -> F9 (0x78)")
        print(f"  O at 0x{o_pos:08X} -> F10 (0x79)")
        
        # Patch P -> F9
        dll_data[p_pos] = 0x78  # F9
        patches_applied += 1
        
        # Patch O -> F10
        dll_data[o_pos] = 0x79  # F10
        patches_applied += 1
        
        print(f"  Applied 2 patches for array {i+1}")
    
    if patches_applied == 0:
        print("No patches applied!")
        return False
    
    # Write the patched DLL
    patched_path = dll_path + ".array_patched"
    with open(patched_path, 'wb') as f:
        f.write(dll_data)
    
    print(f"\nArray patch ready!")
    print(f"Applied {patches_applied} array patches to {len(top_candidates)} P-O pairs")
    
    apply = input("\nApply array patch to game? (y/N): ").lower().strip()
    if apply == 'y':
        # Backup original and apply patch
        if not os.path.exists(dll_path + ".original"):
            shutil.copy2(dll_path, dll_path + ".original")
        shutil.copy2(patched_path, dll_path)
        print("✓ Array patch applied!")
        print("✓ Spawn keys changed: P -> F9, O -> F10 (array level)")
        print("✓ Restart Sizebox to test!")
        return True
    else:
        print("Patch not applied.")
        return True

def restore_original():
    """Restore the original DLL"""
    dll_path = r"Sizebox_Data\Managed\Assembly-CSharp.dll"
    original_path = dll_path + ".original"
    
    if os.path.exists(original_path):
        shutil.copy2(original_path, dll_path)
        print("✓ Original DLL restored")
        return True
    else:
        print("✗ Original backup not found")
        return False

if __name__ == "__main__":
    try:
        print("Array-based Sizebox Spawn Key Patcher")
        print("====================================")
        print("1. Apply array spawn key patch (P->F9, O->F10)")
        print("2. Restore original keys")
        print()
        
        choice = input("Enter choice (1 or 2): ").strip()
        
        if choice == "1":
            success = patch_po_arrays()
        elif choice == "2":
            success = restore_original()
        else:
            print("Invalid choice!")
            success = False
            
        if success:
            print("\nOperation completed!")
        else:
            print("\nOperation failed!")
            
    except Exception as e:
        print(f"Error: {e}")
    
    input("Press Enter to exit...")

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.Morphs Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_morphs.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="class_lua_1_1_morphs-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.Morphs Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Component to control the morphs for giantess entities.  
 <a href="class_lua_1_1_morphs.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:ad8c500e4a1dafbc13c39b762b860d49b"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_morphs.html#ad8c500e4a1dafbc13c39b762b860d49b">GetMorphCount</a> ()</td></tr>
<tr class="memdesc:ad8c500e4a1dafbc13c39b762b860d49b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets the amount of morphs this entity has.  <a href="class_lua_1_1_morphs.html#ad8c500e4a1dafbc13c39b762b860d49b">More...</a><br /></td></tr>
<tr class="separator:ad8c500e4a1dafbc13c39b762b860d49b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aef69d4abbbf5f61ff1a2d4fe50737b4b"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_morphs.html#aef69d4abbbf5f61ff1a2d4fe50737b4b">FindMorphIndex</a> (string morphName)</td></tr>
<tr class="memdesc:aef69d4abbbf5f61ff1a2d4fe50737b4b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets the index of a morph by name.  <a href="class_lua_1_1_morphs.html#aef69d4abbbf5f61ff1a2d4fe50737b4b">More...</a><br /></td></tr>
<tr class="separator:aef69d4abbbf5f61ff1a2d4fe50737b4b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1fc28aa3c4e3aa18fd044f2420d9a32b"><td class="memItemLeft" align="right" valign="top"><a id="a1fc28aa3c4e3aa18fd044f2420d9a32b" name="a1fc28aa3c4e3aa18fd044f2420d9a32b"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>ResetMorphs</b> ()</td></tr>
<tr class="memdesc:a1fc28aa3c4e3aa18fd044f2420d9a32b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reset this entity's morphs. Please try not to use this method if you plan on releasing the script you use it in. <br /></td></tr>
<tr class="separator:a1fc28aa3c4e3aa18fd044f2420d9a32b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a223235569f3712c33c6975f26ec83549"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_morphs.html#a223235569f3712c33c6975f26ec83549">HasMorph</a> (string morphName)</td></tr>
<tr class="memdesc:a223235569f3712c33c6975f26ec83549"><td class="mdescLeft">&#160;</td><td class="mdescRight">Checks if this entity has a morph.  <a href="class_lua_1_1_morphs.html#a223235569f3712c33c6975f26ec83549">More...</a><br /></td></tr>
<tr class="separator:a223235569f3712c33c6975f26ec83549"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aefc93668891557c3c980857b3ee6aa0b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_morphs.html#aefc93668891557c3c980857b3ee6aa0b">SetMorphValue</a> (string morphName, float weight)</td></tr>
<tr class="memdesc:aefc93668891557c3c980857b3ee6aa0b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Will set the morph to the weight if the entity is a Giantess.  <a href="class_lua_1_1_morphs.html#aefc93668891557c3c980857b3ee6aa0b">More...</a><br /></td></tr>
<tr class="separator:aefc93668891557c3c980857b3ee6aa0b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2abc83753280bacf187fe9be8215327b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_morphs.html#a2abc83753280bacf187fe9be8215327b">SetMorphValue</a> (int morphIndex, float weight)</td></tr>
<tr class="memdesc:a2abc83753280bacf187fe9be8215327b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Will set the morph to the weight if the entity is a Giantess.  <a href="class_lua_1_1_morphs.html#a2abc83753280bacf187fe9be8215327b">More...</a><br /></td></tr>
<tr class="separator:a2abc83753280bacf187fe9be8215327b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa2abe4bd241377c0589799e32bb6fe55"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_morphs.html#aa2abe4bd241377c0589799e32bb6fe55">GetMorphValue</a> (string morphName)</td></tr>
<tr class="memdesc:aa2abe4bd241377c0589799e32bb6fe55"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the weight of the morph.  <a href="class_lua_1_1_morphs.html#aa2abe4bd241377c0589799e32bb6fe55">More...</a><br /></td></tr>
<tr class="separator:aa2abe4bd241377c0589799e32bb6fe55"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a18a50bb7510eb2c33f285a0cccbee9a4"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_morphs.html#a18a50bb7510eb2c33f285a0cccbee9a4">GetMorphValue</a> (int morphIndex)</td></tr>
<tr class="memdesc:a18a50bb7510eb2c33f285a0cccbee9a4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the weight of the morph.  <a href="class_lua_1_1_morphs.html#a18a50bb7510eb2c33f285a0cccbee9a4">More...</a><br /></td></tr>
<tr class="separator:a18a50bb7510eb2c33f285a0cccbee9a4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae9a3153146428adc11341bddf1363442"><td class="memItemLeft" align="right" valign="top">string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_morphs.html#ae9a3153146428adc11341bddf1363442">GetMorphName</a> (int index)</td></tr>
<tr class="memdesc:ae9a3153146428adc11341bddf1363442"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the name of a morph at the given index.  <a href="class_lua_1_1_morphs.html#ae9a3153146428adc11341bddf1363442">More...</a><br /></td></tr>
<tr class="separator:ae9a3153146428adc11341bddf1363442"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae7b53cf0daa20f7fa1baa338badcb633"><td class="memItemLeft" align="right" valign="top">string[]&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_morphs.html#ae7b53cf0daa20f7fa1baa338badcb633">GetMorphList</a> ()</td></tr>
<tr class="memdesc:ae7b53cf0daa20f7fa1baa338badcb633"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets a list of morphs for this Giantess.  <a href="class_lua_1_1_morphs.html#ae7b53cf0daa20f7fa1baa338badcb633">More...</a><br /></td></tr>
<tr class="separator:ae7b53cf0daa20f7fa1baa338badcb633"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >Component to control the morphs for giantess entities. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="aef69d4abbbf5f61ff1a2d4fe50737b4b" name="aef69d4abbbf5f61ff1a2d4fe50737b4b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aef69d4abbbf5f61ff1a2d4fe50737b4b">&#9670;&nbsp;</a></span>FindMorphIndex()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int Lua.Morphs.FindMorphIndex </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>morphName</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Gets the index of a morph by name. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">morphName</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The morph index (or -1 if it could not find the morph)</dd></dl>

</div>
</div>
<a id="ad8c500e4a1dafbc13c39b762b860d49b" name="ad8c500e4a1dafbc13c39b762b860d49b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad8c500e4a1dafbc13c39b762b860d49b">&#9670;&nbsp;</a></span>GetMorphCount()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int Lua.Morphs.GetMorphCount </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Gets the amount of morphs this entity has. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ae7b53cf0daa20f7fa1baa338badcb633" name="ae7b53cf0daa20f7fa1baa338badcb633"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae7b53cf0daa20f7fa1baa338badcb633">&#9670;&nbsp;</a></span>GetMorphList()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">string[] Lua.Morphs.GetMorphList </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Gets a list of morphs for this Giantess. </p>
<dl class="section return"><dt>Returns</dt><dd>A list of morphs</dd></dl>

</div>
</div>
<a id="ae9a3153146428adc11341bddf1363442" name="ae9a3153146428adc11341bddf1363442"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae9a3153146428adc11341bddf1363442">&#9670;&nbsp;</a></span>GetMorphName()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">string Lua.Morphs.GetMorphName </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>index</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the name of a morph at the given index. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">index</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The name of the morph</dd></dl>

</div>
</div>
<a id="a18a50bb7510eb2c33f285a0cccbee9a4" name="a18a50bb7510eb2c33f285a0cccbee9a4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a18a50bb7510eb2c33f285a0cccbee9a4">&#9670;&nbsp;</a></span>GetMorphValue() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Morphs.GetMorphValue </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>morphIndex</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the weight of the morph. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">morphIndex</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The weight of the specified morph</dd></dl>

</div>
</div>
<a id="aa2abe4bd241377c0589799e32bb6fe55" name="aa2abe4bd241377c0589799e32bb6fe55"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa2abe4bd241377c0589799e32bb6fe55">&#9670;&nbsp;</a></span>GetMorphValue() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.Morphs.GetMorphValue </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>morphName</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the weight of the morph. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">morphName</td><td></td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The weight of the specified morph</dd></dl>

</div>
</div>
<a id="a223235569f3712c33c6975f26ec83549" name="a223235569f3712c33c6975f26ec83549"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a223235569f3712c33c6975f26ec83549">&#9670;&nbsp;</a></span>HasMorph()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.Morphs.HasMorph </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>morphName</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Checks if this entity has a morph. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">morphName</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a2abc83753280bacf187fe9be8215327b" name="a2abc83753280bacf187fe9be8215327b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2abc83753280bacf187fe9be8215327b">&#9670;&nbsp;</a></span>SetMorphValue() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Morphs.SetMorphValue </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>morphIndex</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>weight</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Will set the morph to the weight if the entity is a Giantess. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">morphIndex</td><td></td></tr>
    <tr><td class="paramname">weight</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="aefc93668891557c3c980857b3ee6aa0b" name="aefc93668891557c3c980857b3ee6aa0b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aefc93668891557c3c980857b3ee6aa0b">&#9670;&nbsp;</a></span>SetMorphValue() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.Morphs.SetMorphValue </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>morphName</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>weight</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Will set the morph to the weight if the entity is a Giantess. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">morphName</td><td></td></tr>
    <tr><td class="paramname">weight</td><td></td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaMorphs.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_morphs.html">Morphs</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.CustomSoundManager Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_custom_sound_manager.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="class_lua_1_1_custom_sound_manager-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.CustomSoundManager Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>An interface to change in-game sound effects with custom sounds in the Sounds folder  
 <a href="class_lua_1_1_custom_sound_manager.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-static-methods" name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:a77518b2fe52893bc0d3b7473d5ac33d8"><td class="memItemLeft" align="right" valign="top">static void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_custom_sound_manager.html#a77518b2fe52893bc0d3b7473d5ac33d8">SetPlayerRaygunArmingSFX</a> (string clip)</td></tr>
<tr class="memdesc:a77518b2fe52893bc0d3b7473d5ac33d8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set a custom sound clip for the player raygun's arming/equipping sound effect. Pass a nil/null to reset to its original sound effect.  <a href="class_lua_1_1_custom_sound_manager.html#a77518b2fe52893bc0d3b7473d5ac33d8">More...</a><br /></td></tr>
<tr class="separator:a77518b2fe52893bc0d3b7473d5ac33d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aedbcce119473f77ce87a9742c2a3bd2d"><td class="memItemLeft" align="right" valign="top">static void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_custom_sound_manager.html#aedbcce119473f77ce87a9742c2a3bd2d">SetPlayerRaygunDisarmingSFX</a> (string clip)</td></tr>
<tr class="memdesc:aedbcce119473f77ce87a9742c2a3bd2d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set a custom sound clip for the player raygun's disarming/unequipping sound effect. Pass a nil/null to reset to its original sound effect.  <a href="class_lua_1_1_custom_sound_manager.html#aedbcce119473f77ce87a9742c2a3bd2d">More...</a><br /></td></tr>
<tr class="separator:aedbcce119473f77ce87a9742c2a3bd2d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6ad73ad3423d997434d87a1829f9d910"><td class="memItemLeft" align="right" valign="top">static void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_custom_sound_manager.html#a6ad73ad3423d997434d87a1829f9d910">SetPlayerRaygunModeSwitchSFX</a> (string clip)</td></tr>
<tr class="memdesc:a6ad73ad3423d997434d87a1829f9d910"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set a custom sound clip for the player raygun's firing mode switch sound effect. Pass a nil/null to reset to its original sound effect.  <a href="class_lua_1_1_custom_sound_manager.html#a6ad73ad3423d997434d87a1829f9d910">More...</a><br /></td></tr>
<tr class="separator:a6ad73ad3423d997434d87a1829f9d910"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa79c6dbaef8130988fcdebb2437cb36e"><td class="memItemLeft" align="right" valign="top">static void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_custom_sound_manager.html#aa79c6dbaef8130988fcdebb2437cb36e">SetPlayerRaygunUtilitySFX</a> (string clip)</td></tr>
<tr class="memdesc:aa79c6dbaef8130988fcdebb2437cb36e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set a custom sound clip for the player raygun's utility (Shift + scroll wheel) sound effect. Pass a nil/null to reset to its original sound effect.  <a href="class_lua_1_1_custom_sound_manager.html#aa79c6dbaef8130988fcdebb2437cb36e">More...</a><br /></td></tr>
<tr class="separator:aa79c6dbaef8130988fcdebb2437cb36e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeb74ff0f630bbb90bada33635f382d66"><td class="memItemLeft" align="right" valign="top">static void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_custom_sound_manager.html#aeb74ff0f630bbb90bada33635f382d66">SetPlayerRaygunPolaritySFX</a> (string clip)</td></tr>
<tr class="memdesc:aeb74ff0f630bbb90bada33635f382d66"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set a custom sound clip for the player raygun's polarity change sound effect. Pass a nil/null to reset to its original sound effect.  <a href="class_lua_1_1_custom_sound_manager.html#aeb74ff0f630bbb90bada33635f382d66">More...</a><br /></td></tr>
<tr class="separator:aeb74ff0f630bbb90bada33635f382d66"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acb7307ffeed459bb90bf63f20f5fa3b9"><td class="memItemLeft" align="right" valign="top">static void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_custom_sound_manager.html#acb7307ffeed459bb90bf63f20f5fa3b9">SetPlayerRaygunProjectileFireSFX</a> (string clip)</td></tr>
<tr class="memdesc:acb7307ffeed459bb90bf63f20f5fa3b9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set a custom sound clip for the player raygun's projectile firing sound effect. Pass a nil/null to reset to its original sound effect.  <a href="class_lua_1_1_custom_sound_manager.html#acb7307ffeed459bb90bf63f20f5fa3b9">More...</a><br /></td></tr>
<tr class="separator:acb7307ffeed459bb90bf63f20f5fa3b9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5de014d5743332790a05044b5f918ee6"><td class="memItemLeft" align="right" valign="top">static void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_custom_sound_manager.html#a5de014d5743332790a05044b5f918ee6">SetPlayerRaygunProjectileImpactSFX</a> (string clip)</td></tr>
<tr class="memdesc:a5de014d5743332790a05044b5f918ee6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set a custom sound clip for the player raygun's projectile impact sound effect. Pass a nil/null to reset to its original sound effect.  <a href="class_lua_1_1_custom_sound_manager.html#a5de014d5743332790a05044b5f918ee6">More...</a><br /></td></tr>
<tr class="separator:a5de014d5743332790a05044b5f918ee6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3d7a983d62142d44f24d0e3dea537a65"><td class="memItemLeft" align="right" valign="top">static void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_custom_sound_manager.html#a3d7a983d62142d44f24d0e3dea537a65">SetPlayerRaygunLaserSFX</a> (string clip)</td></tr>
<tr class="memdesc:a3d7a983d62142d44f24d0e3dea537a65"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set a custom sound clip for the player raygun's laser sound effect. Pass a nil/null to reset to its original sound effect.  <a href="class_lua_1_1_custom_sound_manager.html#a3d7a983d62142d44f24d0e3dea537a65">More...</a><br /></td></tr>
<tr class="separator:a3d7a983d62142d44f24d0e3dea537a65"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a923ad0297c9ec8e83ec48880b29a7670"><td class="memItemLeft" align="right" valign="top">static void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_custom_sound_manager.html#a923ad0297c9ec8e83ec48880b29a7670">SetPlayerRaygunSonicFireSFX</a> (string clip)</td></tr>
<tr class="memdesc:a923ad0297c9ec8e83ec48880b29a7670"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set a custom sound clip for the player raygun's initial sonic firing sound effect. Pass a nil/null to reset to its original sound effect.  <a href="class_lua_1_1_custom_sound_manager.html#a923ad0297c9ec8e83ec48880b29a7670">More...</a><br /></td></tr>
<tr class="separator:a923ad0297c9ec8e83ec48880b29a7670"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a09ff65bfa0a9c0144771c73edcc3643a"><td class="memItemLeft" align="right" valign="top">static void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_custom_sound_manager.html#a09ff65bfa0a9c0144771c73edcc3643a">SetPlayerRaygunSonicSustainSFX</a> (string clip)</td></tr>
<tr class="memdesc:a09ff65bfa0a9c0144771c73edcc3643a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set a custom sound clip for the player raygun's sonic sound effect. Pass a nil/null to reset to its original sound effect.  <a href="class_lua_1_1_custom_sound_manager.html#a09ff65bfa0a9c0144771c73edcc3643a">More...</a><br /></td></tr>
<tr class="separator:a09ff65bfa0a9c0144771c73edcc3643a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a920b942d5d7751c3ff7676a48189c8ca"><td class="memItemLeft" align="right" valign="top">static void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_custom_sound_manager.html#a920b942d5d7751c3ff7676a48189c8ca">SetNpcRaygunProjectileFireSFX</a> (string clip)</td></tr>
<tr class="memdesc:a920b942d5d7751c3ff7676a48189c8ca"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set a custom sound clip for NPCs/AIs raygun's projectile firing sound effect. Pass a nil/null to reset to its original sound effect.  <a href="class_lua_1_1_custom_sound_manager.html#a920b942d5d7751c3ff7676a48189c8ca">More...</a><br /></td></tr>
<tr class="separator:a920b942d5d7751c3ff7676a48189c8ca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5cf94ae4cc82a6ae9137b953c2b37b0f"><td class="memItemLeft" align="right" valign="top">static void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_custom_sound_manager.html#a5cf94ae4cc82a6ae9137b953c2b37b0f">SetNpcRaygunProjectileImpactSFX</a> (string clip)</td></tr>
<tr class="memdesc:a5cf94ae4cc82a6ae9137b953c2b37b0f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set a custom sound clip for NPCs/AIs raygun's projectile impact sound effect. Pass a nil/null to reset to its original sound effect.  <a href="class_lua_1_1_custom_sound_manager.html#a5cf94ae4cc82a6ae9137b953c2b37b0f">More...</a><br /></td></tr>
<tr class="separator:a5cf94ae4cc82a6ae9137b953c2b37b0f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a98258d37f9bfcd6d0ca5a53974629ab7"><td class="memItemLeft" align="right" valign="top">static void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_custom_sound_manager.html#a98258d37f9bfcd6d0ca5a53974629ab7">SetNpcSmgProjectileFireSFX</a> (string clip)</td></tr>
<tr class="memdesc:a98258d37f9bfcd6d0ca5a53974629ab7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set a custom sound clip for NPCs/AIs SMG's projectile firing sound effect. Pass a nil/null to reset to its original sound effect.  <a href="class_lua_1_1_custom_sound_manager.html#a98258d37f9bfcd6d0ca5a53974629ab7">More...</a><br /></td></tr>
<tr class="separator:a98258d37f9bfcd6d0ca5a53974629ab7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac2be140403d813db6f58549e8e1ec024"><td class="memItemLeft" align="right" valign="top">static void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_custom_sound_manager.html#ac2be140403d813db6f58549e8e1ec024">SetNpcSmgProjectileImpactSFX</a> (string clip)</td></tr>
<tr class="memdesc:ac2be140403d813db6f58549e8e1ec024"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set a custom sound clip for NPCs/AIs SMG's projectile impact sound effect. Pass a nil/null to reset to its original sound effect.  <a href="class_lua_1_1_custom_sound_manager.html#ac2be140403d813db6f58549e8e1ec024">More...</a><br /></td></tr>
<tr class="separator:ac2be140403d813db6f58549e8e1ec024"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >An interface to change in-game sound effects with custom sounds in the Sounds folder </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a920b942d5d7751c3ff7676a48189c8ca" name="a920b942d5d7751c3ff7676a48189c8ca"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a920b942d5d7751c3ff7676a48189c8ca">&#9670;&nbsp;</a></span>SetNpcRaygunProjectileFireSFX()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static void Lua.CustomSoundManager.SetNpcRaygunProjectileFireSFX </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>clip</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Set a custom sound clip for NPCs/AIs raygun's projectile firing sound effect. Pass a nil/null to reset to its original sound effect. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">clip</td><td>the sound clip file name</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a5cf94ae4cc82a6ae9137b953c2b37b0f" name="a5cf94ae4cc82a6ae9137b953c2b37b0f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5cf94ae4cc82a6ae9137b953c2b37b0f">&#9670;&nbsp;</a></span>SetNpcRaygunProjectileImpactSFX()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static void Lua.CustomSoundManager.SetNpcRaygunProjectileImpactSFX </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>clip</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Set a custom sound clip for NPCs/AIs raygun's projectile impact sound effect. Pass a nil/null to reset to its original sound effect. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">clip</td><td>the sound clip file name</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a98258d37f9bfcd6d0ca5a53974629ab7" name="a98258d37f9bfcd6d0ca5a53974629ab7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a98258d37f9bfcd6d0ca5a53974629ab7">&#9670;&nbsp;</a></span>SetNpcSmgProjectileFireSFX()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static void Lua.CustomSoundManager.SetNpcSmgProjectileFireSFX </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>clip</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Set a custom sound clip for NPCs/AIs SMG's projectile firing sound effect. Pass a nil/null to reset to its original sound effect. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">clip</td><td>the sound clip file name</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ac2be140403d813db6f58549e8e1ec024" name="ac2be140403d813db6f58549e8e1ec024"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac2be140403d813db6f58549e8e1ec024">&#9670;&nbsp;</a></span>SetNpcSmgProjectileImpactSFX()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static void Lua.CustomSoundManager.SetNpcSmgProjectileImpactSFX </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>clip</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Set a custom sound clip for NPCs/AIs SMG's projectile impact sound effect. Pass a nil/null to reset to its original sound effect. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">clip</td><td>the sound clip file name</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a77518b2fe52893bc0d3b7473d5ac33d8" name="a77518b2fe52893bc0d3b7473d5ac33d8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a77518b2fe52893bc0d3b7473d5ac33d8">&#9670;&nbsp;</a></span>SetPlayerRaygunArmingSFX()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static void Lua.CustomSoundManager.SetPlayerRaygunArmingSFX </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>clip</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Set a custom sound clip for the player raygun's arming/equipping sound effect. Pass a nil/null to reset to its original sound effect. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">clip</td><td>the sound clip file name</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="aedbcce119473f77ce87a9742c2a3bd2d" name="aedbcce119473f77ce87a9742c2a3bd2d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aedbcce119473f77ce87a9742c2a3bd2d">&#9670;&nbsp;</a></span>SetPlayerRaygunDisarmingSFX()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static void Lua.CustomSoundManager.SetPlayerRaygunDisarmingSFX </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>clip</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Set a custom sound clip for the player raygun's disarming/unequipping sound effect. Pass a nil/null to reset to its original sound effect. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">clip</td><td>the sound clip file name</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a3d7a983d62142d44f24d0e3dea537a65" name="a3d7a983d62142d44f24d0e3dea537a65"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3d7a983d62142d44f24d0e3dea537a65">&#9670;&nbsp;</a></span>SetPlayerRaygunLaserSFX()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static void Lua.CustomSoundManager.SetPlayerRaygunLaserSFX </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>clip</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Set a custom sound clip for the player raygun's laser sound effect. Pass a nil/null to reset to its original sound effect. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">clip</td><td>the sound clip file name</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a6ad73ad3423d997434d87a1829f9d910" name="a6ad73ad3423d997434d87a1829f9d910"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6ad73ad3423d997434d87a1829f9d910">&#9670;&nbsp;</a></span>SetPlayerRaygunModeSwitchSFX()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static void Lua.CustomSoundManager.SetPlayerRaygunModeSwitchSFX </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>clip</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Set a custom sound clip for the player raygun's firing mode switch sound effect. Pass a nil/null to reset to its original sound effect. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">clip</td><td>the sound clip file name</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="aeb74ff0f630bbb90bada33635f382d66" name="aeb74ff0f630bbb90bada33635f382d66"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aeb74ff0f630bbb90bada33635f382d66">&#9670;&nbsp;</a></span>SetPlayerRaygunPolaritySFX()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static void Lua.CustomSoundManager.SetPlayerRaygunPolaritySFX </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>clip</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Set a custom sound clip for the player raygun's polarity change sound effect. Pass a nil/null to reset to its original sound effect. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">clip</td><td>the sound clip file name</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="acb7307ffeed459bb90bf63f20f5fa3b9" name="acb7307ffeed459bb90bf63f20f5fa3b9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acb7307ffeed459bb90bf63f20f5fa3b9">&#9670;&nbsp;</a></span>SetPlayerRaygunProjectileFireSFX()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static void Lua.CustomSoundManager.SetPlayerRaygunProjectileFireSFX </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>clip</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Set a custom sound clip for the player raygun's projectile firing sound effect. Pass a nil/null to reset to its original sound effect. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">clip</td><td>the sound clip file name</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a5de014d5743332790a05044b5f918ee6" name="a5de014d5743332790a05044b5f918ee6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5de014d5743332790a05044b5f918ee6">&#9670;&nbsp;</a></span>SetPlayerRaygunProjectileImpactSFX()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static void Lua.CustomSoundManager.SetPlayerRaygunProjectileImpactSFX </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>clip</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Set a custom sound clip for the player raygun's projectile impact sound effect. Pass a nil/null to reset to its original sound effect. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">clip</td><td>the sound clip file name</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a923ad0297c9ec8e83ec48880b29a7670" name="a923ad0297c9ec8e83ec48880b29a7670"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a923ad0297c9ec8e83ec48880b29a7670">&#9670;&nbsp;</a></span>SetPlayerRaygunSonicFireSFX()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static void Lua.CustomSoundManager.SetPlayerRaygunSonicFireSFX </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>clip</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Set a custom sound clip for the player raygun's initial sonic firing sound effect. Pass a nil/null to reset to its original sound effect. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">clip</td><td>the sound clip file name</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a09ff65bfa0a9c0144771c73edcc3643a" name="a09ff65bfa0a9c0144771c73edcc3643a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a09ff65bfa0a9c0144771c73edcc3643a">&#9670;&nbsp;</a></span>SetPlayerRaygunSonicSustainSFX()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static void Lua.CustomSoundManager.SetPlayerRaygunSonicSustainSFX </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>clip</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Set a custom sound clip for the player raygun's sonic sound effect. Pass a nil/null to reset to its original sound effect. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">clip</td><td>the sound clip file name</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="aa79c6dbaef8130988fcdebb2437cb36e" name="aa79c6dbaef8130988fcdebb2437cb36e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa79c6dbaef8130988fcdebb2437cb36e">&#9670;&nbsp;</a></span>SetPlayerRaygunUtilitySFX()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static void Lua.CustomSoundManager.SetPlayerRaygunUtilitySFX </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>clip</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Set a custom sound clip for the player raygun's utility (Shift + scroll wheel) sound effect. Pass a nil/null to reset to its original sound effect. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">clip</td><td>the sound clip file name</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaSfxManager.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_custom_sound_manager.html">CustomSoundManager</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

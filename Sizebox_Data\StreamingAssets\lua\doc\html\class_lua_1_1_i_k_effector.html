<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.IKEffector Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_i_k_effector.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#properties">Properties</a> &#124;
<a href="class_lua_1_1_i_k_effector-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.IKEffector Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Each effector lets you control one bone and animate the body.  
 <a href="class_lua_1_1_i_k_effector.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="properties" name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a8fca3762ba9e4b8e90d21f7bd701048a"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_i_k_effector.html#a8fca3762ba9e4b8e90d21f7bd701048a">position</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a8fca3762ba9e4b8e90d21f7bd701048a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Target position of the bone.  <a href="class_lua_1_1_i_k_effector.html#a8fca3762ba9e4b8e90d21f7bd701048a">More...</a><br /></td></tr>
<tr class="separator:a8fca3762ba9e4b8e90d21f7bd701048a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9237f631ddbe3043a8be096d4a51a5dd"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_i_k_effector.html#a9237f631ddbe3043a8be096d4a51a5dd">positionWeight</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a9237f631ddbe3043a8be096d4a51a5dd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Position Weight, how much percentage the character bone must match the target position. (0 to 1).  <a href="class_lua_1_1_i_k_effector.html#a9237f631ddbe3043a8be096d4a51a5dd">More...</a><br /></td></tr>
<tr class="separator:a9237f631ddbe3043a8be096d4a51a5dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adb93ca27f68dbfc04d70e0df9f285210"><td class="memItemLeft" align="right" valign="top"><a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_i_k_effector.html#adb93ca27f68dbfc04d70e0df9f285210">rotation</a><code> [get, set]</code></td></tr>
<tr class="memdesc:adb93ca27f68dbfc04d70e0df9f285210"><td class="mdescLeft">&#160;</td><td class="mdescRight">Target Rotation of the bone  <a href="class_lua_1_1_i_k_effector.html#adb93ca27f68dbfc04d70e0df9f285210">More...</a><br /></td></tr>
<tr class="separator:adb93ca27f68dbfc04d70e0df9f285210"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1c101608c632fc144c0a098fc3a37986"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_i_k_effector.html#a1c101608c632fc144c0a098fc3a37986">rotationWeight</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a1c101608c632fc144c0a098fc3a37986"><td class="mdescLeft">&#160;</td><td class="mdescRight">Rotation Weight, how much percentage the bone must match the target rotation (0 to 1).  <a href="class_lua_1_1_i_k_effector.html#a1c101608c632fc144c0a098fc3a37986">More...</a><br /></td></tr>
<tr class="separator:a1c101608c632fc144c0a098fc3a37986"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >Each effector lets you control one bone and animate the body. </p>
</div><h2 class="groupheader">Property Documentation</h2>
<a id="a8fca3762ba9e4b8e90d21f7bd701048a" name="a8fca3762ba9e4b8e90d21f7bd701048a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8fca3762ba9e4b8e90d21f7bd701048a">&#9670;&nbsp;</a></span>position</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a> Lua.IKEffector.position</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Target position of the bone. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a9237f631ddbe3043a8be096d4a51a5dd" name="a9237f631ddbe3043a8be096d4a51a5dd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9237f631ddbe3043a8be096d4a51a5dd">&#9670;&nbsp;</a></span>positionWeight</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.IKEffector.positionWeight</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Position Weight, how much percentage the character bone must match the target position. (0 to 1). </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="adb93ca27f68dbfc04d70e0df9f285210" name="adb93ca27f68dbfc04d70e0df9f285210"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adb93ca27f68dbfc04d70e0df9f285210">&#9670;&nbsp;</a></span>rotation</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="class_lua_1_1_quaternion.html">Quaternion</a> Lua.IKEffector.rotation</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Target Rotation of the bone </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a1c101608c632fc144c0a098fc3a37986" name="a1c101608c632fc144c0a098fc3a37986"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1c101608c632fc144c0a098fc3a37986">&#9670;&nbsp;</a></span>rotationWeight</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.IKEffector.rotationWeight</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Rotation Weight, how much percentage the bone must match the target rotation (0 to 1). </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaIKEffector.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_i_k_effector.html">IKEffector</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

HunterGts = RegisterBehavior("Hunter Gts")

HunterGts.data = { 
    menuEntry = "Walk/Hunt Micros",
    agent = {
        type = { "giantess"},
        exclude = { "player" }
    },
    target = {
        type = { "micro", "player" }
    },
    settings = {
        {"wanderMaxDuration", "Min Wander Duration (seconds)", "string", "3"},
        {"wanderMinDuration", "Max Wander Duration (seconds)", "string", "10"},
        {"sightField", "Field Of View", "string", "120"},
        {"viewRange", "View Range", "string", "150"},
        {"hearRange", "Hear Raygun Range (based on player's scale)", "string", "300"}
    }
}

shrinkRatio = 0.07
idleAnimation = { "Searching Pockets", "Edge Slip", "Look Around 2", "Stomping", "Quick Informal Bow", "No", "Gathering Objects", "Bashful" }
exitIdleAnimation = "Idle 2"
walkAnimation = { "Texting and Walking", "Walk", "Female Walk" }

WAITING, WALKING, STOMPING, WANDERING = 0, 1, 2, 3

stompAnimModule = require "stomp_anim"

function HunterGts:Start()
    --log(self.agent.senses.baseVisibilityDistance)
    self.state = WAITING
    self.waitTime = 0 -- used to delay actions for a given number of seconds
    self.hasLooked1 = false
    self.hasLooked2 = false
    self.agent.senses.fieldOfView = tonumber(self.sightField)
    self.agent.senses.baseVisibilityDistance = tonumber(self.viewRange)
    self.iterator = 0 -- used to delay actions for a given number of frames
    self.pastLastSeenPos = false
    self.stopToCheck = false
    self.targetFound = false
    self.idleAnim = idleAnimation[math.random(#idleAnimation)]
    self.giveUp = false
    self.timesStrolled = 0
    self.target = nil
    self.shotFired = false
end

function HunterGts:Update()

    -- When the player fires the raygun when the gts is in the area, the gts will target the player and record the location of where the raygun was fired
    if player.isAiming and Input.GetMouseButtonUp(0) and self.agent.distanceTo(player.agent.position) <= tonumber(self.hearRange) * player.agent.scale then
        --log(self.agent.distanceTo(player.agent.position))
        self.target = player.agent
        if not self.agent.senses.canSee(self.target) then
            self.lastSeenPos = self.target.position
            self.iterator = 0
        end
        self.shotFired = true
    end

    -- Gts will try to look at their target while idleing
    if self.state == WAITING and self.target and self.target.isMicro() and self.waitTime > Time.time then
        self.agent.LookAt(self.target)
    end

    -- Waiting occurs when gts was pursuing a target, but has lost sight of it
    if self.state == WAITING and self.waitTime < Time.time then
        if not self.target or not self.target.IsTargettable() or self.target.isDead() then --if no target then find new target
            self.target = nil -- removes old target
            self:targetClosestMicroInSight()    -- chooses closest visable micro and targets them
            self.targetFound = false

        elseif self.agent.senses.canSee(self.target) or self.shotFired then -- target can be seen 
            self.agent.animation.Set(walkAnimation[2])
            self.hasLooked1 = false -- resets hasLooked variables if target is spotted
            self.hasLooked2 = false
            self.waitTime = Time.time
            self.shotFired = false
            self.state = WALKING

        elseif not self.hasLooked1 then -- if can't be seen and hasn't already looked, try to look at target
            --self.agent.ai.StopAction()
            if not self.agent.ai.IsActionActive() then
                --self.agent.animation.Set(exitIdleAnimation)
                --log("is looking")
                if self.agent.senses.canSee(self.target) then self.waitTime = Time.time else self.waitTime = Time.time + 5 end
                self.hasLooked1 = true
            end
        else -- can't find current target, look around for a new one
            self.target = nil -- removes old target
            self:targetClosestMicroInSight()    -- chooses closest visable micro and targets them
            self.targetFound = false
        end
        if self.hasLooked2 then
            --self.hasLooked2 = false
            self.hasLooked1 = false
            self.waitTime = Time.time + 5
            self.target = nil -- removes old target
            self.idleAnim = idleAnimation[math.random(#idleAnimation)]
            self.state = WANDERING
        end
    end

    -- Walking occurs when the gts discovers a micro and will begin moving towards them
    if self.state == WALKING and self.target and not self.target.isDead() then

        -- these are used to judge distance between the gts and target micro
        local separation = (self.agent.scale + self.target.scale) * 0.2 
        local targetDirection = self.target.position - self.agent.position
        local sizeRatio = self.agent.scale * shrinkRatio / self.target.scale

        -- runs if target is out of sight and gts has recorded its last known position
        if self.lastSeenPos then
            local targetLegacy = self.lastSeenPos - self.agent.position
            --log(targetLegacy.magnitude)
            if self.iterator == 100 and self.pastLastSeenPos then -- ends walking toward target if 150 frames have past and has already walked past the last seen position of the target before
                self.giveUp = true
            elseif targetLegacy.magnitude - math.abs(self.lastSeenPos.y - self.agent.position.y) < self.agent.scale * 0.5 and self.iterator == 100  then -- ends walking once target reaches last seen position
                self.giveUp = true
            elseif targetLegacy.magnitude < self.agent.scale * 0.3 then -- records that gts has walked past last seen position of target. Mostly necessary due to LookAt() interfereing with other actions
                self.pastLastSeenPos = true
            elseif self.iterator == 100 and targetLegacy.magnitude > self.agent.scale * 50 then
                self.giveUp = true
            end
            if self.giveUp then
                self.iterator = 0
                self.lastSeenPos = nil
                self.agent.ai.StopAction()
                self.agent.animation.Set(exitIdleAnimation)
                self.shotFired = false
                --log("back to waiting")
                self.state = WAITING
                self.giveUp = false
                self.pastLastSeenPos = false
            end
        end

        -- stomps target if within range
        if sizeRatio < 0.95 and targetDirection.magnitude < separation * 2.4 then
            self.agent.ai.StopAction()
            self.agent.animation.Set(exitidleAnimation)
            self.target.Grow(sizeRatio - 1, 2)
            self.state = WAITING
            self.waitTime = Time.time + 2

        elseif sizeRatio >= 0.95 and targetDirection.magnitude < separation * 1.4 then
            self.agent.ai.StopAction()
            self.agent.animation.Set(stompAnimModule.getRandomStompAnim())
            self.agent.Stomp(self.target)
            self.iterator = 0
            self.state = STOMPING
            --log("stomp")

        elseif not self.agent.senses.canSee(self.target) then -- if loses sight of target, records target's last seen position
            if not self.lastSeenPos and self.iterator == 0 then
                self.lastSeenPos = self.target.position
                --log("Target lost")
            end
            if self.iterator == 99 and self.lastSeenPos then -- stops moving towards target if target has not been seen for 99 frames and moves toward their last seen position
                self.agent.ai.StopAction()
                self.agent.animation.Set(walkAnimation[2])
                self.agent.MoveTo(self.lastSeenPos)
            end
            if self.iterator < 100 then -- counts the number of frames passing
                self.iterator = self.iterator + 1
                --log(self.iterator)
            end
        else -- occurs when gts can see target and moves toward them. also resets other variables
            if self.lastSeenPos then
                self.agent.ai.StopAction()
                self.agent.animation.Set(walkAnimation[2])
                self.pastLastSeenPos = false
            end
            self.iterator = 0
            self.agent.MoveTo(self.target)
            self.lastSeenPos = nil
            --log("Seen")
        end
    elseif self.state == WALKING then
        self.iterator = 0
        self.lastSeenPos = nil
        self.agent.ai.StopAction()
        self.agent.animation.Set(exitIdleAnimation)
        self.shotFired = false
        --log("back to waiting")
        self.state = WAITING
        self.giveUp = false
        self.pastLastSeenPos = false
    end

    -- Stomping occurs when the targeted micro is in range of stomping and is small enought to crush
    if self.state == STOMPING then
        if not self.agent.ai.IsActionActive() then -- if gts is done stomping
            self.state = WAITING
            self.waitTime = Time.time + 2
        end
    end

    -- Wandering occurs when gts cannot see any micros. Gts prioritizes wandering toward buildings as apposed to wandering randomly
    if self.state == WANDERING then

        if not self.target and self.iterator == 0 then
            self.target = self.agent.FindRandomBuilding(self.agent)
            self.agent.animation.Set(walkAnimation[math.random(#walkAnimation)])
            --self.agent.MoveTo(self.target)
            self.iterator = 30
            self.targetFound = false
            --log("is working")
        end
        if self.shotFired and player.agent and self.target then
            if self.target == player.agent then
                self.targetFound = true
                self.shotFired = false
            end
        end
        if self.waitTime < Time.time then
            if not self.stopToCheck then
                self.agent.ai.StopAction()
                self.stopToCheck = true
            end
            self:targetClosestMicroInSight()
        end
        if self.hasLooked2 then
            local time = math.random(tonumber(self.wanderMinDuration), tonumber(self.wanderMaxDuration))

            -- Wandering time increases when gts is fairly small compared to micros in the area
            if self.agent.FindClosestMicro() then
                --log(self.agent.FindClosestMicro().scale)
                if self.agent.FindClosestMicro().scale * 3 > self.agent.scale then
                    time = math.random(tonumber(self.wanderMinDuration), tonumber(self.wanderMaxDuration)) * 3
                    --log("tripled")
                elseif self.agent.FindClosestMicro().scale * 10 > self.agent.scale then
                    time = math.random(tonumber(self.wanderMinDuration), tonumber(self.wanderMaxDuration)) * 2
                    --log("doubled")
                end
            else
                self.timesStrolled = self.timesStrolled - 1
            end
            --log(time)
            self.waitTime = Time.time + time
            self.hasLooked2 = false
            self.timesStrolled = self.timesStrolled + 1
            if self.timesStrolled == 5 then
                self.timesStrolled = 0
                --log("Strolled 5 times")
                self.target = nil
            end
            self.stopToCheck = false
            self.idleAnim = idleAnimation[math.random(#idleAnimation)]
            self.agent.animation.Set(walkAnimation[math.random(#walkAnimation)])
            if self.target then
                self.agent.MoveTo(self.target)
                --log("strolling")
            else
                self.agent.wander(time)
                --log("wandering")
            end
        end
        if self.agent.senses.getVisibleMicros(100)[1] then
            self.waitTime = Time.time
        end
        if self.targetFound then
            self.agent.animation.Set(walkAnimation[2])
            self.state = WALKING
            self.iterator = 0
            --log("moving to engage")
            self.idleAnim = idleAnimation[math.random(#idleAnimation)]
            self.hasLooked2 = false
            self.timesStrolled = 0
            self.stopToCheck = false
        elseif self.target then
            local buildingDistance = self.agent.distanceTo(self.target)
            if self.iterator > 0 then
                self.iterator = self.iterator - 1
            end
            if buildingDistance < self.agent.scale * 0.5 then
                self.target = nil
            end
        end
    end
end

function HunterGts:Exit() 
    -- This will be called once the actions is cancelled, or a new action has been selected to replace this one.
end

-- This function is called when gts is stopped and searching for micros
function HunterGts:targetClosestMicroInSight()

    self.microsInSight = self.agent.senses.getVisibleMicros(100) -- List of all micros in giantess's sight
    self.microCount = 1 -- Used to loop through the list
    self.minDistance = 100000
    self.closestMicro = 0
    if player.isAiming and self.agent.senses.canSee(player.agent) then -- Prioritize player aiming raygun
        self.target = player.agent
        self.targetFound = true
    else
        -- Loops through list, looking for and selecting the closest micro
        while self.microsInSight[self.microCount] do
            if self.agent.distanceTo(self.microsInSight[self.microCount]) < self.minDistance then
                self.closestMicro = self.microCount
            end
            self.microCount = self.microCount + 1
        end
        -- Targets closest micro if any are seen
        if self.closestMicro ~= 0 then
            self.target = self.microsInSight[self.closestMicro]
            --log("Target Found")
            self.targetFound = true
        end
    end
    if self.agent.animation.Get() ~= self.idleAnim then
        self.agent.animation.Set(self.idleAnim)
        --log(self.agent.animation.Get())
    end
    
    --log(self.agent.animation.GetProgress())
    -- animations looks down to help spot micros
    if self.agent.animation.GetProgress() >= 0.9 and self.agent.animation.Get() == self.idleAnim then
        self.hasLooked2 = true
        --log(self.agent.animation.GetProgress())
        self.iterator = 0
    end
    if self.targetFound then self.waitTime = Time.time else self.waitTime = Time.time + 0.5 end
end
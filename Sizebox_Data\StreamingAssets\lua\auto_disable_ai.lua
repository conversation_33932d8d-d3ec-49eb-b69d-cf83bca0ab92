-- This script disables AI, growth, movement, and forces idle for every character, new or existing.
-- It also sets min/max size for all entities on load and spawn.

local idleNames = {"Idle 3", "Idle", "Idle 1", "Idle 2", "Idle4", "Idle 4", "Idle5", "Idle 5", "idle", "idle 3", "idle 2", "idle 1"}
local maxTries = 20

local function freeze_and_idle(entity)
    local tries = 0
    local timerHandle
    timerHandle = Timer.Every(1, function()
        tries = tries + 1
        if not entity then return end
        -- Set min/max size (try both direct and growth component)
        entity.maxSize = 100
        entity.minSize = 0.0000001
        if entity.growth then
            entity.growth.maxSize = 100
            entity.growth.minSize = 0.0000001
        end
        -- Disable AI
        if entity.ai and entity.ai.DisableAI then entity.ai.DisableAI() end
        -- Disable growth
        if entity.growth and entity.growth.DisableGrowth then entity.growth.DisableGrowth() end
        -- Stop all animations
        if entity.anim and entity.anim.StopAll then entity.anim.StopAll() end
        -- Disable movement
        if entity.movement and entity.movement.Disable then entity.movement.Disable() end
        if entity.nav and entity.nav.Disable then entity.nav.Disable() end
        if entity.controller and entity.controller.Disable then entity.controller.Disable() end
        -- Try to freeze physics (if supported)
        if entity.physics and entity.physics.SetVelocity then
            entity.physics.SetVelocity({x=0, y=0, z=0})
        end
        if entity.physics and entity.physics.Freeze then
            entity.physics.Freeze(true)
        end
        -- Try to disable all behaviors (if supported)
        if entity.behaviors and entity.behaviors.Clear then
            entity.behaviors.Clear()
        end
        -- Try to play idle animation
        for _, name in ipairs(idleNames) do
            local played = false
            if entity.anim and entity.anim.Play then
                local ok = entity.anim.Play(name)
                if ok ~= false then played = true end
            elseif entity.anim and entity.anim.SetAndWait then
                local ok = entity.anim.SetAndWait(name)
                if ok ~= false then played = true end
            elseif entity.animation and entity.animation.SetAndWait then
                local ok = entity.animation.SetAndWait(name)
                if ok ~= false then played = true end
            elseif entity.animation and entity.animation.Play then
                local ok = entity.animation.Play(name)
                if ok ~= false then played = true end
            end
            if played then break end
        end
        if tries >= maxTries then
            if timerHandle and Timer.Cancel then
                Timer.Cancel(timerHandle)
            end
        end
    end)
end

-- For already existing entities
if Entity and Entity.GetAll then
    for i, entity in ipairs(Entity.GetAll()) do
        freeze_and_idle(entity)
    end
end

-- For new entities as they spawn
if Event and Event.OnEntityInitialized then
    Event.OnEntityInitialized(function(entity)
        freeze_and_idle(entity)
    end)
end
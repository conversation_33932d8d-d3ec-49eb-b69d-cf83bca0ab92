--v1.3 13/04/21
Wander = RegisterBehavior("Wander 2.0")
Wander.data = {
	menuEntry = "Walk/Wander (d)(Macro)",
	ai = true,
	agent = {
		type = {"giantess"}
	},
	target = {
		type = {"none"}
	}
}

--IDLE ANIMATIONS ARRAY
	idles = {
	"Idle", "Idle 4", "Breathing Idle", "Yawn", "Wait Strech Arms", "Wait Torso Twist", "Crossarms", 
	"Crouch Idle", "Look Down", "Thinking 2", "Happy", "Look Away Gesture", "Reflesh", "Scratch Head",
	"Neutral Idle", "Neutral Idle", "Neutral Idle"
	}

function Wander:Start()
	--NUMERICS
	self.minTime = 5 -- min time to wait (seconds)
	self.maxTime = 20  -- max time to wait (seconds)

	--BOOLEANS
	self.log1 = false
	self.stop = false -- i added a stop variable to end the behavior.. this is custom for this script
	
	--START FIRST ACTION
	self.idleAnimation = idles[math.random(#idles)]
	self.agent.movement.speed = globals["walkspeed"]
	self.agent.animation.Set(globals["walk"])
	local time = math.random(self.minTime, self.maxTime)
	self.agent.wander(time)
	self.agent.animation.Set(self.idleAnimation)

	--STOMP SCRIPT REFS
	self.globalWanderStop = (self.agent.id.."wander stop")
	self.globalWanderOn = (self.agent.id.."wander on")
	
	if globals[self.globalWanderOn] == true then --If Stomp All behavior has enabled Wander, lower the times to wait
		self.minTime = 4
		self.maxTime = 10
	end
	
	--Only do randomseed once per scene
    if not globals["wanderDRand"] then math.randomseed(tonumber(os.time()) + self.agent.id) globals["wanderDRand"] = true end
	
	log("Wandering...")
end

function Wander:Update()

	--STOMP SCRIPT REF --If Stomp All has enabled Wander, and has finally found a target, stop Wander.
	if globals[self.globalWanderStop] == true then
		self.stop = true
	end

	if not self.agent.ai.IsActionActive() then
		if self.stop then
			self.agent.ai.StopBehavior() -- if you use Update() you must manually tell when to end the behavior, after this the Exit() method will run
		else
			if self.agent.animation.IsCompleted() then
				self.idleAnimation = idles[math.random(#idles)]
				self.agent.movement.speed = globals["walkspeed"]
				self.agent.animation.Set(globals["walk"]) -- set the walk animation
				local time = math.random(self.minTime, self.maxTime) -- choose a random number between minTime and maxTime
				self.agent.wander(time)
				self.agent.animation.Set(self.idleAnimation)
			end
		end
	else
		if self.log1 then self.log1 = false end
	end

	--WALK ANIMATION TOGGLE
	if self.agent.GetSelectedEntity() == self.agent then
		if Input.GetKeyDown("right alt") or Input.GetKeyDown("right ctrl") or Input.GetKeyDown("left") then
			self.agent.ai.StopAction()
			self.idleAnimation = idles[math.random(#idles)]
			self.agent.movement.speed = globals["walkspeed"]
			self.agent.animation.Set(globals["walk"]) -- set the walk animation
			local time = math.random(self.minTime, self.maxTime) -- choose a random number between minTime and maxTime
			self.agent.wander(time)
			self.agent.animation.Set(self.idleAnimation)
		end
	end
end

function Wander:Exit()
	if globals[self.globalWanderStop] == true then 
		log("Found a tiny...")
		globals[self.globalWanderStop] = false
		globals[(self.globalWanderOn)] = false
	else
		self.idleAnimation = idles[math.random(#idles)]
		self.agent.animation.Set(self.idleAnimation)
	end
end
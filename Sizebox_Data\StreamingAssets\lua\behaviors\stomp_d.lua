--v1.3 13/04/21
Stomp = RegisterBehavior("Stomp (d)")
Stomp.data = {
	ai = true,
	agent = {
		type = {"giantess"}
	},
	target = {
		type = {"micro"}
	}
}
	
idles = {
	"Neutral Idle", "Neutral Idle", "Neutral Idle", "Neutral Idle", "Neutral Idle", "Idle", "Idle 4",
	"Breathing Idle", "Yawn", "Happy", "Wait Strech Arms", "Wait Torso Twist", "Bashful", "Bored"
	}

function Stomp:Start()
	self.stop = false -- i added a stop variable to end the behavior.. this is custom for this script
	
	--Only do randomseed once per scene
    if not globals["stompDRand"] then math.randomseed(tonumber(os.time()) + self.agent.id) globals["stompDRand"] = true end
end

function Stomp:Update()
	if not self.agent.ai.IsActionActive() then

		if self.stop then
			self.agent.ai.StopBehavior() -- if you use Update() you must manually tell when to end the behavior, after this the Exit() method will run
			return
		else

			if not self.target or self.target.IsDead() then -- when looping the action, it needs to change the self.target when
				self.target = self.agent.findClosestMicro()    -- the first self.target is dead
				if not self.target then
					idleAnimation = idles[math.random(#idles)]
					self.agent.animation.SetAndWait(idleAnimation)
					return
				end
			end

			if self.target.ai and not self.target.isPlayer() then
				 self.target.ai.SetBehavior("Fear") -- i change the behavior of the target, this behavior is defined below
			end

			self.agent.LookAt(self.target)
			self.agent.movement.speed = globals["walkspeed"]
			self.agent.animation.Set(globals["walk"],true)
			self.agent.MoveTo(self.target)
			self.agent.animation.Set("Idle 2",true)
			self.agent.Stomp(self.target)
		end
	end
end

function Stomp:Exit()
	self.agent.LookAt(nil)
	idleAnimation = idles[math.random(#idles)]
	self.agent.animation.Set(idleAnimation,true)
end

StompAll = RegisterBehavior("Stomp All")
StompAll.data = {
	flags = { "stomp" },
	ai = true,
	agent = {
		type = { "giantess"}
	},
	target = {
		type = {"oneself"}
	}
}

function StompAll:Start()
	log("Stomping all micros.")
	self.target = self.agent.findClosestMicro()
	self.wander = false
	self.wandering = false
	
	--Only do randomseed once per scene
    if not globals["stompAllDRand"] then math.randomseed(tonumber(os.time()) + self.agent.id) globals["stompAllDRand"] = true end
end

function StompAll:Update()
	if not self.agent.ai.IsActionActive() then	--Used for not looping stomp action. Also means "what to do when stomp done".
		
		if not self.target or self.target.IsDead() then		--When looping the action, it needs to change the
			self.target = self.agent.findClosestMicro()		--self.target when the first self.target is dead
			
			--NO TARGET FOUND
			if not self.target then

				--IF WANDER OFF
				if not self.wander then
					if self.agent.animation.IsCompleted() then
						idleAnimation = idles[math.random(#idles)]
						self.agent.animation.Set(idleAnimation,true)
					end
					
				--IF WANDER ON
				else
					if not self.wandering then
						if self.agent.animation.IsCompleted() then
							idleAnimation = idles[math.random(#idles)]
							self.agent.animation.Set(idleAnimation,true)
							self.wandering = true
						end
					else
						if self.agent.animation.IsCompleted() then
							self.agent.ai.SetBehavior("Wander 2.0")
							globals[(self.agent.id.."wander on")] = true
						end
					end
				end
			
			--IF TARGET FOUND
			else 

				--IF WANDERING, STOP WANDERING
				if self.wander and self.wandering then
					globals[(self.agent.id.."wander stop")] = true
					globals[(self.agent.id.."wander on")] = false
					self.wandering = false
				end
			end

		--TARGET AQUIRED, START CHASE
		else
			if self.target.ai and not self.target.isPlayer() then
				self.target.ai.SetBehavior("Fear") --Change target behavior to "Fear". Defined below.
			end
			self.agent.LookAt(self.target)
			self.agent.movement.speed = globals["walkspeed"]
			self.agent.animation.Set(globals["walk"],true)
			self.agent.MoveTo(self.target)
			self.agent.animation.Set("Idle 2",true)
			self.agent.Stomp(self.target)
		end
	end
   
	-- KEYBOARD INPUTS --
	--=================--
	if self.agent.GetSelectedEntity() == self.agent then --Toggle wandering when all visible targets are dead
		
		--WANDER TOGGLE
		if Input.GetKeyDown("j") then
			self.wander = not self.wander
			if not self.wander then
				log("Disabled Wandering when out of tinies")
				if not globals[(self.agent.id.."wander stop")] then
					globals[(self.agent.id.."wander stop")] = true
					globals[(self.agent.id.."wander on")] = false
				end
			else
				log("Enabled Wandering when out of tinies")
			end
		end

		--WALK ANIMATION TOGGLE
		if Input.GetKeyDown("right alt") or Input.GetKeyDown("right ctrl") or Input.GetKeyDown("left") then
			self.agent.ai.StopAction()
			if self.target.ai and not self.target.isPlayer() then
				self.target.ai.SetBehavior("Fear")
			end
			self.agent.LookAt(self.target)
			self.agent.movement.speed = globals["walkspeed"]
			self.agent.animation.Set(globals["walk"])
			self.agent.MoveTo(self.target)
			self.agent.animation.Set("Idle 2",true)
			self.agent.Stomp(self.target)
		end
	end
end

StompStop = RegisterBehavior("Stomp All: Stop")
StompStop.data = {
	flags = { "stomp" },
	ai = true,
	agent = {
		type = { "giantess"}
	},
	target = {
		type = {"oneself"}
	}
}

function StompStop:Start()
	globals[(self.agent.id.."wander stop")] = true
	globals[(self.agent.id.."wander on")] = false
	self.agent.ai.StopBehavior()
	self.agent.ai.StopAction()
	idleAnimation = idles[math.random(#idles)]
	self.agent.animation.Set(idleAnimation, true)
end

function StompStop:Exit()
	self.agent.LookAt(nil)
	log("Stopped the stomping of all micros.")
end

-- Here i'm making a custom reaction to this behavior
-- the micro will be scared for at least 10 seconds
MicroFear = RegisterBehavior("Fear")
MicroFear.react = true -- i just mark it as react so is not interrupted by the micro running
MicroFear.data = {
	hideMenu = true,
	agent = {
		type = { "micro"}, 
		exclude = {"player"}
	},
	target = {
		type = {"oneself"}
	}
}

fearAnim = "Nervously Look Around"

function MicroFear:Start()
	roll = math.random(1,10)
	if roll < 5 then
		self.agent.ai.StopBehavior()
	else
		self.agent.animation.Set(fearAnim)
		self.startTime = Time.time
	end
end 

function MicroFear:Update()
	if Time.time < 10 then
		self.agent.ai.StopBehavior()
	end
end

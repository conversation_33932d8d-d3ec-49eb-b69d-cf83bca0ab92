Idle = RegisterBehavior("RIdle")
Idle.scores = {
	normal = 30
}
Idle.data = {
	agent = {
		type = { "humanoid" }
	},
	target = {
		type = { "oneself" }
	},
	tags = "macro, micro, animation",
	settings = {
		{"expressiveAnims", "Expressive Animations", "bool", false}
	}
}


--[[ You can define global data outside of the function.
	 This data will be shared by all characters.
	 If you change something in runtime all characters will be
	 affected by it. ]]

--[[ To declare local variables, use the name of the Idle.variableName = "data",
	 or create the self.variableName = "data" inside a function. ]]

--[[ All entities will use the same set of animation,
	 so it is ok to declare this outside of the function. ]]

basicAnimList = {
	"Neutral Idle"
}

macroAnimList = {
	
}

microAnimList = {
	
}


function Idle:Start()

	if globals["Idle"] then
		self.expressiveAnims = globals["Idle"] == "expressive"
		globals["Idle"] = nil
	end

	if self.expressiveAnims then
		if self.agent.isGiantess() then
			animationList = macroAnimList
		else
			animationList = microAnimList
		end
	else
		animationList = basicAnimList
	end

	-- Only do randomseed once per scene.
	if not globals["idleRand"] then math.randomseed(tonumber(os.time()) + self.agent.id) globals["idleRand"] = true end

	self:SetAnim() -- Calls the Idle:SetAnim() function.
end

function Idle:Update()
	if not self.agent.ai.IsActionActive() then -- Will not run this block until no action is active. "SetAndWait()" constitutes such an action.
		self:SetAnim()
	end
end


function Idle:SetAnim()
	local list = animationList -- Set 'list' as the previously decided animation list
	if self.expressiveAnims then list = math.random(7) < 3 and animationList or basicAnimList end -- If using expressive anims, roll for a 2/7 chance of an expressive anim.
	local animation = list[math.random(#list)]	--[[ Choose animation name via randomized index of 'list'.
													 '#list' returns the length of table 'list' (a number).
													 'math.random(n)' chooses random number between 1 and number 'n'. ]]
	self.agent.animation.SetAndWait(animation)  --[[ Play animation on agent, will wait until it completes once.
													 "IsActionActive()" returns true during that time, because "SetAndWait()" is an action. ]]
end

--[[
	todo:
	- divide anims between basic and expressive, short and long
	- make better balanced odds choosing system
]]

\hypertarget{class_lua_1_1_i_k_effector}{}\section{Lua.\+I\+K\+Effector Class Reference}
\label{class_lua_1_1_i_k_effector}\index{Lua.IKEffector@{Lua.IKEffector}}


Each effector lets you control one bone and animate the body.  


\subsection*{Properties}
\begin{DoxyCompactItemize}
\item 
\mbox{\hyperlink{class_lua_1_1_vector3}{Vector3}} \mbox{\hyperlink{class_lua_1_1_i_k_effector_a8fca3762ba9e4b8e90d21f7bd701048a}{position}}\hspace{0.3cm}{\ttfamily  \mbox{[}get, set\mbox{]}}
\begin{DoxyCompactList}\small\item\em Target position of the bone. \end{DoxyCompactList}\item 
float \mbox{\hyperlink{class_lua_1_1_i_k_effector_a9237f631ddbe3043a8be096d4a51a5dd}{position\+Weight}}\hspace{0.3cm}{\ttfamily  \mbox{[}get, set\mbox{]}}
\begin{DoxyCompactList}\small\item\em Position Weight, how much percentage the character bone must match the target position. (0 to 1). \end{DoxyCompactList}\item 
\mbox{\hyperlink{class_lua_1_1_quaternion}{Quaternion}} \mbox{\hyperlink{class_lua_1_1_i_k_effector_adb93ca27f68dbfc04d70e0df9f285210}{rotation}}\hspace{0.3cm}{\ttfamily  \mbox{[}get, set\mbox{]}}
\begin{DoxyCompactList}\small\item\em Target Rotation of the bone \end{DoxyCompactList}\item 
float \mbox{\hyperlink{class_lua_1_1_i_k_effector_a1c101608c632fc144c0a098fc3a37986}{rotation\+Weight}}\hspace{0.3cm}{\ttfamily  \mbox{[}get, set\mbox{]}}
\begin{DoxyCompactList}\small\item\em Rotation Weight, how much percentage the bone must match the target rotation (0 to 1). \end{DoxyCompactList}\end{DoxyCompactItemize}


\subsection{Detailed Description}
Each effector lets you control one bone and animate the body. 



\subsection{Property Documentation}
\mbox{\Hypertarget{class_lua_1_1_i_k_effector_a8fca3762ba9e4b8e90d21f7bd701048a}\label{class_lua_1_1_i_k_effector_a8fca3762ba9e4b8e90d21f7bd701048a}} 
\index{Lua.IKEffector@{Lua.IKEffector}!position@{position}}
\index{position@{position}!Lua.IKEffector@{Lua.IKEffector}}
\subsubsection{\texorpdfstring{position}{position}}
{\footnotesize\ttfamily \mbox{\hyperlink{class_lua_1_1_vector3}{Vector3}} Lua.\+I\+K\+Effector.\+position\hspace{0.3cm}{\ttfamily [get]}, {\ttfamily [set]}}



Target position of the bone. 

\begin{DoxyReturn}{Returns}

\end{DoxyReturn}
\mbox{\Hypertarget{class_lua_1_1_i_k_effector_a9237f631ddbe3043a8be096d4a51a5dd}\label{class_lua_1_1_i_k_effector_a9237f631ddbe3043a8be096d4a51a5dd}} 
\index{Lua.IKEffector@{Lua.IKEffector}!positionWeight@{positionWeight}}
\index{positionWeight@{positionWeight}!Lua.IKEffector@{Lua.IKEffector}}
\subsubsection{\texorpdfstring{positionWeight}{positionWeight}}
{\footnotesize\ttfamily float Lua.\+I\+K\+Effector.\+position\+Weight\hspace{0.3cm}{\ttfamily [get]}, {\ttfamily [set]}}



Position Weight, how much percentage the character bone must match the target position. (0 to 1). 

\begin{DoxyReturn}{Returns}

\end{DoxyReturn}
\mbox{\Hypertarget{class_lua_1_1_i_k_effector_adb93ca27f68dbfc04d70e0df9f285210}\label{class_lua_1_1_i_k_effector_adb93ca27f68dbfc04d70e0df9f285210}} 
\index{Lua.IKEffector@{Lua.IKEffector}!rotation@{rotation}}
\index{rotation@{rotation}!Lua.IKEffector@{Lua.IKEffector}}
\subsubsection{\texorpdfstring{rotation}{rotation}}
{\footnotesize\ttfamily \mbox{\hyperlink{class_lua_1_1_quaternion}{Quaternion}} Lua.\+I\+K\+Effector.\+rotation\hspace{0.3cm}{\ttfamily [get]}, {\ttfamily [set]}}



Target Rotation of the bone 

\begin{DoxyReturn}{Returns}

\end{DoxyReturn}
\mbox{\Hypertarget{class_lua_1_1_i_k_effector_a1c101608c632fc144c0a098fc3a37986}\label{class_lua_1_1_i_k_effector_a1c101608c632fc144c0a098fc3a37986}} 
\index{Lua.IKEffector@{Lua.IKEffector}!rotationWeight@{rotationWeight}}
\index{rotationWeight@{rotationWeight}!Lua.IKEffector@{Lua.IKEffector}}
\subsubsection{\texorpdfstring{rotationWeight}{rotationWeight}}
{\footnotesize\ttfamily float Lua.\+I\+K\+Effector.\+rotation\+Weight\hspace{0.3cm}{\ttfamily [get]}, {\ttfamily [set]}}



Rotation Weight, how much percentage the bone must match the target rotation (0 to 1). 

\begin{DoxyReturn}{Returns}

\end{DoxyReturn}


The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
Lua\+I\+K\+Effector.\+cs\end{DoxyCompactItemize}

\hypertarget{class_lua_1_1_custom_sound_manager}{}\section{Lua.\+Custom\+Sound\+Manager Class Reference}
\label{class_lua_1_1_custom_sound_manager}\index{Lua.CustomSoundManager@{Lua.CustomSoundManager}}


An interface to change in-\/game sound effects with custom sounds in the Sounds folder  


\subsection*{Static Public Member Functions}
\begin{DoxyCompactItemize}
\item 
static void \mbox{\hyperlink{class_lua_1_1_custom_sound_manager_a77518b2fe52893bc0d3b7473d5ac33d8}{Set\+Player\+Raygun\+Arming\+S\+FX}} (string clip)
\begin{DoxyCompactList}\small\item\em Set a custom sound clip for the player raygun\textquotesingle{}s arming/equipping sound effect. Pass a nil/null to reset to its original sound effect. \end{DoxyCompactList}\item 
static void \mbox{\hyperlink{class_lua_1_1_custom_sound_manager_aedbcce119473f77ce87a9742c2a3bd2d}{Set\+Player\+Raygun\+Disarming\+S\+FX}} (string clip)
\begin{DoxyCompactList}\small\item\em Set a custom sound clip for the player raygun\textquotesingle{}s disarming/unequipping sound effect. Pass a nil/null to reset to its original sound effect. \end{DoxyCompactList}\item 
static void \mbox{\hyperlink{class_lua_1_1_custom_sound_manager_a6ad73ad3423d997434d87a1829f9d910}{Set\+Player\+Raygun\+Mode\+Switch\+S\+FX}} (string clip)
\begin{DoxyCompactList}\small\item\em Set a custom sound clip for the player raygun\textquotesingle{}s firing mode switch sound effect. Pass a nil/null to reset to its original sound effect. \end{DoxyCompactList}\item 
static void \mbox{\hyperlink{class_lua_1_1_custom_sound_manager_aa79c6dbaef8130988fcdebb2437cb36e}{Set\+Player\+Raygun\+Utility\+S\+FX}} (string clip)
\begin{DoxyCompactList}\small\item\em Set a custom sound clip for the player raygun\textquotesingle{}s utility (Shift + scroll wheel) sound effect. Pass a nil/null to reset to its original sound effect. \end{DoxyCompactList}\item 
static void \mbox{\hyperlink{class_lua_1_1_custom_sound_manager_aeb74ff0f630bbb90bada33635f382d66}{Set\+Player\+Raygun\+Polarity\+S\+FX}} (string clip)
\begin{DoxyCompactList}\small\item\em Set a custom sound clip for the player raygun\textquotesingle{}s polarity change sound effect. Pass a nil/null to reset to its original sound effect. \end{DoxyCompactList}\item 
static void \mbox{\hyperlink{class_lua_1_1_custom_sound_manager_acb7307ffeed459bb90bf63f20f5fa3b9}{Set\+Player\+Raygun\+Projectile\+Fire\+S\+FX}} (string clip)
\begin{DoxyCompactList}\small\item\em Set a custom sound clip for the player raygun\textquotesingle{}s projectile firing sound effect. Pass a nil/null to reset to its original sound effect. \end{DoxyCompactList}\item 
static void \mbox{\hyperlink{class_lua_1_1_custom_sound_manager_a5de014d5743332790a05044b5f918ee6}{Set\+Player\+Raygun\+Projectile\+Impact\+S\+FX}} (string clip)
\begin{DoxyCompactList}\small\item\em Set a custom sound clip for the player raygun\textquotesingle{}s projectile impact sound effect. Pass a nil/null to reset to its original sound effect. \end{DoxyCompactList}\item 
static void \mbox{\hyperlink{class_lua_1_1_custom_sound_manager_a3d7a983d62142d44f24d0e3dea537a65}{Set\+Player\+Raygun\+Laser\+S\+FX}} (string clip)
\begin{DoxyCompactList}\small\item\em Set a custom sound clip for the player raygun\textquotesingle{}s laser sound effect. Pass a nil/null to reset to its original sound effect. \end{DoxyCompactList}\item 
static void \mbox{\hyperlink{class_lua_1_1_custom_sound_manager_a923ad0297c9ec8e83ec48880b29a7670}{Set\+Player\+Raygun\+Sonic\+Fire\+S\+FX}} (string clip)
\begin{DoxyCompactList}\small\item\em Set a custom sound clip for the player raygun\textquotesingle{}s initial sonic firing sound effect. Pass a nil/null to reset to its original sound effect. \end{DoxyCompactList}\item 
static void \mbox{\hyperlink{class_lua_1_1_custom_sound_manager_a09ff65bfa0a9c0144771c73edcc3643a}{Set\+Player\+Raygun\+Sonic\+Sustain\+S\+FX}} (string clip)
\begin{DoxyCompactList}\small\item\em Set a custom sound clip for the player raygun\textquotesingle{}s sonic sound effect. Pass a nil/null to reset to its original sound effect. \end{DoxyCompactList}\item 
static void \mbox{\hyperlink{class_lua_1_1_custom_sound_manager_a920b942d5d7751c3ff7676a48189c8ca}{Set\+Npc\+Raygun\+Projectile\+Fire\+S\+FX}} (string clip)
\begin{DoxyCompactList}\small\item\em Set a custom sound clip for N\+P\+Cs/\+A\+Is raygun\textquotesingle{}s projectile firing sound effect. Pass a nil/null to reset to its original sound effect. \end{DoxyCompactList}\item 
static void \mbox{\hyperlink{class_lua_1_1_custom_sound_manager_a5cf94ae4cc82a6ae9137b953c2b37b0f}{Set\+Npc\+Raygun\+Projectile\+Impact\+S\+FX}} (string clip)
\begin{DoxyCompactList}\small\item\em Set a custom sound clip for N\+P\+Cs/\+A\+Is raygun\textquotesingle{}s projectile impact sound effect. Pass a nil/null to reset to its original sound effect. \end{DoxyCompactList}\item 
static void \mbox{\hyperlink{class_lua_1_1_custom_sound_manager_a98258d37f9bfcd6d0ca5a53974629ab7}{Set\+Npc\+Smg\+Projectile\+Fire\+S\+FX}} (string clip)
\begin{DoxyCompactList}\small\item\em Set a custom sound clip for N\+P\+Cs/\+A\+Is S\+MG\textquotesingle{}s projectile firing sound effect. Pass a nil/null to reset to its original sound effect. \end{DoxyCompactList}\item 
static void \mbox{\hyperlink{class_lua_1_1_custom_sound_manager_ac2be140403d813db6f58549e8e1ec024}{Set\+Npc\+Smg\+Projectile\+Impact\+S\+FX}} (string clip)
\begin{DoxyCompactList}\small\item\em Set a custom sound clip for N\+P\+Cs/\+A\+Is S\+MG\textquotesingle{}s projectile impact sound effect. Pass a nil/null to reset to its original sound effect. \end{DoxyCompactList}\end{DoxyCompactItemize}


\subsection{Detailed Description}
An interface to change in-\/game sound effects with custom sounds in the Sounds folder 



\subsection{Member Function Documentation}
\mbox{\Hypertarget{class_lua_1_1_custom_sound_manager_a920b942d5d7751c3ff7676a48189c8ca}\label{class_lua_1_1_custom_sound_manager_a920b942d5d7751c3ff7676a48189c8ca}} 
\index{Lua.CustomSoundManager@{Lua.CustomSoundManager}!SetNpcRaygunProjectileFireSFX@{SetNpcRaygunProjectileFireSFX}}
\index{SetNpcRaygunProjectileFireSFX@{SetNpcRaygunProjectileFireSFX}!Lua.CustomSoundManager@{Lua.CustomSoundManager}}
\subsubsection{\texorpdfstring{SetNpcRaygunProjectileFireSFX()}{SetNpcRaygunProjectileFireSFX()}}
{\footnotesize\ttfamily static void Lua.\+Custom\+Sound\+Manager.\+Set\+Npc\+Raygun\+Projectile\+Fire\+S\+FX (\begin{DoxyParamCaption}\item[{string}]{clip }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}



Set a custom sound clip for N\+P\+Cs/\+A\+Is raygun\textquotesingle{}s projectile firing sound effect. Pass a nil/null to reset to its original sound effect. 


\begin{DoxyParams}{Parameters}
{\em clip} & the sound clip file name\\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{class_lua_1_1_custom_sound_manager_a5cf94ae4cc82a6ae9137b953c2b37b0f}\label{class_lua_1_1_custom_sound_manager_a5cf94ae4cc82a6ae9137b953c2b37b0f}} 
\index{Lua.CustomSoundManager@{Lua.CustomSoundManager}!SetNpcRaygunProjectileImpactSFX@{SetNpcRaygunProjectileImpactSFX}}
\index{SetNpcRaygunProjectileImpactSFX@{SetNpcRaygunProjectileImpactSFX}!Lua.CustomSoundManager@{Lua.CustomSoundManager}}
\subsubsection{\texorpdfstring{SetNpcRaygunProjectileImpactSFX()}{SetNpcRaygunProjectileImpactSFX()}}
{\footnotesize\ttfamily static void Lua.\+Custom\+Sound\+Manager.\+Set\+Npc\+Raygun\+Projectile\+Impact\+S\+FX (\begin{DoxyParamCaption}\item[{string}]{clip }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}



Set a custom sound clip for N\+P\+Cs/\+A\+Is raygun\textquotesingle{}s projectile impact sound effect. Pass a nil/null to reset to its original sound effect. 


\begin{DoxyParams}{Parameters}
{\em clip} & the sound clip file name\\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{class_lua_1_1_custom_sound_manager_a98258d37f9bfcd6d0ca5a53974629ab7}\label{class_lua_1_1_custom_sound_manager_a98258d37f9bfcd6d0ca5a53974629ab7}} 
\index{Lua.CustomSoundManager@{Lua.CustomSoundManager}!SetNpcSmgProjectileFireSFX@{SetNpcSmgProjectileFireSFX}}
\index{SetNpcSmgProjectileFireSFX@{SetNpcSmgProjectileFireSFX}!Lua.CustomSoundManager@{Lua.CustomSoundManager}}
\subsubsection{\texorpdfstring{SetNpcSmgProjectileFireSFX()}{SetNpcSmgProjectileFireSFX()}}
{\footnotesize\ttfamily static void Lua.\+Custom\+Sound\+Manager.\+Set\+Npc\+Smg\+Projectile\+Fire\+S\+FX (\begin{DoxyParamCaption}\item[{string}]{clip }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}



Set a custom sound clip for N\+P\+Cs/\+A\+Is S\+MG\textquotesingle{}s projectile firing sound effect. Pass a nil/null to reset to its original sound effect. 


\begin{DoxyParams}{Parameters}
{\em clip} & the sound clip file name\\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{class_lua_1_1_custom_sound_manager_ac2be140403d813db6f58549e8e1ec024}\label{class_lua_1_1_custom_sound_manager_ac2be140403d813db6f58549e8e1ec024}} 
\index{Lua.CustomSoundManager@{Lua.CustomSoundManager}!SetNpcSmgProjectileImpactSFX@{SetNpcSmgProjectileImpactSFX}}
\index{SetNpcSmgProjectileImpactSFX@{SetNpcSmgProjectileImpactSFX}!Lua.CustomSoundManager@{Lua.CustomSoundManager}}
\subsubsection{\texorpdfstring{SetNpcSmgProjectileImpactSFX()}{SetNpcSmgProjectileImpactSFX()}}
{\footnotesize\ttfamily static void Lua.\+Custom\+Sound\+Manager.\+Set\+Npc\+Smg\+Projectile\+Impact\+S\+FX (\begin{DoxyParamCaption}\item[{string}]{clip }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}



Set a custom sound clip for N\+P\+Cs/\+A\+Is S\+MG\textquotesingle{}s projectile impact sound effect. Pass a nil/null to reset to its original sound effect. 


\begin{DoxyParams}{Parameters}
{\em clip} & the sound clip file name\\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{class_lua_1_1_custom_sound_manager_a77518b2fe52893bc0d3b7473d5ac33d8}\label{class_lua_1_1_custom_sound_manager_a77518b2fe52893bc0d3b7473d5ac33d8}} 
\index{Lua.CustomSoundManager@{Lua.CustomSoundManager}!SetPlayerRaygunArmingSFX@{SetPlayerRaygunArmingSFX}}
\index{SetPlayerRaygunArmingSFX@{SetPlayerRaygunArmingSFX}!Lua.CustomSoundManager@{Lua.CustomSoundManager}}
\subsubsection{\texorpdfstring{SetPlayerRaygunArmingSFX()}{SetPlayerRaygunArmingSFX()}}
{\footnotesize\ttfamily static void Lua.\+Custom\+Sound\+Manager.\+Set\+Player\+Raygun\+Arming\+S\+FX (\begin{DoxyParamCaption}\item[{string}]{clip }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}



Set a custom sound clip for the player raygun\textquotesingle{}s arming/equipping sound effect. Pass a nil/null to reset to its original sound effect. 


\begin{DoxyParams}{Parameters}
{\em clip} & the sound clip file name\\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{class_lua_1_1_custom_sound_manager_aedbcce119473f77ce87a9742c2a3bd2d}\label{class_lua_1_1_custom_sound_manager_aedbcce119473f77ce87a9742c2a3bd2d}} 
\index{Lua.CustomSoundManager@{Lua.CustomSoundManager}!SetPlayerRaygunDisarmingSFX@{SetPlayerRaygunDisarmingSFX}}
\index{SetPlayerRaygunDisarmingSFX@{SetPlayerRaygunDisarmingSFX}!Lua.CustomSoundManager@{Lua.CustomSoundManager}}
\subsubsection{\texorpdfstring{SetPlayerRaygunDisarmingSFX()}{SetPlayerRaygunDisarmingSFX()}}
{\footnotesize\ttfamily static void Lua.\+Custom\+Sound\+Manager.\+Set\+Player\+Raygun\+Disarming\+S\+FX (\begin{DoxyParamCaption}\item[{string}]{clip }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}



Set a custom sound clip for the player raygun\textquotesingle{}s disarming/unequipping sound effect. Pass a nil/null to reset to its original sound effect. 


\begin{DoxyParams}{Parameters}
{\em clip} & the sound clip file name\\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{class_lua_1_1_custom_sound_manager_a3d7a983d62142d44f24d0e3dea537a65}\label{class_lua_1_1_custom_sound_manager_a3d7a983d62142d44f24d0e3dea537a65}} 
\index{Lua.CustomSoundManager@{Lua.CustomSoundManager}!SetPlayerRaygunLaserSFX@{SetPlayerRaygunLaserSFX}}
\index{SetPlayerRaygunLaserSFX@{SetPlayerRaygunLaserSFX}!Lua.CustomSoundManager@{Lua.CustomSoundManager}}
\subsubsection{\texorpdfstring{SetPlayerRaygunLaserSFX()}{SetPlayerRaygunLaserSFX()}}
{\footnotesize\ttfamily static void Lua.\+Custom\+Sound\+Manager.\+Set\+Player\+Raygun\+Laser\+S\+FX (\begin{DoxyParamCaption}\item[{string}]{clip }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}



Set a custom sound clip for the player raygun\textquotesingle{}s laser sound effect. Pass a nil/null to reset to its original sound effect. 


\begin{DoxyParams}{Parameters}
{\em clip} & the sound clip file name\\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{class_lua_1_1_custom_sound_manager_a6ad73ad3423d997434d87a1829f9d910}\label{class_lua_1_1_custom_sound_manager_a6ad73ad3423d997434d87a1829f9d910}} 
\index{Lua.CustomSoundManager@{Lua.CustomSoundManager}!SetPlayerRaygunModeSwitchSFX@{SetPlayerRaygunModeSwitchSFX}}
\index{SetPlayerRaygunModeSwitchSFX@{SetPlayerRaygunModeSwitchSFX}!Lua.CustomSoundManager@{Lua.CustomSoundManager}}
\subsubsection{\texorpdfstring{SetPlayerRaygunModeSwitchSFX()}{SetPlayerRaygunModeSwitchSFX()}}
{\footnotesize\ttfamily static void Lua.\+Custom\+Sound\+Manager.\+Set\+Player\+Raygun\+Mode\+Switch\+S\+FX (\begin{DoxyParamCaption}\item[{string}]{clip }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}



Set a custom sound clip for the player raygun\textquotesingle{}s firing mode switch sound effect. Pass a nil/null to reset to its original sound effect. 


\begin{DoxyParams}{Parameters}
{\em clip} & the sound clip file name\\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{class_lua_1_1_custom_sound_manager_aeb74ff0f630bbb90bada33635f382d66}\label{class_lua_1_1_custom_sound_manager_aeb74ff0f630bbb90bada33635f382d66}} 
\index{Lua.CustomSoundManager@{Lua.CustomSoundManager}!SetPlayerRaygunPolaritySFX@{SetPlayerRaygunPolaritySFX}}
\index{SetPlayerRaygunPolaritySFX@{SetPlayerRaygunPolaritySFX}!Lua.CustomSoundManager@{Lua.CustomSoundManager}}
\subsubsection{\texorpdfstring{SetPlayerRaygunPolaritySFX()}{SetPlayerRaygunPolaritySFX()}}
{\footnotesize\ttfamily static void Lua.\+Custom\+Sound\+Manager.\+Set\+Player\+Raygun\+Polarity\+S\+FX (\begin{DoxyParamCaption}\item[{string}]{clip }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}



Set a custom sound clip for the player raygun\textquotesingle{}s polarity change sound effect. Pass a nil/null to reset to its original sound effect. 


\begin{DoxyParams}{Parameters}
{\em clip} & the sound clip file name\\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{class_lua_1_1_custom_sound_manager_acb7307ffeed459bb90bf63f20f5fa3b9}\label{class_lua_1_1_custom_sound_manager_acb7307ffeed459bb90bf63f20f5fa3b9}} 
\index{Lua.CustomSoundManager@{Lua.CustomSoundManager}!SetPlayerRaygunProjectileFireSFX@{SetPlayerRaygunProjectileFireSFX}}
\index{SetPlayerRaygunProjectileFireSFX@{SetPlayerRaygunProjectileFireSFX}!Lua.CustomSoundManager@{Lua.CustomSoundManager}}
\subsubsection{\texorpdfstring{SetPlayerRaygunProjectileFireSFX()}{SetPlayerRaygunProjectileFireSFX()}}
{\footnotesize\ttfamily static void Lua.\+Custom\+Sound\+Manager.\+Set\+Player\+Raygun\+Projectile\+Fire\+S\+FX (\begin{DoxyParamCaption}\item[{string}]{clip }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}



Set a custom sound clip for the player raygun\textquotesingle{}s projectile firing sound effect. Pass a nil/null to reset to its original sound effect. 


\begin{DoxyParams}{Parameters}
{\em clip} & the sound clip file name\\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{class_lua_1_1_custom_sound_manager_a5de014d5743332790a05044b5f918ee6}\label{class_lua_1_1_custom_sound_manager_a5de014d5743332790a05044b5f918ee6}} 
\index{Lua.CustomSoundManager@{Lua.CustomSoundManager}!SetPlayerRaygunProjectileImpactSFX@{SetPlayerRaygunProjectileImpactSFX}}
\index{SetPlayerRaygunProjectileImpactSFX@{SetPlayerRaygunProjectileImpactSFX}!Lua.CustomSoundManager@{Lua.CustomSoundManager}}
\subsubsection{\texorpdfstring{SetPlayerRaygunProjectileImpactSFX()}{SetPlayerRaygunProjectileImpactSFX()}}
{\footnotesize\ttfamily static void Lua.\+Custom\+Sound\+Manager.\+Set\+Player\+Raygun\+Projectile\+Impact\+S\+FX (\begin{DoxyParamCaption}\item[{string}]{clip }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}



Set a custom sound clip for the player raygun\textquotesingle{}s projectile impact sound effect. Pass a nil/null to reset to its original sound effect. 


\begin{DoxyParams}{Parameters}
{\em clip} & the sound clip file name\\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{class_lua_1_1_custom_sound_manager_a923ad0297c9ec8e83ec48880b29a7670}\label{class_lua_1_1_custom_sound_manager_a923ad0297c9ec8e83ec48880b29a7670}} 
\index{Lua.CustomSoundManager@{Lua.CustomSoundManager}!SetPlayerRaygunSonicFireSFX@{SetPlayerRaygunSonicFireSFX}}
\index{SetPlayerRaygunSonicFireSFX@{SetPlayerRaygunSonicFireSFX}!Lua.CustomSoundManager@{Lua.CustomSoundManager}}
\subsubsection{\texorpdfstring{SetPlayerRaygunSonicFireSFX()}{SetPlayerRaygunSonicFireSFX()}}
{\footnotesize\ttfamily static void Lua.\+Custom\+Sound\+Manager.\+Set\+Player\+Raygun\+Sonic\+Fire\+S\+FX (\begin{DoxyParamCaption}\item[{string}]{clip }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}



Set a custom sound clip for the player raygun\textquotesingle{}s initial sonic firing sound effect. Pass a nil/null to reset to its original sound effect. 


\begin{DoxyParams}{Parameters}
{\em clip} & the sound clip file name\\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{class_lua_1_1_custom_sound_manager_a09ff65bfa0a9c0144771c73edcc3643a}\label{class_lua_1_1_custom_sound_manager_a09ff65bfa0a9c0144771c73edcc3643a}} 
\index{Lua.CustomSoundManager@{Lua.CustomSoundManager}!SetPlayerRaygunSonicSustainSFX@{SetPlayerRaygunSonicSustainSFX}}
\index{SetPlayerRaygunSonicSustainSFX@{SetPlayerRaygunSonicSustainSFX}!Lua.CustomSoundManager@{Lua.CustomSoundManager}}
\subsubsection{\texorpdfstring{SetPlayerRaygunSonicSustainSFX()}{SetPlayerRaygunSonicSustainSFX()}}
{\footnotesize\ttfamily static void Lua.\+Custom\+Sound\+Manager.\+Set\+Player\+Raygun\+Sonic\+Sustain\+S\+FX (\begin{DoxyParamCaption}\item[{string}]{clip }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}



Set a custom sound clip for the player raygun\textquotesingle{}s sonic sound effect. Pass a nil/null to reset to its original sound effect. 


\begin{DoxyParams}{Parameters}
{\em clip} & the sound clip file name\\
\hline
\end{DoxyParams}
\mbox{\Hypertarget{class_lua_1_1_custom_sound_manager_aa79c6dbaef8130988fcdebb2437cb36e}\label{class_lua_1_1_custom_sound_manager_aa79c6dbaef8130988fcdebb2437cb36e}} 
\index{Lua.CustomSoundManager@{Lua.CustomSoundManager}!SetPlayerRaygunUtilitySFX@{SetPlayerRaygunUtilitySFX}}
\index{SetPlayerRaygunUtilitySFX@{SetPlayerRaygunUtilitySFX}!Lua.CustomSoundManager@{Lua.CustomSoundManager}}
\subsubsection{\texorpdfstring{SetPlayerRaygunUtilitySFX()}{SetPlayerRaygunUtilitySFX()}}
{\footnotesize\ttfamily static void Lua.\+Custom\+Sound\+Manager.\+Set\+Player\+Raygun\+Utility\+S\+FX (\begin{DoxyParamCaption}\item[{string}]{clip }\end{DoxyParamCaption})\hspace{0.3cm}{\ttfamily [static]}}



Set a custom sound clip for the player raygun\textquotesingle{}s utility (Shift + scroll wheel) sound effect. Pass a nil/null to reset to its original sound effect. 


\begin{DoxyParams}{Parameters}
{\em clip} & the sound clip file name\\
\hline
\end{DoxyParams}


The documentation for this class was generated from the following file\+:\begin{DoxyCompactItemize}
\item 
Lua\+Sfx\+Manager.\+cs\end{DoxyCompactItemize}

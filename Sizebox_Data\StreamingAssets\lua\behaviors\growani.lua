Spurt = RegisterBehavior("Grow Spurt Ani")
Spurt.agentType = "humanoid"
Spurt.targetType = "oneself"

function Spurt:Start()
    self.Idletrigger = false -- Initialize the variable properly in Start
end

function Spurt:Update()
    -- Check if animation is completed or trigger is set
    if self.agent.animation.IsCompleted() or self.Idletrigger then
        self.agent.animation.Set("Defeat") -- Play Defeat animation
    else
        self.agent.animation.Set("Idle") -- Play Idle animation
        self.Idletrigger = true -- Set trigger
    end

    -- Grow the agent slowly
    self.agent.Grow(0.1, 1) -- (speed, scale amount)
end

function Spurt:End()
    -- Stop growth
    self.agent.Grow(0)
end

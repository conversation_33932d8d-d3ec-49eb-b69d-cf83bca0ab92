<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Class Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('functions_p.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_p" name="index_p"></a>- p -</h3><ul>
<li>parent&#160;:&#160;<a class="el" href="class_lua_1_1_transform.html#ad7ea4f4af441f263f3cbfcb853d3edfa">Lua.Transform</a></li>
<li>Pause()&#160;:&#160;<a class="el" href="class_lua_1_1_audio_source.html#aad8aea6c6f265dfe440a6a8620416bf4">Lua.AudioSource</a></li>
<li>PerlinNoise()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a3044ff5b1dd835169520fd054c713d63">Lua.Mathf</a></li>
<li>PI&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a408b4fa7c06dd48e2aa0d6fcde7adedc">Lua.Mathf</a></li>
<li>PingPong()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#a8eed89df943f9dc0df1398e541e023ad">Lua.Mathf</a></li>
<li>pitch&#160;:&#160;<a class="el" href="class_lua_1_1_audio_source.html#a49d0053bb3cfa0b42c70f3b678e0d78f">Lua.AudioSource</a></li>
<li>Play()&#160;:&#160;<a class="el" href="class_lua_1_1_audio_source.html#aef1a519a4611e2aa72570d113d92c904">Lua.AudioSource</a></li>
<li>PlayAs()&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#aa3ec8ca2693205f43b8e1e620b209018">Lua.Entity</a></li>
<li>PlayClipAtPoint()&#160;:&#160;<a class="el" href="class_lua_1_1_audio_source.html#a132381c13acd88e5ef3e53e0e8c1ad66">Lua.AudioSource</a></li>
<li>PlayDelayed()&#160;:&#160;<a class="el" href="class_lua_1_1_audio_source.html#a10ba6ce0794c8050458466082302bc09">Lua.AudioSource</a></li>
<li>PlayOneShot()&#160;:&#160;<a class="el" href="class_lua_1_1_audio_source.html#a404e19781f62fa9186ecdcf535e7d4ae">Lua.AudioSource</a></li>
<li>PoseExists()&#160;:&#160;<a class="el" href="class_lua_1_1_animation.html#a7826d8216e7d202f625e697341ae62fc">Lua.Animation</a></li>
<li>position&#160;:&#160;<a class="el" href="class_lua_1_1_entity.html#ad8bd97d98fddc9b89f8410512b502c3f">Lua.Entity</a>, <a class="el" href="class_lua_1_1_i_k_effector.html#a8fca3762ba9e4b8e90d21f7bd701048a">Lua.IKEffector</a>, <a class="el" href="class_lua_1_1_rigidbody.html#a9bef020808bd389b43ac5d2f7d429dc9">Lua.Rigidbody</a>, <a class="el" href="class_lua_1_1_transform.html#a789b6abed611a7576ca2262bb9c5e6c3">Lua.Transform</a></li>
<li>positionWeight&#160;:&#160;<a class="el" href="class_lua_1_1_i_k_effector.html#a9237f631ddbe3043a8be096d4a51a5dd">Lua.IKEffector</a></li>
<li>Pow()&#160;:&#160;<a class="el" href="class_lua_1_1_mathf.html#ace6b91fa037354fa541a5de450ba6e23">Lua.Mathf</a></li>
<li>predictiveAiming&#160;:&#160;<a class="el" href="class_lua_1_1_shooting.html#adc410fd560cfdc7bdbd5b870a7adedbf">Lua.Shooting</a></li>
<li>Print()&#160;:&#160;<a class="el" href="class_lua_1_1_game_1_1_toast.html#a5ebc4bf8c4b6a0a14947ac2ff5dfd25c">Lua.Game.Toast</a></li>
<li>Project()&#160;:&#160;<a class="el" href="class_lua_1_1_vector3.html#aa9b7aad25b72d46c5e49e847bbc41353">Lua.Vector3</a></li>
<li>ProjectOnPlane()&#160;:&#160;<a class="el" href="class_lua_1_1_vector3.html#af4bc2b40c64c31d8ade94277052e46d1">Lua.Vector3</a></li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

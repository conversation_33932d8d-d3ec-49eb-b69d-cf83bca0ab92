#!/usr/bin/env python3
"""
Targeted Sizebox Spawn Key Patcher
Only patches spawn-specific key bindings, not all P/O references
"""

import os
import shutil
import hashlib

def backup_file(source, backup_dir):
    """Create a backup of the original file"""
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    
    backup_path = os.path.join(backup_dir, os.path.basename(source) + '.targeted_backup')
    shutil.copy2(source, backup_path)
    print(f"Backup created: {backup_path}")
    return backup_path

def find_spawn_patterns(dll_data):
    """Find specific spawn-related patterns in the DLL"""
    
    # Look for specific spawn-related byte patterns
    # These are more targeted patterns that should only match spawn code
    spawn_patterns = [
        # Pattern 1: KeyCode.P followed by spawn-related strings
        b"KeyCode.P",
        b"KeyCode.O", 
        # Pattern 2: Input.GetKeyDown with P/O
        b"GetKeyDown.*P",
        b"GetKeyDown.*O",
        # Pattern 3: Spawn method names
        b"SpawnMicro",
        b"SpawnCharacter",
        b"CreateMicro",
        # Pattern 4: Key binding arrays
        b"spawnKey",
        b"microSpawn",
    ]
    
    print("Searching for targeted spawn patterns...")
    
    # Unity KeyCode enum values
    keycodes = {
        'P': b'\x50',  # KeyCode.P = 80 (0x50)
        'O': b'\x4F',  # KeyCode.O = 79 (0x4F)
        'F9': b'\x78',  # KeyCode.F9 = 120 (0x78)
        'F10': b'\x79', # KeyCode.F10 = 121 (0x79)
    }
    
    # Find spawn-specific contexts
    spawn_contexts = []
    
    # Look for "spawn" text near P/O keycodes
    for i, byte_val in enumerate(dll_data):
        if byte_val == keycodes['P'][0] or byte_val == keycodes['O'][0]:
            # Check 200 bytes before and after for spawn-related text
            start = max(0, i - 200)
            end = min(len(dll_data), i + 200)
            context = dll_data[start:end].lower()
            
            # Look for spawn-specific indicators
            spawn_indicators = [
                b'spawn',
                b'micro',
                b'character',
                b'instantiate',
                b'create',
                b'input',
                b'keydown',
                b'getkey'
            ]
            
            # Count how many spawn indicators are in this context
            indicator_count = sum(1 for indicator in spawn_indicators if indicator in context)
            
            # If we find multiple spawn indicators, this is likely a spawn key
            if indicator_count >= 2:
                key_type = 'P' if byte_val == keycodes['P'][0] else 'O'
                spawn_contexts.append({
                    'offset': i,
                    'key': key_type,
                    'context': context,
                    'indicators': indicator_count
                })
                print(f"Found potential spawn {key_type} key at offset: 0x{i:08X} (confidence: {indicator_count})")
    
    return spawn_contexts, keycodes

def patch_targeted_spawn_keys():
    """Main targeted patching function"""
    dll_path = r"Sizebox_Data\Managed\Assembly-CSharp.dll"
    backup_dir = r"Sizebox_Backup_Targeted"
    
    print("Targeted Sizebox Spawn Key Patcher")
    print("==================================")
    print("This will ONLY change spawn-specific P and O keys to F9 and F10")
    print("Much safer than the previous broad approach")
    print()
    
    # Verify file exists
    if not os.path.exists(dll_path):
        print(f"ERROR: {dll_path} not found!")
        return False
    
    # Create backup
    backup_path = backup_file(dll_path, backup_dir)
    
    # Load the DLL
    with open(dll_path, 'rb') as f:
        dll_data = bytearray(f.read())
    
    print(f"Loaded DLL: {len(dll_data)} bytes")
    
    # Find targeted spawn patterns
    spawn_contexts, keycodes = find_spawn_patterns(dll_data)
    
    if not spawn_contexts:
        print("No spawn-specific key bindings found!")
        print("The spawn keys might be implemented differently.")
        return False
    
    print(f"\nFound {len(spawn_contexts)} potential spawn key references")
    
    # Show what we found and ask for confirmation
    print("\nTargeted patches to apply:")
    p_patches = [ctx for ctx in spawn_contexts if ctx['key'] == 'P']
    o_patches = [ctx for ctx in spawn_contexts if ctx['key'] == 'O']
    
    print(f"• {len(p_patches)} P key spawn bindings -> F9")
    print(f"• {len(o_patches)} O key spawn bindings -> F10")
    print(f"• Total: {len(spawn_contexts)} patches (vs 1907 in previous attempt)")
    
    if len(spawn_contexts) > 50:
        print("WARNING: Still found many references. This might be too broad.")
        proceed = input("Continue anyway? (y/N): ").lower().strip()
        if proceed != 'y':
            print("Aborted for safety.")
            return False
    
    # Apply targeted patches
    patches_applied = 0
    
    for ctx in spawn_contexts:
        offset = ctx['offset']
        if ctx['key'] == 'P':
            dll_data[offset] = keycodes['F9'][0]  # P -> F9
            patches_applied += 1
            print(f"Patched spawn P -> F9 at offset: 0x{offset:08X}")
        elif ctx['key'] == 'O':
            dll_data[offset] = keycodes['F10'][0]  # O -> F10
            patches_applied += 1
            print(f"Patched spawn O -> F10 at offset: 0x{offset:08X}")
    
    if patches_applied == 0:
        print("No patches applied!")
        return False
    
    # Write the patched DLL
    patched_path = dll_path + ".targeted_patched"
    with open(patched_path, 'wb') as f:
        f.write(dll_data)
    
    print(f"\nTargeted patch ready!")
    print(f"Applied {patches_applied} targeted patches (much safer than 1907)")
    
    apply = input("\nApply targeted patch to game? (y/N): ").lower().strip()
    if apply == 'y':
        # Backup original and apply patch
        if not os.path.exists(dll_path + ".original"):
            shutil.copy2(dll_path, dll_path + ".original")
        shutil.copy2(patched_path, dll_path)
        print("✓ Targeted patch applied!")
        print("✓ Spawn keys changed: P -> F9, O -> F10 (spawn only)")
        print("✓ Restart Sizebox to test!")
        return True
    else:
        print("Patch not applied.")
        return True

def restore_original():
    """Restore the original DLL"""
    dll_path = r"Sizebox_Data\Managed\Assembly-CSharp.dll"
    original_path = dll_path + ".original"
    
    if os.path.exists(original_path):
        shutil.copy2(original_path, dll_path)
        print("✓ Original DLL restored")
        return True
    else:
        print("✗ Original backup not found")
        return False

if __name__ == "__main__":
    try:
        print("Targeted Sizebox Spawn Key Patcher")
        print("==================================")
        print("1. Apply targeted spawn key patch (P->F9, O->F10)")
        print("2. Restore original keys")
        print()
        
        choice = input("Enter choice (1 or 2): ").strip()
        
        if choice == "1":
            success = patch_targeted_spawn_keys()
        elif choice == "2":
            success = restore_original()
        else:
            print("Invalid choice!")
            success = False
            
        if success:
            print("\nOperation completed!")
        else:
            print("\nOperation failed!")
            
    except Exception as e:
        print(f"Error: {e}")
    
    input("Press Enter to exit...")

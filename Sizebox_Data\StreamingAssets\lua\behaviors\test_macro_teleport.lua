-- Test Macro Teleportation Script
-- Simple script to test macro teleportation

local TestMacroTeleport = RegisterBehavior("Test Macro Teleport")
TestMacroTeleport.data = {
    menuEntry = "Test/Macro Teleport Test",
    agent = { type = { "micro", "giantess" } },
    target = { type = { "giantess" } },
    settings = {},
    secondary = true
}

function TestMacroTeleport:Start()
    print("=== MACRO TELEPORT TEST SCRIPT STARTED ===")

    -- Determine who we're controlling
    if self.target and self.target ~= self.agent then
        -- We're a micro controlling a giantess
        self.macroCharacter = self.target
        print("Mode: Micro controlling giantess")
    else
        -- We're the giantess being controlled
        self.macroCharacter = self.agent
        print("Mode: Direct giantess control")
    end

    if self.macroCharacter then
        print("Macro character found: " .. tostring(self.macroCharacter.name or "unnamed"))
        print("Has transform: " .. tostring(self.macroCharacter.transform ~= nil))
        if self.macroCharacter.transform then
            local pos = self.macroCharacter.transform.position
            print("Current position: " .. string.format("%.1f,%.1f,%.1f", pos.x, pos.y, pos.z))
        end
    else
        print("ERROR: No macro character found!")
    end

    -- Auto-run test after a short delay
    self.testTimer = 3.0  -- Wait 3 seconds then test
    self.testRun = false
end

function TestMacroTeleport:Update()
    -- Simple timer countdown
    if self.testTimer and self.testTimer > 0 and not self.testRun then
        self.testTimer = self.testTimer - 0.016  -- Assume ~60fps

        if self.testTimer <= 0 then
            self:RunBasicTest()
            self.testRun = true
        end
    end
end

function TestMacroTeleport:RunBasicTest()
    print("=== RUNNING BASIC MACRO TELEPORT TEST ===")

    if not self.macroCharacter or not self.macroCharacter.transform then
        print("ERROR: No macro character or transform!")
        return
    end

    local currentPos = self.macroCharacter.transform.position
    print("BEFORE teleport: " .. string.format("%.1f,%.1f,%.1f", currentPos.x, currentPos.y, currentPos.z))

    -- Try to create Vector3 like AI companion script
    local targetPos = nil
    if Vector3 and Vector3.new then
        targetPos = Vector3.new(currentPos.x, currentPos.y + 50, currentPos.z)
        print("TARGET position (Vector3): " .. string.format("%.1f,%.1f,%.1f", targetPos.x, targetPos.y, targetPos.z))
    else
        print("ERROR: Vector3.new not available!")
        return
    end

    -- Try to set position using the same method as AI companion
    self.macroCharacter.transform.position = targetPos

    -- Check result
    local afterPos = self.macroCharacter.transform.position
    print("AFTER teleport:  " .. string.format("%.1f,%.1f,%.1f", afterPos.x, afterPos.y, afterPos.z))

    local yDiff = math.abs(afterPos.y - targetPos.y)
    local success = yDiff < 5

    print("Y difference: " .. string.format("%.1f", yDiff))
    print("SUCCESS: " .. tostring(success))

    if success then
        print("✅ MACRO TELEPORTATION WORKS!")
    else
        print("❌ MACRO TELEPORTATION FAILED!")
    end
end

return TestMacroTeleport

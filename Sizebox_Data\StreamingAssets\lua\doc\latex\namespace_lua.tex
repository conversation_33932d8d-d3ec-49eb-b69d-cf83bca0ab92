\hypertarget{namespace_lua}{}\section{Lua Namespace Reference}
\label{namespace_lua}\index{Lua@{Lua}}
\subsection*{Classes}
\begin{DoxyCompactItemize}
\item 
class \mbox{\hyperlink{class_lua_1_1_a_i}{AI}}
\begin{DoxyCompactList}\small\item\em Controls the \mbox{\hyperlink{class_lua_1_1_a_i}{AI}} of humanoid agent. \end{DoxyCompactList}\item 
class \mbox{\hyperlink{class_lua_1_1_all_giantess}{All\+Giantess}}
\begin{DoxyCompactList}\small\item\em A class containing settings affecting all giantesses. \end{DoxyCompactList}\item 
class \mbox{\hyperlink{class_lua_1_1_all_micros}{All\+Micros}}
\begin{DoxyCompactList}\small\item\em A class containing settings affecting all micros. \end{DoxyCompactList}\item 
class \mbox{\hyperlink{class_lua_1_1_animation}{Animation}}
\begin{DoxyCompactList}\small\item\em Component to control the animation for humanoid entities. \end{DoxyCompactList}\item 
class \mbox{\hyperlink{class_lua_1_1_audio_source}{Audio\+Source}}
\begin{DoxyCompactList}\small\item\em A representation of audio sources in 3D. \end{DoxyCompactList}\item 
class \mbox{\hyperlink{class_lua_1_1_bones}{Bones}}
\begin{DoxyCompactList}\small\item\em Access bone transforms of humanoid characters. \end{DoxyCompactList}\item 
class \mbox{\hyperlink{class_lua_1_1_custom_sound_manager}{Custom\+Sound\+Manager}}
\begin{DoxyCompactList}\small\item\em An interface to change in-\/game sound effects with custom sounds in the Sounds folder \end{DoxyCompactList}\item 
class \mbox{\hyperlink{class_lua_1_1_entity}{Entity}}
\begin{DoxyCompactList}\small\item\em A \mbox{\hyperlink{class_lua_1_1_entity}{Entity}} represents Characters and Objects \end{DoxyCompactList}\item 
class \mbox{\hyperlink{class_lua_1_1_event}{Event}}
\begin{DoxyCompactList}\small\item\em Interface of the \mbox{\hyperlink{class_lua_1_1_event}{Event}} system. \end{DoxyCompactList}\item 
class \mbox{\hyperlink{class_lua_1_1_globals}{Globals}}
\begin{DoxyCompactList}\small\item\em Global dictionary. It can be used to store and exchange arbitrary data between scripts. \end{DoxyCompactList}\item 
class \mbox{\hyperlink{class_lua_1_1_i_k}{IK}}
\begin{DoxyCompactList}\small\item\em Inverse Kinematics lets you animate individual bones to create procedural animations. \end{DoxyCompactList}\item 
class \mbox{\hyperlink{class_lua_1_1_i_k_effector}{I\+K\+Effector}}
\begin{DoxyCompactList}\small\item\em Each effector lets you control one bone and animate the body. \end{DoxyCompactList}\item 
class \mbox{\hyperlink{class_lua_1_1_input}{Input}}
\begin{DoxyCompactList}\small\item\em Interface into the \mbox{\hyperlink{class_lua_1_1_input}{Input}} system. \end{DoxyCompactList}\item 
class \mbox{\hyperlink{class_lua_1_1_lua_player_raygun}{Lua\+Player\+Raygun}}
\begin{DoxyCompactList}\small\item\em Use this component to control some elements of the raygun of the player \end{DoxyCompactList}\item 
class \mbox{\hyperlink{class_lua_1_1_mathf}{Mathf}}
\begin{DoxyCompactList}\small\item\em A collection of common Unity math functions. Largely overlaps with built-\/in \mbox{\hyperlink{namespace_lua}{Lua}} math library (\href{https://www.lua.org/manual/5.3/manual.html\#6.7}{\texttt{ https\+://www.\+lua.\+org/manual/5.\+3/manual.\+html\#6.\+7}}) \end{DoxyCompactList}\item 
class \mbox{\hyperlink{class_lua_1_1_morphs}{Morphs}}
\begin{DoxyCompactList}\small\item\em Component to control the morphs for giantess entities. \end{DoxyCompactList}\item 
class \mbox{\hyperlink{class_lua_1_1_movement}{Movement}}
\begin{DoxyCompactList}\small\item\em Use this component to control the movement of agents. \end{DoxyCompactList}\item 
class \mbox{\hyperlink{class_lua_1_1_player_entity}{Player\+Entity}}
\begin{DoxyCompactList}\small\item\em A \mbox{\hyperlink{class_lua_1_1_player_entity}{Player\+Entity}} represents player-\/controlled character. \end{DoxyCompactList}\item 
class \mbox{\hyperlink{class_lua_1_1_quaternion}{Quaternion}}
\begin{DoxyCompactList}\small\item\em Quaternions are used to represent rotations. \end{DoxyCompactList}\item 
class \mbox{\hyperlink{class_lua_1_1_random}{Random}}
\begin{DoxyCompactList}\small\item\em Class for generating random data. \end{DoxyCompactList}\item 
class \mbox{\hyperlink{class_lua_1_1_rigidbody}{Rigidbody}}
\begin{DoxyCompactList}\small\item\em Control of an object\textquotesingle{}s position through physics simulation. \end{DoxyCompactList}\item 
class \mbox{\hyperlink{class_lua_1_1_screen}{Screen}}
\begin{DoxyCompactList}\small\item\em Access to display information. \end{DoxyCompactList}\item 
class \mbox{\hyperlink{class_lua_1_1_senses}{Senses}}
\begin{DoxyCompactList}\small\item\em Control the senses of a entity such as the vision. \end{DoxyCompactList}\item 
class \mbox{\hyperlink{class_lua_1_1_shooting}{Shooting}}
\begin{DoxyCompactList}\small\item\em Use this component to control the shooting-\/related and gun properties of agents. \end{DoxyCompactList}\item 
class \mbox{\hyperlink{class_lua_1_1_time}{Time}}
\begin{DoxyCompactList}\small\item\em The interface to get time information from Unity. \end{DoxyCompactList}\item 
class \mbox{\hyperlink{class_lua_1_1_transform}{Transform}}
\begin{DoxyCompactList}\small\item\em Position, rotation and scale of an object. \end{DoxyCompactList}\item 
class \mbox{\hyperlink{class_lua_1_1_vector3}{Vector3}}
\begin{DoxyCompactList}\small\item\em Representation of 3D vectors and points. \end{DoxyCompactList}\item 
class \mbox{\hyperlink{class_lua_1_1_world}{World}}
\begin{DoxyCompactList}\small\item\em A class containing settings affecting the world. \end{DoxyCompactList}\end{DoxyCompactItemize}

-- ManualPickup (Raycast version)
ManualPickup = RegisterBehavior("ManualPickup")
ManualPickup.data = {
    menuEntry = "Interaction/Manual Pickup (Raycast)",
    agent = { type = { "giantess" } },
    target = { type = { "oneself" } }
}

function ManualPickup:Start()
    self.heldMicro = nil
    self.grabState = "idle"
    Log("Manual Pickup: Aim at a micro and press G to grab, R to release.")
end

-- Helper: get first available idle pose
local function getIdlePose()
    local poses = Animation.GetPoseList()
    local idles = { "Idle", "Idle 2", "Idle 3", "Idle 4" }
    for _, pose in ipairs(idles) do
        for _, available in ipairs(poses) do
            if pose == available then return pose end
        end
    end
    return poses[1] or "Idle"
end

-- Helper: raycast to find micro in front of agent
local function raycastMicro(agent, maxDist)
    local origin = agent.transform.position
    local dir = agent.transform.forward
    local hits = Physics.RaycastAll(origin, dir, maxDist or 10)
    for _, hit in ipairs(hits) do
        local micro = hit.collider and hit.collider.gameObject and hit.collider.gameObject.micro
        if micro and not micro.isDead() then
            return micro
        end
    end
    return nil
end

function ManualPickup:Update()
    -- Grab micro with raycast
    if self.grabState == "idle" and Input.GetKeyDown("g") and not self.heldMicro and self.agent then
        local micro = raycastMicro(self.agent, 10)
        if micro then
            if self.agent.lookAt then self.agent:lookAt(micro) end
            if self.agent.animation and self.agent.animation.Set then
                self.agent.animation:Set("Crouch Idle", true)
            end
            Event.AddTimeout(0.2, function()
                if not micro or (micro.isDead and micro:isDead()) then
                    Log("Micro is gone before grab.")
                    return
                end
                if self.agent and self.agent.grab then self.agent:grab(micro) end
                self.heldMicro = micro
                Log("Grabbed " .. (micro.name or "unknown"))
                -- Try holding pose, fallback to idle
                local poses = Animation.GetPoseList()
                local holding = nil
                for _, p in ipairs({ "HoldingI", "Looming5", "Looming6" }) do
                    for _, avail in ipairs(poses) do
                        if p == avail then holding = p break end
                    end
                    if holding then break end
                end
                if holding and self.agent.animation and self.agent.animation.Set then
                    self.agent.animation:Set(holding, true)
                    Log("Set holding pose: " .. holding)
                else
                    local idle = getIdlePose()
                    if self.agent.animation and self.agent.animation.Set then
                        self.agent.animation:Set(idle, true)
                    end
                    Log("No holding pose, set idle: " .. idle)
                end
                self.grabState = "holding"
            end)
        else
            Log("No micro found in front.")
        end
    end

    -- Release micro
    if self.grabState == "holding" and Input.GetKeyDown("r") and self.heldMicro then
        local micro = self.heldMicro
        pcall(function()
            if micro and micro.Release then micro:Release() end
            if micro and micro.transform and micro.transform.SetParent then micro.transform:SetParent(nil) end
            if micro and micro.rigidbody then
                micro.rigidbody.isKinematic = false
                micro.rigidbody.useGravity = true
                micro.rigidbody.detectCollisions = true
            end
            if micro and micro.collider then micro.collider.enabled = true end
            if micro and micro.isPlayer and micro.player then
                micro.player.isGrabbed = false
                micro.player.isFlying = true
                Event.AddTimeout(0.2, function()
                    pcall(function() if micro and micro.player then micro.player.isFlying = false end end)
                end)
                micro.player.isCrouching = true
                Event.AddTimeout(0.4, function()
                    pcall(function() if micro and micro.player then micro.player.isCrouching = false end end)
                end)
            end
        end)
        if self.agent and self.agent.release and micro then self.agent:release(micro) end
        Log("Released " .. (micro and micro.name or "unknown"))
        self.heldMicro = nil
        local idle = getIdlePose()
        if self.agent and self.agent.animation and self.agent.animation.Set then
            self.agent.animation:Set(idle, true)
        end
        self.grabState = "idle"
    end

    -- If held micro dies or is removed, clear reference and return to idle
    if self.heldMicro and (
        (self.heldMicro.isDead and self.heldMicro:isDead()) or
        not (self.heldMicro.transform and self.heldMicro.transform.IsChildOf and self.agent and self.agent.transform and self.heldMicro.transform:IsChildOf(self.agent.transform))
    ) then
        self.heldMicro = nil
        local idle = getIdlePose()
        if self.agent and self.agent.animation and self.agent.animation.Set then
            self.agent.animation:Set(idle, true)
        end
        self.grabState = "idle"
    end
end
TPickup = RegisterBehavior("T Pickup")
TPickup.data = {
    menuEntry = "T Pickup",
    description = "Press T to make the macro pick you up.",
    tags = "macro, micro, pickup",
    agent = {
        type = { "micro", "player" }
    },
    target = {
        type = { "giantess" },
        exclude = { "player" }
    }
}

function TPickup:Start()
    self.stop = false
    self.chasing = false
    self.crouching = false
    self.defaultLowerLegPos = self.target.bones.leftLowerLeg.position.y - self.target.transform.position.y
    self.standingUp = false
    self.standUpTime = 0
    self.keyPressed = false
    self.releasePressed = false
    self.animationSet = false
    
    if self.target.height < self.agent.height * 5 then
        self.target.animation.SetAndWait("No 2", true)
        log("Target is too small to grab you!")
        self.target.animation.Set("Idle 2", true)
        self.stop = true
    end
    
    log("T Pickup activated - Press T to make the macro pick you up")
    log("Press T again to be released")
    log("Press X to exit")
end

function TPickup:Update()
    -- Debug key press detection
    if Input.anyKeyDown then
        log("Key pressed: " .. Input.inputString)
    end
    
    -- Check for T key press (grab/release) - try multiple ways to detect it
    if Input.GetKeyDown("t") or Input.GetKeyDown(KeyCode.T) or Input.inputString == "t" or Input.inputString == "T" then
        log("T key detected!")
        if self.agent.transform.IsChildOf(self.target.transform) then
            -- Already being held, so release
            self.releasePressed = true
            log("T key pressed - releasing")
        else
            -- Not being held, so grab
            self.keyPressed = true
            log("T key pressed - grabbing")
            
            -- Directly try to make the target approach and grab
            self.target.MoveTo(self.agent)
            self.target.Face(self.agent)
            self.target.LookAt(self.agent)
            
            -- Try direct grab after a short delay
            Event.AddTimeout(0.5, function()
                pcall(function()
                    log("Attempting direct grab")
                    self.target.grab(self.agent)
                end)
            end)
        end
    end
    
    -- Check for X key press (exit behavior)
    if Input.GetKeyDown("x") or Input.GetKeyDown(KeyCode.X) or Input.inputString == "x" or Input.inputString == "X" then
        log("X key detected - exiting behavior")
        self:Exit()
        self.agent.ai.StopBehavior()
        return
    end
    
    -- Handle release if requested
    if self.releasePressed then
        -- Try to release micro
        if self.agent.transform.IsChildOf(self.target.transform) then
            log("Releasing micro")
            
            -- Save the current position before detaching
            local currentPos = self.agent.transform.position
            
            -- Try to detach the agent
            pcall(function() self.agent.transform.SetParent(nil) end)
            
            -- Keep the micro at the same position but ensure it's not under the map
            pcall(function()
                -- Keep the player at nearly the same position with a small offset to help unstick
                local safePos = Vector3.New(
                    currentPos.x + 0.05, -- Add a small X offset to help unstick
                    currentPos.y + 0.05, -- Keep Y position but add a small offset to help unstick
                    currentPos.z + 0.05 -- Add a small Z offset to help unstick
                )
                
                -- Set the micro's position
                self.agent.transform.position = safePos
                
                -- Enable physics if available
                if self.agent.rigidbody then
                    self.agent.rigidbody.isKinematic = false
                    self.agent.rigidbody.detectCollisions = true
                end
                
                -- Try to enable player control if it's a player
                pcall(function()
                    if self.agent.isPlayer then
                        self.agent.movement.enabled = true
                        self.agent.movement.isGrounded = false
                        
                        -- Try to reset player state and briefly toggle flight to unstick
                        if self.agent.player then
                            self.agent.player.isGrabbed = false
                            
                            -- Briefly enable and disable flight to unstick the player
                            self.agent.player.isFlying = true
                            
                            -- Schedule flight to be disabled after a short delay
                            Event.AddTimeout(0.2, function()
                                pcall(function()
                                    self.agent.player.isFlying = false
                                    log("Flight briefly toggled to unstick player")
                                end)
                            end)
                        end
                    end
                end)
            end)
            
            -- Also stop the action
            pcall(function() 
                self.target.ai.StopAction()
                self.target.animation.Set("Idle 4", true)
            end)
            
            -- Reset flags to allow grabbing again
            self.releasePressed = false
            self.keyPressed = false
            self.chasing = false
            self.animationSet = false
            self.stop = false  -- Make sure the behavior doesn't stop
            
            -- Stand up if crouching
            if self.target.animation.Get() == "Crouch Idle" then
                self.target.ai.StopAction()
                self.target.animation.Set("Idle 4", true)
                self.standingUp = true
                self.standUpTime = 0
                self.crouching = false
            end
        end
    end

    if not self.target.ai.IsActionActive() then
        if self.stop then
            self.agent.ai.StopBehavior() -- if you use Update() you must manually tell when to end the behavior, after this the Exit() method will run
            return
        else
            -- If we're in the standing up transition, handle that first
            if self.standingUp then
                self.standUpTime = self.standUpTime + Time.deltaTime
                
                -- Keep the agent in place during the standing up transition
                if self.standUpTime < 1.0 then
                    -- Keep the idle animation going and prevent rotation
                    self.target.animation.Set("Idle 4", true)
                    
                    -- Position arm near chest if we have IK
                    if self.target.ik and self.target.ik.rightHand then
                        -- Get chest position
                        local chestPos = self.target.bones.spine2.position
                        
                        -- Offset slightly to the right and forward
                        local targetPos = Vector3.New(
                            chestPos.x + self.target.scale * 0.1,
                            chestPos.y,
                            chestPos.z + self.target.scale * 0.15
                        )
                        
                        -- Set the right hand position
                        self.target.ik.rightHand.position = targetPos
                        self.target.ik.rightHand.positionWeight = 1
                    end
                else
                    -- Transition complete
                    self.standingUp = false
                    
                    -- Just make sure we're in idle animation after standing up
                    if self.agent.transform.IsChildOf(self.target.transform) then
                        self.target.animation.Set("Idle 4")
                        self.animationSet = true
                    end
                end
                
                -- Continue to next frame
                return
            end
            
            -- Always use standing distance for following
            local closest = self.target.scale * 0.4
            
            -- Check if we need to move to the agent
            if self.target.DistanceTo(Vector3.New(self.agent.position.x, self.target.position.y, self.agent.position.z)) > closest then
                -- We're too far, need to move closer
                if not self.chasing then
                    self.chasing = true
                    self.target.ai.StopAction()
                    self.target.LookAt(self.agent)
                    self.target.animation.Set("Walk", true)
                    self.target.MoveTo(self.agent)
                    self.target.Face(self.agent)
                    log("Moving toward micro")
                    -- Don't stop the behavior after approaching
                    self.stop = false
                end
            else
                -- We're close enough to the agent
                if self.chasing then
                    -- Just stop and stand idle - no crouching until T is pressed
                    self.chasing = false
                    self.crouching = false
                    self.target.ai.StopAction()
                    self.target.animation.Set("Idle 4", true)
                    log("In position - press T to grab")
                end
                
                -- Only crouch and grab if T key was pressed
                if self.keyPressed then
                    -- Determine if we need to crouch based on agent height
                    local needToCrouch = self.agent.transform.position.y <= self.target.transform.position.y + self.defaultLowerLegPos
                    
                    -- If we need to crouch and aren't already crouching, do it now
                    if needToCrouch and not self.crouching then
                        self.crouching = true
                        self.target.animation.Set("Crouch Idle", true)
                        -- Give a small delay to allow the crouch animation to play
                        self.target.Wait(0.5)
                    end
                    
                    log("Executing grab action")
                    self.target.grab(self.agent)
                    self.keyPressed = false
                end
            end
        end
    elseif not self.chasing and self.agent.transform.IsChildOf(self.target.transform) then
        -- Handle standing up from crouch while holding micro
        if self.target.animation.Get() == "Crouch Idle" then
            -- Start the standing up transition
            self.target.ai.StopAction()
            self.target.animation.Set("Idle 4", true)
            self.standingUp = true
            self.standUpTime = 0
            self.crouching = false
            
            log("Standing up from crouch with micro")
        end
        
        -- Make sure we're in idle animation if not already set
        if not self.animationSet and not self.standingUp then
            self.target.animation.Set("Idle 4")
            self.animationSet = true
        end
    end
end

function TPickup:Exit()
    -- Release the micro if we're still holding it
    if self.agent.transform.IsChildOf(self.target.transform) then
        -- Save the current position before detaching
        local currentPos = self.agent.transform.position
        
        -- Try to detach the agent
        pcall(function() self.agent.transform.SetParent(nil) end)
        
        -- Keep the micro at the same position but ensure it's not under the map
        pcall(function()
            -- Keep the player at nearly the same position with a small offset to help unstick
            local safePos = Vector3.New(
                currentPos.x + 0.05, -- Add a small X offset to help unstick
                currentPos.y + 0.05, -- Keep Y position but add a small offset to help unstick
                currentPos.z + 0.05 -- Add a small Z offset to help unstick
            )
            
            -- Set the micro's position
            self.agent.transform.position = safePos
            
            -- Enable physics if available
            if self.agent.rigidbody then
                self.agent.rigidbody.isKinematic = false
                self.agent.rigidbody.detectCollisions = true
            end
            
            -- Try to enable player control if it's a player
            pcall(function()
                if self.agent.isPlayer then
                    self.agent.movement.enabled = true
                    self.agent.movement.isGrounded = false
                    
                    -- Try to reset player state and briefly toggle flight to unstick
                    if self.agent.player then
                        self.agent.player.isGrabbed = false
                        
                        -- Apply a small downward force to help the player get to the ground
                        if self.agent.rigidbody then
                            -- Apply a downward force
                            self.agent.rigidbody.AddForce(Vector3.New(0, -5, 0), ForceMode.Impulse)
                            log("Applied downward force to help player reach ground")
                        end
                    end
                end
            end)
        end)
        
        -- Also stop the action
        pcall(function() 
            self.target.ai.StopAction()
            self.target.animation.Set("Idle 4", true)
        end)
    end
    
    -- Reset IK weights if we were using them
    if self.target.ik and self.target.ik.rightHand then
        self.target.ik.rightHand.positionWeight = 0
        self.target.ik.rightHand.rotationWeight = 0
    end
    
    log("T Pickup behavior ended")
end
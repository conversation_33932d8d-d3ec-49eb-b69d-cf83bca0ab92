Gizmo = RegisterBehavior("grow_on_crush_s")
Gizmo.data =  {
    menuEntry = "Size/Grow On Crush (S)",
    secondary = true,
    agent = {
        type = { "humanoid" }
    },
    target = {
        type = { "oneself" }
    }
}

CONSTANT = {
    multiplier = 0.05   -- grow by a constant percentage, not affected by victim's size
}

LINEAR = {
    multiplier = 2.5,   -- gain victim's HEIGHT multiplied by this factor
    power = 1           -- this mode features moderate growth at small victim/gts scale
}                       -- differences, that decreases only slightly as scale difference rises

QUADRIC = {
    multiplier = 256,   -- gain victim's AREA multiplied by this factor
    power = 2           -- this mode features fast growth at small victim/gts scale differences
}                       -- and slow growth at larger scale differences

CUBIC = {
    multiplier = 19700, -- gain victim's VOLUME multiplied by this factor
    power = 3           -- this mode features dramatic growth at small victim/gts scale differences
}                       -- and very slow growth at larger scale differences


MODE = CONSTANT -- change this to select growth mode
--GROW_DURATION = 1 -- in seconds

-- this function is not called unless you hook it to a event
-- OnCrush event will pass folowing data to this function:
-- crusher - crusher entity
-- victim - victim entity
function Gizmo:Listener(data)
    -- We need make sure crusher.id is the same as self.agent.id otherwise all event listeners will grow
    if data.crusher != nil then
		if  data.crusher.id == self.agent.id then
			if MODE != CONSTANT then
				scaleRatio = data.victim.scale / data.crusher.scale
                self.fRATE = math.pow(1 + math.pow(scaleRatio, MODE.power) * MODE.multiplier, 1/MODE.power) - 1
                self.paused = false
			    -- print("factor: " .. factor)
                --self.agent.grow(factor, GROW_DURATION)
			else
                self.fRATE_normal = self.fRATE
                self.fRATE = self.fRATE_normal
                self.paused = false
                --self.agent.grow(MODE.multiplier, GROW_DURATION)
            end
            
		end
    end
end

function Gizmo:Start()
    -- subscribe Listener() to a "OnCrush" event
    self.agent.dict.OnCrush = Event.Register(self, EventCode.OnCrush, self.Listener)

    --ARRAYS
    sfx = {"Rumblev1.wav", "stretchmix.ogg" , "stretchmixbig.ogg", "customboing.wav", "customboingbig.wav"} -- Sounds used (found in ...Sizebox/Sounds).
    sfxlog = {"Rumble & Moans", "Stretch Mix", "Stretch Mix (Big)", "Boing Spurts", "Boing Spurts (Big)"}   -- Used for console logging the sound names.
	self.s = 1  -- Sets which sound is used (Defaults at "Rumble & Moans")

    --SCRIPT STARTUP INFO
	Log("Now Growing on Crush~")
    Log(" - Press '9, 0, - or =' to change growth parameters (spurt length and growth rate)")
    log(" - Press 'Left Ctrl' + '-' or '=' to lessen or increase smoothing effect")
	Log(" - Press 'H' to switch between sound effects")
	Log(" - Press 'M' to mute sound effects")
	Log(" - Press 'N' to toggle shrinking or growing")
	Log(" - Press 'Y' to compare your size with a giantess")

    --FUNCTION REFS ----------Used to reference self references in functions outside the main behavior.
	selfRef = self

	--VARIABLES
	self.fRATE_normal = 0.12	--Full rate. Intended full speed. --------------------------------- [Edit with = or -]
	self.sRATE = 0				--Starter rate. Adds same amount to mRATE every frame.
	self.dRATE_normal = 20		--Rate dimmer. Lessens the sRATE if smoother (slower/longer) easing is wanted.
	self.mRATE = 0				--Modifier rate. Adds increasing amounts to cRATE every frame. 
	self.cRATE = 0				--Changing rate. Rises exponentially from additions of mRATE.
	self.fRATEp = self.fRATE	--Stores fRATE position after Accel to compare later.
	
	self.DURATION_normal = 1.5	--Mean average duration of the spurts. ---------------------------- [Edit with 0 or 9]
	self.aDURATION = nil		--Acceleration duration. Calculated from DURATION.

	self.fRATE = self.fRATE_normal
	self.DURATION =	self.DURATION_normal
	self.dRATE = self.dRATE_normal

	--BOOLEANS
	self.paused = false		        --Used for pausing/unpausing the spurts. Starts paused ------------ [Press t to toggle]
	self.growing = false			--Used to mark portions where growth is happening
	self.spurtStarted = false	    --Used to mark the portion where full speed growth starts
	self.spurtDone = false
	self.scalePolarity = 1		    --Scale up or down (1 or -1). Used as both boolean and scale factor [Press n to toggle]
	self.cRATEfix = false		    --Fixes the small cRATE difference with fRATE if fRATE is changed
    self.confusedConsole = false    --Silly log boolean for the size info keypress
    self.singleEdit = true      

	--TIMERS
    self.gTIMER = Time.time		    --Growth timer. Tells the script when to stop or resume growing.
    self.t = Time.time			    --Keypress timer. Used to slow held keys keypress speed for better adjustment.
    
    --AUDIO BOOLEANS
	self.paused = true
	self.playing = false
	self.fading = false
	self.muted = false
	self.growplay = false
	
	--AUDIO VARIABLES
	self.gs = AudioSource:new(self.agent.bones.spine)
	self.gs.clip = "Rumblev1.wav"
	self.gs.spatialBlend = 1
	self.gs.loop = true
	self.gs.minDistance = (0.01 * (self.agent.scale * 0.25))
	self.gs.pitch = ((0.8 * (1 / math.sqrt((self.agent.scale *1000 / 125) + 1))) + 0.8)	
	self.gsFull = (1.7 - self.gs.pitch)
	self.mute = self.gsFull
	self.gs.volume = self.gsFull - self.mute
	self.gs:Play()
    self.gs:Pause()
    
end

function Gizmo:Update()
    --AUDIO
	if self.muted then
		if self.paused then
			if not self.playing then 
				if self.mute ~= self.gsFull then self.mute = self.gsFull end
			else
				if self.mute < self.gsFull then
					self.mute = self.mute + 0.7 * (self.gsFull / ((math.sqrt(8 * (self.fRATE / self.sRATE) + 1) - 1) / 2))
					gsUpdate()
				end
			end
		else
			if self.mute < self.gsFull then
				self.mute = self.mute + 0.7 * (self.gsFull / ((math.sqrt(8 * (self.fRATE / self.sRATE) + 1) - 1) / 2))
				gsUpdate()
			end
		end
    else
		if not self.paused then
			if self.spurtDone then
				if self.mute < self.gsFull then
					self.mute = self.mute + 0.7 * (self.gsFull / ((math.sqrt(8 * (self.fRATE / self.sRATE) + 1) - 1) / 2))
					gsUpdate()
				end
			else
				if self.mute > 0 then
					self.mute = self.mute - 0.7 * (self.gsFull / ((math.sqrt(8 * (self.fRATE / self.sRATE) + 1) - 1) / 2))
					gsUpdate()
				else
					if self.fading then log("FadeIn Done") self.fading = false end
				end
			end
            --log("Full Volume: ".. self.gsFull.."    Volume: "..self.gs.volume.."    mute: "..self.mute.."(In)")
        else
            if self.mute < self.gsFull then
                self.mute = self.mute + 0.7 * (self.gsFull / ((math.sqrt(8 * (self.fRATE / self.sRATE) + 1) - 1) / 2))
                gsUpdate()
            else
                if self.playing then log("FadeOut Done") self.gs:Pause() self.playing = false end
            end
            --log("Full Volume: ".. self.gsFull.."    Volume: "..self.gs.volume.."    mute: "..self.mute.."(Out)")
        end
	end
    
	if not self.paused then	
		if self.growing then
			
			
			--SET self.aDURATION
			if self.DURATION < 0.1 then
				self.aDURATION = self.DURATION / 2
			elseif self.DURATION < 3 then
				self.aDURATION = ((self.DURATION - 0.1) / ((6 - (0.1 / (self.DURATION ^ 2 + 0.01))) + (121 / 96))) + 0.1	--Formula I got for roughly getting to where I want
			elseif self.DURATION < 9 then															  			--(pretty close actually but not the cleanest way)
				self.aDURATION = self.DURATION / 6
			else
				self.aDURATION = 1.5
			end
				
			--SET self.sRATE
			if self.DURATION < 0.1 then
				self.sRATE = self.fRATE / (30 * self.aDURATION * (30 * self.aDURATION + 1) / 2) / (0.25 * self.dRATE + 0.5 / self.DURATION)
			else
				self.sRATE = self.fRATE / (30 * self.aDURATION * (30 * self.aDURATION + 1) / 2) / (2 *(0.25 * (self.dRATE + 1)))
			end


			--START SPURT
			if not self.spurtStarted then
				if not self.growplay then self.gs:Play() end
				self.growplay = true
				

				--ACCEL
				if self.cRATE < self.fRATE and self.DURATION > 0.001 then --If DURATION is low enough, skip accel
					
					self.mRATE = self.mRATE + self.sRATE
					self.cRATE = self.cRATE + self.mRATE

				--ACCEL DONE
				else
					if self.DURATION <= 0.001 then self.cRATE = self.fRATE self.mRATE = self.cRATE * 0.6 end --Set cRATE if accel is skipped
					self.spurtStarted = true
					self.gTIMER = Time.time + self.DURATION
					self.fRATEp = self.fRATE
                end
                
			else
				--STEADY GROWTH
				if Time.time < self.gTIMER then --What to do during the steady growth segment
					--Update cRATE to match new fRATE and fix small difference made at accel
					if (self.fRATE >= self.cRATE or self.fRATE < self.fRATEp) and not self.cRATEfix then
						self.cRATE = self.fRATE
						self.cRATEfix = true
					elseif self.cRATEfix then
						self.cRATE = self.fRATE
					end

				--DECEL
				else
					if not self.spurtDone then self.spurtDone = true end
					decel()
				end
			end

			--GROWTH FUNCTION (every frame, apply new scale)
            gro(self.cRATE)
        
        --GROWTH RESET
		else
			self.gTIMER = Time.time
            spurtReset()
			self.growplay = false
		end

	--PAUSED
	else
		if self.growing then
			decel()
			gro(self.cRATE)
		end
	end
	
	
	--KEYBOARD INPUTS
--==================================================================--
  if self.agent.GetSelectedEntity() == self.agent and self.singleEdit then
	--VARIABLE TWEAKS
	if Time.time > self.t + 0.05 then --Allows for easier single strokes

		--[dRATE(+)]
		if Input.GetKey("left ctrl") and Input.GetKey("=") then
			self.dRATE = self.dRATE + 1
			log("Smoothifier: ".. self.dRATE)
			self.t = Time.time

		--[dRATE(-)]
		elseif Input.GetKey("left ctrl") and Input.GetKey("-") and self.dRATE > 0 then
			self.dRATE = self.dRATE - 1
			if self.dRATE < 0 then self.dRATE = 0 end
			log("Smoothifier: ".. self.dRATE)
			self.t = Time.time

		--[fRATE(+)]
		elseif Input.GetKey("=") and not Input.GetKey("left ctrl") then
			if self.fRATE < 0.295 then	    	--(0.01-0.3)
				self.fRATE = self.fRATE + 0.01
			elseif self.fRATE < 1.95 then   	--(0.3-2)
				self.fRATE = self.fRATE + 0.1
			elseif self.fRATE < 4.9 then    	--(2-5)
				self.fRATE = self.fRATE + 0.2
			elseif self.fRATE < 9.75 then
				self.fRATE = self.fRATE + 0.5
			elseif self.fRATE < 29.5 then
				self.fRATE = self.fRATE + 1
			elseif self.fRATE < 59 then
				self.fRATE = self.fRATE + 2
			else
				self.fRATE = self.fRATE + 5
			end
			log("Rate: "..self.fRATE)
			self.t = Time.time
		
		--[fRATE(-)]
		elseif Input.GetKey("-") and not Input.GetKey("left ctrl") and self.fRATE > 0.015 then
			if self.fRATE > 62.5 then
				self.fRATE = self.fRATE - 5
			elseif self.fRATE > 31 then
				self.fRATE = self.fRATE - 2
			elseif self.fRATE > 10.5 then
				self.fRATE = self.fRATE - 1
			elseif self.fRATE > 5.25 then
				self.fRATE = self.fRATE - 0.5
			elseif self.fRATE > 2.1 then    	--(2-5)
				self.fRATE = self.fRATE - 0.2
			elseif self.fRATE > 0.35 then       --(0.3-2)
				self.fRATE = self.fRATE - 0.1
				if self.fRATE > 0.95	and self.fRATE < 1.05 then
					self.fRATE = 1
				end
			else						        --(0.01-0.3)
				self.fRATE = self.fRATE - 0.01
				if self.fRATE > 0.085 and self.fRATE < 0.095 then
					self.fRATE = 0.09
				end
			end
			log("Rate: "..self.fRATE)
			self.t = Time.time
		
		--[DURATION(+)]
		elseif Input.GetKey("0") then
			if self.DURATION < 0.0095 then
				self.DURATION = self.DURATION + 0.001
			elseif self.DURATION < 0.095 then
				self.DURATION = self.DURATION + 0.01
			elseif self.DURATION < 1.95 then
				self.DURATION = self.DURATION + 0.1
			elseif self.DURATION < 4.9 then
				self.DURATION = self.DURATION + 0.2
			elseif self.DURATION < 9.75 then
				self.DURATION = self.DURATION + 0.5
			elseif self.DURATION < 29.5 then
				self.DURATION = self.DURATION + 1
			elseif self.DURATION < 59 then
				self.DURATION = self.DURATION + 2
			else
				self.DURATION = self.DURATION + 5
			end
			log("Spurt length: "..self.DURATION)
			self.t = Time.time
		
		--[DURATION(-)]
		elseif Input.GetKey("9") and self.DURATION > 0.0015 then
			if self.DURATION > 62.5 then
				self.DURATION = self.DURATION - 5
			elseif self.DURATION > 31 then
				self.DURATION = self.DURATION - 2
			elseif self.DURATION > 10.5 then
				self.DURATION = self.DURATION - 1
			elseif self.DURATION > 5.25 then
				self.DURATION = self.DURATION - 0.5
			elseif self.DURATION > 2.1 then
				self.DURATION = self.DURATION - 0.2
			elseif self.DURATION > 0.15 then
				self.DURATION = self.DURATION - 0.1
				if self.DURATION < 1.1 and self.DURATION > 0.95 then
					self.DURATION = 1
				end
			elseif self.DURATION > 0.015 then
				self.DURATION = self.DURATION - 0.01
				if self.DURATION > 0.09 then
					self.DURATION = 0.09
				end
			else
				self.DURATION = self.DURATION - 0.001
				if self.DURATION > 0.009 then
					self.DURATION = 0.009
				end
			end

			--Drop Safe
			if self.DURATION < 0.001 then
				self.DURATION = 0.001
			end
			log("Spurt length: "..self.DURATION)
			self.t = Time.time
		end
	end
	
	--SOUND SWITCHER
	if Input.GetKeyDown("h") then
		self.s = self.s + 1
		if self.s > #sfx then self.s = 1 end
		self.gs.clip = sfx[self.s]
		if self.s < 4 then self.gs.loop = true else self.gs.loop = false end
		if self.growing then self.gs:Play() end
		log(sfxlog[self.s])
	end

	--SOUND MUTE
	if Input.GetKeyDown("m") then
		self.muted = not self.muted
		if self.muted then
			log("Sounds Muted.")
		else
			log("Sounds Unmuted.")
		end
	end

	--SHRINK SWITCH
	if Input.GetKeyDown("n") then --If positive, grow; else shrink.
		self.scalePolarity = self.scalePolarity * -1 
		if self.scalePolarity > 0 then
			log("Growing!")
		else
			log("Shrinking!")
		end
	end

	--GTS COMPARE
	if Input.GetKeyDown("y") then
		self.gpRatio = round(((self.agent.scale) / player.scale), 2)
		if self.gpRatio >= 0.98 and self.gpRatio <= 1.02 then
			if self.confusedConsole then
				Log("You are both the same size...")
			else
				Log("This Giantess is... as small as, err... you're as big as-- Y-You're the same size!")
				self.confusedConsole =  true
			end
		else
			Log("This Giantess is... " .. self.gpRatio .. " times your size...")
        end
    end
  end
end

-- SIDE FUNCTIONS --
--==================================================================--
function gro(factor)
	selfRef.agent.scale = selfRef.agent.scale * (1 + selfRef.scalePolarity * factor * Time.deltaTime)
end

function spurtReset()
	selfRef.growing = true
	selfRef.mRATE = 0
	selfRef.cRATE = 0
	selfRef.spurtStarted = false
	selfRef.spurtDone = false
	selfRef.cRATEfix = false
	selfRef.gs:UnPause()
	selfRef.playing = true
	selfRef.fading = true
end

function decel()
	if selfRef.cRATE > 0 and selfRef.mRATE > 0 then
		selfRef.cRATE = selfRef.cRATE - selfRef.mRATE
		selfRef.mRATE = selfRef.mRATE - selfRef.sRATE
    else 
		selfRef.paused = true log("Paused.")
		selfRef.growing = false
	end
end

function gsUpdate()
	selfRef.gs.minDistance = (0.01 * (selfRef.agent.scale * 0.25))
	selfRef.gs.pitch = ((0.8 * (1 / math.sqrt((selfRef.agent.scale *1000 / 125) + 1))) + 0.8)	
	selfRef.gs.volume = (1.7 - selfRef.gs.pitch) - selfRef.mute
	selfRef.gsFull = (1.7 - selfRef.gs.pitch)
end

function round(num, numDecimalPlaces) -- Function for rounding some printed numbers at 2 decimals
	return tonumber(string.format("%." .. (numDecimalPlaces or 0) .. "f", num))
end

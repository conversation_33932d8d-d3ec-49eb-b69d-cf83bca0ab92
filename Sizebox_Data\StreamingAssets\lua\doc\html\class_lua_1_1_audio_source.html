<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.4"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Sizebox: Lua.AudioSource Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Sizebox
   </div>
   <div id="projectbrief">Sizebox Lua API</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.4 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('class_lua_1_1_audio_source.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="class_lua_1_1_audio_source-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">Lua.AudioSource Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>A representation of audio sources in 3D.  
 <a href="class_lua_1_1_audio_source.html#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-methods" name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:aad8aea6c6f265dfe440a6a8620416bf4"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_audio_source.html#aad8aea6c6f265dfe440a6a8620416bf4">Pause</a> ()</td></tr>
<tr class="memdesc:aad8aea6c6f265dfe440a6a8620416bf4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Pauses playing the clip.  <a href="class_lua_1_1_audio_source.html#aad8aea6c6f265dfe440a6a8620416bf4">More...</a><br /></td></tr>
<tr class="separator:aad8aea6c6f265dfe440a6a8620416bf4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aef1a519a4611e2aa72570d113d92c904"><td class="memItemLeft" align="right" valign="top"><a id="aef1a519a4611e2aa72570d113d92c904" name="aef1a519a4611e2aa72570d113d92c904"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>Play</b> ()</td></tr>
<tr class="memdesc:aef1a519a4611e2aa72570d113d92c904"><td class="mdescLeft">&#160;</td><td class="mdescRight">Plays the clip. <br /></td></tr>
<tr class="separator:aef1a519a4611e2aa72570d113d92c904"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a10ba6ce0794c8050458466082302bc09"><td class="memItemLeft" align="right" valign="top"><a id="a10ba6ce0794c8050458466082302bc09" name="a10ba6ce0794c8050458466082302bc09"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>PlayDelayed</b> (float delay)</td></tr>
<tr class="memdesc:a10ba6ce0794c8050458466082302bc09"><td class="mdescLeft">&#160;</td><td class="mdescRight">Plays the clip with a delay specified in seconds. <br /></td></tr>
<tr class="separator:a10ba6ce0794c8050458466082302bc09"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac8a51a95f2285660337ddf45fd10252e"><td class="memItemLeft" align="right" valign="top"><a id="ac8a51a95f2285660337ddf45fd10252e" name="ac8a51a95f2285660337ddf45fd10252e"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>PlayOneShot</b> (string <a class="el" href="class_lua_1_1_audio_source.html#a4913b6f1fa8dfe5eb528cd7e40f91684">clip</a>, float volumeScale)</td></tr>
<tr class="memdesc:ac8a51a95f2285660337ddf45fd10252e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Plays an AudioClip, and scales the <a class="el" href="class_lua_1_1_audio_source.html" title="A representation of audio sources in 3D.">AudioSource</a> volume by volumeScale. <br /></td></tr>
<tr class="separator:ac8a51a95f2285660337ddf45fd10252e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a404e19781f62fa9186ecdcf535e7d4ae"><td class="memItemLeft" align="right" valign="top"><a id="a404e19781f62fa9186ecdcf535e7d4ae" name="a404e19781f62fa9186ecdcf535e7d4ae"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>PlayOneShot</b> (string <a class="el" href="class_lua_1_1_audio_source.html#a4913b6f1fa8dfe5eb528cd7e40f91684">clip</a>)</td></tr>
<tr class="memdesc:a404e19781f62fa9186ecdcf535e7d4ae"><td class="mdescLeft">&#160;</td><td class="mdescRight">Plays an AudioClip. <br /></td></tr>
<tr class="separator:a404e19781f62fa9186ecdcf535e7d4ae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3a622d080321f25beda52619d417dbce"><td class="memItemLeft" align="right" valign="top"><a id="a3a622d080321f25beda52619d417dbce" name="a3a622d080321f25beda52619d417dbce"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>Stop</b> ()</td></tr>
<tr class="memdesc:a3a622d080321f25beda52619d417dbce"><td class="mdescLeft">&#160;</td><td class="mdescRight">Stops playing the clip. <br /></td></tr>
<tr class="separator:a3a622d080321f25beda52619d417dbce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3bbbde9384f38a43f97e48cd162b0dac"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_audio_source.html#a3bbbde9384f38a43f97e48cd162b0dac">UnPause</a> ()</td></tr>
<tr class="memdesc:a3bbbde9384f38a43f97e48cd162b0dac"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unpause the paused playback of this <a class="el" href="class_lua_1_1_audio_source.html" title="A representation of audio sources in 3D.">AudioSource</a>.  <a href="class_lua_1_1_audio_source.html#a3bbbde9384f38a43f97e48cd162b0dac">More...</a><br /></td></tr>
<tr class="separator:a3bbbde9384f38a43f97e48cd162b0dac"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-static-methods" name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:a9960981498216301c82794fa6a14d7ac"><td class="memItemLeft" align="right" valign="top"><a id="a9960981498216301c82794fa6a14d7ac" name="a9960981498216301c82794fa6a14d7ac"></a>
static <a class="el" href="class_lua_1_1_audio_source.html">AudioSource</a>&#160;</td><td class="memItemRight" valign="bottom"><b>New</b> (<a class="el" href="class_lua_1_1_entity.html">Entity</a> entity)</td></tr>
<tr class="memdesc:a9960981498216301c82794fa6a14d7ac"><td class="mdescLeft">&#160;</td><td class="mdescRight">Create a new audiosource on a <a class="el" href="class_lua_1_1_entity.html" title="A Entity represents Characters and Objects">Entity</a> <br /></td></tr>
<tr class="separator:a9960981498216301c82794fa6a14d7ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6b5a139f410004dd985f118c90dd5179"><td class="memItemLeft" align="right" valign="top"><a id="a6b5a139f410004dd985f118c90dd5179" name="a6b5a139f410004dd985f118c90dd5179"></a>
static <a class="el" href="class_lua_1_1_audio_source.html">AudioSource</a>&#160;</td><td class="memItemRight" valign="bottom"><b>New</b> (<a class="el" href="class_lua_1_1_transform.html">Transform</a> transform)</td></tr>
<tr class="memdesc:a6b5a139f410004dd985f118c90dd5179"><td class="mdescLeft">&#160;</td><td class="mdescRight">Create a new audiosource on a transform <br /></td></tr>
<tr class="separator:a6b5a139f410004dd985f118c90dd5179"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a132381c13acd88e5ef3e53e0e8c1ad66"><td class="memItemLeft" align="right" valign="top">static void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_audio_source.html#a132381c13acd88e5ef3e53e0e8c1ad66">PlayClipAtPoint</a> (string <a class="el" href="class_lua_1_1_audio_source.html#a4913b6f1fa8dfe5eb528cd7e40f91684">clip</a>, <a class="el" href="class_lua_1_1_vector3.html">Vector3</a> position, float <a class="el" href="class_lua_1_1_audio_source.html#a6e631df6c296491e5a29c8025878ddb4">volume</a>)</td></tr>
<tr class="memdesc:a132381c13acd88e5ef3e53e0e8c1ad66"><td class="mdescLeft">&#160;</td><td class="mdescRight">Plays an AudioClip at a given position in world space.  <a href="class_lua_1_1_audio_source.html#a132381c13acd88e5ef3e53e0e8c1ad66">More...</a><br /></td></tr>
<tr class="separator:a132381c13acd88e5ef3e53e0e8c1ad66"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7d14ab87ad493f85fa9ee1b747bf6df0"><td class="memItemLeft" align="right" valign="top">static float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_audio_source.html#a7d14ab87ad493f85fa9ee1b747bf6df0">GetClipLength</a> (string <a class="el" href="class_lua_1_1_audio_source.html#a4913b6f1fa8dfe5eb528cd7e40f91684">clip</a>)</td></tr>
<tr class="memdesc:a7d14ab87ad493f85fa9ee1b747bf6df0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Gets length of a specified AudioClip.  <a href="class_lua_1_1_audio_source.html#a7d14ab87ad493f85fa9ee1b747bf6df0">More...</a><br /></td></tr>
<tr class="separator:a7d14ab87ad493f85fa9ee1b747bf6df0"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="properties" name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a55ad4d09380c973f2001b2164aba4771"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_audio_source.html#a55ad4d09380c973f2001b2164aba4771">maxDistance</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a55ad4d09380c973f2001b2164aba4771"><td class="mdescLeft">&#160;</td><td class="mdescRight">MaxDistance is the distance where the sound is completely inaudible.  <a href="class_lua_1_1_audio_source.html#a55ad4d09380c973f2001b2164aba4771">More...</a><br /></td></tr>
<tr class="separator:a55ad4d09380c973f2001b2164aba4771"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66155ad664c665455476ea70f4779669"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_audio_source.html#a66155ad664c665455476ea70f4779669">minDistance</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a66155ad664c665455476ea70f4779669"><td class="mdescLeft">&#160;</td><td class="mdescRight">Within the Min distance the <a class="el" href="class_lua_1_1_audio_source.html" title="A representation of audio sources in 3D.">AudioSource</a> will cease to grow louder in volume.  <a href="class_lua_1_1_audio_source.html#a66155ad664c665455476ea70f4779669">More...</a><br /></td></tr>
<tr class="separator:a66155ad664c665455476ea70f4779669"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4913b6f1fa8dfe5eb528cd7e40f91684"><td class="memItemLeft" align="right" valign="top">string&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_audio_source.html#a4913b6f1fa8dfe5eb528cd7e40f91684">clip</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a4913b6f1fa8dfe5eb528cd7e40f91684"><td class="mdescLeft">&#160;</td><td class="mdescRight">The default AudioClip to play.  <a href="class_lua_1_1_audio_source.html#a4913b6f1fa8dfe5eb528cd7e40f91684">More...</a><br /></td></tr>
<tr class="separator:a4913b6f1fa8dfe5eb528cd7e40f91684"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaef47740090cbe41ff5b4f4b40aad7b7"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_audio_source.html#aaef47740090cbe41ff5b4f4b40aad7b7">isPlaying</a><code> [get]</code></td></tr>
<tr class="memdesc:aaef47740090cbe41ff5b4f4b40aad7b7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Is the clip playing right now (Read Only)?  <a href="class_lua_1_1_audio_source.html#aaef47740090cbe41ff5b4f4b40aad7b7">More...</a><br /></td></tr>
<tr class="separator:aaef47740090cbe41ff5b4f4b40aad7b7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4ffd0dfe8f989efe964e368cc2a5995c"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_audio_source.html#a4ffd0dfe8f989efe964e368cc2a5995c">loop</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a4ffd0dfe8f989efe964e368cc2a5995c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Is the audio clip looping?  <a href="class_lua_1_1_audio_source.html#a4ffd0dfe8f989efe964e368cc2a5995c">More...</a><br /></td></tr>
<tr class="separator:a4ffd0dfe8f989efe964e368cc2a5995c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad82dcfe66567f1dac8a15edc327c89ff"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_audio_source.html#ad82dcfe66567f1dac8a15edc327c89ff">mute</a><code> [get, set]</code></td></tr>
<tr class="memdesc:ad82dcfe66567f1dac8a15edc327c89ff"><td class="mdescLeft">&#160;</td><td class="mdescRight">Un- / Mutes the <a class="el" href="class_lua_1_1_audio_source.html" title="A representation of audio sources in 3D.">AudioSource</a>. Mute sets the volume=0, Un-Mute restore the original volume.  <a href="class_lua_1_1_audio_source.html#ad82dcfe66567f1dac8a15edc327c89ff">More...</a><br /></td></tr>
<tr class="separator:ad82dcfe66567f1dac8a15edc327c89ff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a49d0053bb3cfa0b42c70f3b678e0d78f"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_audio_source.html#a49d0053bb3cfa0b42c70f3b678e0d78f">pitch</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a49d0053bb3cfa0b42c70f3b678e0d78f"><td class="mdescLeft">&#160;</td><td class="mdescRight">The pitch of the audio source.  <a href="class_lua_1_1_audio_source.html#a49d0053bb3cfa0b42c70f3b678e0d78f">More...</a><br /></td></tr>
<tr class="separator:a49d0053bb3cfa0b42c70f3b678e0d78f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a88e74fc4c2c4cf17747d6bbdad516a8f"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_audio_source.html#a88e74fc4c2c4cf17747d6bbdad516a8f">spatialBlend</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a88e74fc4c2c4cf17747d6bbdad516a8f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets how much this <a class="el" href="class_lua_1_1_audio_source.html" title="A representation of audio sources in 3D.">AudioSource</a> is affected by 3D spatialisation calculations (attenuation, doppler etc). 0.0 makes the sound full 2D, 1.0 makes it full 3D.  <a href="class_lua_1_1_audio_source.html#a88e74fc4c2c4cf17747d6bbdad516a8f">More...</a><br /></td></tr>
<tr class="separator:a88e74fc4c2c4cf17747d6bbdad516a8f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6e631df6c296491e5a29c8025878ddb4"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_lua_1_1_audio_source.html#a6e631df6c296491e5a29c8025878ddb4">volume</a><code> [get, set]</code></td></tr>
<tr class="memdesc:a6e631df6c296491e5a29c8025878ddb4"><td class="mdescLeft">&#160;</td><td class="mdescRight">The volume of the audio source (0.0 to 1.0).  <a href="class_lua_1_1_audio_source.html#a6e631df6c296491e5a29c8025878ddb4">More...</a><br /></td></tr>
<tr class="separator:a6e631df6c296491e5a29c8025878ddb4"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >A representation of audio sources in 3D. </p>
<p >You can play a single audio clip using Play, Pause and Stop. You can also adjust its volume while playing using the volume property, or seek using time. Multiple sounds can be played on one <a class="el" href="class_lua_1_1_audio_source.html" title="A representation of audio sources in 3D.">AudioSource</a> using PlayOneShot. You can play a clip at a static position in 3D space using PlayClipAtPoint. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a7d14ab87ad493f85fa9ee1b747bf6df0" name="a7d14ab87ad493f85fa9ee1b747bf6df0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7d14ab87ad493f85fa9ee1b747bf6df0">&#9670;&nbsp;</a></span>GetClipLength()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static float Lua.AudioSource.GetClipLength </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>clip</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Gets length of a specified AudioClip. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">clip</td><td>Audio data filename.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>length in seconds</dd></dl>

</div>
</div>
<a id="aad8aea6c6f265dfe440a6a8620416bf4" name="aad8aea6c6f265dfe440a6a8620416bf4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aad8aea6c6f265dfe440a6a8620416bf4">&#9670;&nbsp;</a></span>Pause()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.AudioSource.Pause </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Pauses playing the clip. </p>
<p >See Also: Play, Stop functions. </p>

</div>
</div>
<a id="a132381c13acd88e5ef3e53e0e8c1ad66" name="a132381c13acd88e5ef3e53e0e8c1ad66"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a132381c13acd88e5ef3e53e0e8c1ad66">&#9670;&nbsp;</a></span>PlayClipAtPoint()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static void Lua.AudioSource.PlayClipAtPoint </td>
          <td>(</td>
          <td class="paramtype">string&#160;</td>
          <td class="paramname"><em>clip</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="class_lua_1_1_vector3.html">Vector3</a>&#160;</td>
          <td class="paramname"><em>position</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>volume</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Plays an AudioClip at a given position in world space. </p>
<p >This function creates an audio source, but automatically disposes of it once the clip has finished playing. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">clip</td><td>Audio data filename.</td></tr>
    <tr><td class="paramname">position</td><td>Position in world space from which sound originates.</td></tr>
    <tr><td class="paramname">volume</td><td>Playback volume.</td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a3bbbde9384f38a43f97e48cd162b0dac" name="a3bbbde9384f38a43f97e48cd162b0dac"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3bbbde9384f38a43f97e48cd162b0dac">&#9670;&nbsp;</a></span>UnPause()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void Lua.AudioSource.UnPause </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Unpause the paused playback of this <a class="el" href="class_lua_1_1_audio_source.html" title="A representation of audio sources in 3D.">AudioSource</a>. </p>
<p >This function is similar to calling Play () on a paused <a class="el" href="class_lua_1_1_audio_source.html" title="A representation of audio sources in 3D.">AudioSource</a>, except that it will not create a new playback voice if it is not currently paused. This is also useful if you have paused one-shots and want to resume playback without creating a new playback voice for the attached AudioClip. </p>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="a4913b6f1fa8dfe5eb528cd7e40f91684" name="a4913b6f1fa8dfe5eb528cd7e40f91684"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4913b6f1fa8dfe5eb528cd7e40f91684">&#9670;&nbsp;</a></span>clip</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">string Lua.AudioSource.clip</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The default AudioClip to play. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="aaef47740090cbe41ff5b4f4b40aad7b7" name="aaef47740090cbe41ff5b4f4b40aad7b7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaef47740090cbe41ff5b4f4b40aad7b7">&#9670;&nbsp;</a></span>isPlaying</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.AudioSource.isPlaying</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Is the clip playing right now (Read Only)? </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a4ffd0dfe8f989efe964e368cc2a5995c" name="a4ffd0dfe8f989efe964e368cc2a5995c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4ffd0dfe8f989efe964e368cc2a5995c">&#9670;&nbsp;</a></span>loop</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.AudioSource.loop</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Is the audio clip looping? </p>
<p >If you disable looping on a playing <a class="el" href="class_lua_1_1_audio_source.html" title="A representation of audio sources in 3D.">AudioSource</a> the sound will stop after the end of the current loop. </p><dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a55ad4d09380c973f2001b2164aba4771" name="a55ad4d09380c973f2001b2164aba4771"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a55ad4d09380c973f2001b2164aba4771">&#9670;&nbsp;</a></span>maxDistance</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.AudioSource.maxDistance</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>MaxDistance is the distance where the sound is completely inaudible. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a66155ad664c665455476ea70f4779669" name="a66155ad664c665455476ea70f4779669"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a66155ad664c665455476ea70f4779669">&#9670;&nbsp;</a></span>minDistance</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.AudioSource.minDistance</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Within the Min distance the <a class="el" href="class_lua_1_1_audio_source.html" title="A representation of audio sources in 3D.">AudioSource</a> will cease to grow louder in volume. </p>
<p >Outside the min distance the volume starts to attenuate. </p><dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="ad82dcfe66567f1dac8a15edc327c89ff" name="ad82dcfe66567f1dac8a15edc327c89ff"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad82dcfe66567f1dac8a15edc327c89ff">&#9670;&nbsp;</a></span>mute</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool Lua.AudioSource.mute</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Un- / Mutes the <a class="el" href="class_lua_1_1_audio_source.html" title="A representation of audio sources in 3D.">AudioSource</a>. Mute sets the volume=0, Un-Mute restore the original volume. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a49d0053bb3cfa0b42c70f3b678e0d78f" name="a49d0053bb3cfa0b42c70f3b678e0d78f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a49d0053bb3cfa0b42c70f3b678e0d78f">&#9670;&nbsp;</a></span>pitch</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.AudioSource.pitch</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The pitch of the audio source. </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a88e74fc4c2c4cf17747d6bbdad516a8f" name="a88e74fc4c2c4cf17747d6bbdad516a8f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a88e74fc4c2c4cf17747d6bbdad516a8f">&#9670;&nbsp;</a></span>spatialBlend</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.AudioSource.spatialBlend</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Sets how much this <a class="el" href="class_lua_1_1_audio_source.html" title="A representation of audio sources in 3D.">AudioSource</a> is affected by 3D spatialisation calculations (attenuation, doppler etc). 0.0 makes the sound full 2D, 1.0 makes it full 3D. </p>
<p >Aside from determining if this <a class="el" href="class_lua_1_1_audio_source.html" title="A representation of audio sources in 3D.">AudioSource</a> is heard as a 2D or 3D source, this property is useful to morph between the two modes. 3D spatial calculations are applied after stereo panning is determined and can be used in conjunction with panStereo. Morphing between the 2 modes is useful for sounds that should be progressively heard as normal 2D sounds the closer they are to the listener. </p><dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<a id="a6e631df6c296491e5a29c8025878ddb4" name="a6e631df6c296491e5a29c8025878ddb4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6e631df6c296491e5a29c8025878ddb4">&#9670;&nbsp;</a></span>volume</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">float Lua.AudioSource.volume</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">get</span><span class="mlabel">set</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The volume of the audio source (0.0 to 1.0). </p>
<dl class="section return"><dt>Returns</dt><dd></dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>LuaAudioSource.cs</li>
</ul>
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="namespace_lua.html">Lua</a></li><li class="navelem"><a class="el" href="class_lua_1_1_audio_source.html">AudioSource</a></li>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.4 </li>
  </ul>
</div>
</body>
</html>

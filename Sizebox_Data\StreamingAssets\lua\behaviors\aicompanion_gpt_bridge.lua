-- AI GPT Bridge Integration (Add-on Script for Sizebox AI Companion)
-- Allows toggling between local prewritten responses and real OpenAI API responses

local AIGPTBridge = RegisterBehavior("AI GPT Bridge")
AIGPTBridge.data = {
    menuEntry = "AI/Enable GPT Bridge",
    agent = { type = { "giantess" } },
    target = { type = { "oneself" } },
    settings = {
        {"enableAI", "Enable AI API (OpenAI)", "boolean", false},
        {"apiKey", "OpenAI API Key", "string", "sk-..."},
        {"chatModel", "Model", "string", "gpt-3.5-turbo"},
        {"maxTokens", "Max Tokens", "int", 150},
        {"temp", "Temperature", "float", 0.8}
    },
    secondary = true
}

function AIGPTBridge:Start()
    self.aiEnabled = self:GetSetting("enableAI")
    self.apiKey = self:GetSetting("apiKey")
    self.model = self:GetSetting("chatModel")
    self.maxTokens = self:GetSetting("maxTokens")
    self.temp = self:GetSetting("temp")
end

function AIGPTBridge:SendPrompt(prompt)
    if not self.aiEnabled then
        return "[AI DISABLED] " .. prompt
    end

    -- Write prompt to file
    local promptFile = "ai_input.txt"
    local out = io.open(promptFile, "w")
    if out then
        out:write(prompt)
        out:close()
    else
        return "[ERROR] Could not write to input file."
    end

    -- Wait for the Python script to write back a response
    local responseFile = "ai_output.txt"
    local reply = ""
    local waitTime = 0
    while waitTime < 6 do
        local f = io.open(responseFile, "r")
        if f then
            reply = f:read("*all")
            f:close()
            if #reply > 0 then break end
        end
        coroutine.yield(0.5)
        waitTime = waitTime + 0.5
    end

    return (#reply > 0) and reply or "[AI TIMEOUT] No response."
end

function AIGPTBridge:OnMessage(agent, message)
    local reply = self:SendPrompt(message)
    agent:Say(reply)
end

-- Animation Test Script
-- Tests if we can execute specific animations on the character

AnimationTest = RegisterBehavior("AnimationTest")
AnimationTest.data = {
    menuEntry = "Test/Animation Test",
    agent = { type = { "humanoid" } },
    target = { type = { "oneself" } },
    secondary = true
}

-- List of animations and poses to test
local testAnimations = {
    {name = "z17LookDown12", type = "animation"}, --animation
    {name = "z17LookDown14", type = "animation"}, --lookstraighdown
    {name = "z18LookDown07", type = "animation"}, --handswaist
    {name = "z17Floor04", type = "animation"}, --layonchest2
    {name = "z17HandFace06", type = "animation"}, --oops
    {name = "z17Stomp20", type = "animation"}, --playfulfakecrush
    {name = "z18Bend08", type = "animation"}, --lookdownsorta
    {name = "z5Hmm", type = "animation"}, --thinking
    {name = "z5idle1", type = "animation"}, --idle animation
    {name = "z5ojigikeirei", type = "animation"}, --bow
    {name = "z5nokubihuri", type = "animation"}, --nono
    {name = "z5pose08", type = "animation"}, --layonchest
    {name = "z5pose13", type = "animation"}, --sexysit
    {name = "z5tray3", type = "animation"}, --hold
    {name = "z7cat", type = "animation"}, --cat-4s
    {name = "z7deepCity02", type = "animation"}, --idle
    {name = "z7hand02", type = "animation"}, --hold
    {name = "z19Boob02", type = "animation"}, --teasechest
    {name = "z19HandFront02", type = "animation"}, --handnearchest
    
}

function AnimationTest:Start()
    print("DEBUG: Animation Test - Starting test sequence...")

    self.testActive = false  -- Start in manual mode
    self.testTimer = 0
    self.currentTestIndex = 1
    self.testInterval = 3.0  -- 3 seconds between tests

    -- Show controls
    Game.Toast.New().Print("Animation/Pose Test Controls: 1-7 = Test items, T = Auto-test, R = Reset to Idle")
    print("Animation/Pose Test Controls:")
    print("1-7: Test specific animations/poses")
    print("T: Start/stop automatic testing")
    print("R: Reset to Idle")
    print("Available animations and poses:")
    for i, item in ipairs(testAnimations) do
        print("  " .. i .. ": " .. item.name .. " (" .. item.type .. ")")
    end
end

function AnimationTest:TestAnimation(testItem)
    local itemName = testItem.name
    local itemType = testItem.type

    print("DEBUG: Animation Test - Testing " .. itemType .. ": " .. itemName)

    -- Try to set the animation or pose
    local success = pcall(function()
        if itemType == "pose" then
            self.agent.animation.setPose(itemName)
        else
            self.agent.animation.Set(itemName)
        end
    end)

    if success then
        print("SUCCESS: " .. itemType .. " '" .. itemName .. "' executed successfully!")
        Game.Toast.New().Print("✅ " .. itemName .. " (" .. itemType .. ") - SUCCESS")
    else
        print("FAILED: " .. itemType .. " '" .. itemName .. "' failed to execute!")
        Game.Toast.New().Print("❌ " .. itemName .. " (" .. itemType .. ") - FAILED")
    end

    return success
end
function AnimationTest:Update()
    -- Manual testing controls
    if Input and Input.GetKeyDown then
        -- Press 1-7 to test specific animations/poses
        if Input.GetKeyDown("1") and testAnimations[1] then
            self:TestAnimation(testAnimations[1])
        elseif Input.GetKeyDown("2") and testAnimations[2] then
            self:TestAnimation(testAnimations[2])
        elseif Input.GetKeyDown("3") and testAnimations[3] then
            self:TestAnimation(testAnimations[3])
        elseif Input.GetKeyDown("4") and testAnimations[4] then
            self:TestAnimation(testAnimations[4])
        elseif Input.GetKeyDown("5") and testAnimations[5] then
            self:TestAnimation(testAnimations[5])
        elseif Input.GetKeyDown("6") and testAnimations[6] then
            self:TestAnimation(testAnimations[6])
        elseif Input.GetKeyDown("7") and testAnimations[7] then
            self:TestAnimation(testAnimations[7])
        elseif Input.GetKeyDown("t") then
            -- Press T to start/stop automatic testing
            if self.testActive then
                self.testActive = false
                Game.Toast.New().Print("Animation testing stopped")
            else
                self.testActive = true
                self.testTimer = 0
                self.currentTestIndex = 1
                Game.Toast.New().Print("Animation testing started - will cycle every 3 seconds")
                self:TestAnimation(testAnimations[1])
            end
        elseif Input.GetKeyDown("r") then
            -- Press R to reset to idle
            if self.agent and self.agent.animation then
                self.agent.animation.Set("Idle")
                Game.Toast.New().Print("Reset to Idle animation")
            end
        end
    end

    if not self.testActive then
        return
    end

    -- Update timer (using simple frame counting since Time.deltaTime might not be available)
    self.testTimer = (self.testTimer or 0) + 0.016  -- Assume ~60 FPS

    -- Check if it's time for next test
    if self.testTimer >= self.testInterval then
        self.testTimer = 0
        self.currentTestIndex = self.currentTestIndex + 1

        -- Check if we've tested all animations
        if self.currentTestIndex > #testAnimations then
            print("DEBUG: Animation Test - All animations tested! Stopping test.")
            self.testActive = false
            Game.Toast.New().Print("Animation Test Complete! Check console for results.")
            return
        end

        -- Test next animation
        local nextAnimation = testAnimations[self.currentTestIndex]
        self:TestAnimation(nextAnimation)
    end
end

function AnimationTest:Stop()
    self.testActive = false
    print("DEBUG: Animation Test - Test stopped")
    
    -- Return to idle
    if self.agent and self.agent.animation then
        self.agent.animation.Set("Idle")
        print("DEBUG: Animation Test - Returned to Idle")
    end
end

print("Animation Test Script Loaded! Use the behavior menu to activate it.")

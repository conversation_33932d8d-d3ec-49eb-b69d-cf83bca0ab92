-- CONSTANTS
local BEHAVIOR_FLAG = "GrowingRampageGame"

local VIEW_RADIUS = 1
local VIEW_RADIUS_CLOSE = VIEW_RADIUS / 4
local VIEW_DISTANCE = 1
local FOV = 180

local STARTING_SCALE = 60
local MINIMUM_PERCENT_CHANGE_TO_GROW = 0.1

local MIN_SPURT_LENGTH = 2.5
local MAX_SPURT_LENGTH = 7.5

local AIMER_OBJECT = "empty.object\\empty"
local EMPTY_OBJECT = "empty.object\\empty"

-- INTERACTION BEHAVIOR NAMES
INTERACTION_EAT = "GtsEatSingle"
INTERACTION_STOMP = "StompSingle"

KEY_EAT_SUCCESS = "Eaten" 
KEY_STOMP_SUCCESS = "StompKill"

GROWTH_EAT = 2
GROWTH_STOMP = 1


MICRO_FREEZE_BEHAVIOR = "Terror"

-- REGISTER SCRIPT
GrowingRampageGame = RegisterBehavior("GrowingRampageGame")
GrowingRampageGame.data = {
    menuEntry = "Game Modes/Growing Rampage",
    secondary = true,
    flags = { BEHAVIOR_FLAG, "game" },
    agent = {
        type = { "giantess"}
    },
    target = {
        type = {"oneself"}
    },
    settings = {
    	{ "eat_key", "Eat Key", "string", "e" },
    	{ "stomp_key", "Stomp Key", "string", "q" },
    	{ "growth_key", "Growth Spurt Key", "string", "c" },
    	{ "use_growth_spurts", "Use Growth Spurts", "bool", true },
    	{ "growth_mult", "Growth Rate Multiplier", "float", 0.2 },
    	{ "start_size_mult", "Start Size Multiplier", "float", 0.1 }

	}
}



function GrowingRampageGame:Start()
	Log("Starting the Rampage")
	Log("-")
	Log("Please stop this script before deleting this giantess.")
	Log("Failure to do so will break crushing in the entire scene.")
	Log("-")

	self.stompKills = 0
	self.eaten = 0

	self.agent.senses.fieldOfView = FOV

	self:CreateAimer()
	self:CreateLookTarget()

	self.agent.LookAt(self.lookTarget)

	self.maxScale = STARTING_SCALE * self.start_size_mult
	self:SetSize(self.maxScale, 1)

	self.crushListener = Event.Register(self, "OnCrush", self.HandleStomp)
end

function GrowingRampageGame:CreateAimer()
	-- Create the aimer
	local aimer = Entity.SpawnObject(AIMER_OBJECT, Vector3.New(0,0,0), Quaternion.identity, 1)
	aimer.transform.SetParent(self.agent.transform)

	aimer.transform.rotation = Quaternion.Euler(90,0,0)
	aimer.transform.localScale = Vector3.New(6000,6000,4000)
	aimer.transform.localPosition = Vector3.New(0,0,500)
	
	self.aimer = aimer
end

function GrowingRampageGame:CreateLookTarget()
	local lookTarget = Entity.SpawnObject(EMPTY_OBJECT, Vector3.New(0,0,0), Quaternion.identity, 1)
	lookTarget.transform.SetParent(self.agent.transform)

	lookTarget.transform.localScale = Vector3.New(1,1,1)
	lookTarget.transform.localPosition = Vector3.New(0,1000,500)
	
	self.lookTarget = lookTarget
end


function GrowingRampageGame:Update()
	
	self:HandleActiveBehaviors()
	if self.skipUpdate then
		return
	end

	-- EAT
	if Input.GetKeyDown(self.eat_key) then
		local target = self:FindTarget()
		if target then
			self:StartBehavior(INTERACTION_EAT, target, true)
			return
		end
	end

	-- STOMP
	if Input.GetKeyDown(self.stomp_key) then
		local target = self:FindTarget()
		if target then
			self:StartBehavior(INTERACTION_STOMP, target, true)
			return
		end
	end

	-- GROWTH SPURT
	if Input.GetKeyDown(self.growth_key) and self.use_growth_spurts then
		self:HandleGrowthSpurt()
	end
end


function GrowingRampageGame:SetSize(newSize, growthTime)
	local currentScale = self.agent.scale
	local scaleFactor = (newSize / currentScale) - 1.0

	if not growthTime then
		growthTime = 1
	end
	self.agent.Grow(scaleFactor, growthTime)
end


-- Starts the given behavior on the gts and caches info about the behavior and target
function GrowingRampageGame:StartBehavior(interactionName, target, shouldTargetFreeze)
	self.agent.LookAt(nil)

	if target.ai and shouldTargetFreeze and not target.isPlayer() then
        target.ai.SetBehavior(MICRO_FREEZE_BEHAVIOR)
    end

    self.previousTarget = target
    self.previousInteraction = interactionName
	self.agent.ai.SetBehavior(interactionName, target)
	return
end

function GrowingRampageGame:HandleActiveBehaviors()
	-- Skip update while a behavior is running
	if self.agent.ai.IsBehaviorActive() then
		self.skipUpdate = true	
		return
	end
	
	-- Once we finish
	if self.skipUpdate then
		self.skipUpdate = false
		
		if self.previousInteraction == INTERACTION_EAT then
			self:HandleEatFinish()
		elseif self.previousInteraction == INTERACTION_STOMP then
			--self:HandleStompFinish()
		end

		self.agent.LookAt(self.lookTarget)
	end
end

function GrowingRampageGame:HandleEatFinish()
	if self.agent.dict[KEY_EAT_SUCCESS] then
		self.agent.dict[KEY_EAT_SUCCESS] = false
		self.eaten = self.eaten + 1
		--Log("Eaten "..self.eaten.." tinies")
		self:IncreaseMaxScale(GROWTH_EAT)
	end
end

function GrowingRampageGame:HandleStomp(data)
	if data.crusher ~= self.agent then
		return
	end

	self.stompKills = self.stompKills + 1
	--Log("Stomped on "..self.stompKills.." tinies")
	self:IncreaseMaxScale(GROWTH_STOMP)
end


function GrowingRampageGame:IncreaseMaxScale(increase)
	self.maxScale = self.maxScale + (increase * self.growth_mult)

	if not self.use_growth_spurts then
		local length = math.random(MIN_SPURT_LENGTH/2, MAX_SPURT_LENGTH/2)
		self:SetSize(self.maxScale, length)

	elseif self.maxScale - self.agent.scale > self.agent.scale * MINIMUM_PERCENT_CHANGE_TO_GROW then
		Log("Growth spurt ready...")
	end
end

function GrowingRampageGame:HandleGrowthSpurt()
	local currentScale = self.agent.scale

	-- If we can grow, and its substantial enough
	if currentScale < self.maxScale and (self.maxScale - currentScale >= currentScale * MINIMUM_PERCENT_CHANGE_TO_GROW) then
		-- Do a spurt
		local length = math.random(MIN_SPURT_LENGTH, MAX_SPURT_LENGTH)
		self:SetSize(self.maxScale, length)
		Log("Growing...")
	end
end


function GrowingRampageGame:Exit()
	self.aimer.Delete()
	self.lookTarget.Delete()

	Event.Unregister(self.crushListener)

	Log("Ending the rampage...")
	Log(self.stompKills.." tinies were trampled.")
	Log(self.eaten.." tinies were eaten.")
end

-- LUA HOW DO HASHSET!?!?!? AAAAAAHHHHHHH
function GrowingRampageGame:FindTarget()
	local visibleMicros = self.agent.senses.GetVisibleMicros(VIEW_DISTANCE)
	local nearbyMicros = self.agent.senses.GetMicrosInRadius(VIEW_RADIUS)

	local targets = {}
	local targetCount = 0

	-- For every nearby micro
	for _, micro in ipairs(nearbyMicros) do
		-- Cross reference with visible micros
		for _, visibleMicro in ipairs(visibleMicros) do
			-- Add target if in both
			if Entity.Equals(micro, visibleMicro) then
				targets[targetCount] = micro
				targetCount = targetCount + 1
				break;
			end
		end
	end 

	-- Check for super close micros if we don't have targets
	if targetCount == 0 then	
		local superCloseMicros = self.agent.senses.GetMicrosInRadius(VIEW_RADIUS_CLOSE)

		-- Make them the targets
		for _, micro in ipairs(superCloseMicros) do
			targets[targetCount] = micro
			targetCount = targetCount + 1
		end

		if targetCount == 0 then
			return nil
		end
	end
	
	if targetCount == 1 then
		return targets[0]
	end

	-- Search and return the closest to the aimer
	
	-- Position to calculate closest from
	local aimerPos = self.aimer.transform.position

	local closest = targets[0]
	local prevDist = Vector3.Distance(aimerPos, closest.transform.position)
	for i=1, targetCount-1 do 
		local newDist = Vector3.Distance(aimerPos, targets[i].transform.position)
		if newDist < prevDist then
			closest = targets[i]
			prevDist = newDist
		end
	end
	return closest
end


-- REGISTER SCRIPT
GrowingRampageGameStop = RegisterBehavior("GrowingRampageGameStop")
GrowingRampageGameStop.data = {
    menuEntry = "Game Modes/^   Stop   ^",
    secondary = true,
    flags = { BEHAVIOR_FLAG },
    agent = {
        type = { "giantess"}
    },
    target = {
        type = {"oneself"}
    }
}

function GrowingRampageGameStop:Start()
	self.agent.ai.StopSecondaryBehavior(BEHAVIOR_FLAG)
end
